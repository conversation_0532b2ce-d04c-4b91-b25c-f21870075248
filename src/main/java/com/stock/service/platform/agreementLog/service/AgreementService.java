package com.stock.service.platform.agreementLog.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.agreementLog.dao.AgreementMapper;
import com.stock.service.platform.agreementLog.dto.AgreementDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


@Service
@Transactional(rollbackFor = Exception.class)
public class AgreementService extends BaseService {

    @Resource
    private AgreementMapper agreementMapper;

    public List<AgreementDto> getAgreement() {
        return agreementMapper.getAgreement();
    }

}
