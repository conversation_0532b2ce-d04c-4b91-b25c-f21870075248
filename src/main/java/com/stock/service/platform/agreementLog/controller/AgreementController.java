package com.stock.service.platform.agreementLog.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.agreementLog.dto.AgreementDto;
import com.stock.service.platform.agreementLog.service.AgreementService;
import com.stock.service.platform.agreementLog.service.LoginAgreementLogService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@RequestMapping("agreement")
public class AgreementController extends BaseController {

    @Resource
    private AgreementService agreementService;

    @Resource
    private LoginAgreementLogService loginAgreementLogService;

    @RequestMapping("getAgreement")
    @ResponseBody
    public JsonResponse<List<AgreementDto>> getAgreement() {
        JsonResponse<List<AgreementDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(agreementService.getAgreement());
        return jsonResponse;
    }

    @RequestMapping("saveLoginAgreementLog")
    @ResponseBody
    public JsonResponse<Boolean> saveLoginAgreementLog(HttpServletRequest request, @RequestBody List<AgreementDto> agreementDtos) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        loginAgreementLogService.saveLoginAgreementLog(request,agreementDtos);
        jsonResponse.setResult(true);
        return jsonResponse;
    }
}
