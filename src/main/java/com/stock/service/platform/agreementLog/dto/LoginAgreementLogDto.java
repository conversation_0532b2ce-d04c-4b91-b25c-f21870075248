package com.stock.service.platform.agreementLog.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class LoginAgreementLogDto {

    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private String agreementId;

    @ExcelProperty(value = "版本号", index = 1)
    @ColumnWidth(12)
    private String version;

    @ExcelIgnore
    private String userId;

    @ExcelProperty(value = "用户名",  index = 2)
    @ColumnWidth(16)
    private String userName;

    @ExcelProperty(value = "用户IP", index = 3)
    @ColumnWidth(16)
    private String loginIp;

    @ExcelIgnore
    private String createUser;

    @ExcelProperty(value = "登录时间", index = 4)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private String updateUser;

    @ExcelIgnore
    private Date updateTime;

    @ExcelProperty(value = "类型", index = 0)
    @ColumnWidth(12)
    private String agreementType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelIgnore
    private Date endTime;

}
