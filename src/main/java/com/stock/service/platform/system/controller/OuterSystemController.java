package com.stock.service.platform.system.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.system.service.OuterSystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("outerSystem")
public class OuterSystemController extends BaseController {

    @Autowired
    private OuterSystemService service;

    @PostMapping("/queryTableData")
    @ResponseBody
    public JsonResponse<Object> queryTableData(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.queryTableData(param));
        }catch (Exception e) {
            response.setErrorMsg("获取接入系统列表数据报错");
        }
        return response;
    }

    @PostMapping("/queryDetailData")
    @ResponseBody
    public JsonResponse<Object> queryDetailData(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.queryDetailData(param));
        }catch (Exception e) {
            response.setErrorMsg("获取接入系统配置数据报错");
        }
        return response;
    }

    @PostMapping("/saveConfigData")
    @ResponseBody
    public JsonResponse<Object> saveConfigData(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.saveConfigData(param));
        }catch (Exception e) {
            response.setErrorMsg("保存接入系统配置数据报错");
        }
        return response;
    }

    @PostMapping("/lockStatusClick")
    @ResponseBody
    public JsonResponse<Object> lockStatusClick(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.lockStatusClick(param));
            if ("1".equals(param.get("status"))) {
                response.setErrorMsg("解锁成功");
            } else {
                response.setErrorMsg("锁定成功");
            }
        }catch (Exception e) {
            if ("1".equals(param.get("status"))) {
                response.setErrorMsg("解锁失败");
            } else {
                response.setErrorMsg("锁定失败");
            }
        }
        return response;
    }
}
