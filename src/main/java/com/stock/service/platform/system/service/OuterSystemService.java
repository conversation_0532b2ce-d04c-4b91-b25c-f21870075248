package com.stock.service.platform.system.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.entity.MessageInfo;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.system.service.DepartmentService;
import com.stock.service.platform.common.util.MessageUtil;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

@Service
public class OuterSystemService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(OuterSystemService.class);

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Resource
    private LogService logService;

    public Object queryTableData(Map<String, Object> param) {
        SystemConfigExample example = new SystemConfigExample();
        example.createCriteria().andSysCodeIsNotNull();
        return systemConfigMapper.selectByExample(example);
    }

    public Object queryDetailData(Map<String, Object> param) {
        return systemConfigMapper.selectByPrimaryKey(MapUtils.getString(param, "id"));
    }

    public boolean saveConfigData(Map<String, Object> param) {
        SystemConfig record = new SystemConfig();
        record.setId(MapUtils.getString(param, "id", ""));
        record.setSysCode(MapUtils.getString(param, "sysCode"));
        record.setSysName(MapUtils.getString(param, "sysName"));
        record.setSysOrder(MapUtils.getInteger(param, "sysOrder"));
        record.setSysConfig(MapUtils.getString(param, "sysConfig"));
        record.setUpdateTime(new Date());
        record.setUpdateUser(getUserInfo().getUserId());
        SystemConfig systemConfigOld = systemConfigMapper.selectByPrimaryKey(record.getId());
        systemConfigMapper.updateByPrimaryKeyWithBLOBs(record);
        try {
            MessageUtil messageUtil = new MessageUtil();
            MessageInfo messageInfo = messageUtil.getMessageContext(record, systemConfigOld);
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(record),
                            messageInfo.getOperationContext(), "编辑接入系统",
                            "系统管理/接入系统管理", "OuterSystemService.saveConfigData",
                            messageInfo.getOperationRemark(), ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return true;
    }

    public Object lockStatusClick(Map<String, Object> param) {
        systemConfigMapper.lockStatusClick(param);
        return true;
    }
}
