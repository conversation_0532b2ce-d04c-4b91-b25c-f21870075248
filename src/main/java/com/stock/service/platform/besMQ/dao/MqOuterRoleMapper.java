package com.stock.service.platform.besMQ.dao;

import com.stock.service.platform.common.entity.OuterRole;
import com.stock.service.platform.common.entity.OuterRoleExample;

public interface MqOuterRoleMapper {

    int insert(OuterRole record);

    int update(OuterRole record);

    int deleteByExample(OuterRoleExample example);

    void deleteSysRole(String sysCode);

    int getCount(String id);
}
