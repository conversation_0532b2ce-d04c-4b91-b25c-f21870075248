package com.stock.service.platform.besMQ.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stock.service.platform.common.dao.SystemWebRequestLogMapper;
import com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ReceiveLogService {

    @Autowired
    private SystemWebRequestLogMapper systemWebRequestLogMapper;

    public void processingBusiness(Map<String, Object> parse) {
        Map<String, Object> info = (Map<String, Object>) parse.get("info");

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            // 将Map转换为实体类
            SystemWebRequestLogWithBLOBs requestLog = objectMapper.convertValue(info, SystemWebRequestLogWithBLOBs.class);
            requestLog.setSystemName(String.valueOf(parse.get("systemType")));
            systemWebRequestLogMapper.insert(requestLog);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
