package com.stock.service.platform.besMQ.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stock.service.platform.besMQ.dao.MqOuterRoleMapper;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.ActiveMqMapper;
import com.stock.service.platform.common.dao.OuterRoleUserMapMapper;
import com.stock.service.platform.common.entity.OuterRole;
import com.stock.service.platform.common.entity.OuterRoleExample;
import com.stock.service.platform.common.entity.OuterRoleUserMapExample;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ReceiveRoleService {

    @Resource
    private ActiveMqMapper activeMqMapper;

    @Resource
    private MqOuterRoleMapper mqOuterRoleMapper;

    @Resource
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    public void processingBusiness(Map<String, Object> parse) {
        String operationType = String.valueOf(parse.get("operationType"));
        try {
            if (!TopicConstant.OP_TYPE_ALL.equals(operationType)) {
                Map<String, Object> dtoMap = new HashMap<>(parse);
                ObjectMapper objectMapper = new ObjectMapper();
                dtoMap.put("info", objectMapper.writeValueAsString(dtoMap.get("info")));
                dtoMap.put("isSuccess", "3");
                activeMqMapper.insertReceiveInfo(dtoMap);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        switch (operationType) {
            case TopicConstant.OP_TYPE_INSERT:
            case TopicConstant.OP_TYPE_UPDATE:
                insertRole(parse);
                break;
            case TopicConstant.OP_TYPE_DELETE:
                delRole(parse);
                break;
            //case TopicConstant.OP_TYPE_ALL:
            //    insertAllRole(parse);
            //    break;
            default:
                // 处理未知的业务类型
                break;
        }

    }

    private void delRole(Map<String, Object> parse) {
        try {
            List<String> idList = (List<String>) parse.get("info");
            // 删除sa_role 角色表
            OuterRoleExample roleExample = new OuterRoleExample();
            roleExample.createCriteria().andIdIn(idList);
            mqOuterRoleMapper.deleteByExample(roleExample);
            // 删除sa_user_role_map表 相同角色Id数据
            OuterRoleUserMapExample userRoleMapExample = new OuterRoleUserMapExample();
            userRoleMapExample.createCriteria().andRoleIdIn(idList);
            outerRoleUserMapMapper.deleteByExample(userRoleMapExample);
        } catch (Exception e) {
            log.error("del subsystem role error");
            e.printStackTrace();
        }
    }

    private void insertRole(Map<String, Object> parse) {
        try {
            //传id roleName roleDesc
            Map<String, Object> info = (Map<String, Object>) parse.get("info");
            ModelMapper modelMapper = new ModelMapper();
            OuterRole role = modelMapper.map(info, OuterRole.class);
            if (mqOuterRoleMapper.getCount(role.getId()) == 0) {
                role.setSource(CodeTableConstant.DATA_STATUS_0);
                mqOuterRoleMapper.insert(role);
            } else {
                role.setSource(CodeTableConstant.DATA_STATUS_0);
                mqOuterRoleMapper.update(role);
            }
        } catch (Exception e) {
            log.error("insert subsystem role error");
            e.printStackTrace();
        }
    }

    //private void updateRole(Map<String, Object> parse) {
    //    //传id roleName roleDesc
    //    try {
    //        Map<String, Object> info = (Map<String, Object>) parse.get("info");
    //        ModelMapper modelMapper = new ModelMapper();
    //        OuterRole role = modelMapper.map(info, OuterRole.class);
    //        //role.setSysCode(String.valueOf(parse.get("systemType")));
    //        role.setSource(CodeTableConstant.DATA_STATUS_0);
    //        mqOuterRoleMapper.update(role);
    //    } catch (Exception e) {
    //        log.error("update subsystem role error");
    //        e.printStackTrace();
    //    }
    //}

    //private void insertAllRole(Map<String, Object> parse) {
    //    try {
    //        // 先删除全部,再插入全部
    //        mqOuterRoleMapper.deleteSysRole(String.valueOf(parse.get("sysCode")));
    //        List<Map<String, Object>> infoList = (List<Map<String, Object>>) parse.get("info");
    //        ModelMapper modelMapper = new ModelMapper();
    //        for (Map<String, Object> info : infoList) {
    //            OuterRole role = modelMapper.map(info, OuterRole.class);
    //            //role.setSysCode(String.valueOf(parse.get("systemType")));
    //            role.setSource(CodeTableConstant.DATA_STATUS_1);
    //            mqOuterRoleMapper.insert(role);
    //        }
    //    } catch (Exception e) {
    //        log.error("insert all subsystem role error");
    //        e.printStackTrace();
    //    }
    //}
}
