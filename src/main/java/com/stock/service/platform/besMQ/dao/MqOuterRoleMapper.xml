<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.besMQ.dao.MqOuterRoleMapper">

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <insert id="insert" parameterType="com.stock.service.platform.common.entity.OuterRole">
        insert into sa_outer_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="sysCode != null">
                sys_code,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleDesc != null">
                role_desc,
            </if>
            <if test="roleType != null">
                role_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="sysCode != null">
                #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDesc != null">
                #{roleDesc,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                #{roleType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <update id="update" parameterType="com.stock.service.platform.common.entity.OuterRole">
        update sa_outer_role
        <set>
            <if test="sysCode != null">
                sys_code = #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                source = #{source,jdbcType=VARCHAR},
            </if>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDesc != null">
                role_desc = #{roleDesc,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                role_type = #{roleType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.OuterRoleExample">
        delete from sa_outer_role
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <delete id="deleteSysRole">
        delete from sa_outer_role where sys_code = #{sysCode}
    </delete>
    <select id="getCount" resultType="java.lang.Integer">
        select count(*) from sa_outer_role where id = #{id}
    </select>

</mapper>