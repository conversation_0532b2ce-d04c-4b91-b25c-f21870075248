package com.stock.service.platform.besMQ.component;

import com.alibaba.druid.support.json.JSONUtils;
import com.stock.service.platform.besMQ.service.ReceiveLogService;
import com.stock.service.platform.besMQ.service.ReceiveRoleService;
import com.stock.service.platform.besMQ.service.ReceiveTimedTasksService;
import com.stock.service.platform.common.dao.ActiveMqMapper;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.dao.PersonMapper;
import com.stock.service.platform.common.dao.UserMapper;
import com.stock.service.platform.common.dto.ActivemqTopicDto;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.Person;
import com.stock.service.platform.common.manager.CscSignaturePersonManager;
import com.stock.service.platform.common.service.RedisDaoService;
import com.stock.service.platform.common.system.dao.DepartmentBizMapper;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.mqManage.dao.ActivemqContactBizMapper;
import com.stock.service.platform.mqManage.dto.ActivemqContactDto;
import com.stock.service.platform.user.service.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;
import com.stock.service.platform.common.constant.TopicConstant;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消费者
 * <AUTHOR>
 */

@Component
public class BESMQConsumer {
    private Logger logger = LoggerFactory.getLogger(BESMQConsumer.class);
    @Resource
    private ActiveMqMapper activeMqMapper;
    @Resource
    private SaCompanyMapper saCompanyMapper;

    @Resource
    private ReceiveLogService receiveLogService;

    @Resource
    private ReceiveTimedTasksService receiveTimedTasksService;

    @Resource
    private ReceiveRoleService receiveRoleService;

    @Resource
    private PersonMapper personMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserService userService;

    @Autowired
    private RedisDaoService redisDaoService;

    @Autowired
    private OrgMapper orgMapper;

    @Resource
    private DepartmentBizMapper mapper;

    @Autowired
    private Environment env;

    @Resource
    private ActivemqContactBizMapper activemqContactBizMapper;

    @Resource
    private CscSignaturePersonManager cscSignaturePersonManager;

    @JmsListener( destination = "${bes.mq.receive-topic-name}", containerFactory = "jmsListenerContainerFactoryTopic" )
    public void onTopicMessage(String msg) throws Exception {
        this.logger.info("Topic接收到一条队列消息：{}", msg);
        Map<String,Object> parse = (Map<String, Object>) JSONUtils.parse(msg);
        String key = parse.get("uid") + "_" + parse.get("sysCode");
        boolean lock = redisDaoService.lock(key, key, 1000 * 1800);
        if(lock) {
            try {
                activeMqMapper.updateIsSuccess(parse);
                // 操作类型为delete
                if ("delete".equals(parse.get("operationType"))) {
                    List<ActivemqTopicDto> mqMsgDtoList = activeMqMapper.selectActivemqTopicByUid((String) parse.get("uid"));
                    if (CollectionUtils.isNotEmpty(mqMsgDtoList)) {
                        //String info = mqMsgDtoList.get(0).getInfo();
                        Map<String, Object> info = (Map<String, Object>) JSONUtils.parse(mqMsgDtoList.get(0).getInfo());
                        boolean anySuccessIsZero = mqMsgDtoList.stream().anyMatch(mqMsgDto -> "0".equals(mqMsgDto.getIsSuccess()));
                        boolean allSuccess = mqMsgDtoList.stream().allMatch(map -> "1".equals(map.getIsSuccess()));
                        ModelMapper modelMapper = new ModelMapper();
                        // 业务类型为company
                        if ("company".equals(parse.get("businessType"))) {
                            String id = modelMapper.map(info.get("id"), String.class);
                            if (anySuccessIsZero) {
                                saCompanyMapper.updateByPrimaryKeys(id, "0");
                                saCompanyMapper.updateSaOrgByPrimaryKeys(id, "1");
                            } else if (allSuccess) {
                                SaCompanyDto param = new SaCompanyDto();
                                saCompanyMapper.deleteByPrimaryKeys(id, param);
                                saCompanyMapper.deleteSaOrgByPrimaryKeys(id, param);
                            }
                        }
                        // 业务类型为org
                        if ("org".equals(parse.get("businessType"))) {
                            Org org = modelMapper.map(info.get("org"), Org.class);
                            if (anySuccessIsZero) {
                                int order = mapper.countByPid(org.getpOrgId()) + 1;
                                orgMapper.updateByPrimaryKeys(org.getId(), "1", order);
                            } else if (allSuccess) {
                                orgMapper.deleteByPrimaryKeys(org.getId());
                            }
                        }
                        // 业务类型为user
                        if ("user".equals(parse.get("businessType"))) {
                            Map<String, Object> personMap = modelMapper.map(info.get("person"), Map.class);
                            Person person = personMapper.selectPersonByTelephone((String) personMap.get("telephone"));
                            if (anySuccessIsZero) {
                                // 还原
                                personMapper.deletingByPersonId(person.getId(), "1");
                                userMapper.deletingByUserId(person.getUserId(), "0", "1");
                            } else if (allSuccess) {
                                // 执行删除
                                if (StringUtils.isNotEmpty(person.getUserId())) {
                                    userService.deleteUserById(person.getUserId());
                                    personMapper.deletingByPersonId(person.getId(), "0");
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                throw new Exception(e);
            } finally {
                redisDaoService.unlock(key, key);
                if ("0".equals(parse.get("status"))) {
                    Profiles profiles = Profiles.of("dev", "prepub-sz");
                    if (!env.acceptsProfiles(profiles)) {
                        List<ActivemqContactDto> activemqContactDtoList = activemqContactBizMapper.getActivemqContact();
                        List<String> mailList = new ArrayList<>();
                        for (ActivemqContactDto activemqContactDto : activemqContactDtoList) {
                            mailList.add(activemqContactDto.getContactEmail());
                        }
                        String messageContent = "上市公司及股东一体化服务平台";
                        ActivemqTopicDto activemqTopicDto = activeMqMapper.getActiveMq((String) parse.get("sysCode"), parse.get("uid"));
                        String content = this.getMsgContentMailNoContactName(activemqTopicDto);
                        cscSignaturePersonManager.pushSendMessage(mailList, messageContent, content, 101);
                    }
                }
            }
        }
    }

    /**
     * 监听 日志和定时任务
     * @param
     */
    @JmsListener( destination = "${bes.mq.receive-USSD}", containerFactory = "jmsListenerContainerFactoryTopic" )
    public void receiveLogMsg(String msg){
        this.logger.info("Topic接收到一条队列消息：{}", msg);
        Map<String,Object> parse = (Map<String, Object>) JSONUtils.parse(msg);
        String key = parse.get("messageId") + "_" + parse.get("sysCode");
        boolean lock = redisDaoService.lock(key, key, 1000 * 1800);
        if(lock) {
            String businessType = String.valueOf(parse.get("businessType"));

            switch (businessType) {
                case TopicConstant.BUS_TYPE_ROLE:
                    receiveRoleService.processingBusiness(parse);
                    break;
                default:
                    // 处理未知的业务类型
                    break;
            }
        }
    }

    //@JmsListener( destination = "test", containerFactory = "jmsListenerContainerFactoryTopic" )
    public void onTopicMessageTest(String msg){
        this.logger.info("Topic接收到一条Test消息：{}", msg);
    }

    public String getMsgContentMail(String contactName, ActivemqTopicDto activemqTopic) {
        return "尊敬的" + contactName + "您好，您关注的上市公司及股东一体化服务平台在" + activemqTopic.getCreateTime() + "对" + systemCodeConversion(activemqTopic.getSysCode()) + "下发的" + businessTypeConversion(activemqTopic.getBusinessType()) + "-" + operationTypeConversion(activemqTopic.getOperationType()) + "消息时未能操作成功,请到消息管理页面查看具体信息";
    }

    public String getMsgContentMailNoContactName(ActivemqTopicDto activemqTopic) {
        return "您好，您关注的上市公司及股东一体化服务平台在" + activemqTopic.getCreateTime() + "对" + systemCodeConversion(activemqTopic.getSysCode()) + "下发的" + businessTypeConversion(activemqTopic.getBusinessType()) + "-" + operationTypeConversion(activemqTopic.getOperationType()) + "消息时未能操作成功,请到消息管理页面查看具体信息";
    }

    public String systemCodeConversion(String systemCode) {
        String systemName = "";
        if ("HGGL".equals(systemCode)) {
            systemName = "回购管理";
        } else if ("GQJL".equals(systemCode)) {
            systemName = "股权激励";
        } else if ("TDGF".equals(systemCode)) {
            systemName = "合规交易";
        }
        return systemName;
    }

    public String businessTypeConversion(String businessType) {
        String businessName = "";
        if ("org".equals(businessType)) {
            businessName = "组织架构";
        } else if ("company".equals(businessType)) {
            businessName = "上市公司";
        } else if ("user".equals(businessType)) {
            businessName = "用户";
        } else if ("permission".equals(businessType)) {
            businessName = "角色分配";
        }
        return businessName;
    }

    public String operationTypeConversion(String operationType) {
        String operationName = "";
        if ("insert".equals(operationType)) {
            operationName = "插入";
        } else if ("update".equals(operationType)) {
            operationName = "更新";
        } else if ("delete".equals(operationType)) {
            operationName = "删除";
        } else if ("lock".equals(operationType)) {
            operationName = "锁定";
        } else if ("setuser".equals(operationType)) {
            operationName = "设置用户(回购专用)";
        }
        return operationName;
    }
}

