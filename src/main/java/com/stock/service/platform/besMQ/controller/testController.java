package com.stock.service.platform.besMQ.controller;

import com.bes.mq.command.BESMQQueue;
import com.bes.mq.command.BESMQTopic;
import com.stock.service.platform.besMQ.component.TopicProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Controller
public class testController {
    @Autowired
    private TopicProducer topicProducer;

    private static final Logger logger = LoggerFactory.getLogger(testController.class);
    @Resource
    private JmsTemplate jmsTemplate;

//    @RequestMapping("sendMsg")
    public String sendMsg() {
        BESMQTopic topic = new BESMQTopic("test");
        for (int i = 0; i < 5; i++) {
            jmsTemplate.convertAndSend(topic, "test" + i);
            logger.info("发送一条test消息：{}", "test" + i);
        }
        return "成功";
    }

}
