package com.stock.service.platform.besMQ.component;

import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dto.MQMsgDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsMessagingTemplate;
import org.springframework.stereotype.Component;

import javax.jms.Queue;
import javax.jms.Topic;
import java.util.UUID;

@Component
public class TopicProducer {

    @Autowired
    private JmsMessagingTemplate jmsMessagingTemplate;

    @Autowired
    private Queue queue;

    @Autowired
    private Topic topic;

    private <T> MQMsgDto<T> getMsgDto(T info, String sysType, String busType, String opType, String uid) throws Exception{
        if(StringUtils.isEmpty(sysType)) { throw new Exception("请填写系统类型"); }
        if(StringUtils.isEmpty(busType)) { throw new Exception("请填写业务类型"); }
        if(StringUtils.isEmpty(opType)) { throw new Exception("请填写操作类型"); }
        if(!TopicConstant.BUS_TYPE_LIST.contains(busType)) { throw new Exception("填写业务类型不规范"); }
        if(!TopicConstant.OP_TYPE_LIST.contains(opType)) { throw new Exception("填写操作类型不规范"); }
        MQMsgDto<T> result = new MQMsgDto<>();
        result.setUid(uid);
        result.setSystemType(sysType);
        result.setBusinessType(busType);
        result.setOperationType(opType);
        result.setInfo(info);
        return result;
    }

    public <T> void sendQueue(T info, String sysType, String busType, String opType, String uid) throws Exception{
        MQMsgDto<T> dto = this.getMsgDto(info, sysType, busType, opType ,uid);
        jmsMessagingTemplate.convertAndSend(queue, JsonUtil.toJson(dto));
    }

    public <T> void sendTopic(T info, String sysType, String busType, String opType, String uid) throws Exception{
        MQMsgDto<T> dto = this.getMsgDto(info, sysType, busType, opType ,uid);
        jmsMessagingTemplate.convertAndSend(topic, JsonUtil.toJson(dto));
    }
}

