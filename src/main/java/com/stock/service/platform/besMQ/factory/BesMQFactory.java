package com.stock.service.platform.besMQ.factory;


import org.springframework.context.annotation.Bean;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.config.JmsListenerContainerFactory;
import org.springframework.stereotype.Component;

import javax.jms.ConnectionFactory;

/**
 * bes MQ配置类：创建监听工厂配置
 * <AUTHOR>
 */

@Component
public class BesMQFactory {

    @Bean(name = "jmsListenerContainerFactoryQueue")
    public JmsListenerContainerFactory<?> jmsListenerContainerFactory(ConnectionFactory connectionFactoryQueue){
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        //是否支持 发布订阅
        factory.setPubSubDomain(false);
        factory.setConnectionFactory(connectionFactoryQueue);
        return factory;
    }

    @Bean(name = "jmsListenerContainerFactoryTopic")
    public JmsListenerContainerFactory<?> jmsListenerContainerTopic(ConnectionFactory connectionFactoryTopic) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setPubSubDomain(true);
        factory.setConnectionFactory(connectionFactoryTopic);
        return factory;
    }
}

