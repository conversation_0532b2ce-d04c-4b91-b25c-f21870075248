package com.stock.service.platform.besMQ.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ReceiveTimedTasksService {

    @Autowired
    private TimedTasksMapper timedTasksMapper;

    public void processingBusiness(Map<String, Object> parse) {
        String operationType = String.valueOf(parse.get("operationType"));

        switch (operationType) {
            case TopicConstant.OP_TYPE_I:
                insertTimedTasks(parse);
                break;
            case TopicConstant.OP_TYPE_U:
                updateTimedTasks(parse);
                break;
            default:
                // 处理未知的业务类型
                break;
        }


    }

    private void updateTimedTasks(Map<String, Object> parse) {
        try {
            timedTasksMapper.updateIsSuccess(parse);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void insertTimedTasks(Map<String, Object> parse) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            parse.put("messageType","1");
            parse.put("info",objectMapper.writeValueAsString(parse.get("info")));
            parse.put("isSuccess", "3");
            timedTasksMapper.insertActivemqTopic01(parse);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
