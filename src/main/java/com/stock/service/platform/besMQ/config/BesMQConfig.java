package com.stock.service.platform.besMQ.config;

import com.bes.mq.command.BESMQQueue;
import com.bes.mq.command.BESMQTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.annotation.EnableJms;

import javax.jms.Queue;
import javax.jms.Topic;

@Configuration
@EnableJms
public class BesMQConfig {

    @Value("${bes.mq.send-queue-name}")
    private String sendQueueName;

    @Value("${bes.mq.send-topic-name}")
    private String sendTopicName;

    @Bean
    public Queue queue() {
        return new BESMQQueue(sendQueueName);
    }
    @Bean
    public Topic topic() {
        return new BESMQTopic(sendTopicName);
    }
}

