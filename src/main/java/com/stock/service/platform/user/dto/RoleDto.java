package com.stock.service.platform.user.dto;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 角色dto
 */
public class RoleDto {

    /**
     * 角色id
     */
    @NotBlank(groups = {UpdateRoleGroup.class},message = "角色id不能为空")
    private String id;

    /**
     * 角色名字
     */
    @NotBlank(groups = {CreateRoleGroup.class,UpdateRoleGroup.class},message = "角色名字不能为空")
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDes;

    /**
     * 路由id列表
     */
    private List<String> routerIdList;

    /**
     * 当前状态
     */
    private String currStatus;

    /**
     * 创建者
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 角色id
     */
    @NotBlank(groups = SetAuthorityGroup.class,message = "角色id不能为空")
    private String roleId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleDes() {
        return roleDes;
    }

    public void setRoleDes(String roleDes) {
        this.roleDes = roleDes;
    }

    public List<String> getRouterIdList() {
        return routerIdList;
    }

    public void setRouterIdList(List<String> routerIdList) {
        this.routerIdList = routerIdList;
    }

    public String getCurrStatus() {
        return currStatus;
    }

    public void setCurrStatus(String currStatus) {
        this.currStatus = currStatus;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * 创建角色校验组
     */
    public interface CreateRoleGroup{
    }

    /**
     * 更新角色校验组
     */
    public interface UpdateRoleGroup{
    }

    /**
     * 设置路由校验组
     */
    public interface SetAuthorityGroup{
    }
}
