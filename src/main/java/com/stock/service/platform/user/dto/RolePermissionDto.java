package com.stock.service.platform.user.dto;

import com.stock.service.platform.common.util.Message;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * 角色权限相关dto
 */
@ApiModel(value = "角色权限dto",description = "角色权限相关dto")
public class RolePermissionDto {

    /**
     * 传入的授权路由列表
     */
    @NotNull(message = "路由参数列表为空")
    @ApiModelProperty(value = "路由参数列表",notes = "路由id列表")
    @Message(title = "菜单", primary = "true")
    private List<String> routerIdList;

    /**
     * 传入的角色id
     */
    @NotBlank(message = "角色id为空")
    @ApiModelProperty(value = "角色id",notes = "角色id")
    private String roleId;


    public List<String> getRouterIdList() {
        return routerIdList;
    }

    public void setRouterIdList(List<String> routerIdList) {
        this.routerIdList = routerIdList;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
