package com.stock.service.platform.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.UserInfo;
import com.stock.core.exception.ApplicationException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.constant.RedisKeyConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.RoleMapper;
import com.stock.service.platform.common.dao.RoleRouterMapMapper;
import com.stock.service.platform.common.dao.UserMapper;
import com.stock.service.platform.common.dao.UserRoleMapMapper;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.RoleRouterMapExample;
import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.entity.UserRoleMap;
import com.stock.service.platform.common.entity.UserRoleMapExample;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.manager.CscSignaturePersonManager;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.MessageService;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.PasswordUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.person.dao.PersonBizMapper;
import com.stock.service.platform.user.dto.RoleListDto;
import com.stock.service.platform.user.dto.UserDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 用户业务类
 */
@Service
@Transactional
public class UserService extends BaseService {
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserRoleMapMapper userRoleMapMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private RoleRouterMapMapper roleRouterMapMapper;

    @Autowired
    private Environment env;

    @Autowired
    private MessageService messageService;

    @Autowired
    private PersonBizMapper personBizMapper;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private SaSystemUtil saSystemUtil;

    @Autowired
    private TopicProducer topicProducer;

    @Resource
    private CscSignaturePersonManager cscSignaturePersonManager;

    @Value("${resetPassword.loginUrl}")
    private String loginUrl;

    @Resource
    private LogService logService;

    @Resource
    private CommonService commonService;

    /**
     * 修改用户密码
     *
     * @param userDto
     * @param userInfo
     * @return
     */
    public boolean changePassword(UserDto userDto, UserInfo userInfo) {
        // 判断 新密码和确认新密码是否一致
        if (!Objects.equals(userDto.getNewPassword(), userDto.getConfirmPassword())) {
            throw new ApplicationException("error.000101");
        }
        String userId = userInfo.getUserId();
        User user = userMapper.selectByPrimaryKey(userId);
        // 检查用户
        if (user == null) {
            throw new ApplicationException("error.000102");
        }
        // 检查旧密码
        if (!passwordEncoder.matches(userDto.getOldPassword(), user.getPassword())) {
            throw new ApplicationException("error.000103");
        }
        // 更新密码
        user.setPassword(passwordEncoder.encode(userDto.getNewPassword()));
        user.setPasswordUpdateTime(this.getDBTime());
        user.setIsFirst("0");
        return userMapper.updateByPrimaryKey(user) > 0;

    }

    /**
     * 修改用户密码
     *
     * @param userDto
     * @param userInfo
     * @return
     */
    public boolean saveNewPassword(UserDto userDto, UserInfo userInfo) {
        User user = userMapper.selectByUserName(userDto.getUserName());
        // 检查用户
        if (ObjectUtils.isEmpty(user)) {
            throw new ApplicationException("error.000102");
        }
        // 校验验证码
        Object redis = redisDao.getObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userDto.getUserName());
        if (ObjectUtils.isNotEmpty(redis)) {
            String redisCode = redis.toString();
            if (!redisCode.equals(userDto.getCaptcha())) {
                throw new ApplicationException("error.000105");
            }
        } else {
            throw new ApplicationException("error.990015");
        }
        // 更新密码
        user.setPassword(passwordEncoder.encode(userDto.getNewPassword()));
        user.setPasswordUpdateTime(this.getDBTime());
        user.setIsFirst("0");
        //修改密码后验证码删除
        redisDao.delObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userDto.getUserName());
        return userMapper.updateByPrimaryKey(user) > 0;
    }

    /**
     * 根据用户id级联删除用户信息
     * 因为userId == roleId 所以 只需要userId即可
     *
     * @param userId
     * @return
     */
    public boolean deleteUserById(String userId) {
        // 删除用户
        doDeleteUser(userId);
        // 删除 角色
        //doDeleteRole(userId);
        // 删除用户对应角色
        doDeleteUserRoleMap(userId);
        // 删除角色对应的路由列表
        //doDeleteRoleRouterMap(userId);
        return true;
    }

    /**
     * 删除角色对应的路由
     * userId == roleId
     *
     * @param userId
     */
    private void doDeleteRoleRouterMap(String userId) {
        RoleRouterMapExample example = new RoleRouterMapExample();
        example.createCriteria().andRoleIdEqualTo(userId);
        roleRouterMapMapper.deleteByExample(example);
    }

    /**
     * 删除用户和角色的关联
     *
     * @param userId
     */
    private void doDeleteUserRoleMap(String userId) {
        UserRoleMapExample example = new UserRoleMapExample();
        example.createCriteria().andUserIdEqualTo(userId);
        userRoleMapMapper.deleteByExample(example);
    }

    /**
     * 删除角色
     * userId == roleId
     *
     * @param userId
     */
    private void doDeleteRole(String userId) {
        Role record =new Role();
        record.setStatus("0");
        record.setId(userId);
        roleMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 删除用户
     *
     * @param userId
     */
    private void doDeleteUser(String userId) {
        User record = new User();
        //状态：0删除、1有效
        record.setId(userId);
        record.setStatus("0");
        userMapper.updateByPrimaryKeySelective(record);
    }


    public List<String> getUserOfRolesByUserId(String userId) {
        List<String> result = new ArrayList<>();
        UserRoleMapExample example = new UserRoleMapExample();
        example.createCriteria().andUserIdEqualTo(userId);
        List<UserRoleMap> maps = userRoleMapMapper.selectByExample(example);
        for (UserRoleMap m : maps) {
            result.add(m.getRoleId());
        }
        return result;
    }

    public Map<String, String> settingsUserRoles(String userId, List<String> roles,String requestUrl) {
        Map<String, String> resultMap = new HashMap<>();
        UserRoleMapExample example = new UserRoleMapExample();
        example.createCriteria().andUserIdEqualTo(userId);
        List<UserRoleMap> userRoleMaps = userRoleMapMapper.selectByExample(example);
        userRoleMapMapper.deleteByExample(example);
        roles.forEach((roleId) -> {
            UserRoleMap map = new UserRoleMap();
            map.setRoleId(roleId);
            map.setUserId(userId);
            userRoleMapMapper.insertSelective(map);
        });
        //操作日志
        try {
            List<String> roleIdsOld = userRoleMaps.stream().map(UserRoleMap::getRoleId).collect(Collectors.toList());
            RoleListDto roleListNew = new RoleListDto();
            roleListNew.setRoleList(userRoleMapMapper.getPlatformRoleNameByRoleIds(roles));
            RoleListDto roleListOld = new RoleListDto();
            roleListOld.setRoleList(userRoleMapMapper.getPlatformRoleNameByRoleIds(roleIdsOld));

            MessageUtil messageUtil = new MessageUtil();
            MessageInfo messageInfo = messageUtil.getMessageContext(roleListNew, roleListOld);
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(roles),
                            null, "设置平台角色",
                            "系统管理/人员信息", "UserService.settingsUserRoles",
                            "设置用户" + userRoleMapMapper.getUserNameByUserId(userId) + "的平台角色{" + messageInfo.getOperationRemark() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return resultMap;
    }

    public Boolean resetPassword(UserDto userDto) {
        //判断提交表单是否被篡改
        Map<String,String> checkParam = new HashMap<>();
        checkParam.put("id",userDto.getId());
        checkParam.put("seriakey",userDto.getSeriakey());
        int checkFlag = personBizMapper.selectByIdSeriakey(checkParam);
        if (checkFlag == 0) {
            throw new ApplicationException("error.000102");
        }
        User user = userMapper.selectByPrimaryKey(userDto.getId());
        // 检查用户
        if (user == null) {
            throw new ApplicationException("error.000102");
        }
        if (StringUtils.isEmpty(user.getMail())) {
            throw new ApplicationException("error.000123");
        }
        // 更新密码
        String passWord;
        Profiles profiles = Profiles.of("dev","prepub-sz");
        //如果是本地就用默认的密码，否则就重置成8位随机新密码
        if(env.acceptsProfiles(profiles)) {
            passWord = LogicConstant.DEFAULT_PASSWORD;
        } else {
            passWord = PasswordUtil.generatePassword();
        }
        String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
        userDto.setNewPassword(MD5Password);
        logger.info("重置后的密码:" + passWord);
        user.setPassword(passwordEncoder.encode(userDto.getNewPassword()));
        user.setErrorNum(0);
        user.setLockType("0");
        user.setPasswordUpdateTime(this.getDBTime());
        user.setIsFirst("1");
        // 发送邮件到用户
        String content = this.getMsgContent(passWord, userDto.getUserName());
        if (!env.acceptsProfiles(profiles)) {
            List<String> mailList = new ArrayList<>();
            mailList.add(user.getMail());
            String messageContent = "上市公司及股东一体化服务平台";
            try {
                cscSignaturePersonManager.pushSendMessage(mailList, messageContent, content, 101);
                logger.info("邮件内容: " + content + "发送状态: 成功");
            } catch (Exception e) {
                logger.info("邮件内容: " + content + "发送状态: 失败");
            }
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(userDto),
                            null, "密码重置",
                            "系统管理/外部人员", "UserService.resetPassword",
                            "用户{" + user.getUserName() + "}密码重置", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return userMapper.updateByPrimaryKey(user) > 0;
    }

    public Boolean resetPasswordOnlyOne(UserDto userDto) {
        //判断提交表单是否被篡改
        Map<String,String> checkParam = new HashMap<>();
        checkParam.put("id",userDto.getId());
        checkParam.put("seriakey",userDto.getSeriakey());
        int checkFlag = personBizMapper.selectByIdSeriakey(checkParam);
        if (checkFlag == 0) {
            throw new ApplicationException("error.000102");
        }
        User user = userMapper.selectByPrimaryKey(userDto.getId());
        // 检查用户
        if (user == null) {
            throw new ApplicationException("error.000102");
        }
        if (StringUtils.isEmpty(user.getMail())) {
            throw new ApplicationException("error.000123");
        }
        // 更新密码
        String passWord;
        Profiles profiles = Profiles.of("dev","prepub-sz");
        //如果是本地就用默认的密码，否则就重置成8位随机新密码
        if(env.acceptsProfiles(profiles)) {
            passWord = LogicConstant.DEFAULT_PASSWORD;
        } else {
            passWord = PasswordUtil.generatePassword();
        }
        String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
        userDto.setNewPassword(MD5Password);
        logger.info("重置后的密码:" + passWord);
        user.setPassword(passwordEncoder.encode(userDto.getNewPassword()));
        user.setErrorNum(0);
        user.setLockType("0");
        user.setPasswordUpdateTime(this.getDBTime());
        user.setIsFirst("1");
        // 发送邮件到用户
        String content = this.getMsgContentOnlyOne(passWord, userDto.getUserName());
        if (!env.acceptsProfiles(profiles)) {
            List<String> mailList = new ArrayList<>();
            mailList.add(user.getMail());
            String messageContent = "上市公司及股东一体化服务平台";
            try {
                cscSignaturePersonManager.pushSendMessage(mailList, messageContent, content, 101);
                logger.info("邮件内容: " + content + "发送状态: 成功");
            } catch (Exception e) {
                logger.info("邮件内容: " + content + "发送状态: 失败");
            }
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(userDto),
                            null, "密码重置",
                            "系统管理/外部人员", "UserService.resetPassword",
                            "用户{" + user.getUserName() + "}密码重置", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return userMapper.updateByPrimaryKey(user) > 0;
    }

    public String getMsgContent(String password, String userName){
        String personName = commonService.getPersonNameByUserName(userName);
        return "尊敬的" + personName + "用户，您好，您上市公司一体化平台系统登录账号密码已经重置，重置后的密码为："
                +password+"，登录地址为：" + loginUrl + "。为了保障您的合法权益，请您及时登录一体化平台，修改并妥善保管登录密码。【上市公司及股东一体化服务平台】";
    }

    public String getMsgContentOnlyOne(String password, String userName){
        return "尊敬的上市公司用户，您好，\n" +
                "    为了提升服务水平，中信建投证券股份有限公司（以下简称“我司”）全新上线“上市公司及股东一体化服务平台”" +
                "（以下简称“一体化平台”）。一体化平台整合了原有的股权激励管理系统和特定股份管理系统功能，并新增丰富功能供您体验。" +
                "一体化平台上线后，您原股权激励管理系统或特定股份管理系统的密码已重置。系统登录账号为：" + userName + "，重置后的密码为："
                +password+"，一体化平台系统登录地址：" + loginUrl + "。为了保障您的合法权益，请您及时登录一体化平台，并修改登录密码。\n" +
                "期待新系统的升级，为您带来更加优质的服务体验！如果您对此次系统升级有任何疑问或建议，请及时联系您的客户经理，我们将尽快给予回复。" +
                "再次感谢您对我司的支持和信任！【上市公司及股东一体化服务平台】";
    }

    public Boolean checkUser(UserDto userDto) {
        Profiles profiles = Profiles.of("dev","prepub-sz","test", "uat");
        //如果是本地或者测试环境返回true
        if(env.acceptsProfiles(profiles)) {
            return true;
        } else {
            if ("admin".equals(userDto.getUserName())) {
                return true;
            }
            String personType = userMapper.getUserTypeByUserName(userDto.getUserName());
            if ("1".equals(personType)) {
                return true;
            } else {
                return false;
            }
        }
    }
}
