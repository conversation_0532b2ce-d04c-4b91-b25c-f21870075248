package com.stock.service.platform.user.dto;

import com.stock.service.platform.person.dto.PersonDto;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * 人员任职记录表
 */
public class PersonResumeRecordDto {

    /**
     * id
     */
    private String id;

    /**
     * 机构id
     */
    @NotBlank(message = "机构编号为空",groups = {PersonDto.UpdatePersonGroup.class,PersonDto.InsertPersonGroup.class})
    @Length(message = "机构编号过长",groups = {PersonDto.InsertPersonGroup.class, PersonDto.UpdatePersonGroup.class},min = 1,max = 20)
    private String orgId;

    /**
     * 身份id
     */
    @NotBlank(message = "身份类型为空",groups = {PersonDto.UpdatePersonGroup.class,PersonDto.InsertPersonGroup.class})
    @Length(message = "身份类型过长",groups = {PersonDto.InsertPersonGroup.class, PersonDto.UpdatePersonGroup.class},min = 1,max = 20)
    private String identityType;

    /**
     * 是否是部门领导负责人
     */
    @Length(message = "部门领导人过长",groups = {PersonDto.InsertPersonGroup.class, PersonDto.UpdatePersonGroup.class},min = 0,max = 2)
    private String leader;

    /**
     * 任职开始日期
     */
    private Date officeStartDate;

    /**
     * 任职结束日期
     */
    private Date officeEndDate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getIdentityType() {
        return identityType;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public Date getOfficeStartDate() {
        return officeStartDate;
    }

    public void setOfficeStartDate(Date officeStartDate) {
        this.officeStartDate = officeStartDate;
    }

    public Date getOfficeEndDate() {
        return officeEndDate;
    }

    public void setOfficeEndDate(Date officeEndDate) {
        this.officeEndDate = officeEndDate;
    }
}
