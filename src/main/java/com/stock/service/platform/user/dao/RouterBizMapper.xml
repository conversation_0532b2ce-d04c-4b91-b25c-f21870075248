<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.user.dao.RouterBizMapper">

    <select id="queryRouterListByRoleId" resultType="com.stock.service.platform.user.dto.RouterDto">
        select router.id,router.p_router_id,router.router_des,temp.id,
        case ifnull(temp.id,'0') when '0' then '0' else '1' end as hasPermission
        from sa_router router left join (
        select
        inner_router.id
        from
        sa_router inner_router
        join sa_role_router_map rrm on rrm.router_id = inner_router.id
        where rrm.role_id = #{roleId}
        ) temp on router.id = temp.id
        where
        <if test="pRouterId == null or pRouterId == ''">
            p_router_id is null or p_router_id = ''
        </if>
        <if test="pRouterId != null and pRouterId != ''">
            p_router_id = #{pRouterId}
        </if>
        ORDER BY router.sort_no
    </select>
    <select id="queryRouterList" resultType="com.stock.service.platform.user.dto.RouterDto">
        SELECT
        router.id,
        router.p_router_id,
        router.router_des,
        router.router_name,
        router.router_type,
        router.router_icon,
        router.path
        FROM
        sa_router router
        <where>
            <if test="routerName != null and routerName != ''">
                router_name = #{routerName}
            </if>
            <if test="pRouterId != null and pRouterId != ''">
                p_router_id = #{pRouterId}
            </if>
            <if test="path != null and path != ''">
                path = #{path}
            </if>
            <if test="routerType != null and routerType != ''">
                router_type = #{routerType}
            </if>
        </where>
    </select>

    <insert id="insertRoleRouterList">
        insert into sa_role_router_map (id,role_id,router_id) values
        <foreach collection="routerIdSet" item="routerId" open="" close="" separator="">
            (SELECT uuid_short(),#{roleId},#{routerId})
        </foreach>
    </insert>

    <insert id="insertRoleRouter">
        insert into sa_role_router_map (id, role_id, router_id)
        select (SELECT uuid_short()), #{roleId}, #{routerId}
        from dual
    </insert>

    <select id="getAllRouter" resultType="com.stock.service.platform.user.dto.RouterDto">
        SELECT
            id,
            p_router_id pRouterId,
            router_des routerDes,
            router_type routerType,
            sort_no sortNo
        FROM
            sa_router
        order by sort_no
    </select>
    <select id="getCheckedRouterIds" resultType="java.lang.String">
        select router_id from sa_role_router_map where role_id = #{roleId}
    </select>
</mapper>