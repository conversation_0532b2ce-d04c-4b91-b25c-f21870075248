package com.stock.service.platform.user.service;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.stock.core.Constant;
import com.stock.core.dto.UserInfo;
import com.stock.core.security.msic.AuthoritiesLoader;
import com.stock.core.security.token.AccessToken;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import com.stock.core.service.BaseService;
import com.stock.core.util.BeanUtil;
import com.stock.core.util.DateUtil;
import com.stock.core.util.WebUtil;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.Global;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.dao.PersonMapper;
import com.stock.service.platform.common.dao.SystemRouterBizMapper;
import com.stock.service.platform.common.dao.UserMapper;
import com.stock.service.platform.common.dto.RouterDto;
import com.stock.service.platform.common.entity.PersonExample;
import com.stock.service.platform.common.entity.PersonWithBLOBs;
import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.entity.UserExample;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserManagerService extends BaseService implements UserDetailsService, AuthoritiesLoader {

    @Resource
    PasswordEncoder passwordEncoder;

    @Resource
    PersonMapper personMapper;

    @Autowired
    private MessageSource appMessageSource;

    @Resource
    private UserMapper userMapper;

    @Resource
    private SystemRouterBizMapper systemRouterBizMapper;

    @Resource
    private AccessTokenStoreStrategy accessTokenStoreStrategy;


    /**
     * 取得用户信息
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        String loginType = WebUtil.getCurrentRequest().getParameter(LogicConstant.LOGIN_TYPE_NAME);
        UserExample userExample = new UserExample();
        userExample.createCriteria().andUserNameEqualTo(username);
        List<User> userList = userMapper.selectByExample(userExample);
        // 用户不存在
        if (userList == null || userList.size() == 0) {
            throw new UsernameNotFoundException(
                    appMessageSource.getMessage("security.username.notFound", null, null));
        }
        // 判断禁用状态
        if (CodeTableConstant.USER_UNAVAILABLE.equals(userList.get(0).getStatus())) {
            throw new AccessDeniedException("用户被禁用，请联系管理员");
        }

        // 判断禁用状态
        if (CodeTableConstant.USER_LOCK.equals(userList.get(0).getLockState())) {
            throw new AccessDeniedException("用户被锁定，请联系管理员");
        }

        PersonWithBLOBs person = null;

        // 超管略过
        if (!Objects.equals(userList.get(0).getIsAdmin(), CodeTableConstant.IS_ADMIN)) {
            // 检查人员是否存在
            PersonExample personExample = new PersonExample();
            personExample.createCriteria().andUserIdEqualTo(userList.get(0).getId());
            List<PersonWithBLOBs> personList = personMapper.selectByExampleWithBLOBs(personExample);
            if (personList.isEmpty()) {
                throw new AccessDeniedException("用户没有对应的人员信息");
            }
            person = personList.get(0);
        }
        //判断如果超过锁定时间，释放锁
        if(Global.LOCK_TYPE_TEM.equals(userList.get(0).getLockType()) &&  DateUtil.dateCompare(getDBTime(),userList.get(0).getUnlockTime()) > 0){
            userList.get(0).setLockType(Global.LOCK_TYPE_UNLOCK);
            releaseLock(userList.get(0));
        }

        UserInfo userInfo = new UserInfo();
        Map<String, Object> infoMap = Maps.newHashMap();
        BeanUtil.copy(userList.get(0), infoMap);
        // 转化
        infoMap.put("id", String.valueOf(userList.get(0).getId()));
        infoMap.put("menuList", getUserResources(userList.get(0).getId()));
        infoMap.put("personName",infoMap.get("realName"));
        if(person != null){
            infoMap.put("personName",person.getPersonName());
            infoMap.put("pictureUrl",person.getPictureUrl());
            infoMap.put("remark",person.getRemark());
        }

        // 手机号码需要再次处理
        if (Objects.equals(LogicConstant.TELEPHONE_VERIFY_CODE_LOGIN_TYPE, loginType)) {
            infoMap.put("password", passwordEncoder.encode((String) infoMap.get("password")));
        }
        userInfo.setInfo(infoMap);
        userInfo.setAuthorities(getAuthorities(userList.get(0).getId().toString()));
        return userInfo;
    }

    /**
     * 取得用户权限信息
     *
     * @param userId
     * @return
     */
    @Override
    public Set<GrantedAuthority> getAuthorities(String userId) {
        List<String> roles = systemRouterBizMapper.selectResourcesByUserId(userId);
        Set<GrantedAuthority> authorities = Sets.newHashSet();
        for (String role : roles) {
            authorities.add(new SimpleGrantedAuthority(role));
        }
        if (authorities.isEmpty()) {
            throw new AccessDeniedException(appMessageSource.getMessage("security.no.granted.authority", null,
                    null));
        }
        return authorities;
    }

    /**
     * 根据refreshToken刷新token
     *
     * @param refreshToken
     * @return
     */
    public String getAccessTokenByRefreshToken(String refreshToken) {
        AccessToken accessToken = accessTokenStoreStrategy.exchangeAccessToken(refreshToken);
        return accessToken.getAccessToken();
    }

    /**
     * 根据登录用户ID获取权限拥有菜单集
     *
     * @param userId
     * @return
     */
    public List<RouterDto> getUserResources(String userId) {
        // 查询一级菜单
        List<RouterDto> list = systemRouterBizMapper.selectByCondition(userId);
        if(list == null || list.isEmpty()) { return new ArrayList<>(); }
        List<RouterDto> result = list.stream().filter(m -> Objects.equals(m.getpMenuId(), ""))
                .map((m) -> {
                            m.setMenuList(getSubMenuList(m, list));
                            return m;
                        }
                ).collect(Collectors.toList());
        return result;
    }

    /**
     * 递归设置子menuList
     *
     * @param router
     * @param list
     * @return
     */
    private static List<RouterDto> getSubMenuList(RouterDto router, List<RouterDto> list) {
        List<RouterDto> tmp = list.stream().filter(item -> Objects.equals(item.getpMenuId(), router.getId())).collect(Collectors.toList());
        if(tmp != null && !tmp.isEmpty()) {
            // 默认将首个有效的子菜单设置为路径
            List<RouterDto> type1Routers = tmp.stream().filter(item ->"1".equals(item.getMenuType())).collect(Collectors.toList());
            if (type1Routers != null && !type1Routers.isEmpty() && StringUtils.isNotEmpty(type1Routers.get(0).getPath())){
                router.setPath(type1Routers.get(0).getPath());
            }
        }
        List<RouterDto> result = tmp.stream()
                .map(item -> {
                    item.setMenuList(getSubMenuList(item, list));
                    return item;
                }).collect(Collectors.toList());
        return result;
    }

    /**
     * 解除锁定用户
     * @param user
     * @return
     */
    public Boolean releaseLock(User user){
        user.setErrorNum(0);
        user.setLockType(Constant.LOCK_STATE_UNLOCK);
        userMapper.updateByPrimaryKeySelective(user);
        return true;
    }
}
