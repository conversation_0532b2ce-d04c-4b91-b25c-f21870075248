<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.user.dao.UserBizMapper">


    <insert id="insertUser" parameterType="com.stock.service.platform.user.dto.UserDto">
        insert into sa_user(
            id,
            company_id,
            org_no,
            user_name,
            password,
            telephone,
            real_name,
            password_update_time,
            error_num,
            lock_type,
            lock_state,
            status,
            is_first,
            mail,
            remark,
            user_type,
            hidden_key,
            sort,
            merge_status
        )
        values (
            #{id},
            #{companyId},
            #{orgId},
            #{userName},
            #{newPassword},
            #{telephone},
            #{realName},
            now(),
            0,
            0,
            0,
            1,
            '1',
            #{mail},
            #{remark},
            #{userType},
            #{hiddenKey},
            #{sort},
            #{mergeStatus}
        )
    </insert>
    <select id="selectUserInfo" resultType="com.stock.service.platform.user.dto.UserManageResponse">
        SELECT
            t1.id as personId,
            t1.person_name as personName,
            t1.org_id as orgId,
            t2.org_name as orgName,
            t2.org_type as orgType,
            t3.id as companyId,
            t3.company_code as companyCode,
            t3.zh_sort_name as companyName,
            t3.market_stock_type as market
        FROM sa_person t1
        left join sa_org t2 on FIND_IN_SET(t2.id, t1.org_id)
        left join sa_company t3 on t2.company_id = t3.id
        WHERE t1.id = #{userId}
        limit 1
    </select>
    <update id="updateUserLockInfo" parameterType="map">
        UPDATE
        sa_user
        SET
        <if test="unlockTime != null and unlockTime != ''">
            unlock_time=str_to_date(#{unlockTime},'%Y-%m-%d %H:%i:%s')
        </if>
        <if test="unlockTime == null or unlockTime == ''">
            unlock_time=NULL
        </if>
        <if test="errorNum != null and errorNum != ''">
            , error_num=#{errorNum}
        </if>
        <if test="errorNum != null and errorNum != ''">
            , lock_type=#{lockType}
        </if>
        WHERE id=#{id}
    </update>

</mapper>