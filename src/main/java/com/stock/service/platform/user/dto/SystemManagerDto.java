package com.stock.service.platform.user.dto;

import com.stock.core.dto.TreeDto;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


public class SystemManagerDto implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("（角色/菜单）状态 0：无效 1：有效")
    private String currStatus;

    @ApiModelProperty("角色描述")
    private String roleDes;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户姓名")
    private String realName;

    @ApiModelProperty("邮箱")
    private String mail;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("路由名称")
    private String menuName;

    @ApiModelProperty("路由描述")
    private String menuDes;

    @ApiModelProperty("用户状态（1：禁用）")
    private String status;

    @ApiModelProperty("路由path-查询")
    private String pathSearch;

    @ApiModelProperty("路由path-新增")
    private String path;

    @ApiModelProperty("路由类型- 0、1-路由 2-页面内路由按钮")
    private List<String> routerTypeList;

    private String routerTypeStr;

    @ApiModelProperty("路由管理-授权标识")
    private String resourceId;

    @ApiModelProperty("路由管理-资源名称")
    private String resourceNameSearch;

    @ApiModelProperty("电话")
    private String telephone;

    @ApiModelProperty("旧密码")
    private String oldPassWord;

    @ApiModelProperty("新密码")
    private String newPassWord;

    @ApiModelProperty("角色Id")
    private String roleId;

    @ApiModelProperty("角色IdList-新增或编辑用户")
    private List<String> roleIdList;

    @ApiModelProperty("设置路由权限: menu_id")
    private List<String> menuIdList;

    @ApiModelProperty("设置资源-新增路由")
    private List<String> resourceIdList;

    @ApiModelProperty("meta数据-新增路由")
    private Map<String, Object> metaData;

    @ApiModelProperty("图标-新增路由")
    private String menuIcon;

    @ApiModelProperty("菜单类型-新增路由")
    private String menuType;

    @ApiModelProperty("父路由-新增子路由")
    private String pMenuId;

    @ApiModelProperty("资源名称")
    private String resourceName;

    @ApiModelProperty("资源url")
    private String resourceUrl;

    @ApiModelProperty("资源描述")
    private String resourceDes;

    @ApiModelProperty("设置菜单排序 sortNo")
    private String sortNo;

    @ApiModelProperty("设置菜单排序 treeDtoList")
    private List<TreeDto> treeDtoList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCurrStatus() {
        return currStatus;
    }

    public void setCurrStatus(String currStatus) {
        this.currStatus = currStatus;
    }

    public String getRoleDes() {
        return roleDes;
    }

    public void setRoleDes(String roleDes) {
        this.roleDes = roleDes;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuDes() {
        return menuDes;
    }

    public void setMenuDes(String menuDes) {
        this.menuDes = menuDes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPathSearch() {
        return pathSearch;
    }

    public void setPathSearch(String pathSearch) {
        this.pathSearch = pathSearch;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<String> getRouterTypeList() {
        return routerTypeList;
    }

    public void setRouterTypeList(List<String> routerTypeList) {
        this.routerTypeList = routerTypeList;
    }

    public String getRouterTypeStr() {
        return routerTypeStr;
    }

    public void setRouterTypeStr(String routerTypeStr) {
        this.routerTypeStr = routerTypeStr;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceNameSearch() {
        return resourceNameSearch;
    }

    public void setResourceNameSearch(String resourceNameSearch) {
        this.resourceNameSearch = resourceNameSearch;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getOldPassWord() {
        return oldPassWord;
    }

    public void setOldPassWord(String oldPassWord) {
        this.oldPassWord = oldPassWord;
    }

    public String getNewPassWord() {
        return newPassWord;
    }

    public void setNewPassWord(String newPassWord) {
        this.newPassWord = newPassWord;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public List<String> getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(List<String> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public List<String> getMenuIdList() {
        return menuIdList;
    }

    public void setMenuIdList(List<String> menuIdList) {
        this.menuIdList = menuIdList;
    }

    public List<String> getResourceIdList() {
        return resourceIdList;
    }

    public void setResourceIdList(List<String> resourceIdList) {
        this.resourceIdList = resourceIdList;
    }

    public Map<String, Object> getMetaData() {
        return metaData;
    }

    public void setMetaData(Map<String, Object> metaData) {
        this.metaData = metaData;
    }

    public String getMenuIcon() {
        return menuIcon;
    }

    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public String getpMenuId() {
        return pMenuId;
    }

    public void setpMenuId(String pMenuId) {
        this.pMenuId = pMenuId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }

    public String getResourceDes() {
        return resourceDes;
    }

    public void setResourceDes(String resourceDes) {
        this.resourceDes = resourceDes;
    }

    public String getSortNo() {
        return sortNo;
    }

    public void setSortNo(String sortNo) {
        this.sortNo = sortNo;
    }

    public List<TreeDto> getTreeDtoList() {
        return treeDtoList;
    }

    public void setTreeDtoList(List<TreeDto> treeDtoList) {
        this.treeDtoList = treeDtoList;
    }
}
