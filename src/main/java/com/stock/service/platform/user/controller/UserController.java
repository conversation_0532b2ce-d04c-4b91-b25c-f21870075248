package com.stock.service.platform.user.controller;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.stock.core.controller.BaseController;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.entity.Role;
import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.outer.service.RoleOuterService;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.person.service.PersonService;
import com.stock.service.platform.user.dto.DynamicCodeAndCheckDto;
import com.stock.service.platform.user.dto.UserDto;
import com.stock.service.platform.user.service.RoleService;
import com.stock.service.platform.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static com.anji.captcha.controller.CaptchaController.getRemoteId;

/**
 * <AUTHOR>
 * 用户controller
 */
@RestController
@RequestMapping("user")
public class UserController extends BaseController {

    @Autowired
    UserService userService;

    @Autowired
    RoleService roleService;

    @Autowired
    private CommonService commonService;

    @Autowired
    RedisDao redisDao;
    @Autowired
    private PersonService personService;

    @Resource
    private RoleOuterService service;

    @Autowired
    private CaptchaService captchaService;

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @RequestMapping(value = "/info")
    @ResponseBody
    public JsonResponse<Map<String, Object>> userInfo() {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        response.setResult(userInfo.getInfo());
        return response;
    }


    /**
     * 修改密码
     *
     * @return
     */
    @RequestMapping(value = "/changePassword")
    @ResponseBody
    public JsonResponse<Boolean> changePassword(@Validated(UserDto.ChangePasswordGroup.class) @RequestBody UserDto userDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        response.setResult(userService.changePassword(userDto, userInfo));
        return response;
    }

    /**
     * 忘记密码修改密码保存
     *
     * @return
     */
    @RequestMapping(value = "/saveNewPassword")
    @ResponseBody
    public JsonResponse<Boolean> saveNewPassword(@RequestBody UserDto userDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        response.setResult(userService.saveNewPassword(userDto, userInfo));
        return response;
    }

    /**
     * 校验当前用户是内部用户还是外部用户
     *
     * @return
     */
    @RequestMapping(value = "/checkUser")
    @ResponseBody
    public JsonResponse<Boolean> checkUser(@RequestBody UserDto userDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(userService.checkUser(userDto));
        return response;
    }

    /**
     * 重置密码
     *
     * @return
     */
    @RequestMapping(value = "/reset")
    @ResponseBody
    public JsonResponse<Boolean> resetPassword(@RequestBody UserDto userDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(userService.resetPassword(userDto));
        return response;
    }

    /**
     * 获取用户已有角色
     */
    @GetMapping(value = "/roles")
    public JsonResponse<List<String>> getUserOfRolesByUserId(@RequestParam("userId") String userId) {
        JsonResponse<List<String>> response = new JsonResponse<>();
        response.setResult(userService.getUserOfRolesByUserId(userId));
        return response;
    }

    /**
     * 设置用户角色
     */
    @PostMapping(value = "/roles/settings")
    public Map<String, String> settingsUserRoles(@RequestParam("userId") String userId, @RequestBody List<String> roles) {
        Map<String, String> resultMap = userService.settingsUserRoles(userId, roles, getRequest().getServerName());
        return resultMap;
    }


    /**
     * 获取所有角色下拉 除超级管理员角色
     */
    @GetMapping(value = "all")
    public List<Role> findAllRoles() {
        return roleService.findAllRoles();
    }


    /**
     * 手机号登录获取验证码
     *
     * @param map
     * @return
     */
/*
    @RequestMapping(value = "/getDynamicCode")
    @ResponseBody
    public JsonResponse<Boolean> getDynamicCode(HttpServletRequest request, @RequestBody Map<String, String> map) {
        //生成验证码 发送短信
        String telephone = map.get("telephone");
        String captchaCode = map.get("captchaCode");
        return commonService.getDynamicCode(request, telephone, captchaCode);
    }
*/

//    /**
//     * 用户名登录获取验证码
//     * 直接用了之前的接口地址；
//     *
//     * @param username 用户名
//     * @return true or false
//     */
//    @PostMapping("/getDynamicCode")
//    public JsonResponse<String> getDynamicCodeByUsername(@RequestParam String username, @RequestParam String password) {
//        return commonService.getDynamicCodeByUsername(username, password);
//    }

    /**
     * 整合 验证验证码和用户名登录获取验证码
     * @param data
     * @param request
     * @return
     */
    @PostMapping({"/getDynamicCodeAndCheck"})
    public JsonResponse<String> check(@RequestBody DynamicCodeAndCheckDto data, HttpServletRequest request) {
        JsonResponse<String> response = new JsonResponse<>();
        data.setBrowserInfo(getRemoteId(request));
        ResponseModel check = this.captchaService.check(data);
        if ("0000".equals(check.getRepCode())) {
            // 验证码成功，发送验证码
            response = commonService.getDynamicCodeByUsername(data.getUsername(), data.getPassword());
        } else {
            // 失败
            response.setErrorCode("1000");
            response.setErrorMsg("验证码验证失败");
        }
        return response;
    }


    /**
     * 手机号登录获取验证码
     *
     * @return
     */
    @RequestMapping(value = "/getUserNameDynamicCode")
    @ResponseBody
    public JsonResponse<Boolean> getUserNameDynamicCode(HttpServletRequest request) {
        return commonService.getUserNameDynamicCode(request.getParameter("telephone"));
    }

    /**
     * 忘记密码获取邮箱验证码
     *
     * @param map
     * @return
     */
    @RequestMapping(value = "/getMailCaptcha")
    @ResponseBody
    public JsonResponse<Boolean> getMailCaptcha(HttpServletRequest request, @RequestBody Map<String, String> map) {
        return commonService.getMailCaptchaByUser(request, map.get("userName"));
        //return commonService.getPhoneCaptchaByUser(request, map.get("userName"));
    }

    /**
     * 查看当前登录人的实时权限
     *
     * @param map
     * @return
     */
    @RequestMapping(value = "/getCurrentAudith")
    @ResponseBody
    public JsonResponse<Boolean> getCurrentAudith(@RequestBody Map<String, String> map) {
        return commonService.getCurrentAudith(map);
    }

    /**
     * 查看当前登录账户是否存在子账户
     *
     * @return
     */
    @RequestMapping(value = "/getChildrenInfo")
    @ResponseBody
    public JsonResponse<PersonDto> getChildrenInfo(@RequestBody PersonDto personDto) {
        JsonResponse<PersonDto> response = new JsonResponse<>();
        response.setResult(personService.getChildrenInfo(personDto));
        return response;
    }



    /**
     * 手机号登录验证码校验
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/captchaCodeOnChange")
    @ResponseBody
    public JsonResponse<Boolean> captchaCodeOnChange(HttpServletRequest request, @RequestBody Map<String, String> map) {
        String captchaCode = map.get("captchaCode");
        return commonService.captchaCodeOnChange(request, captchaCode);
    }

    ///**
    // * 退出登录mq通知子系统
    // *
    // * @param
    // * @return
    // */
    //@RequestMapping(value = "/logoutMq")
    //@ResponseBody
    //public void logoutMq() {
    //    commonService.logoutMq();
    //}

    @PostMapping("/getRoleListGroupBySystem")
    @ResponseBody
    public JsonResponse<Object> getRoleListGroupBySystem(@RequestBody User param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.getRoleListGroupBySystem(param));
        }catch (Exception e) {
            response.setErrorMsg("获取角色列表失败");
            e.printStackTrace();
        }
        return response;
    }
}