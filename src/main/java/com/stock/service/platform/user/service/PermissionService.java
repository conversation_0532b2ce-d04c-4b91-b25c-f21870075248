package com.stock.service.platform.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stock.core.dto.TreeDto;
import com.stock.core.exception.ApplicationException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.MessageService;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.PasswordUtil;
import com.stock.service.platform.user.dao.RoleBizMapper;
import com.stock.service.platform.user.dao.RouterBizMapper;
import com.stock.service.platform.user.dao.UserBizMapper;
import com.stock.service.platform.user.dto.RolePermissionDto;
import com.stock.service.platform.user.dto.RouterDto;
import com.stock.service.platform.user.dto.UserDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 权限相关的service
 */
@Service
public class PermissionService extends BaseService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionService.class);

    @Autowired
    private Environment env;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private UserBizMapper userBizMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private RoleBizMapper roleBizMapper;

    @Autowired
    private UserRoleMapMapper userRoleMapMapper;

    @Autowired
    private RoleRouterMapMapper roleRouterMapMapper;

    @Autowired
    private RouterBizMapper routerBizMapper;

    @Autowired
    private MessageService messageService;

    @Autowired
    private OrgMapper orgMapper;

    @Resource
    private LogService logService;

    /**
     * 获取所有的router（菜单）
     *
     * @return
     */
    //public List<RouterDto> listRouterByRoleId(String roleId) {
    //    List<RouterDto> parentList = routerBizMapper.queryRouterListByRoleId(roleId,null);
    //    recursionRouter(parentList,roleId);
    //    return parentList;
    //}
    public Map<String,Object> listRouterByRoleId(String roleId) {
        Map<String,Object> result = new HashMap<>();
        // 获取全部权限
        List<RouterDto> allRouter = routerBizMapper.getAllRouter();
        List<TreeDto> routerTree = new ArrayList<>();
        allRouter.forEach(item -> {
            if ("1".equals(item.getRouterType())) {
                TreeDto dto = new TreeDto();
                dto.setId(item.getId());
                dto.setParentId(item.getpRouterId());
                dto.setName(item.getRouterDes());
                dto.setValue(item.getId());
                dto.setFlag(item.getpRouterId());
                dto.setLevel(1);
                routerTree.add(dto);
            }
        });
        Map<String, List<TreeDto>> map = routerTree.stream().collect(Collectors.groupingBy(TreeDto::getFlag));
        List<TreeDto> routers = new ArrayList<>();
        allRouter.forEach(item -> {
            if ("0".equals(item.getRouterType())) {
                TreeDto dto = new TreeDto();
                dto.setId(item.getId());
                dto.setName(item.getRouterDes());
                dto.setValue(item.getId());
                dto.setLevel(0);
                dto.setChildren(map.get(item.getId()));
                routers.add(dto);
            }
        });

        List<String> checkedRouterIds = routerBizMapper.getCheckedRouterIds(roleId);
        result.put("routers", routers);
        result.put("checkedRouterIds", checkedRouterIds);
        return result;
    }

    /**
     * 递归查询router列表
     * @param parentList
     * @param roleId
     */
    private void recursionRouter(List<RouterDto> parentList,String roleId) {
        for(RouterDto routerDto: parentList){
            if(routerDto.getId() != null) {
                List<RouterDto> temp = routerBizMapper.queryRouterListByRoleId(roleId, routerDto.getId());
                routerDto.setRouterList(temp);
                recursionRouter(temp,roleId);
            }
        }
    }

    /**
     * 设置用户
     *
     * @param userDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean setUser(UserDto userDto) {
        String userId = userDto.getId();
        // 检查密码
        /*checkPassword(userDto);*/

        // 检查用户是否存在
        checkUserExists(userId, userDto.getUserName());
        // 检查用户状态
        checkPersonStatus(userId);

        String passWord;
        Profiles profiles = Profiles.of("dev","prepub-sz","test", "uat");
        if(env.acceptsProfiles(profiles)) {
            passWord = LogicConstant.DEFAULT_PASSWORD;
        } else {
            passWord = PasswordUtil.randomPassword(8);
        }
        String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
        userDto.setNewPassword(MD5Password);
        // 发送短信
        String content = this.getMsgContent(passWord, userDto.getUserName());
        logger.info("发送短信接口返回结果为:" + JSON.toJSONString(messageService.sendMessage(userDto.getTelephone(), content)));
        // 设置
        return doSetUser(userDto);
    }

    public String getMsgContent(String password,String userName){
        logger.info("message content :"+"您好，您股权激励管理系统登录账号为："+userName+"，登录初始密码为："+password+"请您及时登录并修改初始密码");
        return "您好，您股权激励管理系统登录账号为："+userName+"，登录初始密码为："+password+"请您及时登录并修改初始密码";
    }

    /**
     * 给用户设置权限
     *
     * @param rolePermissionDto
     * @return
     */
    public boolean setPermission(RolePermissionDto rolePermissionDto) {
        HashSet<String> routerIdSet = new HashSet<>(rolePermissionDto.getRouterIdList());
        // 固定添加首页的路由展示
        routerIdSet.add(CodeTableConstant.ROUTER_CODE_INDEX);
        RolePermissionDto rolePermissionDtoOld = new RolePermissionDto();
        rolePermissionDtoOld.setRouterIdList(roleRouterMapMapper.getRouterIdListByRoleId(rolePermissionDto.getRoleId()));
        // 清除掉当前角色的路由
        doDeleteRoleRouterByRoleId(rolePermissionDto.getRoleId());
        // 重新插入角色的路由
        doInsertRoleRouterByRoleIdAndRouterList(rolePermissionDto.getRoleId(), routerIdSet);
        try {
            rolePermissionDto.setRouterIdList(roleRouterMapMapper.getRouterDesByRouterIdList(routerIdSet));
            MessageUtil messageUtil = new MessageUtil();
            MessageInfo messageInfo = messageUtil.getMessageContext(rolePermissionDto, rolePermissionDtoOld);
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(rolePermissionDto),
                            messageInfo.getOperationContext(), "平台角色设置权限",
                            "系统管理/平台角色信息", "PermissionService.setPermission",
                            messageInfo.getOperationRemark(), ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }

        return true;
    }

    /**
     * 检查用户明是否存在
     *
     * @return
     */
    public boolean checkUserNameExists(String userName) {
        return checkUserNameExist(userName);
    }

    /**
     * 插入用户的相关路由列表
     * userId == roleId
     */
    private void doInsertRoleRouterByRoleIdAndRouterList(String roleId, HashSet<String> routerIdSet) {
        for(String routerId : routerIdSet){
            routerBizMapper.insertRoleRouter(roleId,routerId);
        }
    }

    /**
     * 删除用户有关的所有路由权限id
     * roleId == userId
     *
     * @param roleId
     */
    private void doDeleteRoleRouterByRoleId(String roleId) {
        RoleRouterMapExample example = new RoleRouterMapExample();
        example.createCriteria().andRoleIdEqualTo(roleId);
        roleRouterMapMapper.deleteByExample(example);
    }

    /**
     * 检查密码
     *
     * @param userDto
     */
    private void checkPassword(UserDto userDto) {
        // 判断 新密码和确认新密码是否一致
        if (!Objects.equals(userDto.getNewPassword(), userDto.getConfirmPassword())) {
            throw new ApplicationException("error.000101");
        }
    }

    /**
     * 设置用户
     *
     * @param userDto
     * @return
     */
    public boolean doSetUser(UserDto userDto) {
        userDto.setNewPassword(passwordEncoder.encode(userDto.getNewPassword()));
        boolean res = userBizMapper.insertUser(userDto) > 0;

        //向其他微服发送消息
        //Map<String,Object> personTopic = new HashMap<>();
        //
        //PersonWithBLOBs personWithBLOBs = personMapper.selectByUserId(userDto.getId());
        //
        //OrgExample example = new OrgExample();
        //example.createCriteria().andIdIn(Arrays.asList(personWithBLOBs.getOrgId().split(",")));
        //List<Org> orgList = orgMapper.selectByExample(example);
        //Org org = orgList.get(0);
        //
        //List<Org> orgListTopic = selPersonOrg(personWithBLOBs.getOrgId());
        //
        ////判断内外部
        //if ("4".equals(org.getOrgType())) {
        //
        //    SaCompanyDto saCompany = saCompanyMapper.getCompanyById(org.getCompanyId());
        //
        //    CompanyContactExample coCoExample = new CompanyContactExample();
        //    coCoExample.createCriteria().andCompanyCodeEqualTo(saCompany.getCompanyCode());
        //    List<CompanyContact> companyContacts = companyContactMapper.selectByExample(coCoExample);
        //
        //    personTopic.put("org",orgListTopic);
        //    personTopic.put("saCompany",saCompany);
        //    personTopic.put("saCompanyContacts",companyContacts);
        //    personTopic.put("person",personWithBLOBs);
        //
        //} else {
        //    personTopic.put("org",orgListTopic);
        //    personTopic.put("person",personWithBLOBs);
        //}
        //
        //personTopic.put("user",userDto);
        //
        //String uid = UUID.randomUUID().toString();
        //MQMsgDto<Map<String,Object>> send = new MQMsgDto<>();
        //send.setUid(uid);
        //send.setSystemType("HGGL");
        //send.setBusinessType(TopicConstant.BUS_TYPE_USER);
        //send.setOperationType(TopicConstant.OP_TYPE_SETUSER);
        //send.setInfo(personTopic);
        //try {
        //    saSystemUtil.insertMqMessage(send);
        //    topicProducer.sendTopic(personTopic, "HGGL", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_SETUSER,uid);
        //} catch (Exception e) {
        //    throw new RuntimeException(e);
        //}
        return res;
    }

    private List<Org> selPersonOrg(String id) {
        List<Org> orgList = new ArrayList<>();
        int maxLevel = orgMapper.selOrgLevel();
        for (int i = 0; i < maxLevel + 2; i++) {
            Org org = orgMapper.selParentById(id);
            if (org == null) {
                break;
            }
            orgList.add(org);
            if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                break;
            }
            id = org.getpOrgId();
        }
        return orgList;
    }

    /**
     * 插入角色相关的信息
     *
     * @param userId
     */
    private void insertRoleAndMap(String userId) {
        Role role = new Role();
        role.setId(userId);
        // 设置状态
        role.setCurrStatus(CodeTableConstant.USER_AVAILABLE);
        role.setStatus(CodeTableConstant.USER_AVAILABLE);
        roleBizMapper.insertRole(role);

        UserRoleMap userRoleMap = new UserRoleMap();
        userRoleMap.setRoleId(userId);
        userRoleMap.setUserId(userId);
        userRoleMapMapper.insert(userRoleMap);
    }

    /**
     * 检查人员状态
     *
     * @param userId
     */
    private void checkPersonStatus(String userId) {
        PersonWithBLOBs person = personMapper.selectByPrimaryKey(userId);
        // 人员为空或者人员的状态为删除状态
        if (person == null || Objects.equals(person.getStatus(), CodeTableConstant.DATA_STATUS_0)) {
            throw new ApplicationException("error.000107");
        }
        person.setUserId(userId);
        // 更新人员信息
        personMapper.updateByPrimaryKeySelective(person);
    }

    /**
     * 检查用户是否存在
     * 用户的id和人员的id是一致的
     *
     * @param userId
     */
    private void checkUserExists(String userId, String userName) {
        User user = userMapper.selectByPrimaryKey(userId);
        if (user != null) {
            throw new ApplicationException("error.000113");
        }
        checkUserName(userName);
    }

    /**
     * 检查用户名
     *
     * @param userName
     */
    private void checkUserName(String userName) {
        UserExample example = new UserExample();
        example.createCriteria().andUserNameEqualTo(userName);
        if (userMapper.countByExample(example) > 0) {
            throw new ApplicationException("error.000114");
        }
    }

    /**
     * 检查用户名
     *
     * @param userName
     */
    private boolean checkUserNameExist(String userName) {
        UserExample example = new UserExample();
        example.createCriteria().andUserNameEqualTo(userName);
        return userMapper.countByExample(example) > 0;
    }

    public Boolean checkUserNameById(String userName, String id) {
        UserExample example = new UserExample();
        if (StringUtils.isEmpty(id)) {
            example.createCriteria().andUserNameEqualTo("CSC" + userName);
        } else {
            example.createCriteria().andUserNameEqualTo("CSC" + userName).andIdNotEqualTo(id);
        }
        return userMapper.countByExample(example) > 0;
    }
}
