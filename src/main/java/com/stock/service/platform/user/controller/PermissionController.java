package com.stock.service.platform.user.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.user.dto.RolePermissionDto;
import com.stock.service.platform.user.dto.RouterDto;
import com.stock.service.platform.user.dto.UserDto;
import com.stock.service.platform.user.service.PermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 权限相关controller
 */
@Api(value = "权限管理", description = "人员列表-用户权限相关接口")
@RestController
@RequestMapping("/permission")
public class PermissionController extends BaseController {

    @Autowired
    PermissionService permissionService;


    /**
     * 人员列表获取用户权限
     *
     * @param routerDto
     * @return
     */
    //@ApiOperation(value = "获取所有权限菜单-带有用户勾选标记", notes = "获取系统的所有权限菜单-带有用户勾选标记")
    //@PostMapping("/queryRouterListByUserId")
    //public JsonResponse<List<RouterDto>> getRouterListByUserId(@Validated @RequestBody RouterDto routerDto) {
    //    JsonResponse<List<RouterDto>> jsonResponse = new JsonResponse<>();
    //    jsonResponse.setResult(permissionService.listRouterByRoleId(routerDto.getRoleId()));
    //    return jsonResponse;
    //}
    @ApiOperation(value = "获取所有权限菜单-带有用户勾选标记", notes = "获取系统的所有权限菜单-带有用户勾选标记")
    @PostMapping("/queryRouterListByUserId")
    public JsonResponse<Map<String,Object>> getRouterListByUserId(@Validated @RequestBody RouterDto routerDto) {
        JsonResponse<Map<String,Object>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(permissionService.listRouterByRoleId(routerDto.getRoleId()));
        return jsonResponse;
    }

    /**
     * 给人员设置用户
     *
     * @param userDto
     * @return
     */
    @ApiOperation(value = "给人员设置用户", notes = "给公司人员设置用户")
    @PostMapping("setUser")
    public JsonResponse<Boolean> setUser(@Validated(UserDto.SetUserGroup.class) @RequestBody UserDto userDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(permissionService.setUser(userDto));
        return jsonResponse;
    }

    /**
     * 给用户设置权限
     *
     * @param rolePermissionDto
     * @return
     */
    @ApiOperation(value = "用户设置权限", notes = "给公司人员设置用户")
    @PostMapping("setPermissions")
    public JsonResponse<Boolean> setUser(@Validated @RequestBody RolePermissionDto rolePermissionDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(permissionService.setPermission(rolePermissionDto));
        return jsonResponse;
    }


    /**
     * 给人员设置用户
     *
     * @param userDto
     * @return
     */
    @ApiOperation(value = "检查用户名是否存在", notes = "检查用户名是否存在")
    @PostMapping("checkUserName")
    public JsonResponse<Boolean> checkUserName(@Validated(UserDto.CheckUserNameGroup.class) @RequestBody UserDto userDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(permissionService.checkUserNameExists(userDto.getUserName()));
        return jsonResponse;
    }

    /**
     * 检查cas账号是否存在
     *
     * @param userDto
     * @return
     */
    @ApiOperation(value = "检查cas账号是否存在", notes = "检查cas账号是否存在")
    @PostMapping("checkUserNameById")
    public JsonResponse<Boolean> checkUserNameById(@Validated(UserDto.CheckUserNameGroup.class) @RequestBody UserDto userDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(permissionService.checkUserNameById(userDto.getUserName(), userDto.getId()));
        return jsonResponse;
    }


}
