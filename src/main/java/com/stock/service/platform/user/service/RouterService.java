package com.stock.service.platform.user.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.RouterMapper;
import com.stock.service.platform.common.dao.RouterResourceMapMapper;
import com.stock.service.platform.common.entity.Router;
import com.stock.service.platform.common.entity.RouterExample;
import com.stock.service.platform.common.entity.RouterResourceMap;
import com.stock.service.platform.common.entity.RouterResourceMapExample;
import com.stock.service.platform.user.dto.RouterDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
@Transactional
public class RouterService extends BaseService {
    @Autowired
    RouterMapper routerMapper;
    @Autowired
    RouterResourceMapMapper routerResourceMapMapper;

    public Boolean editRouter(RouterDto router) {
        if (router.getId() == null) {
            Router r = new Router();
            BeanUtils.copyProperties(router, r);
            routerMapper.insertSelective(r);
            List<String> resourceIdList = router.getResourceIdList();
            for (String s : resourceIdList) {
                RouterResourceMap rsm = new RouterResourceMap();
                rsm.setRouterId(r.getId());
                rsm.setResourceId(s);
                routerResourceMapMapper.insert(rsm);
            }
        } else {
            Router r = new Router();
            BeanUtils.copyProperties(router, r);
            routerMapper.updateByPrimaryKeySelective(r);
            RouterResourceMapExample example = new RouterResourceMapExample();
            example.createCriteria().andRouterIdEqualTo(r.getId());
            routerResourceMapMapper.deleteByExample(example);
            List<String> resourceIdList = router.getResourceIdList();
            for (String s : resourceIdList) {
                RouterResourceMap rsm = new RouterResourceMap();
                rsm.setRouterId(r.getId());
                rsm.setResourceId(s);
                routerResourceMapMapper.insert(rsm);
            }

        }
        return true;
    }

    public Boolean menuNameCheck(RouterDto router) {
        RouterExample example = new RouterExample();
        example.createCriteria().andRouterNameEqualTo(router.getRouterName());
        List<Router> routers = routerMapper.selectByExample(example);
        if (routers.size() == 1) {
            if (routers.get(0).getId().equals(router.getId())) {
                return true;
            } else {
                return false;
            }
        } else if (routers.size() > 1) {
            return false;
        } else {
            return true;
        }
    }

    public RouterDto queryMenuById(RouterDto router) {
        RouterDto result = new RouterDto();
        Router r = routerMapper.selectByPrimaryKey(router.getId());
        BeanUtils.copyProperties(r, result);
        RouterResourceMapExample example = new RouterResourceMapExample();
        example.createCriteria().andRouterIdEqualTo(router.getId());
        List<RouterResourceMap> routerResourceMaps = routerResourceMapMapper.selectByExample(example);
        List<String> resourceIdList = new ArrayList<>();
        for (RouterResourceMap map : routerResourceMaps) {
            resourceIdList.add(String.valueOf(map.getResourceId()));
        }
        result.setResourceIdList(resourceIdList);
        return result;
    }

    public Boolean deleteMenu(String idList) {
        if (StringUtils.isNotEmpty(idList)) {
            String[] split = idList.split(",");
            for (String s : split) {
                RouterResourceMapExample example = new RouterResourceMapExample();
                example.createCriteria().andRouterIdEqualTo(s);
                routerResourceMapMapper.deleteByExample(example);
                routerMapper.deleteByPrimaryKey(s);
            }
        }
        return true;
    }
}
