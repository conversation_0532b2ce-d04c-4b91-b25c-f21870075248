package com.stock.service.platform.user.dao;

import com.stock.service.platform.user.dto.RouterDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
@Repository
public interface RouterBizMapper {
    /**
     * 查询router路由列表，带有用户是否拥有的标记
     * @param roleId
     * @param pRouterId
     * @return
     */
    List<RouterDto> queryRouterListByRoleId(@Param("roleId") String roleId, @Param("pRouterId") String pRouterId);

    /**
     * 插入 roleRouterList
     * @param roleId
     * @param routerIdSet
     * @return
     */
    int insertRoleRouterList(@Param("roleId") String roleId, @Param("routerIdSet") Set<String> routerIdSet);

    /**
     * 插入 roleRouterList
     * @param roleId
     * @param routerId
     * @return
     */
    int insertRoleRouter(@Param("roleId") String roleId, @Param("routerId") String routerId);

    List<RouterDto> queryRouterList(@Param("userId") String userId);

    List<RouterDto> getAllRouter();

    List<String> getCheckedRouterIds(String roleId);
}
