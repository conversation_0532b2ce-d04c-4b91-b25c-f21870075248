package com.stock.service.platform.user.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.common.entity.Resource;
import com.stock.service.platform.user.dto.ResourceManageResponse;
import com.stock.service.platform.user.service.ResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("resource")
public class ResourceController extends BaseController {

    @Autowired
    ResourceService resourceService;


    /**
     * 展示资源信息列表
     */
    @RequestMapping(value = "queryResourceList")
    public Map<String, Object> queryPagingCompanyBySelective(@RequestBody ResourceManageResponse dto) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.user.dao.ResourceBizMapper.queryResourceList",dto);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /**
     * 创建资源信息
     */
    @PostMapping("createResource")
    public JsonResponse<Boolean> createPersonInfo(@RequestBody Resource resource) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        resource.setId(null);
        jsonResponse.setResult(resourceService.editResource(resource));
        return jsonResponse;
    }

    /**
     * 编辑用户信息
     */
    @PostMapping("updateResource")
    public JsonResponse<Boolean> updatePersonInfo(@RequestBody Resource resource) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(resourceService.editResource(resource));
        return jsonResponse;
    }

    /**
     * 删除资源
     */
    @PostMapping("deleteResourceByIds")
    public JsonResponse<Boolean> deletePersonByIds(@RequestBody Map<String,Object> map) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(resourceService.deleteResourceByIds(String.valueOf(map.get("idList"))));
        return response;
    }

    /**
     * 资源名称校验
     */
    @PostMapping("resourceNameCheck")
    public JsonResponse<Boolean> resourceNameCheck(@RequestBody Resource resource) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(resourceService.resourceNameCheck(resource));
        return response;
    }

    /**
     * 资源下拉
     */
    @PostMapping("getResourceDownList")
    public JsonResponse<List<Map<String,Object>>> getResourceDownList(@RequestBody Resource resource) {
        JsonResponse<List<Map<String,Object>>> response = new JsonResponse<>();
        response.setResult(resourceService.getResourceDownList());
        return response;
    }
}
