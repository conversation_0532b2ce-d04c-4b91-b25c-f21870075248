package com.stock.service.platform.user.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.user.dto.RouterDto;
import com.stock.service.platform.user.service.RouterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("router")
public class RouterController extends BaseController {

    @Autowired
    RouterService routerService;


    /**
     * 展示路由信息列表
     */
    @PostMapping("queryRouterList")
    public Map<String, Object> listRouterInfo(@RequestBody RouterDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.user.dao.RouterBizMapper.queryRouterList",dto);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }


    /**
     * 创建路由信息
     */
    @PostMapping("createRouter")
    public JsonResponse<Boolean> createRouter(@RequestBody RouterDto router) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        router.setId(null);
        jsonResponse.setResult(routerService.editRouter(router));
        return jsonResponse;
    }

    /**
     * 编辑路由信息
     */
    @PostMapping("modifyMenu")
    public JsonResponse<Boolean> modifyMenu(@RequestBody RouterDto router) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(routerService.editRouter(router));
        return jsonResponse;
    }

    /**
     * 删除路由
     */
    @PostMapping("deleteMenu")
    public JsonResponse<Boolean> deleteMenu(@RequestBody Map<String,Object> map) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(routerService.deleteMenu(String.valueOf(map.get("idList"))));
        return response;
    }

    /**
     * 路由名称校验
     */
    @PostMapping("menuNameCheck")
    public JsonResponse<Boolean> menuNameCheck(@RequestBody RouterDto router) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(routerService.menuNameCheck(router));
        return response;
    }

    /**
     * 单个路由信息查询
     */
    @PostMapping("queryMenuById")
    public JsonResponse<RouterDto> queryMenuById(@RequestBody RouterDto router) {
        JsonResponse<RouterDto> response = new JsonResponse<>();
        response.setResult(routerService.queryMenuById(router));
        return response;
    }


}
