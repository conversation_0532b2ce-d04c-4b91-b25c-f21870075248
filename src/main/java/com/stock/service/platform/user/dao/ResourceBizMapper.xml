<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.user.dao.ResourceBizMapper">

    <resultMap id="BaseResultMap" type="com.stock.service.platform.user.dto.ResourceManageResponse">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="p_id" jdbcType="VARCHAR" property="pId"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="resource_des" jdbcType="VARCHAR" property="resourceDes"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl"/>
        <result column="curr_status" jdbcType="VARCHAR" property="currStatus"/>
        <result column="relation" jdbcType="VARCHAR" property="relation"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <select id="queryResourceList" parameterType="com.stock.service.platform.user.dto.SystemManagerDto" resultMap="BaseResultMap">
        SELECT t.id, t.p_id,t.resource_name,t.resource_des,t.resource_url,t.resource_type
        FROM sa_resource t
        <where>
            <if test="id != null and id != ''">
                and t.id like concat('%',#{id},'%')
            </if>
            <if test="resourceName != null and resourceName != ''">
                and t.resource_name like concat('%',#{resourceName},'%')
            </if>
            <if test="resourceUrl != null and resourceUrl != ''">
                and t.resource_url like concat('%',#{resourceUrl},'%')
            </if>
        </where>
    </select>

    <select id="queryById" parameterType="com.stock.service.platform.user.dto.SystemManagerDto" resultMap="BaseResultMap">
        SELECT t.id, ro.role_name, t.mail, t.telephone, t.user_name
        FROM sa_user t LEFT JOIN sa_user_role_map map ON t.id = map.user_id LEFT JOIN sa_role ro ON map.role_id = ro.id
        <where>
            <if test="id != null and id != ''">
                and t.id like concat(#{id},'%')
            </if>
            <if test="mail != null and mail != ''">
                and t.mail like concat(#{mail},'%')
            </if>
            <if test="userName != null and userName != ''">
                and t.user_name like concat(#{userName},'%')
            </if>
            <if test="roleIdList != null and roleIdList.size > 0">
                ro.id in
                <foreach collection="roleIdList" item="result" index="index" open="(" close=")" separator=",">
                    #{result}
                </foreach>
            </if>
        </where>
    </select>


    <select id="getRoleDownList" resultType="com.stock.service.platform.common.entity.Code">
        SELECT id AS codeValue, resource_name AS codeName
        FROM sa_resource
    </select>

</mapper>