package com.stock.service.platform.user.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.ResourceMapper;
import com.stock.service.platform.common.entity.Resource;
import com.stock.service.platform.common.entity.ResourceExample;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Transactional
public class ResourceService extends BaseService {
    @Autowired
    ResourceMapper resourceMapper;

    public Boolean editResource(Resource resource) {
        if (resource.getId() == null) {
            resourceMapper.insert(resource);
        } else {
            resourceMapper.updateByPrimaryKeySelective(resource);
        }
        return true;
    }

    public Boolean deleteResourceByIds(String idList) {
        String[] split = {};
        if (StringUtils.isNotEmpty(idList)) {
            split = idList.split(",");
        }
        for (String id : split) {
            resourceMapper.deleteByPrimaryKey(id);
        }
        return true;
    }

    public Boolean resourceNameCheck(Resource resource) {
        ResourceExample example = new ResourceExample();
        example.createCriteria().andResourceNameEqualTo(resource.getResourceName());
        List<Resource> resources = resourceMapper.selectByExample(example);
        if (resources.size() == 1) {
            if (resources.get(0).getId().equals(resource.getId())) {
                return true;
            } else {
                return false;
            }
        } else if (resources.size() > 1) {
            return false;
        } else {
            return true;
        }
    }

    public List<Map<String, Object>> getResourceDownList() {
        List<Map<String, Object>> result = new ArrayList<>();
        ResourceExample example = new ResourceExample();
        List<Resource> resources = resourceMapper.selectByExample(example);
        for (Resource resource : resources) {
            Map<String, Object> map = new HashMap<>();
            map.put("codeName", String.valueOf(resource.getId()));
            map.put("codeValue", String.valueOf(resource.getResourceDes()));
            result.add(map);
        }
        return result;
    }
}
