<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.user.dao.RoleBizMapper">

   <resultMap id="BaseResultMap" type="com.stock.service.platform.user.dto.RoleManageResponse">
      <id column="id" jdbcType="VARCHAR" property="id" />
      <result column="role_name" jdbcType="VARCHAR" property="roleName" />
      <result column="role_des" jdbcType="VARCHAR" property="roleDes" />
      <result column="curr_status" jdbcType="VARCHAR" property="currStatus" />
      <result column="company_id" jdbcType="VARCHAR" property="companyId" />
      <result column="create_user" jdbcType="VARCHAR" property="createUser" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="status" jdbcType="VARCHAR" property="status" />
   </resultMap>
   <insert id="insertRole">
      insert into sa_role (id, role_name, role_des,
                           curr_status, company_id, create_user,
                           create_time, update_user, update_time,
                           status)
      values (#{id,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{roleDes,jdbcType=VARCHAR},
              #{currStatus,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, #{createUser,jdbcType=BIGINT},
              #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
              #{status,jdbcType=VARCHAR})
   </insert>

   <select id="queryRoleList" parameterType="com.stock.service.platform.user.dto.SystemManagerDto" resultMap="BaseResultMap">
      SELECT id, role_name, role_des, curr_status
      FROM sa_role
      <where>
         <if test="roleDes != null and roleDes != ''">
            and role_des like concat('%',#{roleDes},'%')
         </if>

         <if test="roleName != null and roleName != ''">
            and role_name like concat('%',#{roleName},'%')
         </if>
         <!-- <foreach collection="moduleIdsList" item="moduleId" open="(" close=")" separator="or" >
             find_in_set(${moduleId}, module_ids)
          </foreach>
          <foreach collection="tagIdsList" item="tagId" open="(" close=")" separator="or" >
             find_in_set(${tagId}, tag_ids)
          </foreach>
          <if test="resultList != null and resultList.size > 0">
             deal_status in
             <foreach collection="resultList" item="result" index="index" open="(" close=")" separator=",">
                #{result}
             </foreach>
          </if>-->
      </where>
   </select>

   <select id="getRoleDownList" resultType="com.stock.service.platform.common.entity.Code">
      SELECT id AS codeValue, role_name AS codeName
      FROM sa_role
   </select>
   <select id="exportRole" resultType="com.stock.service.platform.user.dto.ExcelRoleManage">
      SELECT
      role_name as roleName,
      m1.router_des as menuL1,
      m2.router_des as menuL2
      FROM
      sa_role_router_map m
      inner	join sa_role r on m.role_id = r.id
      INNER JOIN (SELECT * FROM sa_router r WHERE r.router_type = '0') m1 on m.router_id = m1.id
      left JOIN (SELECT * FROM sa_router r WHERE r.router_type = '1') m2 on m1.id = m2.p_router_id
      WHERE role_name is not null
      <if test="roleDes != null and roleDes != ''">
         and role_des like concat('%',#{roleDes},'%')
      </if>
      <if test="roleName != null and roleName != ''">
         and role_name like concat('%',#{roleName},'%')
      </if>
      ORDER BY role_name,m1.router_type,m1.sort_no
   </select>

</mapper>