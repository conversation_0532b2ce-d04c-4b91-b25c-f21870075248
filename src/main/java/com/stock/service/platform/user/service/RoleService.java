package com.stock.service.platform.user.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.RoleMapper;
import com.stock.service.platform.common.dao.RoleRouterMapMapper;
import com.stock.service.platform.common.dao.UserRoleMapMapper;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.user.dao.RoleBizMapper;
import com.stock.service.platform.user.dao.RouterBizMapper;
import com.stock.service.platform.user.dto.ExcelRoleManage;
import com.stock.service.platform.user.dto.RoleManageResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Service
@Transactional
public class RoleService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(RoleService.class);
    @Autowired
    RoleMapper roleMapper;
    @Autowired
    RoleRouterMapMapper roleRouterMapMapper;
    @Autowired
    UserRoleMapMapper userRoleMapMapper;
    @Autowired
    RoleBizMapper roleBizMapper;

    @Resource
    private LogService logService;

    @Autowired
    private RouterBizMapper routerBizMapper;

    public Boolean editRole(RoleManageResponse role) {
        if (role.getId() == null) {
            Role r = new Role();
            BeanUtils.copyProperties(role, r);
            roleMapper.insert(r);
            routerBizMapper.insertRoleRouter(r.getId(), "20");
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(r),
                                null, "新增平台角色",
                                "系统管理/平台角色信息", "RoleService.editRole",
                                "新增平台角色{角色名:" + r.getRoleName() + "}", ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        } else {
            Role r = new Role();
            BeanUtils.copyProperties(role, r);
            Role roleOld = roleMapper.selectByPrimaryKey(role.getId());
            roleMapper.updateByPrimaryKeySelective(r);
            try {
                MessageUtil messageUtil = new MessageUtil();
                MessageInfo messageInfo = messageUtil.getMessageContext(r, roleOld);
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(r),
                                messageInfo.getOperationContext(), "编辑平台角色",
                                "系统管理/平台角色信息", "RoleService.editRole",
                                messageInfo.getOperationRemark(), ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        }
        return true;
    }

    public Boolean deleteRole(String idList) {
        // 判断用户是否有分配该角色
        if (StringUtils.isNotEmpty(idList)) {
            String[] split = idList.split(",");
            for (String s : split) {
                UserRoleMapExample example = new UserRoleMapExample();
                example.createCriteria().andRoleIdEqualTo(s);
                List<UserRoleMap> userRoleMaps = userRoleMapMapper.selectByExample(example);
                if (userRoleMaps.size() > 0) {
                    return false;
                }
                Role roleOld = roleMapper.selectByPrimaryKey(s);
                roleMapper.deleteByPrimaryKey(s);
                try {
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    HttpServletRequest request = attributes.getRequest();
                    String ip = CommonService.getIp(request);
                    logService.asyncSaveOperationLog(
                            new OperationLogEvent(s,
                                    null, "删除平台角色",
                                    "系统管理/平台角色信息", "RoleService.deleteRole",
                                    "删除平台角色{" + roleOld.getRoleName() + "}", ip, getUserInfo()));
                } catch (Exception e) {
                    logger.info(e.getMessage());
                }
            }
        }
        return true;
    }

    public Boolean roleNameCheck(RoleManageResponse role) {
        RoleExample example = new RoleExample();
        example.createCriteria().andRoleNameEqualTo(role.getRoleName());
        List<Role> roles = roleMapper.selectByExample(example);
        if (roles.size() == 1) {
            if (roles.get(0).getId().equals(role.getId())) {
                return true;
            } else {
                return false;
            }
        } else if (roles.size() > 1) {
            return false;
        } else {
            return true;
        }
    }

    public List<Role> findAllRoles() {
        RoleExample example = new RoleExample();
        example.createCriteria().andRoleNameNotEqualTo("超级管理员");
        return roleMapper.selectByExample(example);
    }

    public List<ExcelRoleManage> exportRole(RoleManageResponse role) {
        return roleBizMapper.exportRole(role);
    }
}
