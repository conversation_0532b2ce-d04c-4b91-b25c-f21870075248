package com.stock.service.platform.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * 用户信息dto
 */
@ApiModel(value = "用户dto", description = "用户dto")
public class UserDto {

    /**
     * 用户id-即人员id
     */
    @ApiModelProperty(value = "用户id", notes = "也是人员的id", example = "1")
    @NotBlank(groups = SetUserGroup.class, message = "人员id为空")
    private String id;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id", notes = "公司id", example = "1")
    @NotBlank(groups = SetUserGroup.class, message = "公司id为空")
    private String companyId;

    /**
     * 组织架构id
     */
    @ApiModelProperty(value = "组织架构id", notes = "组织架构id", example = "1")
    @NotBlank(groups = SetUserGroup.class, message = "组织架构id为空")
    private String orgId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名", notes = "用户姓名", example = "1")
    @NotBlank(groups = SetUserGroup.class, message = "用户姓名为空")
    private String realName;

    /**
     * 用户名-登录使用
     */
    @ApiModelProperty(value = "用户名", notes = "登录使用", example = "bin.han")
    @NotBlank(groups = {SetUserGroup.class,CheckUserNameGroup.class}, message = "用户名为空")
    private String userName;

    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 旧密码
     */
    @NotBlank(message = "旧密码为空", groups = ChangePasswordGroup.class)
    @ApiModelProperty(value = "旧密码", notes = "修改密码时使用", example = "bin.han")
    private String oldPassword;

    /**
     * 新密码
     */

    @ApiModelProperty(value = "新密码", notes = "修改密码时使用，设置用户时使用", example = "bin.han")
    private String newPassword;

    /**
     * 新密码确认
     */

    @ApiModelProperty(value = "确认新密码", notes = "修改密码时使用，设置用户时使用", example = "bin.han")
    private String confirmPassword;



    @ApiModelProperty(value = "确认新密码")
    private String seriakey;

    @ApiModelProperty(value = "电话号码")
    private String telephone;

    @ApiModelProperty(value = "邮件")
    private String mail;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "备注")
    private String userType;

    @ApiModelProperty(value = "备注")
    private String hiddenKey;

    @ApiModelProperty(value = "备注")
    private String sort;

    @ApiModelProperty(value = "重复新密码")
    private String repeatNewPassword;

    @ApiModelProperty(value = "电话号码")
    private String captcha;

    private String mergeStatus;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getHiddenKey() {
        return hiddenKey;
    }

    public void setHiddenKey(String hiddenKey) {
        this.hiddenKey = hiddenKey;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getRepeatNewPassword() {
        return repeatNewPassword;
    }

    public void setRepeatNewPassword(String repeatNewPassword) {
        this.repeatNewPassword = repeatNewPassword;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 修改密码校验组
     */
    public static interface ChangePasswordGroup {
    }

    /**
     * 设置用户校验组
     */
    public static interface SetUserGroup {
    }

    /**
     * 检查用户名校验组
     */
    public static interface CheckUserNameGroup {
    }

    public String getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(String mergeStatus) {
        this.mergeStatus = mergeStatus;
    }
}
