package com.stock.service.platform.user.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import java.io.Serializable;

public class ExcelRoleManage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("角色名称")
    @ColumnWidth(20)
    private String roleName;

    @ExcelProperty("一级菜单")
    @ColumnWidth(30)
    private String menuL1;

    @ExcelProperty("二级菜单")
    @ColumnWidth(30)
    private String menuL2;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getMenuL1() {
        return menuL1;
    }

    public void setMenuL1(String menuL1) {
        this.menuL1 = menuL1;
    }

    public String getMenuL2() {
        return menuL2;
    }

    public void setMenuL2(String menuL2) {
        this.menuL2 = menuL2;
    }
}
