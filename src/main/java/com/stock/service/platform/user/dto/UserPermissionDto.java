package com.stock.service.platform.user.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * 用户权限相关dto
 */

public class UserPermissionDto {

    /**
     * 传入的授权路由列表
     */
    @NotNull(message = "路由参数列表为空")
    private List<String> routerIdList;

    /**
     * 传入的用户id
     */
    @NotBlank(message = "用户id为空")
    private String userId;


    public List<String> getRouterIdList() {
        return routerIdList;
    }

    public void setRouterIdList(List<String> routerIdList) {
        this.routerIdList = routerIdList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
