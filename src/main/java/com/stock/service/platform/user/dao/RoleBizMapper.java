package com.stock.service.platform.user.dao;

import com.stock.service.platform.common.entity.Code;
import com.stock.service.platform.common.entity.Role;
import com.stock.service.platform.user.dto.ExcelRoleManage;
import com.stock.service.platform.user.dto.RoleDto;
import com.stock.service.platform.user.dto.RoleManageResponse;
import com.stock.service.platform.user.dto.SystemManagerDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleBizMapper {

    List<RoleManageResponse> queryRoleList(SystemManagerDto systemManagerForm);

    List<Code> getRoleDownList();

    /**
     * 插入role
     * @param role
     * @return
     */
    int insertRole(Role role);

    List<ExcelRoleManage> exportRole(RoleManageResponse dto);
}
