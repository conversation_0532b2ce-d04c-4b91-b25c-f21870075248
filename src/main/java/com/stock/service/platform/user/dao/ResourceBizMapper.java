package com.stock.service.platform.user.dao;

import com.stock.service.platform.common.entity.Code;
import com.stock.service.platform.user.dto.ResourceManageResponse;
import com.stock.service.platform.user.dto.SystemManagerDto;

import java.util.List;

public interface ResourceBizMapper {


    ResourceManageResponse queryById(String id);

    List<ResourceManageResponse> queryResourceList(SystemManagerDto systemManagerForm);

    List<Code> getRoleDownList();
}
