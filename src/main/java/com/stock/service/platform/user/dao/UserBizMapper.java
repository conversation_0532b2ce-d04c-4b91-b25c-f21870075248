package com.stock.service.platform.user.dao;

import com.stock.service.platform.user.dto.UserDto;
import com.stock.service.platform.user.dto.UserManageResponse;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
public interface UserBizMapper {

    /**
     * 插入用户-设置用户
     * @param userDto
     * @return
     */
    int insertUser(UserDto userDto);


    UserManageResponse selectUserInfo(String userId);
    int updateUserLockInfo(Map<String, String> params);
}
