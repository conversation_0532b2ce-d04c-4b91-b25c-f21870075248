package com.stock.service.platform.user.controller;

import com.alibaba.excel.EasyExcel;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.user.dto.ExcelRoleManage;
import com.stock.service.platform.user.dto.RoleManageResponse;
import com.stock.service.platform.user.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("role")
public class RoleController extends BaseController {

    @Autowired
    RoleService roleService;


    /**
     * 展示角色信息列表
     */
    @RequestMapping(value = "queryRoleList")
    public Map<String, Object> queryRoleList(@RequestBody RoleManageResponse dto) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.user.dao.RoleBizMapper.queryRoleList",dto);
        resultMap.put("tableData", info.get("data"));
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /**
     * 创建角色信息
     */
    @PostMapping("createRole")
    public JsonResponse<Boolean> createRole(@RequestBody RoleManageResponse role) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        role.setId(null);
        jsonResponse.setResult(roleService.editRole(role));
        return jsonResponse;
    }

    /**
     * 编辑角色信息
     */
    @PostMapping("modifyRole")
    public JsonResponse<Boolean> modifyRole(@RequestBody RoleManageResponse role) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(roleService.editRole(role));
        return jsonResponse;
    }

    /**
     * 删除角色
     */
    @PostMapping("deleteRole")
    public JsonResponse<Boolean> deleteRole(@RequestBody Map<String, Object> map) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(roleService.deleteRole(String.valueOf(map.get("idList"))));
        return response;
    }

    /**
     * 角色名称校验
     */
    @PostMapping("roleNameCheck")
    public JsonResponse<Boolean> roleNameCheck(@RequestBody RoleManageResponse role) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(roleService.roleNameCheck(role));
        return response;
    }

    /**
     * 导出角色信息
     * @return
     */
    @PostMapping(value = "exportRole")
    @ResponseBody
    public void exportRole(@RequestBody RoleManageResponse role, HttpServletResponse response){
        List<ExcelRoleManage> excelList = roleService.exportRole(role);
        String fileName = "角色信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, ExcelRoleManage.class).sheet("角色信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}
