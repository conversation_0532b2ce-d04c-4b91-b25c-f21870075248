<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.person.dao.PersonMergeMapper">
    <update id="updateCertificateById">
        update sa_person set certificate = #{certificate} where id = #{id}
    </update>

    <!--获取用户信息-->
    <select id="selectPersonInfo"   resultType="com.stock.service.platform.person.dto.PersonInfoDto">
        select sp.source,
               sp.person_type                                                                                  as personType,
               sp.we_chat_id                                                                                   as weChatId,
               so.org_type                                                                                     as orgType,
               (select group_concat(sucm.company_id) from sa_user_company_map sucm where sucm.user_id = sp.id) as orgIds
        from sa_person sp
                 left join sa_org so on so.id = sp.org_id
        where sp.id = #{userId}
    </select>
    <select id="getCompanyCode" resultType="java.lang.String">
        select company_code from sa_company where id = #{companyId}
    </select>
    <select id="getCertificate" resultType="java.lang.String">
        select certificate from sa_person where id = #{id}
    </select>
    <select id="getMainUserSource" resultType="java.lang.String">
        select source from sa_person where id = #{id}
    </select>


</mapper>