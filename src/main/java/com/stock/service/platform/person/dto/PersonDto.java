package com.stock.service.platform.person.dto;

import com.stock.service.platform.common.entity.PersonWithBLOBs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 人员信息
 */
@ApiModel(value = "人员dto",description = "人员dto")
public class PersonDto extends PersonWithBLOBs {

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id",example = "1",notes = "更新时必填")
    @NotBlank(message = "人员id为空", groups = {UpdatePersonGroup.class,QueryPersonInfoGroup.class})
    private String id;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别",example = "1")
    private String sex;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id",example = "01")
    private String orgId;

    private List<String> orgIdList;

    /**
     * 搜索字符串
     */
    @ApiModelProperty(value = "搜索字符串（姓名，手机等）",example = "张三")
    @Length(min = 0,max = 30 ,message = "搜索字符串过长")
    private String searchString;

    /**
     * 用户锁状态
     */
    @ApiModelProperty(value = "用户锁定状态",example = "1")
    @NotBlank(message = "更新锁定状态为空",groups = {UpdateUserLockStatusGroup.class})
    private String userLockStatus;

    /**
     * 用户可用状态
     */
    @ApiModelProperty(value = "用户可用状态",example = "1")
    @NotBlank(message = "更新可用状态为空",groups = {UpdateUserAvailableStatusGroup.class})
    private String userAvailableStatus;

    /**
     * 人员名
     */
    @ApiModelProperty(value = "人员名字",example = "张三")
    @NotBlank(message = "人员名字为空", groups = {InsertPersonGroup.class, UpdatePersonGroup.class})
    private String personName;

    /**
     * 用户名字
     */
    @ApiModelProperty(value = "用户名字",example = "zhang.san")
    @NotBlank(message = "用户名为空",groups = {UpdateUserAvailableStatusGroup.class,UpdateUserLockStatusGroup.class})
    @Length(message = "用户名过长",groups = {UpdateUserAvailableStatusGroup.class,UpdateUserLockStatusGroup.class},min = 1,max = 20)
    private String userName;

    /**
     * 人员手机
     */
    @ApiModelProperty(value = "人员手机号",example = "13000000000")
    @NotBlank(message = "手机号为空",groups = SearchTelephoneGroup.class)
    @Length(message = "手机号过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 20)
    private String telephone;

    /**
     * 机构名字
     */
    @ApiModelProperty(value = "机构名字",example = "董事会")
    private String orgNames;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源 0-新增，1-OA",example = "1")
    private String source;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注",example = "这是备注")
    @Length(message = "备注过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 30)
    private String remark;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱",example = "<EMAIL>")
    @NotBlank(message = "邮箱为空",groups = SearchMailGroup.class)
    @Length(message = "邮箱过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 64)
    private String mail;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址",example = "<EMAIL>")
    @Length(message = "头像地址过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 100)
    private String pictureUrl;

    /**
     * 限制机构权限
     */
    @ApiModelProperty(value = "限制机构权限")
    private String orgLimitList;

    @ApiModelProperty(value = "人员id列表")
    @NotNull
    private List<Map<String,String>> personIds;

    /**
     * 人员id列表
     */
    @ApiModelProperty(value = "人员idSeriakey列表")
    private List<Map<String,String>> personIdSeriakeys;

    @ApiModelProperty(value = "机构类别")
    private String orgType;

    @ApiModelProperty(value = "内部/外部账号")
    private String userType;

    @ApiModelProperty(value = "内部/外部账号")
    private String userTypeCode;

    @ApiModelProperty(value = "所属机构类别")
    private String isMgr;

    @ApiModelProperty(value = "是否是管理员")
    private String isAdmin;

    @ApiModelProperty(value = "是否锁定")
    private String isLock;

    @ApiModelProperty(value = "锁定类型")
    private String lockType;

    @ApiModelProperty(value = "账户锁定计数")
    private String lockCnt;

    @ApiModelProperty(value = "无用字段")
    private String seriakey;

    @ApiModelProperty(value = "所属营业部")
    private String businessDepartmentStr;

    @ApiModelProperty(value = "是否修改过密码")
    private String isUpdatePwd;

    @ApiModelProperty(value = "证件号码")
    private String certificate;

    @ApiModelProperty(value = "证件类型(0:身份证)")
    private String certificateType;

    /**
     * 机构ids
     */
    @ApiModelProperty(value = "机构ids",example = "01")
    private List<String> orgIds;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    @ApiModelProperty(value = "人员类型")
    private String personType;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "确认密码")
    private String rePassword;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "职位")
    private String jobs;

    @ApiModelProperty(value = "职位")
    private String postion;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "传真")
    private String fax;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "公司ID")
    private String companyId;

    @ApiModelProperty(value = "职务")
    private String position;

    @ApiModelProperty(value = "客户号")
    private String weChatId;

    @ApiModelProperty(value = "合并状态")
    private String mergeStatus;

    @ApiModelProperty(value = "子账号用户名")
    private String childrenUserName;

    @ApiModelProperty(value = "子账号所属营业部")
    private String childrenOrg;

    @ApiModelProperty(value = "子账号所属营业部集合")
    private List<String> childrenOrgList;


    @ApiModelProperty(value = "营业部ID")
    private String childrenOrgId;

    @ApiModelProperty(value = "子账号角色")
    private String childrenRole;

    @ApiModelProperty(value = "子账号角色集合")
    private List<String> childrenRoleList;
    @ApiModelProperty(value = "子账号信息")
    private Map<String,Object> childrenUserInfo;

    @ApiModelProperty(value = "子账号状态")
    private String childrenLock;

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getWeChatId() {
        return weChatId;
    }

    public void setWeChatId(String weChatId) {
        this.weChatId = weChatId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getRePassword() {
        return rePassword;
    }

    public void setRePassword(String rePassword) {
        this.rePassword = rePassword;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getJobs() {
        return jobs;
    }

    public void setJobs(String job) {
        this.jobs = job;
    }

    public String getPostion() {
        return postion;
    }

    public void setPostion(String postion) {
        this.postion = postion;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getUserTypeCode() {
        return userTypeCode;
    }

    public void setUserTypeCode(String userTypeCode) {
        this.userTypeCode = userTypeCode;
    }

    public String getBusinessDepartmentStr() {
        return businessDepartmentStr;
    }

    public void setBusinessDepartmentStr(String businessDepartmentStr) {
        this.businessDepartmentStr = businessDepartmentStr;
    }

    public String getIsUpdatePwd() {
        return isUpdatePwd;
    }

    public void setIsUpdatePwd(String isUpdatePwd) {
        this.isUpdatePwd = isUpdatePwd;
    }

    public String getLockCnt() {
        return lockCnt;
    }

    public void setLockCnt(String lockCnt) {
        this.lockCnt = lockCnt;
    }

    public String getLockType() {
        return lockType;
    }

    public void setLockType(String lockType) {
        this.lockType = lockType;
    }

    public String getIsLock() {
        return isLock;
    }

    public void setIsLock(String isLock) {
        this.isLock = isLock;
    }

    public String getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(String isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }


    public List<Map<String, String>> getPersonIdSeriakeys() {
        return personIdSeriakeys;
    }

    public void setPersonIdSeriakeys(List<Map<String, String>> personIdSeriakeys) {
        this.personIdSeriakeys = personIdSeriakeys;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSearchString() {
        return searchString;
    }

    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }

    public String getUserLockStatus() {
        return userLockStatus;
    }

    public void setUserLockStatus(String userLockStatus) {
        this.userLockStatus = userLockStatus;
    }

    public String getUserAvailableStatus() {
        return userAvailableStatus;
    }

    public void setUserAvailableStatus(String userAvailableStatus) {
        this.userAvailableStatus = userAvailableStatus;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        if(StringUtils.isBlank(telephone)){
            this.telephone = null;
            return;
        }
        this.telephone = telephone;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        if(StringUtils.isBlank(mail)){
            this.mail = null;
            return;
        }
        this.mail = mail;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<Map<String, String>> getPersonIds() {
        return personIds;
    }

    public void setPersonIds(List<Map<String, String>> personIds) {
        this.personIds = personIds;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    /**
     * <AUTHOR>
     * 新增用户校验组
     */
    public static interface InsertPersonGroup {
    }

    /**
     * <AUTHOR>
     * 修改用户校验组
     */
    public static interface UpdatePersonGroup {
    }

    /**
     * <AUTHOR>
     * 修改用户可用状态用户组
     */
    public static interface UpdateUserAvailableStatusGroup{
    }

    /**
     * <AUTHOR>
     * 修改用户锁定状态用户组
     */
    public static interface UpdateUserLockStatusGroup{
    }

    /**
     * <AUTHOR>
     * 查询人员信息组
     */
    public static interface QueryPersonInfoGroup{
    }

    /**
     * <AUTHOR>
     * 删除人员信息组
     */
    public static interface DeletePersonInfoGroup{
    }

    /**
     * <AUTHOR>
     * 查询手机号检验组
     */
    public static interface SearchTelephoneGroup{
    }

    /**
     * <AUTHOR>
     * 查询邮箱校验组
     */
    public static interface SearchMailGroup{
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLimitList() {
        return orgLimitList;
    }

    public void setOrgLimitList(String orgLimitList) {
        this.orgLimitList = orgLimitList;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getIsMgr() {
        return isMgr;
    }

    public void setIsMgr(String isMgr) {
        this.isMgr = isMgr;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public List<String> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<String> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public String getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(String mergeStatus) {
        this.mergeStatus = mergeStatus;
    }

    public String getChildrenUserName() {
        return childrenUserName;
    }

    public void setChildrenUserName(String childrenUserName) {
        this.childrenUserName = childrenUserName;
    }

    public String getChildrenOrg() {
        return childrenOrg;
    }

    public void setChildrenOrg(String childrenOrg) {
        this.childrenOrg = childrenOrg;
    }

    public String getChildrenRole() {
        return childrenRole;
    }

    public void setChildrenRole(String childrenRole) {
        this.childrenRole = childrenRole;
    }

    public String getChildrenOrgId() {
        return childrenOrgId;
    }

    public void setChildrenOrgId(String childrenOrgId) {
        this.childrenOrgId = childrenOrgId;
    }

    public List<String> getChildrenOrgList() {
        return childrenOrgList;
    }

    public void setChildrenOrgList(List<String> childrenOrgList) {
        this.childrenOrgList = childrenOrgList;
    }

    public List<String> getChildrenRoleList() {
        return childrenRoleList;
    }

    public void setChildrenRoleList(List<String> childrenRoleList) {
        this.childrenRoleList = childrenRoleList;
    }

    public Map<String, Object> getChildrenUserInfo() {
        return childrenUserInfo;
    }

    public void setChildrenUserInfo(Map<String, Object> childrenUserInfo) {
        this.childrenUserInfo = childrenUserInfo;
    }

    public String getChildrenLock() {
        return childrenLock;
    }

    public void setChildrenLock(String childrenLock) {
        this.childrenLock = childrenLock;
    }
}
