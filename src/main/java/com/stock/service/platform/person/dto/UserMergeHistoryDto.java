package com.stock.service.platform.person.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024-03-04 15:54
 */
@Data
public class UserMergeHistoryDto {
    private String id;
    private String userId;
    private String subsidiaryUserId;
    private String mergeFlag;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String createUser;

    // 前台展示
    private String userName;
}
