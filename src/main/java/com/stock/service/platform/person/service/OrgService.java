package com.stock.service.platform.person.service;

import com.stock.core.util.BeanUtil;
import com.stock.service.platform.common.dao.OrgBizMapper;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.OrgExample;
import com.stock.service.platform.person.dto.OrgDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrgService {

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private OrgBizMapper orgBizMapper;

    /**
     * 查询org的列表
     *
     * @return
     */
    public List<OrgDto> listOrgDto(String orgId,String orgType) {
        OrgExample example = new OrgExample();
        List<String> orgIdList = Arrays.asList(orgId.split(","));
        if ("1".equals(orgType)){
            // 查询所有的
            example.createCriteria().andStatusEqualTo("1");
        }else{
            // 查询所属机构
            example.createCriteria().andStatusEqualTo("1").andIdIn(orgIdList);
        }
        example.setOrderByClause("org_code");
        List<Org> orgList = orgMapper.selectByExample(example);
        List<OrgDto> resultList = new ArrayList<>();
        for (Org org : orgList) {
            OrgDto orgDto = new OrgDto();
            BeanUtil.copy(org, orgDto);
            orgDto.setOrgId(String.valueOf(org.getId()));
            resultList.add(orgDto);
        }
        return resultList;
    }

    /**
     * 查询org的列表
     *
     * @return
     */
    public List<OrgDto> listOrgDtoByUserType(String orgId,String orgType,String userType) {
        OrgExample example = new OrgExample();
        List<String> orgIdList = Arrays.asList(orgId.split(","));
        if ("1".equals(orgType)){
            // 查询所有的
            example.createCriteria().andStatusEqualTo("1");
        }else{
            // 查询所属机构
            example.createCriteria().andStatusEqualTo("1").andIdIn(orgIdList);
        }
        example.setOrderByClause("org_code");
        List<Org> orgList = orgMapper.selectByExample(example);
        List<OrgDto> resultList = new ArrayList<>();
        for (Org org : orgList) {
            OrgDto orgDto = new OrgDto();
            BeanUtil.copy(org, orgDto);
            orgDto.setOrgId(String.valueOf(org.getId()));
            orgDto.setCompanyId(String.valueOf(org.getCompanyId()));
            if (StringUtils.isNotEmpty(userType) && "0".equals(userType) && "4".equals(org.getOrgType())) {
                continue;
            } else if (StringUtils.isNotEmpty(userType) && "1".equals(userType) && !"4".equals(org.getOrgType())) {
                continue;
            }
            resultList.add(orgDto);
        }
        if (StringUtils.isNotEmpty(userType) && "1".equals(userType)) {
            resultList = new ArrayList<>(resultList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(OrgDto::getCompanyId, Function.identity(), (existing, replacement) -> existing),
                            Map::values
                    )));
        }
        return resultList;
    }

    public List<OrgDto> listOrgDtoByUserTypeOnlyThree(String orgId,String orgType) {
        OrgExample example = new OrgExample();
        List<String> orgIdList = Arrays.asList(orgId.split(","));
        if ("1".equals(orgType)){
            // 查询所有的
            example.createCriteria().andStatusEqualTo("1").andOrgTypeEqualTo("3");
        }else{
            // 查询所属机构
            example.createCriteria().andStatusEqualTo("1").andIdIn(orgIdList).andOrgTypeEqualTo("3");
        }
        example.setOrderByClause("org_code");
        List<Org> orgList = orgMapper.selectByExample(example);
        List<OrgDto> resultList = new ArrayList<>();
        for (Org org : orgList) {
            OrgDto orgDto = new OrgDto();
            BeanUtil.copy(org, orgDto);
            orgDto.setOrgId(String.valueOf(org.getId()));
            orgDto.setCompanyId(String.valueOf(org.getCompanyId()));
            resultList.add(orgDto);
        }
        return resultList;
    }

    /**
     * 根据机构获取有权限的机构串
     * @param orgId
     * @param orgType
     * @return
     */
    public String selectOrgLimitList(String orgId,String orgType,String isAdmin) {
        if("1".equals(orgType) || "1".equals(isAdmin)) {
            return "total";
        } else if("2".equals(orgType)) {
            return orgBizMapper.selectOrgIdByBranch(orgId);
        } else if ("3".equals(orgType)) {
            String pId = orgBizMapper.checkBranchIfShare(orgId);
            if(StringUtils.isEmpty(pId)) {
                //父机构不是营业部共享的
                return orgBizMapper.selectOrgIdByDept(orgId);
            } else {
                return orgBizMapper.selectOrgIdByBranchBesb(pId);
            }
        } else {
            return orgId;
        }
    }
}
