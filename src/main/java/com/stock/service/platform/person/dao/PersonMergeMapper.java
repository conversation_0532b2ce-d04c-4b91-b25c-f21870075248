package com.stock.service.platform.person.dao;

import com.stock.service.platform.person.dto.PersonInfoDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PersonMergeMapper {

    List<PersonInfoDto> selectPersonInfo(@Param("userId")  String  userId );

    String getCompanyCode(String companyId);

    String getCertificate(String id);

    String getMainUserSource(String id);

    void updateCertificateById(String id, String certificate);
}
