package com.stock.service.platform.person.dao;

import com.stock.core.dto.QueryInfo;
import com.stock.service.platform.common.dto.SelectTreeTagDto;
import com.stock.service.platform.common.entity.PersonWithBLOBs;
import com.stock.service.platform.person.dto.ExcelUserManage;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.person.dto.UserMergeHistoryDto;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 人员业务mapper
 */
@Repository
public interface PersonBizMapper {
    List<PersonDto> listPersonInfo(QueryInfo<PersonDto> queryInfo);

    List<ExcelUserManage> exportUserInfo(PersonDto personDto);

    List<SelectTreeTagDto> getCodeLabelByNo(Map<String,Object> paramMap);

    int selectByIdSeriakey(Map<String,String> userDto);

    List<UserMergeHistoryDto> queryTableDate(String id);

    List<PersonDto> getOutPersonIdList();

    PersonDto selectPersonInfoByIdSeriakey(Map<String,String> param);

    int updatePersonChildrenInfo(PersonDto personDto);

    int deletePersonChildrenInfo(PersonDto personDto);

    PersonDto getChildrenInfo(PersonDto personDto);

    int insertPerson(PersonDto personDto);

    String getPersonId(PersonDto personDto);
}
