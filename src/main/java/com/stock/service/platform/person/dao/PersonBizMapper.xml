<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.person.dao.PersonBizMapper">

    <resultMap id="selectChildrenInfoMap" type="com.stock.service.platform.person.dto.PersonDto">
        <result property="childrenUserName"  column="children_user_name"/>
        <result property="childrenOrg"  column="children_org"/>
        <result property="childrenRole"  column="children_role"/>
        <result property="childrenOrgId"  column="children_org"/>
        <result property="childrenLock"  column="children_lock"/>
    </resultMap>
    <insert id="insertPerson">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_person (id, employee_number, person_type,
        person_name, foreign_name, title_name,
        job_id, org_name, org_id,
        sex, birthday, telephone,
        telephone_spare1, telephone_spare2, phone,
        mail, mail_spare1, mail_spare2,
        fax, post, address,
        nationality, certificate_type, certificate,
        picture_url, user_id, resume_status,
        active_status, company_name, identity_type,
        share_tradable, share_count, company_shareholder,
        eid, company_code, source,
        create_user, create_time, update_user,
        update_time, status, resume,
        remark,hidden_key, we_chat_id, jobs,children_org,children_user_name,children_role)
        values (#{id,jdbcType=VARCHAR}, #{employeeNumber,jdbcType=VARCHAR}, #{personType,jdbcType=VARCHAR},
        #{personName,jdbcType=VARCHAR}, #{foreignName,jdbcType=VARCHAR}, #{titleName,jdbcType=VARCHAR},
        #{jobId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR},
        #{sex,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE}, #{telephone,jdbcType=VARCHAR},
        #{telephoneSpare1,jdbcType=VARCHAR}, #{telephoneSpare2,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
        #{mail,jdbcType=VARCHAR}, #{mailSpare1,jdbcType=VARCHAR}, #{mailSpare2,jdbcType=VARCHAR},
        #{fax,jdbcType=VARCHAR}, #{post,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
        #{nationality,jdbcType=VARCHAR}, #{certificateType,jdbcType=VARCHAR}, #{certificate,jdbcType=VARCHAR},
        #{pictureUrl,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, #{resumeStatus,jdbcType=VARCHAR},
        #{activeStatus,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{identityType,jdbcType=VARCHAR},
        #{shareTradable,jdbcType=DECIMAL}, #{shareCount,jdbcType=DECIMAL}, #{companyShareholder,jdbcType=VARCHAR},
        #{eid,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR}, now(), #{updateUser,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, #{resume,jdbcType=LONGVARCHAR},
        #{remark,jdbcType=LONGVARCHAR},uuid(), #{weChatId,jdbcType=VARCHAR}, #{jobs,jdbcType=VARCHAR},
        #{childrenOrg,jdbcType=VARCHAR}, #{childrenUserName,jdbcType=VARCHAR}, #{childrenRole,jdbcType=VARCHAR})
    </insert>

    <update id="updatePersonChildrenInfo">
        update sa_person
            <set>
                <if test="childrenUserName != null and childrenUserName != ''">
                    children_user_name = #{childrenUserName},
                </if>
                <if test="childrenOrg != null and childrenOrg != ''">
                    children_org = #{childrenOrg},
                </if>
                <if test="childrenRole != null and childrenRole != ''">
                    children_role = #{childrenRole},
                </if>
                <if test="childrenLock != null and childrenLock != ''">
                    children_lock = #{childrenLock},
                </if>
            </set>
            where
                id = #{id}
    </update>
    
    <update id="deletePersonChildrenInfo">
        update sa_person
        set
            children_user_name = null,
            children_org = null,
            children_role = null
        where
            id = #{id}
    </update>

<!--    <if test="orgId != null and orgId != ''">-->
<!--        and p.org_id like concat('%',#{orgId},'%')-->
<!--    </if>-->
    <select id="listPersonInfo" parameterType="com.stock.service.platform.person.dto.PersonDto" resultType="com.stock.service.platform.person.dto.PersonDto">
        SELECT
        p.person_name AS personName,
        p.sex AS sex,
        p.id AS id,
        GROUP_CONCAT(DISTINCT so.org_name ORDER BY so.org_name ASC SEPARATOR ', ') AS orgNames, -- 使用GROUP_CONCAT来合并多个组织名称
        u.user_name AS userName,
        p.telephone AS telephone,
        u.lock_state AS userLockStatus,
        u.STATUS AS userAvailableStatus,
        p.hidden_key AS seriakey,
        p.org_id AS orgId,
        so.org_type AS orgType,
        p.source AS source,
        u.user_type AS userType,
        u.merge_status as mergeStatus
        FROM
        sa_person p
        LEFT JOIN sa_user u ON p.user_id = u.id
        LEFT JOIN sa_org so ON FIND_IN_SET(so.id, p.org_id) > 0
        <where>
            p.status = '1'
            <if test="orgId != null and orgId != '' and orgId == '2'">
                and so.id = #{orgId}
            </if>
            <if test="orgId != null and orgId != '' and orgId != '2'">
                and so.id like #{orgId}
            </if>
            <if test="userLockStatus != null and userLockStatus != ''">
                and u.lock_state = #{userLockStatus}
            </if>
            <if test="userAvailableStatus != null and userAvailableStatus != ''">
                and u.status = #{userAvailableStatus}
            </if>
            <if test="searchString != null and searchString != ''">
                and (
                u.user_name like concat('%',#{searchString},'%')
                or
                p.person_name like concat('%',#{searchString},'%')
                )
            </if>
            <if test="orgLimitList == null or orgLimitList != 'total'.toString()">
                and find_in_set(so.id,#{orgLimitList})
            </if>
            <if test="userType != null and userType != ''">
                <choose>
                    <when test="userType == '0'.toString()">
                        and so.org_type in ('1','2','3')
                    </when>
                    <otherwise>
                        and so.org_type = '4'
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY p.id
        order by p.create_time desc

    </select>
    <select id="exportUserInfo" resultType="com.stock.service.platform.person.dto.ExcelUserManage">
        SELECT
        p.person_name AS personName,
        GROUP_CONCAT( DISTINCT so.org_name ORDER BY so.org_name ASC SEPARATOR ', ' ) AS orgNames,
        u.user_name AS userName,
        CASE WHEN u.lock_state = '0' THEN '未锁定' WHEN u.lock_state = '1' THEN '已锁定' ELSE '--' END AS userLockStatus,
        r.role_name AS roleName,
        (SELECT GROUP_CONCAT( sor.role_name ) FROM sa_outer_role sor LEFT JOIN sa_outer_role_user_map sorum ON sor.id = sorum.role_id WHERE sorum.user_id = p.id AND sor.sys_code = 'HGGL' GROUP BY p.id) AS HGGLRoleName,
        (SELECT GROUP_CONCAT( sor.role_name ) FROM sa_outer_role sor LEFT JOIN sa_outer_role_user_map sorum ON sor.id = sorum.role_id WHERE sorum.user_id = p.id AND sor.sys_code = 'GQJL' GROUP BY p.id) AS GQJLRoleName,
        (SELECT GROUP_CONCAT( sor.role_name ) FROM sa_outer_role sor LEFT JOIN sa_outer_role_user_map sorum ON sor.id = sorum.role_id WHERE sorum.user_id = p.id AND sor.sys_code = 'TDGF' GROUP BY p.id) AS TDGFRoleName,
        p.remark AS remark,
        CASE WHEN so.org_type = '1' OR so.org_type = '2' OR so.org_type = '3' THEN '券商账号' ELSE '上市公司账号' END AS isQs
        FROM
        sa_person p
        LEFT JOIN sa_user u ON p.user_id = u.id
        LEFT JOIN sa_org so ON FIND_IN_SET( so.id, p.org_id ) > 0
        LEFT JOIN sa_user_role_map m ON p.user_id = m.user_id
        LEFT JOIN sa_role r ON r.id = m.role_id
        <where>
            p.status = '1'
            <if test="orgId != null and orgId != ''">
                and p.org_id like concat('%',#{orgId},'%')
            </if>
            <if test="userLockStatus != null and userLockStatus != ''">
                and u.lock_state = #{userLockStatus}
            </if>
            <if test="userAvailableStatus != null and userAvailableStatus != ''">
                and u.status = #{userAvailableStatus}
            </if>
            <if test="searchString != null and searchString != ''">
                and (
                u.user_name like concat('%',#{searchString},'%')
                or
                p.person_name like concat('%',#{searchString},'%')
                or
                p.telephone like concat('%',#{searchString},'%')
                )
            </if>
            <if test="orgLimitList == null or orgLimitList != 'total'.toString()">
                and find_in_set(p.org_id,#{orgLimitList})
            </if>
            <if test="userType != null and userType != ''">
                <choose>
                    <when test="userType == '0'.toString()">
                        and so.org_type in ('1','2','3')
                    </when>
                    <otherwise>
                        and so.org_type = '4'
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY p.id
        order by p.create_time desc
    </select>


    <select id="getCodeLabelByNo" parameterType="java.util.Map"
            resultType="com.stock.service.platform.common.dto.SelectTreeTagDto">
        select t.code_value labelValue,
        t.code_name labelName,
        t.code_name name,
        t.id id,
        0 pId
        from sa_code t
        where t.code_no = #{labelCode}
        and t.valid_flag = '1'
        <if test="codeValueList != null and codeValueList.size !=0">
            and t.code_value in
            <foreach collection="codeValueList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by t.sort_no
    </select>

    <select id="selectByIdSeriakey" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM sa_person
        WHERE id = #{id,jdbcType=VARCHAR}
        <if test="seriakey != null and seriakey != ''">
          AND hidden_key = #{seriakey,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryTableDate" resultType="com.stock.service.platform.person.dto.UserMergeHistoryDto">
        select sumh.id,
               sumh.user_id userId,
               sumh.subsidiary_user_id subsidiaryUserId,
               sumh.create_time createTime,
               sumh.create_user createUser,
               sumh.merge_flag mergeFlag,
               su.user_name userName
        from sa_user_merge_history sumh
        left join sa_user su on su.id = sumh.subsidiary_user_id
        where user_id = #{id}
    </select>
<!--    <select id="getOutPersonIdList" resultType="com.stock.service.platform.person.dto.PersonDto">-->
<!--        select-->
<!--            p.id as id,-->
<!--            p.person_name as personName,-->
<!--            p.telephone as telephone,-->
<!--            p.mail as mail-->
<!--        from-->
<!--            sa_person p-->
<!--                left join sa_org o on o.id = p.org_id-->
<!--        where-->
<!--            (o.org_type REGEXP '^-?[0-9]+$') AND CAST(o.org_type AS SIGNED) > 3-->

<!--    </select>-->
    <select id="getOutPersonIdList" resultType="com.stock.service.platform.person.dto.PersonDto">
        select
            p.id as id,
            s.user_name as personName,
            p.telephone as telephone,
            p.mail as mail
        from
            sa_person p
            left join sa_user s on s.id = p.id
        where
            s.user_type = '1'
        and s.lock_state != '1'
    </select>


    <select id="selectPersonInfoByIdSeriakey" parameterType="java.util.Map" resultMap="selectChildrenInfoMap">
        select
        children_user_name,children_org,children_role
        from sa_person
        where id = #{id,jdbcType=VARCHAR}
        and hidden_key = #{seriakey,jdbcType=VARCHAR}
    </select>
    <select id="getChildrenInfo" resultMap="selectChildrenInfoMap">
        select
            children_user_name,children_org,children_role,telephone,org_id, children_lock
        from sa_person
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="getPersonId" resultType="java.lang.String">
        SELECT sp.id from sa_person sp left join sa_user su on su.id = sp.id where su.user_name = #{userName,jdbcType=VARCHAR}
    </select>

</mapper>