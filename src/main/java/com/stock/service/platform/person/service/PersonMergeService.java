package com.stock.service.platform.person.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.service.BaseService;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.manager.CscSignaturePersonManager;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.outer.service.RoleOuterService;
import com.stock.service.platform.person.dao.PersonMergeMapper;
import com.stock.service.platform.person.dto.MergePersonDto;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.person.dto.PersonInfoDto;
import com.stock.service.platform.person.dto.SystemRoleVerify;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户合并相关功能
 */
@Service
public class PersonMergeService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(PersonMergeService.class);

    @Autowired
    private OuterRoleMapper outerRoleMapper;

    @Autowired
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    @Autowired
    private PersonService personService;

    @Autowired
    private SaUserMergeHistoryMapper saUserMergeHistoryMapper;

    @Autowired
    private UserRoleMapMapper userRoleMapMapper;

    @Autowired
    private UserDtoMapper userDtoMapper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private Environment env;

    @Autowired
    private CscSignaturePersonManager cscSignaturePersonManager;

    @Autowired
    private SaSystemUtil saSystemUtil;

    @Autowired
    private TopicProducer topicProducer;

    @Autowired
    private PersonMergeMapper personMergeMapper;

    @Resource
    private QuestionnaireService questionnaireService;

    @Resource
    private LogService logService;

    @Resource
    private CommonService commonService;

    public boolean haveOtherSystemRole(List<SystemRoleVerify> systemRoleVerifyList) {

        if (CollectionUtils.isNotEmpty(systemRoleVerifyList) && systemRoleVerifyList.size() != 2) {
            throw new IllegalArgumentException("参数错误:" + systemRoleVerifyList);
        }

        for (SystemRoleVerify systemRoleVerify : systemRoleVerifyList) {

            OuterRoleUserMapExample userMapExample = new OuterRoleUserMapExample();
            userMapExample.createCriteria().andUserIdEqualTo(systemRoleVerify.getUserId());
            List<OuterRoleUserMap> outerRoleUserMaps = outerRoleUserMapMapper.selectByExample(userMapExample);

            for (OuterRoleUserMap outerRoleUserMap : outerRoleUserMaps) { // 判断当前用户选择的接入系统角色是否有自己来源之外的，如果包含不让合并；
                if (!outerRoleUserMap.getSysCode().equals(systemRoleVerify.getSource())) {
                    return true;
                }
            }
        }

        return false;

    }

    /**
     * 合并用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean mergePerson(List<MergePersonDto> userList) {

        if (CollectionUtils.isNotEmpty(userList) && userList.size() != 2) {
            throw new IllegalArgumentException("参数错误:" + userList);
        }

        //----------找到合并和待合并用户-----------------------------------------------
        MergePersonDto mainPersonDto = userList.stream()
                .filter(dto -> "1".equals(dto.getMainUser()))
                .findAny().orElseThrow(IllegalArgumentException::new);

        MergePersonDto mergePersonDto = userList.stream()
                .filter(dto -> !"1".equals(dto.getMainUser()))
                .findAny().orElseThrow(IllegalArgumentException::new);

        UserDto mainUser = userDtoMapper.selectByPrimaryKey(mainPersonDto.getUserId());
        UserDto mergeUser = userDtoMapper.selectByPrimaryKey(mergePersonDto.getUserId());
        //---------------------------------------------------------

        // 获取两个人的平台角色
        UserRoleMapExample mainUserMapExample = new UserRoleMapExample();
        mainUserMapExample.createCriteria().andUserIdEqualTo(mainPersonDto.getUserId());
        List<UserRoleMap> mainUserRoleList = userRoleMapMapper.selectByExample(mainUserMapExample);

        UserRoleMapExample mergeUserMapExample = new UserRoleMapExample();
        mergeUserMapExample.createCriteria().andUserIdEqualTo(mergePersonDto.getUserId());
        List<UserRoleMap> mergeUserRoleList = userRoleMapMapper.selectByExample(mergeUserMapExample);
        // 提取两人的平台角色id
        List<String> mainRoleId = mainUserRoleList.stream().map(UserRoleMap::getRoleId).collect(Collectors.toList());
        List<String> mergeRoleId = mergeUserRoleList.stream().map(UserRoleMap::getRoleId).collect(Collectors.toList());

        // 被合并改为锁定
        boolean flag = personService.updateUserLockStatus(mergePersonDto.getUserName(), "1", "0");
        personMapper.updateUserStatus(mergePersonDto.getUserId(), "0");

        // 历史表新增
        SaUserMergeHistory saUserMergeHistory = new SaUserMergeHistory();
        saUserMergeHistory.setCreateUser(getUserInfo().getUserId());
        saUserMergeHistory.setUserId(mainPersonDto.getUserId());
        saUserMergeHistory.setSubsidiaryUserId(mergePersonDto.getUserId());
        saUserMergeHistory.setMainUserMargeRoleId(String.join(",", mainRoleId));
        saUserMergeHistory.setMergeFlag("0");
        saUserMergeHistory.setMergedRoleId(String.join(",", mergeRoleId));
        saUserMergeHistory.setCreateTime(new Date());
        int i = saUserMergeHistoryMapper.insertSelective(saUserMergeHistory);

        // 两人的平台角色id合并
        List<String> newRoleIdList = new ArrayList<>();
        newRoleIdList.addAll(mainRoleId);
        newRoleIdList.addAll(mergeRoleId);
        newRoleIdList = newRoleIdList.stream().distinct().collect(Collectors.toList());

        // 把被合并的平台角色给主用户
        this.settingsUserRoles(mainPersonDto.getUserId(), newRoleIdList);

        // 接入系统角色合并-------------------------------------------------------------------------
        OuterRoleUserMapExample mainOuterUserMapExample = new OuterRoleUserMapExample();
        mainOuterUserMapExample.createCriteria().andUserIdEqualTo(mainPersonDto.getUserId());
        List<OuterRoleUserMap> mainOuterUserRoleList = outerRoleUserMapMapper.selectByExample(mainOuterUserMapExample);

        OuterRoleUserMapExample mergeOuterUserMapExample = new OuterRoleUserMapExample();
        mergeOuterUserMapExample.createCriteria().andUserIdEqualTo(mergePersonDto.getUserId());
        List<OuterRoleUserMap> mergeOuterUserRoleList = outerRoleUserMapMapper.selectByExample(mergeOuterUserMapExample);

        List<String> mainOuterRoleId = mainOuterUserRoleList.stream().map(OuterRoleUserMap::getRoleId).collect(Collectors.toList());
        List<String> mergeOuterRoleId = mergeOuterUserRoleList.stream().map(OuterRoleUserMap::getRoleId).collect(Collectors.toList());

        // 接入系统角色合并
        List<String> newOuterRoleIdList = new ArrayList<>();
        newOuterRoleIdList.addAll(mainOuterRoleId);
        newOuterRoleIdList.addAll(mergeOuterRoleId);
        newOuterRoleIdList = newOuterRoleIdList.stream().distinct().collect(Collectors.toList());

        // 先删除这个用户对应的权限
        OuterRoleUserMapExample mapExample = new OuterRoleUserMapExample();
        mapExample.createCriteria().andUserIdEqualTo(mainPersonDto.getUserId());
        outerRoleUserMapMapper.deleteByExample(mapExample);
        // 插入
        OuterRoleExample roleExample = new OuterRoleExample();
        roleExample.createCriteria().andIdIn(newOuterRoleIdList);
        List<OuterRole> roleList = outerRoleMapper.selectByExample(roleExample);
        roleList.forEach(item -> {
            OuterRoleUserMap record = new OuterRoleUserMap();
            record.setRoleId(item.getId());
            record.setUserId(mainPersonDto.getUserId());
            record.setSource(CodeTableConstant.DATA_STATUS_0);
            record.setSysCode(item.getSysCode());
            outerRoleUserMapMapper.insertSelective(record);
        });
        //+++++++++++sa_user表需要修改合并状态字段++++++++++++++++++++++++++++++++++++++++++++++++++++++
        UserDto mainUserDto = new UserDto();
        mainUserDto.setId(mainPersonDto.getUserId());
        mainUserDto.setMergeStatus("1");
        userDtoMapper.updateByPrimaryKeySelective(mainUserDto);

        UserDto mergeUserDto = new UserDto();
        mergeUserDto.setId(mergePersonDto.getUserId());
        mergeUserDto.setMergeStatus("2");
        userDtoMapper.updateByPrimaryKeySelective(mergeUserDto);
        //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

        boolean successFlag = flag && i > 0;
        if (!successFlag) {
            throw new RuntimeException("合并失败");
        }

        // 发邮件
        this.sendMergeEmail(mainUser.getMail(), mainUser.getRealName(), mainUser.getUserName(), mergeUser.getUserName());

        List<PersonInfoDto> personInfoDtos = personMergeMapper.selectPersonInfo(mainPersonDto.getUserId()); // 一些补充信息需要再查查
        PersonInfoDto personInfoDto = personInfoDtos.stream().findAny().orElseThrow(RuntimeException::new);

        PersonDto personMainDto = new PersonDto();
        personMainDto.setId(mainUser.getId());
        personMainDto.setOrgId(mainUser.getOrgNo());
        personMainDto.setPersonName(mainUser.getRealName());
        personMainDto.setUserName(mainUser.getUserName());
        Profiles profiles1 = Profiles.of("dev", "prepub-sz");
        if (!env.acceptsProfiles(profiles1)) {
            personMainDto.setTelephone(questionnaireService.deCodeAes(mainUser.getTelephone()));
        } else {
            personMainDto.setTelephone(mainUser.getTelephone());
        }
        personMainDto.setSource(personInfoDto.getSource());
        personMainDto.setRemark(mainUser.getRemark());
        personMainDto.setMail(mainUser.getMail());
        personMainDto.setUserType(mainUser.getUserType());
        personMainDto.setUserTypeCode(mainUser.getUserType());
        personMainDto.setIsMgr(personInfoDto.getOrgType());
        personMainDto.setIsAdmin("0");
        personMainDto.setIsLock("0");
        personMainDto.setLockCnt("0");
        personMainDto.setBusinessDepartmentStr(mainUser.getOrgNo());
        personMainDto.setStatus(mainUser.getStatus());
        personMainDto.setCompanyCode(personMergeMapper.getCompanyCode(mainUser.getCompanyId()));
        personMainDto.setPersonType(personInfoDto.getPersonType());
        personMainDto.setRealName(mainUser.getRealName());
        personMainDto.setJobs(mainUser.getJobs());
        personMainDto.setPostion(mainUser.getJobs());
        personMainDto.setPhone(mainUser.getTelephone());
        personMainDto.setFax(mainUser.getFax());
        personMainDto.setPhone(mainUser.getTelephone());
        personMainDto.setUserId(mainUser.getId());
        personMainDto.setCompanyId(mainUser.getCompanyId());
        personMainDto.setPosition(mainUser.getJobs());
        personMainDto.setCertificateType("SF");
        personMainDto.setCertificate(personMergeMapper.getCertificate(mainUser.getId()));

        PersonDto personMergeDto = new PersonDto();
        personMergeDto.setId(mergeUser.getId());
        personMergeDto.setOrgId(mergeUser.getOrgNo());
        personMergeDto.setPersonName(mergeUser.getRealName());
        personMergeDto.setUserName(mergeUser.getUserName());
        if (!env.acceptsProfiles(profiles1)) {
            personMergeDto.setTelephone(questionnaireService.deCodeAes(mergeUser.getTelephone()));
        } else {
            personMergeDto.setTelephone(mergeUser.getTelephone());
        }
        personMergeDto.setSource(personInfoDto.getSource());
        personMergeDto.setRemark(mergeUser.getRemark());
        personMergeDto.setMail(mergeUser.getMail());
        personMergeDto.setUserType(mergeUser.getUserType());
        personMergeDto.setUserTypeCode(mergeUser.getUserType());
        personMergeDto.setIsMgr(personInfoDto.getOrgType());
        personMergeDto.setIsAdmin("0");
        personMergeDto.setIsLock("0");
        personMergeDto.setLockCnt("0");
        personMergeDto.setBusinessDepartmentStr(mergeUser.getOrgNo());
        personMergeDto.setStatus(mergeUser.getStatus());
        personMergeDto.setCompanyCode(personMergeMapper.getCompanyCode(mergeUser.getCompanyId()));
        personMergeDto.setPersonType(personInfoDto.getPersonType());
        personMergeDto.setRealName(mergeUser.getRealName());
        personMergeDto.setJobs(mergeUser.getJobs());
        personMergeDto.setPostion(mergeUser.getJobs());
        personMergeDto.setPhone(mergeUser.getTelephone());
        personMergeDto.setFax(mergeUser.getFax());
        personMergeDto.setPhone(mergeUser.getTelephone());
        personMergeDto.setUserId(mergeUser.getId());
        personMergeDto.setCompanyId(mergeUser.getCompanyId());
        personMergeDto.setPosition(mergeUser.getJobs());
        personMergeDto.setCertificateType("SF");
        personMergeDto.setCertificate(personMergeMapper.getCertificate(mergeUser.getId()));

        // 保留合规账号为主账号,并且激励账号的证件号码不为空时,使用激励的证件号码
        if ("TDGF".equals(personMainDto.getSource()) && StringUtils.isNotEmpty(personMergeDto.getCertificate())) {
            personMergeMapper.updateCertificateById(mainUser.getId(), personMergeDto.getCertificate());
            personMainDto.setCertificate(personMergeDto.getCertificate());
        }

        // 向其他微服发送消息======================================================================================================
        String mainUserSource = personMergeMapper.getMainUserSource(mainUser.getId());
        Map<String, Object> personTopicGQJL = new HashMap<>();
        personTopicGQJL.put("userId", getUserInfo().getInfo().get("id"));
        personTopicGQJL.put("mergeState", "0");
        Map<String, Object> personTopicTDGF = new HashMap<>();
        personTopicTDGF.put("userId", getUserInfo().getInfo().get("id"));
        personTopicTDGF.put("mergeState", "0");
        if ("GQJL".equals(mainUserSource)) {
            /*
            激励为主给激励发:old=new=主账号(激励)
             */
            personTopicGQJL.put("oldUserName", mainUser.getUserName());
            personTopicGQJL.put("newUserName", mainUser.getUserName());
            personTopicGQJL.put("person", personMainDto);

             /*
            激励为主给合规发:old=被合并账号(合规),new=主账号(激励)
             */
            personTopicTDGF.put("oldUserName", mergeUser.getUserName());
            personTopicTDGF.put("newUserName", mainUser.getUserName());
            personTopicTDGF.put("person", personMainDto);
        } else if ("TDGF".equals(mainUserSource)) {
            /*
            合规为主给激励发:old=被合并账号(激励),new=主账号(合规)
             */
            personTopicGQJL.put("oldUserName", mergeUser.getUserName());
            personTopicGQJL.put("newUserName", mainUser.getUserName());
            personTopicGQJL.put("person", personMainDto);
            /*
            合规为主给合规发:old=new=主账号(合规)
             */
            personTopicTDGF.put("oldUserName", mainUser.getUserName());
            personTopicTDGF.put("newUserName", mainUser.getUserName());
            personTopicTDGF.put("person", personMainDto);
        }
        try {
            String uidGQJL = UUID.randomUUID().toString();
            String uidTDGF = UUID.randomUUID().toString();
            MQMsgDto<Map<String, Object>> sendGQJL = new MQMsgDto<>();
            sendGQJL.setUid(uidGQJL);
            sendGQJL.setSystemType("GQJL");
            sendGQJL.setBusinessType(TopicConstant.BUS_TYPE_USER);
            sendGQJL.setOperationType(TopicConstant.OP_TYPE_MERGE);
            sendGQJL.setInfo(personTopicGQJL);
            saSystemUtil.insertMqMessage(sendGQJL);
            topicProducer.sendTopic(personTopicGQJL, "GQJL", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_MERGE, uidGQJL);
            MQMsgDto<Map<String, Object>> sendTDGF = new MQMsgDto<>();
            sendTDGF.setUid(uidTDGF);
            sendTDGF.setSystemType("TDGF");
            sendTDGF.setBusinessType(TopicConstant.BUS_TYPE_USER);
            sendTDGF.setOperationType(TopicConstant.OP_TYPE_MERGE);
            sendTDGF.setInfo(personTopicTDGF);
            saSystemUtil.insertMqMessage(sendTDGF);
            topicProducer.sendTopic(personTopicTDGF, "TDGF", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_MERGE, uidTDGF);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(userList),
                            null, "用户合并",
                            "系统管理/外部人员", "PersonMergeService.mergePerson",
                            "主账号:{" + mainUser.getUserName() + "},被合并账号:{" + mergeUser.getUserName() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return successFlag;
    }

    private void settingsUserRoles(String userId, List<String> roles) {
        UserRoleMapExample example = new UserRoleMapExample();
        example.createCriteria().andUserIdEqualTo(userId);
        userRoleMapMapper.deleteByExample(example);
        roles.forEach((roleId) -> {
            UserRoleMap map = new UserRoleMap();
            map.setRoleId(roleId);
            map.setUserId(userId);
            userRoleMapMapper.insertSelective(map);
        });
    }

    /**
     * 解除合并
     *
     * @param userName 被合并的人的userName
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelMerge(String historyId, String userName) {

        if (StringUtils.isEmpty(historyId)) {
            throw new IllegalArgumentException("参数错误");
        }

        // 查合并历史
        SaUserMergeHistory saUserMergeHistory = saUserMergeHistoryMapper.selectByPrimaryKey(historyId);

        UserDto mainUser = userDtoMapper.selectByPrimaryKey(saUserMergeHistory.getUserId());
        UserDto mergeUser = userDtoMapper.selectByPrimaryKey(saUserMergeHistory.getSubsidiaryUserId());

        // 被合并的人解锁
        boolean flag = personService.updateUserLockStatus(userName, "0", "0");
        personMapper.updateUserStatus(saUserMergeHistory.getSubsidiaryUserId(), "1");

        // 合并的人 子系统角色恢复--只是把不属于他的子系统角色删掉就可以了 , 找自己的来源，和来源不符的删除
        PersonWithBLOBs personWithBLOBs = personMapper.selectByPrimaryKey(saUserMergeHistory.getUserId());
        String source = personWithBLOBs.getSource();
        OuterRoleUserMapExample roleUserMapExample = new OuterRoleUserMapExample();
        roleUserMapExample.createCriteria()
                .andUserIdEqualTo(saUserMergeHistory.getUserId())
                .andSysCodeNotEqualTo(source);

        outerRoleUserMapMapper.deleteByExample(roleUserMapExample);

        // 两个人平台角色恢复
        this.settingsUserRoles(saUserMergeHistory.getUserId()
                , Arrays.stream(saUserMergeHistory.getMainUserMargeRoleId().split(",")).collect(Collectors.toList()));

        this.settingsUserRoles(saUserMergeHistory.getSubsidiaryUserId()
                , Arrays.stream(saUserMergeHistory.getMergedRoleId().split(",")).collect(Collectors.toList()));

        //+++++++++++sa_user表需要修改合并状态字段++++++++++++++++++++++++++++++++++++++++++++++++++++++
        UserDto mainUserDto = new UserDto();
        mainUserDto.setId(saUserMergeHistory.getUserId());
        mainUserDto.setMergeStatus("");
        userDtoMapper.updateByPrimaryKeySelective(mainUserDto);

        UserDto mergeUserDto = new UserDto();
        mergeUserDto.setId(saUserMergeHistory.getSubsidiaryUserId());
        mergeUserDto.setMergeStatus("");
        userDtoMapper.updateByPrimaryKeySelective(mergeUserDto);
        //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

        // 合并历史表 数据删除
        int i = saUserMergeHistoryMapper.deleteByPrimaryKey(historyId);

        boolean successFlag = i > 0 && flag;
        if (!successFlag) {
            throw new RuntimeException("解除合并失败");
        }

        // 发email
        sendCancelMergeEmail(mainUser.getMail(), mainUser.getRealName(), mainUser.getUserName(), mergeUser.getUserName());

        // 向其他微服发送消息========注意 这里面的user是被合并的用户,发消息时两个人反过来了===================================================================
        List<PersonInfoDto> personInfoDtos = personMergeMapper.selectPersonInfo(mergeUser.getId()); // 一些补充信息需要再查查
        PersonInfoDto personInfoDto = personInfoDtos.stream().findAny().orElseThrow(RuntimeException::new);

        PersonDto personMainDto = new PersonDto();
        personMainDto.setId(mainUser.getId());
        personMainDto.setOrgId(mainUser.getOrgNo());
        personMainDto.setPersonName(mainUser.getRealName());
        personMainDto.setUserName(mainUser.getUserName());
        Profiles profiles1 = Profiles.of("dev", "prepub-sz");
        if (!env.acceptsProfiles(profiles1)) {
            personMainDto.setTelephone(questionnaireService.deCodeAes(mainUser.getTelephone()));
        } else {
            personMainDto.setTelephone(mainUser.getTelephone());
        }
        personMainDto.setSource(personInfoDto.getSource());
        personMainDto.setRemark(mainUser.getRemark());
        personMainDto.setMail(mainUser.getMail());
        personMainDto.setUserType(mainUser.getUserType());
        personMainDto.setUserTypeCode(mainUser.getUserType());
        personMainDto.setIsMgr(personInfoDto.getOrgType());
        personMainDto.setIsAdmin("0");
        personMainDto.setIsLock("0");
        personMainDto.setLockCnt("0");
        personMainDto.setBusinessDepartmentStr(mainUser.getOrgNo());
        personMainDto.setStatus(mainUser.getStatus());
        personMainDto.setCompanyCode(personMergeMapper.getCompanyCode(mainUser.getCompanyId()));
        personMainDto.setPersonType(personInfoDto.getPersonType());
        personMainDto.setRealName(mainUser.getRealName());
        personMainDto.setJobs(mainUser.getJobs());
        personMainDto.setPostion(mainUser.getJobs());
        personMainDto.setPhone(mainUser.getTelephone());
        personMainDto.setFax(mainUser.getFax());
        personMainDto.setPhone(mainUser.getTelephone());
        personMainDto.setUserId(mainUser.getId());
        personMainDto.setCompanyId(mainUser.getCompanyId());
        personMainDto.setPosition(mainUser.getJobs());
        personMainDto.setCertificateType("SF");
        personMainDto.setCertificate(personMergeMapper.getCertificate(mainUser.getId()));

        PersonDto personMergeDto = new PersonDto();
        personMergeDto.setId(mergeUser.getId());
        personMergeDto.setOrgId(mergeUser.getOrgNo());
        personMergeDto.setPersonName(mergeUser.getRealName());
        personMergeDto.setUserName(mergeUser.getUserName());
        if (!env.acceptsProfiles(profiles1)) {
            personMergeDto.setTelephone(questionnaireService.deCodeAes(mergeUser.getTelephone()));
        } else {
            personMergeDto.setTelephone(mergeUser.getTelephone());
        }
        personMergeDto.setSource(personInfoDto.getSource());
        personMergeDto.setRemark(mergeUser.getRemark());
        personMergeDto.setMail(mergeUser.getMail());
        personMergeDto.setUserType(mergeUser.getUserType());
        personMergeDto.setUserTypeCode(mergeUser.getUserType());
        personMergeDto.setIsMgr(personInfoDto.getOrgType());
        personMergeDto.setIsAdmin("0");
        personMergeDto.setIsLock("0");
        personMergeDto.setLockCnt("0");
        personMergeDto.setBusinessDepartmentStr(mergeUser.getOrgNo());
        personMergeDto.setStatus(mergeUser.getStatus());
        personMergeDto.setCompanyCode(personMergeMapper.getCompanyCode(mergeUser.getCompanyId()));
        personMergeDto.setPersonType(personInfoDto.getPersonType());
        personMergeDto.setRealName(mergeUser.getRealName());
        personMergeDto.setJobs(mergeUser.getJobs());
        personMergeDto.setPostion(mergeUser.getJobs());
        personMergeDto.setPhone(mergeUser.getTelephone());
        personMergeDto.setFax(mergeUser.getFax());
        personMergeDto.setPhone(mergeUser.getTelephone());
        personMergeDto.setUserId(mergeUser.getId());
        personMergeDto.setCompanyId(mergeUser.getCompanyId());
        personMergeDto.setPosition(mergeUser.getJobs());
        personMergeDto.setCertificateType("SF");
        personMergeDto.setCertificate(personMergeMapper.getCertificate(mergeUser.getId()));

        String mainUserSource = personMergeMapper.getMainUserSource(mainUser.getId());
        Map<String, Object> personTopicGQJL = new HashMap<>();
        personTopicGQJL.put("userId", getUserInfo().getInfo().get("id"));
        personTopicGQJL.put("mergeState", "1");
        Map<String, Object> personTopicTDGF = new HashMap<>();
        personTopicTDGF.put("userId", getUserInfo().getInfo().get("id"));
        personTopicTDGF.put("mergeState", "1");
        if ("GQJL".equals(mainUserSource)) {
            /*
            激励为主给激励发:old=new=主账号(激励)
             */
            personTopicGQJL.put("oldUserName", mainUser.getUserName());
            personTopicGQJL.put("newUserName", mainUser.getUserName());
            personTopicGQJL.put("person", personMainDto);
            /*
            激励为主给合规发:old=主账号(激励),new=被合并账号(合规)
             */
            personTopicTDGF.put("oldUserName", mainUser.getUserName());
            personTopicTDGF.put("newUserName", mergeUser.getUserName());
            personTopicTDGF.put("person", personMergeDto);
        } else if ("TDGF".equals(mainUserSource)) {
            /*
            合规为主给激励发:old=主账号(合规),new=被合并账号(激励)
             */
            personTopicGQJL.put("oldUserName", mainUser.getUserName());
            personTopicGQJL.put("newUserName", mergeUser.getUserName());
            personTopicGQJL.put("person", personMergeDto);
            /*
            合规为主给合规发:old=new=主账号(合规)
             */
            personTopicTDGF.put("oldUserName", mainUser.getUserName());
            personTopicTDGF.put("newUserName", mainUser.getUserName());
            personTopicTDGF.put("person", personMainDto);
        }

        try {
            String uidGQJL = UUID.randomUUID().toString();
            String uidTDGF = UUID.randomUUID().toString();
            MQMsgDto<Map<String, Object>> sendGQJL = new MQMsgDto<>();
            sendGQJL.setUid(uidGQJL);
            sendGQJL.setSystemType("GQJL");
            sendGQJL.setBusinessType(TopicConstant.BUS_TYPE_USER);
            sendGQJL.setOperationType(TopicConstant.OP_TYPE_MERGE);
            sendGQJL.setInfo(personTopicGQJL);
            saSystemUtil.insertMqMessage(sendGQJL);
            topicProducer.sendTopic(personTopicGQJL, "GQJL", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_MERGE, uidGQJL);
            MQMsgDto<Map<String, Object>> sendTDGF = new MQMsgDto<>();
            sendTDGF.setUid(uidTDGF);
            sendTDGF.setSystemType("TDGF");
            sendTDGF.setBusinessType(TopicConstant.BUS_TYPE_USER);
            sendTDGF.setOperationType(TopicConstant.OP_TYPE_MERGE);
            sendTDGF.setInfo(personTopicTDGF);
            saSystemUtil.insertMqMessage(sendTDGF);
            topicProducer.sendTopic(personTopicTDGF, "TDGF", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_MERGE, uidTDGF);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(userName),
                            null, "用户解除合并",
                            "系统管理/外部人员", "PersonMergeService.cancelMerge",
                            "主账号:{" + mainUser.getUserName() + "},被合并账号:{" + mergeUser.getUserName() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return successFlag;
    }

    /**
     * 发送合并email
     */
    private void sendMergeEmail(String email, String mainUserNameStr, String saveUserName, String mergeUserName) {

        Profiles profiles = Profiles.of("dev", "prepub-sz");
        if (env.acceptsProfiles(profiles)) { // 测试环境返回
            return;
        }

        List<String> destList = Collections.singletonList(email);

        String messageContent = "上市公司及股东一体化服务平台用户合并通知";

        String content = "尊敬的" + mainUserNameStr + "用户，您好，您的上市公司及股东一体化服务平台其中两个账号已经合并，保留的用户名为"
                + saveUserName + "，被合并的用户名为" + mergeUserName + "，登录密码为保留用户名对应密码。【上市公司及股东一体化服务平台】";

        cscSignaturePersonManager.pushSendMessage(destList, messageContent, content, 101);
    }

    /**
     * 发送解除合并email
     */
    private void sendCancelMergeEmail(String email, String mainUserNameStr, String saveUserName, String mergeUserName) {

        Profiles profiles = Profiles.of("dev", "prepub-sz");
        if (env.acceptsProfiles(profiles)) { // 本地环境返回
            return;
        }

        List<String> destList = Collections.singletonList(email);

        String messageContent = "上市公司及股东一体化服务平台用户解除合并通知";

        String content = "尊敬的" + mainUserNameStr + "用户，您好，您的上市公司及股东一体化服务平台其中两个账号已经解除合并，原用户名为"
                + saveUserName + "，解除锁定的用户名为" + mergeUserName + "，登录密码为各用户名原对应密码。【上市公司及股东一体化服务平台】";

        cscSignaturePersonManager.pushSendMessage(destList, messageContent, content, 101);
    }
}
