package com.stock.service.platform.person.controller;

import com.alibaba.excel.EasyExcel;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.RegTreeDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.service.InfoSyncService;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.mqManage.service.MQManagerService;
import com.stock.service.platform.person.dto.*;
import com.stock.service.platform.person.service.IdentityService;
import com.stock.service.platform.person.service.OrgService;
import com.stock.service.platform.person.service.PersonMergeService;
import com.stock.service.platform.person.service.PersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 人员controller
 */
@Api(value = "人员管理", description = "人员信息相关api")
@RestController
@RequestMapping("person")
public class PersonController extends BaseController {

    @Resource
    PersonService personService;

    @Resource
    IdentityService identityService;

    @Resource
    SaCompanyMapper saCompanyMapper;

    @Resource
    CommonBizMapper commonBizMapper;

    @Resource
    OrgService orgService;

    @Resource
    CommonService commonService;

    @Resource
    QuestionnaireService questionnaireService;

    @Resource
    private InfoSyncService infoSyncService;

    @Resource
    private PersonMergeService personMergeService;

    @Resource
    private MQManagerService mqManagerService;

    @Resource
    private Environment env;

    @Resource
    private SaUserCompanyMapMapper saUserCompanyMapMapper;

    @Resource
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    @Resource
    private UserRoleMapMapper userRoleMapMapper;

    @Resource
    private UserDtoMapper userDtoMapper;

    @Resource
    private PersonMapper personMapper;

    @Resource
    private CompanyDtoMapper companyDtoMapper;

    @ApiOperation(value = "查询人员列表", notes = "查询人员列表")
    @PostMapping("/queryPersonList")
    public Map<String, Object> listPersonInfo(@RequestBody PersonDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        //账户所属机构
        String orgType = (String)getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String isAdmin = (String) getUserInfo().getInfo().get("isAdmin");
        String orgList = orgService.selectOrgLimitList(orgId,orgType,isAdmin);
        dto.setOrgLimitList(orgList);
        Map<String, Object> info = super.commonQuery("com.stock.service.platform.person.dao.PersonBizMapper.listPersonInfo",dto);
        List<PersonDto> tableData = (List<PersonDto>)info.get("data");
        tableData.forEach(item -> {
            Profiles profiles1 = Profiles.of("dev", "prepub-sz");
            if (!env.acceptsProfiles(profiles1)) {
                item.setTelephone(questionnaireService.deCodeAes(item.getTelephone()));
            } else {
                item.setTelephone(item.getTelephone());
            }
            item.setOrgIdList(Arrays.asList(item.getOrgId().split(",")));
        });
        resultMap.put("tableData", tableData);
        resultMap.put("total", info.get("recordsTotal"));
        return resultMap;
    }

    /**
     * 创建人员信息
     *
     * @param personDto
     * @return
     */
    @ApiOperation(value = "创建人员信息", notes = "创建人员信息")
    @PostMapping("createPersonInfo")
    public JsonResponse<Boolean> createPersonInfo(@Validated(PersonDto.InsertPersonGroup.class) @RequestBody PersonDto personDto) throws IOException, IllegalAccessException {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        personDto.setId(null);
        jsonResponse.setResult(personService.editPersonInfo(personDto));
        if (jsonResponse.getResult() && StringUtils.isNotEmpty(personDto.getChildrenUserName()) && StringUtils.isNotEmpty(personDto.getChildrenRole()) && StringUtils.isNotEmpty(personDto.getChildrenOrg())) {
            // 创建成功后，更新子账号信息
            String personId = personService.getPersonId(personDto);
            personDto.setId(personId);
            personDto.setChildrenUserName(personDto.getChildrenUserName());
            personService.updateChildrenInfo(personDto);
        }
        return jsonResponse;
    }

    /**
     * 编辑用户信息
     *
     * @param personDto
     * @return
     */
    @ApiOperation(value = "修改人员信息", notes = "修改人员信息")
    @PostMapping("updatePersonInfo")
    public JsonResponse<Boolean> updatePersonInfo(@Validated(PersonDto.UpdatePersonGroup.class) @RequestBody PersonDto personDto) throws IOException, IllegalAccessException {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(personService.editPersonInfo(personDto));
        return jsonResponse;
    }

    /**
     * 获取身份类型列表
     *
     * @return
     */
    @ApiOperation(value = "查询身份类型列表", notes = "查询身份类型列表")
    @PostMapping("queryIdentityTypeList")
    public JsonResponse<List<IdentityDto>> getIdentityTypeList() {
        JsonResponse<List<IdentityDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(identityService.listIdentityDto());
        return jsonResponse;
    }

    /**
     * 查询机构的列表
     *
     * @return
     */
    @ApiOperation(value = "查询机构列表", notes = "查询机构列表")
    @PostMapping("queryOrgList")
    public JsonResponse<List<OrgDto>> getOrgList() {
        //账户所属机构
        String orgType = (String)getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String isAdmin = (String) getUserInfo().getInfo().get("isAdmin");
        String orgList = orgService.selectOrgLimitList(orgId,orgType,isAdmin);

        JsonResponse<List<OrgDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(orgService.listOrgDto(orgList,orgType));
        return jsonResponse;
    }

    /**
     * 查询内部/外部机构列表
     *
     * @return
     */
    @ApiOperation(value = "查询内部/外部机构列表", notes = "查询内部/外部机构列表")
    @PostMapping("getOrgListByType")
    public JsonResponse<List<OrgDto>> getOrgListByType(@RequestBody PersonDto dto) {
        //账户所属机构
        String orgType = (String)getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String isAdmin = (String) getUserInfo().getInfo().get("isAdmin");
        String orgList = orgService.selectOrgLimitList(orgId,orgType,isAdmin);

        JsonResponse<List<OrgDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(orgService.listOrgDtoByUserType(orgList,orgType,dto.getUserType()));
        return jsonResponse;
    }

    /**
     * 查询内部/外部机构列表
     *
     * @return
     */
    @ApiOperation(value = "查询营业部", notes = "查询营业部")
    @PostMapping("_getOrgListOnlyThree")
    public JsonResponse<List<OrgDto>> _getOrgListOnlyThree() {
        //账户所属机构
        String orgType = (String)getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String isAdmin = (String) getUserInfo().getInfo().get("isAdmin");
        String orgList = orgService.selectOrgLimitList(orgId,orgType,isAdmin);

        JsonResponse<List<OrgDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(orgService.listOrgDtoByUserTypeOnlyThree(orgList,orgType));
        return jsonResponse;
    }

    /**
     * 更新用户可用状态
     *
     * @return
     */
    @ApiOperation(value = "更新用户可用状态", notes = "更新用户可用状态")
    @PostMapping("updateUserAvailableStatus")
    public JsonResponse<Boolean> updateUserAvailableStatus(@Validated(PersonDto.UpdateUserAvailableStatusGroup.class) @RequestBody PersonDto personDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        boolean flag = personService.updateUserAvailableStatus(personDto.getUserName(), personDto.getUserAvailableStatus());
        jsonResponse.setResult(flag);
        return jsonResponse;
    }


    /**
     * 更新用户锁定状态
     *
     * @return
     */
    @ApiOperation(value = "更新用户锁定状态", notes = "更新用户锁定状态")
    @PostMapping("updateUserLockStatus")
    public JsonResponse<Boolean> updateUserLockStatus(@Validated(PersonDto.UpdateUserLockStatusGroup.class) @RequestBody PersonDto personDto) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        boolean flag = personService.updateUserLockStatus(personDto.getUserName(), personDto.getUserLockStatus(), "1");
        jsonResponse.setResult(flag);
        return jsonResponse;
    }

    /**
     * 根据id获取用户信息
     *
     * @param personDto
     * @return
     */
    @ApiOperation(value = "根据用户id查询用户信息", notes = "根据用户id查询用户信息")
    @PostMapping("queryPersonInfoById")
    public JsonResponse<PersonDto> queryPersonInfoById(@Validated(PersonDto.QueryPersonInfoGroup.class) @RequestBody PersonDto personDto) {
        JsonResponse<PersonDto> response = new JsonResponse<>();
        response.setResult(personService.getPersonInfoByIdSeriakey(personDto.getId(),personDto.getSeriakey()));
        return response;
    }

    /**
     * 删除人员
     *
     * @param personDto
     * @return
     */
    @ApiOperation(value = "根据人员id删除人员", notes = "根据人员id删除人员")
    @PostMapping("deletePersonByIds")
    public JsonResponse<Boolean> deletePersonByIds(@Validated(PersonDto.DeletePersonInfoGroup.class) @RequestBody PersonDto personDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(personService.deletePersonByIds(personDto.getPersonIds(),personDto.getPersonIdSeriakeys()));
        return response;
    }

    ///**
    // * 查询是否存在手机号
    // *
    // * @param personDto
    // * @return
    // */
    //@ApiOperation(value = "是否有手机号", notes = "是否有手机号")
    //@PostMapping("hasTelephone")
    //public JsonResponse<Boolean> hasTelephone(@Validated(PersonDto.SearchTelephoneGroup.class) @RequestBody PersonDto personDto) {
    //    JsonResponse<Boolean> response = new JsonResponse<>();
    //    response.setResult(personService.hasTelephone(personDto.getTelephone(), personDto.getId()));
    //    return response;
    //}

    ///**
    // * 查询是否存在邮箱
    // *
    // * @param personDto
    // * @return
    // */
    //@ApiOperation(value = "是否有邮箱", notes = "是否有邮箱")
    //@PostMapping("hasMail")
    //public JsonResponse<Boolean> hasMail(@Validated(PersonDto.SearchMailGroup.class) @RequestBody PersonDto personDto) {
    //    JsonResponse<Boolean> response = new JsonResponse<>();
    //    response.setResult(personService.hasMail(personDto.getMail(), personDto.getId()));
    //    return response;
    //}

    /**
     * 查询是否存在邮箱
     *
     * @return
     */
    @ApiOperation(value = "同步人员信息", notes = "同步人员信息")
    @PostMapping("syncPersonInfo")
    public JsonResponse<Boolean> syncPersonInfo() {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(true);
        return response;
    }

    @ApiOperation(value = "获取归属机构下拉列表", notes = "获取归属机构下拉列表")
    @PostMapping("getOrgSelection")
    public JsonResponse<Object> getOrgSelection(@RequestBody String params) {
        JsonResponse<Object> response = new JsonResponse<>();
        response.setResult(personService.getOrgSelection(params));
        return response;
    }

    /**
     * 导出用户信息
     * @return
     */
    @PostMapping(value = "exportUserInfo")
    @ResponseBody
    public void exportUserInfo(@RequestBody PersonDto personDto, HttpServletResponse response){
        String orgType = (String)getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String isAdmin = (String) getUserInfo().getInfo().get("isAdmin");
        String orgList = orgService.selectOrgLimitList(orgId,orgType,isAdmin);
        personDto.setOrgLimitList(orgList);
        List<ExcelUserManage> excelList = personService.exportUserInfo(personDto);

        String fileName = "用户信息";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            response.setHeader("fileName", java.net.URLEncoder.encode(fileName + ".xls", "utf-8"));
            OutputStream out = response.getOutputStream();
            EasyExcel.write(out, ExcelUserManage.class).sheet("用户信息").doWrite(excelList);
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 获取员工管理下拉
     * @return
     */
    @PostMapping(value = "getStaffSelectData")
    @ResponseBody
    public JsonResponse<Map<String, Object>> getStaffSelectData() throws IllegalAccessException {
        Map<String, Object> selectDataMap = new HashMap<>();
        selectDataMap.put("yesOrNoDataList",personService.getSelectData("YES_OR_NO"));
        //证件类型
        selectDataMap.put("cardTypeDataList",personService.getSelectData("CARD_TYPE"));
        //工作状态
        selectDataMap.put("workStateDataList",personService.getSelectData("EQU_WORK_STATE"));
        //职位类型
        selectDataMap.put("postTypeDataList",personService.getSelectData("POST_TYPE"));

        //拟上市板块
        List<RegTreeDto> plateTreeTag = commonBizMapper.getSaCodeByNo("CASE_BELONGS_PLATE");
        selectDataMap.put("stockMarketDataList", commonService.sortSelectList(plateTreeTag));

        //公司性质
        List<RegTreeDto> companyNatureList = commonBizMapper.getSaCodeByCode("REP_COMPANY_NATURE");
        selectDataMap.put("companyNatureList", commonService.sortSelectList(companyNatureList));

        String companyCode = (String) getUserInfo().getInfo().get("companyCode");
        SaCompanyDto companyDto = saCompanyMapper.getCompanyByCode1(companyCode);
        StripTrailingZerosUtil.convertBigDecimalsToPlainString(companyDto);
        String stockMarket = "";
        if(companyDto != null){
            stockMarket = companyDto.getMarketStockType();
        }
        //当前账号的证券市场
        selectDataMap.put("stockMarketData",stockMarket);

        String company = (String) getUserInfo().getInfo().get("companyZhName");
        if(company == null){
            company = "暂无公司";
        }
        selectDataMap.put("companyName",company);
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(selectDataMap);
        return response;
    }

    @ApiOperation(value = "查询人员合并历史", notes = "查询人员合并历史")
    @PostMapping("/queryTableDate")
    public Map<String, Object> queryTableDate(@RequestBody PersonDto dto) {
        Map<String, Object> resultMap = new HashMap<>();
        List<UserMergeHistoryDto> tableData = personService.queryTableDate(dto);
        resultMap.put("tableData", tableData);
        return resultMap;
    }

    /**
     * 同步内部用户，上线后废弃
     *
     * @param files 文件
     */
    @PostMapping("/importInnerUserInfo")
    public JsonResponse<Integer> importInnerUserInfo(@RequestParam("files") List<MultipartFile> files) {
        JsonResponse<Integer> jsonResponse = new JsonResponse<>();
        int result;
        UserInfo userInfo = getUserInfo();
        try {
            mqManagerService.setButtonStatus("person","导入内部用户","0",userInfo.getPersonName());
            result = infoSyncService.importInnerUserInfo(files);
            jsonResponse.setSuccess(true);
            jsonResponse.setResult(result);
        } catch (Exception e) {
            UserDtoExample userDtoExample = new UserDtoExample();
            userDtoExample.createCriteria().andUserTypeEqualTo("0").andIdNotEqualTo("1");
            List<UserDto> userDtos = userDtoMapper.selectByExample(userDtoExample);
            List<String> deleteIdList = userDtos.stream().map(UserDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteIdList)) {
                // 清理user表
                UserDtoExample userDtoExample1 = new UserDtoExample();
                userDtoExample1.createCriteria().andIdIn(deleteIdList);
                userDtoMapper.deleteByExample(userDtoExample1);
                // 清理person表
                PersonExample personExample = new PersonExample();
                personExample.createCriteria().andIdIn(deleteIdList);
                personMapper.deleteByExample(personExample);
                // 清理outerRoleUserMap表
                OuterRoleUserMapExample roleUserMapExample = new OuterRoleUserMapExample();
                roleUserMapExample.createCriteria().andUserIdIn(deleteIdList);
                outerRoleUserMapMapper.deleteByExample(roleUserMapExample);
                // 清理userRoleMap表
                UserRoleMapExample userRoleMapExample = new UserRoleMapExample();
                userRoleMapExample.createCriteria().andUserIdIn(deleteIdList);
                userRoleMapMapper.deleteByExample(userRoleMapExample);
                // 清理saUserCompanyMap表
                SaUserCompanyMapExample saUserCompanyMapExample = new SaUserCompanyMapExample();
                saUserCompanyMapExample.createCriteria().andUserIdIn(deleteIdList);
                saUserCompanyMapMapper.deleteByExample(saUserCompanyMapExample);
            }
            mqManagerService.setButtonStatus("person","导入内部用户","1",userInfo.getPersonName());
            jsonResponse.setSuccess(false);
            jsonResponse.setErrorMsg(e.getMessage());
        }
        return jsonResponse;
    }

    /**
     * 同步外部用户，上线后废弃
     *
     * @param files 文件
     */
    @PostMapping("/importOuterUserInfo")
    public JsonResponse<Integer> importOuterUserInfo(@RequestParam("files") List<MultipartFile> files) {
        JsonResponse<Integer> jsonResponse = new JsonResponse<>();
        int result;
        UserInfo userInfo = getUserInfo();
        try {
            mqManagerService.setButtonStatus("person","导入外部用户","0",userInfo.getPersonName());
            result = infoSyncService.importOuterUserInfo(files);
            jsonResponse.setResult(result);
        } catch (Exception e) {
            UserDtoExample userDtoExample = new UserDtoExample();
            userDtoExample.createCriteria().andUserTypeEqualTo("1").andIdNotEqualTo("1");
            List<UserDto> userDtos = userDtoMapper.selectByExample(userDtoExample);
            List<String> deleteIdList = userDtos.stream().map(UserDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteIdList)) {
                UserDtoExample userDtoExample1 = new UserDtoExample();
                userDtoExample1.createCriteria().andIdIn(deleteIdList);
                userDtoMapper.deleteByExample(userDtoExample1);
                PersonExample personExample = new PersonExample();
                personExample.createCriteria().andIdIn(deleteIdList);
                personMapper.deleteByExample(personExample);
                OuterRoleUserMapExample roleUserMapExample = new OuterRoleUserMapExample();
                roleUserMapExample.createCriteria().andUserIdIn(deleteIdList);
                outerRoleUserMapMapper.deleteByExample(roleUserMapExample);
                UserRoleMapExample userRoleMapExample = new UserRoleMapExample();
                userRoleMapExample.createCriteria().andUserIdIn(deleteIdList);
                userRoleMapMapper.deleteByExample(userRoleMapExample);
                SaUserCompanyMapExample saUserCompanyMapExample = new SaUserCompanyMapExample();
                saUserCompanyMapExample.createCriteria().andUserIdIn(deleteIdList);
                saUserCompanyMapMapper.deleteByExample(saUserCompanyMapExample);
                // 清空sa_company表的system_state字段
                companyDtoMapper.resettingSystemState();
            }
            mqManagerService.setButtonStatus("person","导入外部用户","1",userInfo.getPersonName());
            jsonResponse.setSuccess(false);
            jsonResponse.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    @ApiOperation(value = "重置外部人员密码", notes = "重置外部人员密码")
    @PostMapping("/resetPersonPassword")
    public JsonResponse<Boolean> resetPersonPassword() {
        JsonResponse<Boolean> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        boolean result;
        try {
            mqManagerService.setButtonStatus("person","邮件通知(账号、密码、登录地址)","0",userInfo.getPersonName());
            result = personService.resetPersonPassword();
            response.setResult(result);
        } catch (Exception e) {
            mqManagerService.setButtonStatus("person","邮件通知(账号、密码、登录地址)","1",userInfo.getPersonName());
            response.setSuccess(false);
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }

    /**
     * 验证是否有其他子系统角色
     *
     * @param userList 两个用户的id
     */
    @PostMapping("/haveOtherSystemRole")
    public JsonResponse<Boolean> haveOtherSystemRole(@RequestBody List<SystemRoleVerify> userList) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();

        boolean b = personMergeService.haveOtherSystemRole(userList);
        jsonResponse.setResult(b);
        return jsonResponse;
    }

    /**
     * 合并用户
     *
     * @param userList 两个用户
     */
    @PostMapping("/mergePerson")
    public JsonResponse<Boolean> mergePerson(@RequestBody List<MergePersonDto> userList) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        boolean b = personMergeService.mergePerson(userList);
        jsonResponse.setResult(b);
        return jsonResponse;
    }

    /**
     * 解除合并
     */
    @PostMapping("/cancelMerge")
    public JsonResponse<Boolean> cancelMerge(@RequestParam String historyId ,@RequestParam String userName) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        boolean b = personMergeService.cancelMerge(historyId , userName);
        jsonResponse.setResult(b);
        return jsonResponse;
    }

    @ApiOperation(value = "删除子账号信息", notes = "删除子账号信息")
    @PostMapping("/deleteChildrenInfo")
    public JsonResponse<Boolean> deleteChildrenInfo(@RequestBody PersonDto personDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(personService.deleteChildrenInfo(personDto));
        return response;
    }

    @ApiOperation(value = "获取子账号信息", notes = "获取子账号信息")
    @PostMapping("/getChildrenInfo")
    public JsonResponse<PersonDto> getChildrenInfo(@RequestBody PersonDto personDto) {
        JsonResponse<PersonDto> response = new JsonResponse<>();
        response.setResult(personService.getChildrenInfo(personDto));
        return response;
    }


}
