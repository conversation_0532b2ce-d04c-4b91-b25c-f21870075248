package com.stock.service.platform.person.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stock.core.dto.TreeDto;
import com.stock.core.exception.ApplicationException;
import com.stock.core.service.BaseService;
import com.stock.core.util.BeanUtil;
import com.stock.core.util.TreeBuilderUtil;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.CompanyConstant;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.dto.SelectTreeTagDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.manager.CscSignaturePersonManager;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.MessageService;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.sysCodeConvert.CodeConvert;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.PasswordUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.person.dao.PersonBizMapper;
import com.stock.service.platform.person.dto.ExcelUserManage;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.person.dto.UserMergeHistoryDto;
import com.stock.service.platform.user.dao.UserBizMapper;
import com.stock.service.platform.user.dto.UserDto;
import com.stock.service.platform.user.service.PermissionService;
import com.stock.service.platform.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 人员业务类
 */
@Slf4j
@Service
public class PersonService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(PermissionService.class);

    @Resource
    private PersonMapper personMapper;

    @Resource
    private CompanyDtoMapper companyDtoMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private UserService userService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private SaCompanyMapper saCompanyMapper;

    @Autowired
    private PersonBizMapper personBizMapper;

    @Autowired
    private SaSystemUtil saSystemUtil;

    @Autowired
    private TopicProducer topicProducer;

    @Resource
    private CompanyContactMapper companyContactMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private Environment env;

    @Autowired
    private MessageService messageService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Resource
    private QuestionnaireService questionnaireService;

    @Resource
    private CscSignaturePersonManager cscSignaturePersonManager;

    @Resource
    private UserBizMapper userBizMapper;

    @Resource
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    @Value("${resetPassword.loginUrl}")
    private String loginUrl;

    @Resource
    private LogService logService;

    @Resource
    private UserRoleMapMapper userRoleMapMapper;

    @Resource
    private CommonService commonService;


    /**
     * 编辑用户信息（新增/修改）
     *
     * @param personDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean editPersonInfo(PersonDto personDto) throws IOException, IllegalAccessException {
        //判断提交表单是否被篡改
        if (!StringUtils.isBlank(personDto.getId())) {
            Map<String, String> checkParam = new HashMap<>();
            checkParam.put("id", personDto.getId());
            checkParam.put("seriakey", personDto.getSeriakey());
            int checkFlag = userMapper.selectByIdSeriakey(checkParam);
            if (checkFlag == 0) {
                throw new ApplicationException("error.000102");
            }
        }
        if ("0".equals(personDto.getUserType()) && !"wx_houwc".equals(personDto.getUserName())) {
            personDto.setUserName("CSC" + personDto.getUserName());
        }
        if(StringUtils.isNotEmpty(personDto.getChildrenUserName()) && !personDto.getChildrenUserName().contains("YCSC")){
            personDto.setChildrenUserName("YCSC" + personDto.getChildrenUserName());
        }
        // 更新或修改
        insertOrUpdatePersonInfo(personDto);
        return true;
    }

    /**
     * 更新用户可用状态
     *
     * @param userName
     * @param status
     * @return
     */
    public boolean updateUserAvailableStatus(String userName, String status) {
        // 判断状态
        if (Objects.equals(status, CodeTableConstant.USER_AVAILABLE) || Objects.equals(status, CodeTableConstant.USER_UNAVAILABLE)) {
            UserExample userExample = new UserExample();
            userExample.createCriteria().andUserNameEqualTo(userName);
            User user = new User();
            user.setStatus(status);
            return userMapper.updateByExampleSelective(user, userExample) > 0;
        }
        return false;
    }

    /**
     * 更新用户锁定状态
     *
     * @param userName
     * @param status
     * @return
     */
    public boolean updateUserLockStatus(String userName, String status, String type) {
        // 判断状态
        if (Objects.equals(status, CodeTableConstant.USER_LOCK) || Objects.equals(status, CodeTableConstant.USER_UNLOCK)) {
            UserExample userExample = new UserExample();
            userExample.createCriteria().andUserNameEqualTo(userName);
            User user = new User();
            user.setLockState(status);
            int num = userMapper.updateByExampleSelective(user, userExample);
            if ("1".equals(type)) {
                if (num > 0) {
                    //向其他微服发送消息
                    Map<String, Object> personTopic = new HashMap<>();
                    List<User> userList = userMapper.selectByExample(userExample);
                    PersonDto personDto = new PersonDto();
                    personDto.setUserName(userName);
                    personDto.setUserId(userList.get(0).getId());
                    personDto.setId(userList.get(0).getId());

                    personTopic.put("person", personDto);
                    personTopic.put("userId", getUserInfo().getInfo().get("id"));
                    personTopic.put("lockStatus", status);
                    String uid = UUID.randomUUID().toString();
                    MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                    send.setUid(uid);
                    send.setSystemType(saSystemUtil.getSysCode());
                    send.setBusinessType(TopicConstant.BUS_TYPE_USER);
                    send.setOperationType(TopicConstant.OP_TYPE_LOCK);
                    send.setInfo(personTopic);
                    try {
                        saSystemUtil.insertMqMessage(send);
                        topicProducer.sendTopic(personTopic, saSystemUtil.getSysCode(), TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_LOCK, uid);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    //锁定解锁操作日志
                    try {
                        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                        HttpServletRequest request = attributes.getRequest();
                        String ip = CommonService.getIp(request);
                        String remark = "";
                        if ("1".equals(status)){
                            remark = "锁定" + "[" +userName + "]" + "用户";
                        }else{
                            remark = "解锁" + "[" +userName + "]" + "用户";
                        }
                        logService.asyncSaveOperationLog(
                                new OperationLogEvent(JSONObject.toJSONString(user),
                                        null, "锁定/解锁人员",
                                        "系统管理/人员信息", "PersonService.updateUserLockStatus",
                                        remark, ip, getUserInfo()));
                    } catch (Exception e) {
                        logger.info(e.getMessage());
                    }
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据id获取用户信息
     *
     * @param personId
     * @return
     */
    public PersonDto getPersonInfoById(String personId) {
        Person person = personMapper.selectByPrimaryKey(personId);
        if (person == null) {
            throw new ApplicationException("error.000107");
        }
        PersonDto personDto = new PersonDto();
        BeanUtil.copy(person, personDto);
        return personDto;
    }

    public PersonDto getPersonInfoByIdSeriakey(String personId, String seriakey) {
        Map<String, String> param = new HashMap<>();
        param.put("id", personId);
        param.put("seriakey", seriakey);
        Person person = personMapper.selectByIdSeriakey(param);
        PersonDto getPersonInfo = personBizMapper.selectPersonInfoByIdSeriakey(param);
        if (person == null) {
            throw new ApplicationException("error.000107");
        }
        PersonDto personDto;
        if(getPersonInfo != null){
            personDto = getPersonInfo;
        }else{
            personDto = new PersonDto();
        }
        BeanUtil.copy(person, personDto);
        Profiles profiles1 = Profiles.of("dev", "prepub-sz");
        if (!env.acceptsProfiles(profiles1)) {
            personDto.setTelephone(questionnaireService.deCodeAes(personDto.getTelephone()));
        } else {
            personDto.setTelephone(personDto.getTelephone());
        }
        personDto.setOrgIds(personMapper.getOrgIdsByPersonId(person.getId()));
        if (personDto.getChildrenUserName() != null && personDto.getChildrenOrg() != null && personDto.getChildrenRole() != null){
            personDto.setChildrenOrgList(Arrays.asList(personDto.getChildrenOrg().split(",")));
            personDto.setChildrenRoleList(Arrays.asList(personDto.getChildrenRole().split(",")));
        }
        return personDto;
    }

    /**
     * 逻辑删除人员id
     * 删除人员所绑定的用户信息
     *
     * @param personIds
     * @return
     */
    public boolean deletePersonByIds(List<Map<String, String>> personIds, List<Map<String, String>> personIdSeriakeys) {

        if (personIdSeriakeys != null && personIdSeriakeys.size() > 0) {
            for (Map<String, String> personIdSeriakey : personIdSeriakeys) {

                //判断提交表单是否被篡改
                Map<String, String> checkParam = new HashMap<>();
                checkParam.put("id", personIdSeriakey.get("id"));
                checkParam.put("seriakey", personIdSeriakey.get("seriakey"));
                int checkFlag = userMapper.selectByIdSeriakey(checkParam);
                if (checkFlag == 0) {
                    throw new ApplicationException("error.000102");
                }
            }
        }
        for (Map<String, String> personIdStr : personIds) {
            Map<String, Object> personTopic = new HashMap<>();
            String personId = personIdStr.get("id");
            String telephone = personIdStr.get("telephone");
            String orgId = personIdStr.get("orgId");
            List<String> orgIds = Arrays.asList(orgId.split(","));
            Person person = personMapper.selectByPrimaryKey(personId);
            if (person != null) {
                // 锁定并修改为删除中状态
                personMapper.deletingByPersonId(personId, "2");
                userMapper.deletingByUserId(person.getUserId(), "1", "2");
                //// 删除user相关信息
                //deleteUserInfo(person.getUserId());
                //person.setStatus("0");
                //personMapper.updateByPrimaryKey(person);

                //向其他微服发送消息
                //OrgExample exampleTopic = new OrgExample();
                //exampleTopic.createCriteria().andIdIn(orgIds);
                //List<Org> orgListTopic = orgMapper.selectByExample(exampleTopic);
                //personTopic.put("org", orgListTopic);
                //Map<String, Object> personMap = new HashMap<>();
                //personMap.put("id", personId);
                //personMap.put("orgId", orgId);
                //personMap.put("telephone", telephone);
                //personTopic.put("person", personMap);
                //String uid = UUID.randomUUID().toString();
                //MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                //send.setUid(uid);
                //send.setSystemType(saSystemUtil.getSysCode());
                //send.setBusinessType(TopicConstant.BUS_TYPE_USER);
                //send.setOperationType(TopicConstant.OP_TYPE_DELETE);
                //send.setInfo(personTopic);
                //try {
                //    saSystemUtil.insertMqMessage(send);
                //    topicProducer.sendTopic(personTopic, saSystemUtil.getSysCode(), TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_DELETE, uid);
                //} catch (Exception e) {
                //    throw new RuntimeException(e);
                //}
            }
        }
        return true;
    }

    /**
     * 删除user相关信息
     *
     * @param userId
     */
    private void deleteUserInfo(String userId) {
        if (userId != null) {
            userService.deleteUserById(userId);
        }
    }

    ///**
    // * 查询是否有手机号
    // *
    // * @param telephone
    // * @return
    // */
    //public boolean hasTelephone(String telephone, String personId) {
    //    PersonExample example = new PersonExample();
    //    PersonExample.Criteria criteria = example.createCriteria();
    //    criteria.andTelephoneEqualTo(telephone);
    //    if (StringUtils.isNotBlank(personId)) {
    //        criteria.andIdNotEqualTo(personId);
    //    }
    //    return personMapper.countByExample(example) > 0;
    //}

    ///**
    // * 查询是否有邮箱
    // *
    // * @param mail
    // * @return
    // */
    //public boolean hasMail(String mail, String personId) {
    //    PersonExample example = new PersonExample();
    //    PersonExample.Criteria criteria = example.createCriteria();
    //    criteria.andMailEqualTo(mail);
    //    if (StringUtils.isNotBlank(personId)) {
    //        criteria.andIdNotEqualTo(personId);
    //    }
    //    return personMapper.countByExample(example) > 0;
    //}

    /**
     * 新增和修改相关的用户信息
     *
     * @param personDto
     * @return
     */
    private PersonWithBLOBs insertOrUpdatePersonInfo(PersonDto personDto) throws IllegalAccessException {
        Profiles profiles = Profiles.of("dev", "prepub-sz");
        PersonWithBLOBs person = new PersonWithBLOBs();
        String telephone = personDto.getTelephone();
        String oldUserName = personDto.getUserName();
        // 加密手机号
        if (!env.acceptsProfiles(profiles)) {
            personDto.setTelephone(questionnaireService.enCodeAec(telephone));
        } else {
            personDto.setTelephone(telephone);
        }
        // 没有id就新增
        if (StringUtils.isBlank(personDto.getId())) {
            String userType = personDto.getUserType();
            String isMgr = personDto.getIsMgr();
            // userType = 0内部人员,isMgr = 1总部
            if ("0".equals(userType) && "1".equals(isMgr)) {
                //设置为总部人员
                OrgExample example = new OrgExample();
                // 查询所有的
                example.createCriteria().andStatusEqualTo("1").andOrgTypeEqualTo("1");
                List<Org> orgList = orgMapper.selectByExample(example);
                if (null != orgList && orgList.size() > 0) {
                    personDto.setOrgId(orgList.get(0).getId());
                }
            }

            BeanUtil.copy(personDto, person);
            // 设置新增
            person.setSource(CodeTableConstant.PERSON_INSERT_SOURCE_INSERT);
            person.setStatus(CodeTableConstant.DATA_STATUS_1);
            String companyCode;
            if ("0".equals(userType)) {
                companyCode = CompanyConstant.SYSTEM_COMPANY_ID;
            } else {
                companyCode = personMapper.selectCompanyCode(person.getOrgId());
            }
            person.setCompanyCode(companyCode);
            if ("0".equals(userType)) {
                person.setOrgId(StringUtils.join(personDto.getOrgIdList(), ","));
            } else if ("1".equals(userType)) {
                person.setOrgId(personMapper.getOrgIdList(personDto.getOrgId()));
            }
            person.setPersonType(person.getUserType());
            person.setCreateUser(getUserInfo().getPersonName());
            personMapper.insert(person);
            UserDto userDto = new UserDto();
            String passWord;
            Profiles profiles1 = Profiles.of("dev", "prepub-sz", "test", "uat");
            if (env.acceptsProfiles(profiles1)) {
                passWord = LogicConstant.DEFAULT_PASSWORD;
            } else {
                passWord = PasswordUtil.generatePassword();
            }
            String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
            if (StringUtils.isNotBlank(person.getId())) {
                // 把person数据给到user
                userDto.setId(person.getId());
                if ("1".equals(userType)) {
                    userDto.setCompanyId(personMapper.getCompanyId(person.getCompanyCode()));
                }
                userDto.setOrgId(person.getOrgId());
                int sort = personMapper.selectMaxSort(companyCode);
                if ("0".equals(userType)) {
                    userDto.setUserName(person.getUserName());
                } else {
                    if (sort < 10) {
                        userDto.setUserName(CodeTableConstant.A + person.getCompanyCode() + "0" + sort);
                    } else {
                        userDto.setUserName(CodeTableConstant.A + person.getCompanyCode() + sort);
                    }
                }
                oldUserName = userDto.getUserName();
                userDto.setRealName(person.getPersonName());
                userDto.setTelephone(person.getTelephone());
                userDto.setMail(person.getMail());
                userDto.setRemark(person.getRemark());
                userDto.setUserType(person.getUserType());
                userDto.setSort(String.valueOf(sort));
                userDto.setHiddenKey(person.getHiddenKey());
                userDto.setNewPassword(MD5Password);
                userDto.setMergeStatus("3");
                // 发送邮件到用户
                if (!env.acceptsProfiles(profiles) && "1".equals(userType)) {
                    List<String> mailList = new ArrayList<>();
                    mailList.add(userDto.getMail());
                    String messageContent = "上市公司及股东一体化服务平台";
                    String content = this.getMsgContentMail(userDto.getUserName(), passWord, person.getPersonName());
                    cscSignaturePersonManager.pushSendMessage(mailList, messageContent, content, 101);
                }
                userDto.setNewPassword(passwordEncoder.encode(userDto.getNewPassword()));
                userBizMapper.insertUser(userDto);
            }
            List<UserCompanyMap> ruleUserCompanyMapList = new ArrayList<>();
            if ("0".equals(userType)) {
                if (CollectionUtils.isNotEmpty(person.getOrgIds())) {
                    for (String companyId : person.getOrgIds()) {
                        UserCompanyMap userCompanyMap = new UserCompanyMap();
                        userCompanyMap.setUserId(person.getId());
                        userCompanyMap.setCompanyId(companyId);
                        ruleUserCompanyMapList.add(userCompanyMap);
                    }
                }
            }
            if ("1".equals(userType)) {
                if (StringUtils.isNotEmpty(person.getOrgId())) {
                    UserCompanyMap userCompanyMap = new UserCompanyMap();
                    userCompanyMap.setUserId(person.getId());
                    userCompanyMap.setCompanyId(person.getOrgId());
                    ruleUserCompanyMapList.add(userCompanyMap);
                }
                // 新增外部人员时默认给一个角色,上市公司管理员,永远不会变GQJL-1
                OuterRoleUserMap record = new OuterRoleUserMap();
                record.setRoleId("GQJL-1");
                record.setUserId(userDto.getId());
                record.setSource(CodeTableConstant.DATA_STATUS_0);
                record.setSysCode("GQJL");
                outerRoleUserMapMapper.insertSelective(record);
                // 查询该用户所属公司的系统开通状态
                List<String> systemStateList = new ArrayList<>();
                String systemState = companyDtoMapper.getSystemState(userDto.getCompanyId());
                if (StringUtils.isNotEmpty(systemState)) {
                    String[] systemStateArray = systemState.split(",");
                    // 将数组转换为列表
                    systemStateList = new ArrayList<>(Arrays.asList(systemStateArray));
                }
                systemStateList.add("股权激励");
                // 刷新sa_company表的system_state字段
                systemStateList = systemStateList.stream().distinct().collect(Collectors.toList());
                companyDtoMapper.updateSystemState(userDto.getCompanyId(), String.join(",", systemStateList));
            }
            if (CollectionUtils.isNotEmpty(ruleUserCompanyMapList)) {
                personMapper.insertUserCompanyMap(ruleUserCompanyMapList);
            }

            //向其他微服发送消息
            BeanUtil.copy(person, personDto);
            personDto.setUserName(userDto.getUserName());
            personDto.setPassword(null);
            personDto.setRePassword(null);
            personDto.setIsAdmin("0");
            personDto.setIsLock("0");
            personDto.setLockCnt("0");
            personDto.setUserTypeCode(personDto.getUserType());
            personDto.setRealName(personDto.getPersonName());
            personDto.setPostion(personDto.getJobs());
            personDto.setPhone(personDto.getTelephone());
            personDto.setUserId(personDto.getId());
            personDto.setCompanyId(userDto.getCompanyId());
            personDto.setTelephone(telephone);
            // 内部人员
            if ("0".equals(userType) && "3".equals(personDto.getIsMgr())) {
                personDto.setBusinessDepartmentStr(personDto.getOrgId());
            }
            // 外部人员
            if ("1".equals(userType)) {
                personDto.setPosition(personDto.getJobs());
                personDto.setBusinessDepartmentStr(personDto.getOrgId());
            }
            sendInsertPersonInfoMsg(personDto, oldUserName, userDto);
            //新增操作日志
            try {
                String operationTypeDetail = "";
                String router = "";
                String remark = "";
                if ("1".equals(person.getUserType())){
                    operationTypeDetail = "新增外部人员";
                    router = "系统管理/外部人员信息";
                    remark = "新增外部人员{用户名:" + userDto.getUserName() + "}";
                }else{
                    operationTypeDetail = "新增内部人员";
                    router = "系统管理/内部人员信息";
                    remark = "新增内部人员{用户名:" + userDto.getUserName() + "}";
                }
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(person),
                                null, operationTypeDetail,
                                router, "PersonService.insertOrUpdatePersonInfo",
                                remark, ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        } else {
            BeanUtil.copy(personDto, person);
            String oldChildrenUserName = personMapper.getOldChildrenUserName(person.getId());
            if (StringUtils.isEmpty(oldChildrenUserName)) {
                oldChildrenUserName = null;
            }
            if (person.getId() != null) {
                String userType = personDto.getUserType();
                if ("0".equals(userType)) {
                    person.setOrgId(StringUtils.join(personDto.getOrgIdList(), ","));
                    personDto.setOrgId(StringUtils.join(personDto.getOrgIdList(), ","));
                } else if ("1".equals(userType)) {
                    person.setOrgId(personMapper.getOrgIdList(personDto.getOrgId()));
                }
                PersonWithBLOBs oldPersonInfo = personMapper.selectByPrimaryKey(person.getId());
                User oldUserInfo = userMapper.selectByPrimaryKey(person.getId());
                oldUserName = oldUserInfo.getUserName();
                personMapper.updateByPrimaryKeySelective(person);
                if(personDto.getIsMgr().equals("2") && StringUtils.isNotEmpty(personDto.getChildrenUserName()) && StringUtils.isNotEmpty(personDto.getChildrenOrg()) && StringUtils.isNotEmpty(personDto.getChildrenRole())){
//                    personDto.setChildrenUserName("YCSC" + personDto.getChildrenUserName());
                    StringBuffer childrenRole = new StringBuffer();
                    personDto.getChildrenRoleList().forEach(item -> {
                        childrenRole.append(item).append(",");
                    });
                    personDto.setChildrenRole(childrenRole.substring(0, childrenRole.length() - 1));
                    StringBuffer childrenOrgList = new StringBuffer();
                    personDto.getChildrenOrgList().forEach(item -> {
                        childrenOrgList.append(item).append(",");
                    });
                    personDto.setChildrenOrg(childrenOrgList.substring(0, childrenOrgList.length() - 1));
                    personBizMapper.updatePersonChildrenInfo(personDto);// 更新子账号信息
                }
                UserDto userDto = new UserDto();
                userDto.setId(person.getId());
                userDto.setUserName(person.getUserName());
                userDto.setOrgId(person.getOrgId());
                userDto.setRealName(person.getPersonName());
                userDto.setTelephone(person.getTelephone());
                userDto.setMail(person.getMail());
                userDto.setRemark(person.getRemark());
                userDto.setUserType(person.getUserType());
                userDto.setHiddenKey(person.getHiddenKey());
                userDto.setMergeStatus("3");
                personMapper.updateUserById(userDto);

                List<UserCompanyMap> ruleUserCompanyMapList = new ArrayList<>();
                if ("0".equals(userType)) {
                    if (CollectionUtils.isNotEmpty(person.getOrgIds())) {
                        for (String companyId : person.getOrgIds()) {
                            UserCompanyMap userCompanyMap = new UserCompanyMap();
                            userCompanyMap.setUserId(person.getId());
                            userCompanyMap.setCompanyId(companyId);
                            ruleUserCompanyMapList.add(userCompanyMap);
                        }
                    }
                }
                if ("1".equals(userType)) {
                    if (StringUtils.isNotEmpty(person.getOrgId())) {
                        UserCompanyMap userCompanyMap = new UserCompanyMap();
                        userCompanyMap.setUserId(person.getId());
                        userCompanyMap.setCompanyId(person.getOrgId());
                        ruleUserCompanyMapList.add(userCompanyMap);
                    }
                }
                personMapper.deleteCompanyMapByUserId(person.getId());
                if (CollectionUtils.isNotEmpty(ruleUserCompanyMapList)) {
                    personMapper.insertUserCompanyMap(ruleUserCompanyMapList);
                }
                //向其他微服发送消息
                personDto.setUserTypeCode(personDto.getUserType());
                personDto.setRealName(personDto.getPersonName());
                personDto.setPostion(personDto.getJobs());
                personDto.setPhone(telephone);
                personDto.setUserId(personDto.getId());
                personDto.setCompanyId(personMapper.getCompanyIdByOrgId(personDto.getOrgId()));
                personDto.setTelephone(telephone);
                personDto.setPassword(null);
                personDto.setRePassword(null);
                sendUpdatePersonInfoMsg(personDto, oldUserName, oldPersonInfo, userDto, oldChildrenUserName);
                //更新操作日志
                try {
                    String operationTypeDetail = "";
                    String router = "";
                    if ("1".equals(person.getUserType())){
                        operationTypeDetail = "编辑外部人员";
                        router = "系统管理/外部人员信息";
                    }else{
                        operationTypeDetail = "编辑内部人员";
                        router = "系统管理/内部人员信息";
                    }
                    PersonMessage personMessageNew = new PersonMessage();
                    PersonMessage personMessageOld = new PersonMessage();
                    BeanUtil.copy(person,personMessageNew);
                    BeanUtil.copy(oldPersonInfo,personMessageOld);

                    personMessageNew = transcoding(personMessageNew);
                    personMessageOld = transcoding(personMessageOld);
                    personMessageOld.setUserName(oldUserInfo.getUserName());
                    personMessageOld.setOrgId(oldUserInfo.getOrgNo());
                    MessageUtil messageUtil = new MessageUtil();
                    MessageInfo messageInfo = messageUtil.getMessageContext(personMessageNew, personMessageOld);
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    HttpServletRequest request = attributes.getRequest();
                    String ip = CommonService.getIp(request);
                    logService.asyncSaveOperationLog(
                            new OperationLogEvent(JSONObject.toJSONString(person),
                                    messageInfo.getOperationContext(), operationTypeDetail,
                                    router, "PersonService.insertOrUpdatePersonInfo",
                                    messageInfo.getOperationRemark(), ip, getUserInfo()));
                } catch (Exception e) {
                    logger.info(e.getMessage());
                }
            }
        }
        return person;
    }

    public PersonMessage transcoding(PersonMessage personMessage) {
        if (StringUtils.isNotEmpty(personMessage.getChildrenOrg())) {
            List<String> childrenOrgIdList = Arrays.asList(personMessage.getChildrenOrg().split(","));
            List<String> childrenOrgName = personMapper.getOrgNameByOrgIds(childrenOrgIdList);
            personMessage.setChildrenOrg(String.join(",", childrenOrgName));
        }
        if (StringUtils.isNotEmpty(personMessage.getChildrenRole())) {
            List<String> childrenRoleIdList = Arrays.asList(personMessage.getChildrenRole().split(","));
            List<String> childrenRoleName = userRoleMapMapper.getRoleNameByRoleIds(childrenRoleIdList);
            personMessage.setChildrenRole(String.join(",", childrenRoleName));
        }
        if (StringUtils.isNotEmpty(personMessage.getChildrenLock())) {
            if ("1".equals(personMessage.getChildrenLock())) {
                personMessage.setChildrenLock("已锁定");
            }
            if ("0".equals(personMessage.getChildrenLock())) {
                personMessage.setChildrenLock("已解锁");
            }
        }
        return personMessage;
    }

    public String getMsgContentMail(String userName, String passWord, String personName) {
        //String personName = commonService.getPersonNameByUserName(userName);
        return "尊敬的" + personName + "用户，您好，您的上市公司及股东一体化服务平台账号创建成功，登录账号为：" + userName + "，初始密码为："
                + passWord + "，登录地址为：" + loginUrl + "。为了保障您的合法权益，请您及时登录一体化平台，修改并妥善保管登录密码。【上市公司及股东一体化服务平台】";
    }

    /**
     * 向其他微服发送消息
     *
     * @param personDto
     */
    private void sendInsertPersonInfoMsg(PersonDto personDto, String oldUserName, UserDto userDto) throws IllegalAccessException {
        //向其他微服发送消息
        Map<String, Object> personTopic = new HashMap<>();
        // userType 0内部，1外部
        String userType = personDto.getUserType();
        String systemType = saSystemUtil.getSysCode();

        //List<Org> orgListTopic = selPersonOrg(personDto.getOrgId());

        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andIdIn(Arrays.asList(personDto.getOrgId().split(",")));
        List<Org> orgListTopic = orgMapper.selectByExample(orgExample);

        String oldChildrenUserName = null;
        if ("0".equals(userType)) {
            // 内部用户放入子账号信息
            if (StringUtils.isNotEmpty(personDto.getChildrenUserName())) {
                oldChildrenUserName = personDto.getChildrenUserName();
                Map<String,Object> childrenUserInfo = new HashMap<>();
                childrenUserInfo.put("childrenUserName", personDto.getChildrenUserName());
                childrenUserInfo.put("childrenOrgIds", personDto.getChildrenOrg());
                childrenUserInfo.put("childrenRole", personDto.getChildrenRole());
                childrenUserInfo.put("childrenLock", personDto.getChildrenLock());
                orgExample.clear();
                orgExample.createCriteria().andIdIn(Arrays.asList(personDto.getChildrenOrg().split(",")));
                List<Org> childrenOrgListTopic = orgMapper.selectByExample(orgExample);
                int maxLevel;
                String id;
                List<Org> orgList = new ArrayList<>();
                for (Org orgTopic : childrenOrgListTopic) {
                    id = orgTopic.getpOrgId();
                    maxLevel = orgMapper.selOrgLevel();
                    for (int i = 0; i <= maxLevel; i++) {
                        Org org = orgMapper.selParentById(id);
                        if (org == null) {
                            break;
                        }
                        orgList.add(org);
                        if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                            break;
                        }
                        id = org.getpOrgId();
                    }
                }
                childrenUserInfo.put("org", childrenOrgListTopic);
                childrenUserInfo.put("orgP", orgList);
                personDto.setChildrenUserInfo(childrenUserInfo);
            }
        } else if ("1".equals(userType)) {
            //OrgExample exampleTopic = new OrgExample();
            //exampleTopic.createCriteria().andIdEqualTo(personDto.getOrgId());
            //List<Org> orgList = orgMapper.selectByExample(exampleTopic);
            Org orgTopic = orgListTopic.get(0);
            SaCompanyDto saCompany = saCompanyMapper.getCompanyById(orgTopic.getCompanyId());
            Map<String, String> subsystemCodes = CodeConvert.convertCode(saCompany.getMarketStockType(), "marketStockType");
            saCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));
            if (StringUtils.isNotEmpty(saCompany.getParentId())) {
                systemType = "GQJL";
            }
            CompanyContactExample example = new CompanyContactExample();
            example.createCriteria().andCompanyCodeEqualTo(saCompany.getCompanyCode());
            List<CompanyContact> companyContacts = companyContactMapper.selectByExample(example);
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompany);
            personTopic.put("saCompany", saCompany);
            personTopic.put("saCompanyContacts", companyContacts);
        } else {
            log.info("用户不属于内外部-----userType:", userType);
        }

        int maxLevel;
        String id;
        List<Org> orgList = new ArrayList<>();
        for (Org orgTopic : orgListTopic) {
            id = orgTopic.getpOrgId();
            maxLevel = orgMapper.selOrgLevel();
            for (int i = 0; i <= maxLevel; i++) {
                Org org = orgMapper.selParentById(id);
                if (org == null) {
                    break;
                }
                orgList.add(org);
                if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                    break;
                }
                id = org.getpOrgId();
            }
        }
        personTopic.put("org", orgListTopic);
        personTopic.put("orgP", orgList);
        personTopic.put("person", personDto);
        personTopic.put("user", userDto);
        personTopic.put("userId", getUserInfo().getInfo().get("id"));
        personTopic.put("oldUserName", oldUserName);
        personTopic.put("oldChildrenUserName", oldChildrenUserName);
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType(systemType);
        send.setBusinessType(TopicConstant.BUS_TYPE_USER);
        send.setOperationType(TopicConstant.OP_TYPE_INSERT);
        send.setInfo(personTopic);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(personTopic, systemType, TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_INSERT, uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 向其他微服发送消息
     *
     * @param personDto
     */
    private void sendUpdatePersonInfoMsg(PersonDto personDto, String oldUserName, PersonWithBLOBs oldPersonInfo, UserDto userDto, String oldChildrenUserName) throws IllegalAccessException {
        //向其他微服发送消息
        Map<String, Object> personTopic = new HashMap<>();
        // userType 0内部，1外部
        String userType = personDto.getUserType();
        String systemType = saSystemUtil.getSysCode();

        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andIdIn(Arrays.asList(personDto.getOrgId().split(",")));
        List<Org> orgListTopic = orgMapper.selectByExample(orgExample);

        if ("0".equals(userType)) {
            // 内部用户放入子账号信息
            if (StringUtils.isNotEmpty(personDto.getChildrenUserName())) {
                Map<String,Object> childrenUserInfo = new HashMap<>();
                childrenUserInfo.put("oldChildrenUserName", StringUtils.isNotEmpty(oldPersonInfo.getChildrenUserName()) ? oldPersonInfo.getChildrenUserName() : null);
                childrenUserInfo.put("childrenUserName", personDto.getChildrenUserName());
                childrenUserInfo.put("childrenOrgIds", personDto.getChildrenOrg());
                childrenUserInfo.put("childrenRole", personDto.getChildrenRole());
                childrenUserInfo.put("childrenLock", personDto.getChildrenLock());
                orgExample.clear();
                orgExample.createCriteria().andIdIn(Arrays.asList(personDto.getChildrenOrg().split(",")));
                List<Org> childrenOrgListTopic = orgMapper.selectByExample(orgExample);
                int maxLevel;
                String id;
                List<Org> orgList = new ArrayList<>();
                for (Org orgTopic : childrenOrgListTopic) {
                    id = orgTopic.getpOrgId();
                    maxLevel = orgMapper.selOrgLevel();
                    for (int i = 0; i <= maxLevel; i++) {
                        Org org = orgMapper.selParentById(id);
                        if (org == null) {
                            break;
                        }
                        orgList.add(org);
                        if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                            break;
                        }
                        id = org.getpOrgId();
                    }
                }
                childrenUserInfo.put("org", childrenOrgListTopic);
                childrenUserInfo.put("orgP", orgList);
                personDto.setChildrenUserInfo(childrenUserInfo);
            } else if (StringUtils.isNotEmpty(oldPersonInfo.getChildrenUserName())) {
                Map<String,Object> childrenUserInfo = new HashMap<>();
                childrenUserInfo.put("oldChildrenUserName", oldPersonInfo.getChildrenUserName());
                personDto.setChildrenUserInfo(childrenUserInfo);
            } else {
                personDto.setChildrenUserInfo(null);
            }
        } else if ("1".equals(userType)) {
            //OrgExample exampleTopic = new OrgExample();
            //exampleTopic.createCriteria().andIdEqualTo(personDto.getOrgId());
            //List<Org> orgList = orgMapper.selectByExample(exampleTopic);
            Org orgTopic = orgListTopic.get(0);
            SaCompanyDto saCompany = saCompanyMapper.getCompanyById(orgTopic.getCompanyId());
            Map<String, String> subsystemCodes = CodeConvert.convertCode(saCompany.getMarketStockType(), "marketStockType");
            saCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));

            CompanyContactExample example = new CompanyContactExample();
            example.createCriteria().andCompanyCodeEqualTo(saCompany.getCompanyCode());
            List<CompanyContact> companyContacts = companyContactMapper.selectByExample(example);
            if (StringUtils.isNotEmpty(saCompany.getParentId())) {
                systemType = "GQJL";
            }
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompany);
            personTopic.put("saCompany", saCompany);
            personTopic.put("saCompanyContacts", companyContacts);
        } else {
            log.info("用户不属于内外部-----userType:", userType);
        }

        int maxLevel;
        String id;
        List<Org> orgList = new ArrayList<>();
        for (Org orgTopic : orgListTopic) {
            id = orgTopic.getpOrgId();
            maxLevel = orgMapper.selOrgLevel();
            for (int i = 0; i <= maxLevel; i++) {
                Org org = orgMapper.selParentById(id);
                if (org == null) {
                    break;
                }
                orgList.add(org);
                if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                    break;
                }
                id = org.getpOrgId();
            }
        }
        personTopic.put("org", orgListTopic);
        personTopic.put("orgP", orgList);
        personTopic.put("person", personDto);
        personTopic.put("user", userDto);
        personTopic.put("userId", getUserInfo().getInfo().get("id"));
        personTopic.put("oldUserName", oldUserName);
        personTopic.put("oldChildrenUserName", oldChildrenUserName);
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType(systemType);
        send.setBusinessType(TopicConstant.BUS_TYPE_USER);
        send.setOperationType(TopicConstant.OP_TYPE_UPDATE);
        send.setInfo(personTopic);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(personTopic, systemType, TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_INSERT, uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private List<Org> selPersonOrg(String id) {
        List<Org> orgList = new ArrayList<>();
        int maxLevel = orgMapper.selOrgLevel();
        for (int i = 0; i < maxLevel + 2; i++) {
            Org org = orgMapper.selParentById(id);
            if (org == null) {
                break;
            }
            orgList.add(org);
            if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                break;
            }
            id = org.getpOrgId();
        }
        return orgList;
    }


    /**
     * 检查手机号
     *
     * @param telephone
     * @param personId
     */
    private void checkTelephone(String telephone, String personId) {
        if (StringUtils.isNotBlank(telephone)) {
            PersonExample example = new PersonExample();
            example.createCriteria().andTelephoneEqualTo(telephone);
            List<Person> personList = personMapper.selectByExample(example);
            String sourcePersonId = null;
            if (!personList.isEmpty()) {
                sourcePersonId = personList.get(0).getId();
            }
            // 手机号已存在
            if (sourcePersonId != null && !Objects.equals(personId, sourcePersonId)) {
                throw new ApplicationException("error.000108");
            }
        }
    }

    /**
     * 检查邮箱
     *
     * @param mail
     * @param personId
     */
    private void checkMail(String mail, String personId) {
        if (StringUtils.isNotBlank(mail)) {
            PersonExample example = new PersonExample();
            example.createCriteria().andMailEqualTo(mail);
            List<Person> personList = personMapper.selectByExample(example);
            String sourcePersonId = null;
            if (!personList.isEmpty()) {
                sourcePersonId = personList.get(0).getId();
            }
            // 邮箱已存在
            if (sourcePersonId != null && !Objects.equals(personId, sourcePersonId)) {
                throw new ApplicationException("error.000109");
            }
        }
    }

    /**
     * 获取所属机构下拉列表
     *
     * @param params
     * @return
     */
    public List<Map<String, Object>> getOrgSelection(String params) {
        JSONObject json = JSONObject.parseObject(params);
        String userType = json.getString("userType");
        //账户所属机构
        String orgType = (String) getUserInfo().getInfo().get("orgType");
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        Map<String, String> map = new HashMap<>();
        if ("0".equals(userType)) {
            //查询所属机构列表
            String isMgr = json.getString("isMgr");
            if (!"1".equals(orgType)) {
                map.put("pId", orgId);
            }
            map.put("orgType", isMgr);
            if ("3".equals(orgType)) {
                //营业部获取可以相互查看的营业部
                String ppId = orgMapper.checkBranchIfShare(orgId);
                if (StringUtils.isNotEmpty(ppId)) {
                    map.put("limitCodeStr", ppId);
                }
            }
            List<Map<String, Object>> companyList = orgMapper.selectOrgDictByIsMgr(map);
            return companyList;
        } else if ("1".equals(userType)) {
            List<TreeDto> tree = saCompanyMapper.getTree();
            List<TreeDto> treeDtos = TreeBuilderUtil.buildListToTree(tree);
            List<Map<String,Object>> result = new ArrayList<>();
            InitOptionTree(treeDtos, result);
            return result;
        }
        return new ArrayList<>();
    }

    public List<ExcelUserManage> exportUserInfo(PersonDto personDto) {
        return personBizMapper.exportUserInfo(personDto);
    }


    /**
     * 获取下拉列表
     *
     * @return
     */
    public List<SelectTreeTagDto> getSelectData(String labelCode) {
        Map<String, Object> equNoParamMap = new HashMap<String, Object>();
        equNoParamMap.put("labelCode", labelCode);
        return personBizMapper.getCodeLabelByNo(equNoParamMap);
    }

    /**
     * 设置用户
     *
     * @param userDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean setUser(UserDto userDto) {
        String passWord;
        Profiles profiles = Profiles.of("dev", "prepub-sz", "test", "uat");
        if (env.acceptsProfiles(profiles)) {
            passWord = LogicConstant.DEFAULT_PASSWORD;
        } else {
            passWord = PasswordUtil.randomPassword(8);
        }
        String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
        userDto.setNewPassword(MD5Password);
        // 发送短信
        String content = this.getMsgContent(passWord, userDto.getUserName());
        logger.info("发送短信接口返回结果为:" + JSON.toJSONString(messageService.sendMessage(userDto.getTelephone(), content)));
        // 设置
        return permissionService.doSetUser(userDto);
    }

    public String getMsgContent(String password, String userName) {
        logger.info("message content :" + "您好，您股权激励管理系统登录账号为：" + userName + "，登录初始密码为：" + password + "请您及时登录并修改初始密码");
        return "您好，您股权激励管理系统登录账号为：" + userName + "，登录初始密码为：" + password + "请您及时登录并修改初始密码";
    }

    public List<UserMergeHistoryDto> queryTableDate(PersonDto dto) {
        return personBizMapper.queryTableDate(dto.getId());
    }

    @Async
    public Boolean resetPersonPassword() {
        List<PersonDto> personIdList = personBizMapper.getOutPersonIdList();
        if (CollectionUtils.isNotEmpty(personIdList)) {
            personIdList.stream().forEach(personDto -> {
                if (StringUtils.isNotEmpty(personDto.getMail())) {
                    UserDto userDto = new UserDto();
                    userDto.setId(personDto.getId());
                    userDto.setUserName(personDto.getPersonName());
                    userDto.setTelephone(personDto.getTelephone());
                    userDto.setMail(personDto.getMail());
                    userService.resetPasswordOnlyOne(userDto);
                }
            });
            return true;
        }
        return false;
    }

    // 递归初始化树状选项
    private static void InitOptionTree(List<TreeDto> list, List<Map<String,Object>> result){
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(item->{
                String id = item.getId();
                String value = item.getValue();
                String name = item.getName();
                String parentId = item.getParentId();
                Map<String,Object> map = new HashMap<>();
                map.put("companyId",id);
                map.put("id",value);// 这个是value
                map.put("key",value);
                map.put("label",name);//这个是text
                List<Map<String,Object>> listMap = new ArrayList<>();
                map.put("children", listMap);
                if(CollectionUtils.isNotEmpty(item.getChildren())){
                    InitOptionTree(item.getChildren(), listMap);
                    map.put("children", listMap);
                }else{
                    map.put("children", null);
                }
                result.add(map);
            });
        }
    }


    public Boolean deleteChildrenInfo(PersonDto personDto) {
        if(personDto.getId() == null){
            return false;
        }
        return personBizMapper.deletePersonChildrenInfo(personDto) > 0;
    }


    public PersonDto getChildrenInfo(PersonDto personDto){
        if (personDto.getId() == null)return null;
        PersonDto childrenInfo = personBizMapper.getChildrenInfo(personDto);
        // 解密手机号
        Profiles profiles = Profiles.of("dev", "prepub-sz");
        if (!env.acceptsProfiles(profiles)) {
            childrenInfo.setTelephone(questionnaireService.deCodeAes(childrenInfo.getTelephone()));
        }
        if (StringUtils.isNotEmpty(childrenInfo.getChildrenOrg())) {
            childrenInfo.setChildrenOrgList(Arrays.asList(childrenInfo.getChildrenOrg().split(",")));
        }
        if (StringUtils.isNotEmpty(childrenInfo.getChildrenRole())) {
            childrenInfo.setChildrenRoleList(Arrays.asList(childrenInfo.getChildrenRole().split(",")));
        }
        return childrenInfo;
    }

    public String getPersonId(PersonDto personDto){
        return personBizMapper.getPersonId(personDto);
    }


    public Boolean updateChildrenInfo(PersonDto personDto) {
        String personId = personBizMapper.getPersonId(personDto);
        if(personId == null)return false;
        personDto.setId(personId);
        return personBizMapper.updatePersonChildrenInfo(personDto) > 0;
    }
}
