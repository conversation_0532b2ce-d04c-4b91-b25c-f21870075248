package com.stock.service.platform.person.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import java.io.Serializable;

public class ExcelUserManage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("用户名")
    @ColumnWidth(20)
    private String userName;

    @ExcelProperty("姓名")
    @ColumnWidth(20)
    private String personName;

    @ExcelProperty("账号状态")
    @ColumnWidth(20)
    private String userLockStatus;

    @ExcelProperty("用户部门")
    @ColumnWidth(50)
    private String orgNames;

    @ExcelProperty("角色名称")
    @ColumnWidth(30)
    private String roleName;

    @ExcelProperty("回购管理角色名称")
    @ColumnWidth(30)
    private String HGGLRoleName;

    @ExcelProperty("股权激励角色名称")
    @ColumnWidth(30)
    private String GQJLRoleName;

    @ExcelProperty("合规交易角色名称")
    @ColumnWidth(30)
    private String TDGFRoleName;

    @ExcelProperty("备注")
    @ColumnWidth(20)
    private String remark;

    @ExcelProperty("账号类别")
    @ColumnWidth(20)
    private String isQs;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getUserLockStatus() {
        return userLockStatus;
    }

    public void setUserLockStatus(String userLockStatus) {
        this.userLockStatus = userLockStatus;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getHGGLRoleName() {
        return HGGLRoleName;
    }

    public void setHGGLRoleName(String HGGLRoleName) {
        this.HGGLRoleName = HGGLRoleName;
    }

    public String getGQJLRoleName() {
        return GQJLRoleName;
    }

    public void setGQJLRoleName(String GQJLRoleName) {
        this.GQJLRoleName = GQJLRoleName;
    }

    public String getTDGFRoleName() {
        return TDGFRoleName;
    }

    public void setTDGFRoleName(String TDGFRoleName) {
        this.TDGFRoleName = TDGFRoleName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsQs() {
        return isQs;
    }

    public void setIsQs(String isQs) {
        this.isQs = isQs;
    }
}
