package com.stock.service.platform.person.service;

import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.dao.CodeMapper;
import com.stock.service.platform.common.entity.Code;
import com.stock.service.platform.common.entity.CodeExample;
import com.stock.service.platform.person.dto.IdentityDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 身份信息相关service
 *
 */
@Service
public class IdentityService {

    @Resource
    CodeMapper codeMapper;

    /**
     * 展示出所有的身份类型
     *
     * @return
     */
    public List<IdentityDto> listIdentityDto() {
        CodeExample example = new CodeExample();
        example.createCriteria().andCodeNoEqualTo(CodeTableConstant.IDENTITY_TYPR_CODE_NO);
        List<Code> codeList = codeMapper.selectByExample(example);
        List<IdentityDto> identityDtoList = new ArrayList<>();
        for(Code perCode:codeList){
            IdentityDto dto = new IdentityDto();
            dto.setIdentityType(perCode.getCodeValue());
            dto.setIdentityName(perCode.getCodeName());
            identityDtoList.add(dto);
        }
        return identityDtoList;
    }
}
