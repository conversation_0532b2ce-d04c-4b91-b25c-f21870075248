<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OuterRoleUserMapMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.OuterRoleUserMap">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sys_code, source, role_id, user_id
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_outer_role_user_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_outer_role_user_map
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_outer_role_user_map
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMapExample">
    delete from sa_outer_role_user_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_outer_role_user_map (id, sys_code, source, 
      role_id, user_id)
    values (#{id,jdbcType=VARCHAR}, #{sysCode,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{roleId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_outer_role_user_map
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="sysCode != null">
        sys_code,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="sysCode != null">
        #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMapExample" resultType="java.lang.Long">
    select count(*) from sa_outer_role_user_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_outer_role_user_map
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sysCode != null">
        sys_code = #{record.sysCode,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_outer_role_user_map
    set id = #{record.id,jdbcType=VARCHAR},
      sys_code = #{record.sysCode,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMap">
    update sa_outer_role_user_map
    <set>
      <if test="sysCode != null">
        sys_code = #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMap">
    update sa_outer_role_user_map
    set sys_code = #{sysCode,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateMergeFlag">
    update sa_user_merge_history set merge_flag = '1' where user_id = #{userId}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.OuterRoleUserMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_outer_role_user_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
<!--    <select id="getCompanyCodeByUserId" resultType="java.lang.String">-->
<!--      select sc.company_code companyCode-->
<!--      from sa_user_company_map sucm-->
<!--      left join sa_org so on so.id = sucm.company_id-->
<!--      left join sa_company sc on sc.id = so.company_id-->
<!--      where sucm.user_id = #{userId}-->
<!--      limit 1-->
<!--    </select>-->
  <select id="getCompanyCodeByUserId" resultType="java.lang.String">
    select sc.company_code companyCode
    from sa_company sc
    left join sa_user su on sc.id = su.company_id
    where su.id = #{userId}
  </select>
  <select id="getSystemStateName" resultType="java.lang.String">
    select sys_name
    from sa_system_config
    <where>
      <if test="systemState != null and systemState.size !=0">
        sys_code in
        <foreach collection="systemState" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
    <select id="getUserSystem" resultType="java.lang.String">
      select source from sa_person where id = #{userId}
    </select>
</mapper>