package com.stock.service.platform.common.entity;

import java.io.Serializable;

public class SaOperationLogWithBLOBs extends SaOperationLog implements Serializable {
    private String parameterJson;

    private String resultJson;

    private static final long serialVersionUID = 1L;

    public String getParameterJson() {
        return parameterJson;
    }

    public void setParameterJson(String parameterJson) {
        this.parameterJson = parameterJson == null ? null : parameterJson.trim();
    }

    public String getResultJson() {
        return resultJson;
    }

    public void setResultJson(String resultJson) {
        this.resultJson = resultJson == null ? null : resultJson.trim();
    }
}