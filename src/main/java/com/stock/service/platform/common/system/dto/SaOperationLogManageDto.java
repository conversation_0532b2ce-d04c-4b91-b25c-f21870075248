package com.stock.service.platform.common.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class SaOperationLogManageDto implements Serializable {

    private static final long serialVersionUID = -2411065663677535056L;

    //主键
    private String id;

    // ip
    private String ip;

    // 操作内容
    private String operationTypeDetail;

    // 入参json
    private String parameterJson;

    // 出参json
    private String resultJson;

    // 路径
    private String router;

    // 调用方法
    private String method;

    // 备注
    private String remark;

    // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 创建人ID
    private String createUser;

    // 用户类别
    private String userType;

    // 所属营业部
    private String orgName;

    // 所属上市公司
    private String companyName;

    /* 查询用字段 */
    //操作账号
    private String userName;

    //所属机构
    private String orgId;

    //所属上市公司
    private String companyId;

    //开始时间
    private String startTime;

    // 结束时间
    private String endTime;
}
