package com.stock.service.platform.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class DateUtil {

  /**
   * 把日期时间格式化为指定格式，如：yyyy-MM-dd HH:mm
   *
   * @param dt java.util.Date
   * @param formatType : 指定日期转化格式字符串模板,例如:yyyy-MM-dd
   * @return 格式化后的日期时间字符串
   */
  public static String formatStrByDate(Date dt, String formatType) {
    String date = "";
    if (dt != null) {
      Locale locale = Locale.CHINESE;
      SimpleDateFormat dateStyle = new SimpleDateFormat(formatType, locale);
      date = dateStyle.format(dt);
    }
    return date;
  }

  public static String formatStrToStr(String str, String formatType1, String formatType2)
      throws ParseException {
    String date = "";
    DateFormat df = new SimpleDateFormat(formatType1);
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(df.parse(str));

    Locale locale = Locale.CHINESE;
    SimpleDateFormat dateStyle = new SimpleDateFormat(formatType2, locale);
    date = dateStyle.format(calendar.getTime());

    return date;
  }


  /**
   * 根据传入格式与日期返回日期类型
   */
  public static Date formatDateByStr(String date, String formatType) throws ParseException {
    if (StringUtil.isEmpty(date)) {
      return null;
    }
    DateFormat df = new SimpleDateFormat(formatType);
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(df.parse(date));
    return calendar.getTime();
  }

  /**
   * 根据传入格式与日期返回String
   */
  public static String formatStrByLocalDate(LocalDate localDate, String formatType) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatType);
    return localDate.format(formatter);
  }

  /**
   * 查询时间段内的每一天日期
   *
   * @return List<LocalDate>
   */
  public static ArrayList<Date> queryTimePeriodDays(String startDateStr, String endDateStr) {
    ArrayList<Date> dateList = new ArrayList<>();
    LocalDate startDate = LocalDate.parse(startDateStr);
    LocalDate endDate = LocalDate.parse(endDateStr);
    while (startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
      dateList.add(localDateToDate(startDate));
      startDate = startDate.plusDays(1);
    }
    return dateList;
  }

  /**
   * 当前格式化时间
   * yyyy-MM-dd HH:mm:ss
   */
  public static String getCurrentDateStr() {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return sdf.format(new Date());
  }

  /**
   * 当前格式化时间
   * yyyy-MM-dd HH:mm:ss
   */
  public static String getCurrentDateStr(String formatType) {
    SimpleDateFormat sdf = new SimpleDateFormat(formatType);
    return sdf.format(new Date());
  }

  //java.util.Date --> java.time.LocalDateTime
  public static LocalDateTime dateToLocalDateTime(Date date) {
    Instant instant = date.toInstant();
    ZoneId zone = ZoneId.systemDefault();
    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
    return localDateTime;
  }

  //java.util.Date --> java.time.LocalDate
  public static LocalDate dateToLocalDate(Date date) {
    Instant instant = date.toInstant();
    ZoneId zone = ZoneId.systemDefault();
    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
    LocalDate localDate = localDateTime.toLocalDate();
    return localDate;
  }

  public static LocalDate strToLocalDate(String dateStr, String formatType) {
    return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(formatType));
  }

  //java.util.Date --> java.time.LocalTime
  public static LocalTime dateToLocalTime(Date date) {
    Instant instant = date.toInstant();
    ZoneId zone = ZoneId.systemDefault();
    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
    LocalTime localTime = localDateTime.toLocalTime();
    return localTime;
  }

  //java.time.LocalDateTime --> java.util.Date
  public static Date localDateTimeToDate(LocalDateTime localDateTime) {
    ZoneId zone = ZoneId.systemDefault();
    Instant instant = localDateTime.atZone(zone).toInstant();
    Date date = Date.from(instant);
    return date;
  }

  //java.time.LocalDate --> java.util.Date
  public static Date localDateToDate(LocalDate localDate) {
    ZoneId zone = ZoneId.systemDefault();
    Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
    Date date = Date.from(instant);
    return date;
  }

  //java.time.LocalTime --> java.util.Date
  public static Date localTimeToDate(LocalDate localDate, LocalTime localTime) {
    LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);
    ZoneId zone = ZoneId.systemDefault();
    Instant instant = localDateTime.atZone(zone).toInstant();
    Date date = Date.from(instant);
    return date;
  }


  /**
   * 将date转换成当天的分钟数
   *
   * @return long
   */
  public static long date2Minute(LocalDateTime date) {
    LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.ofSecondOfDay(0));
    return (date.toInstant(ZoneOffset.of("+8")).toEpochMilli() - todayStart
        .toInstant(ZoneOffset.of("+8")).toEpochMilli()) / (1000 * 60);
  }

  /**
   * 将date转换成当天的分钟数
   *
   * @return long
   */
  public static long date2Minute(LocalTime time) {
    LocalTime todayStart = LocalTime.ofSecondOfDay(0);
    return (time.toSecondOfDay() - todayStart.toSecondOfDay()) / 60;
  }

  /**
   * 将date转换成当天的分钟数
   *
   * @return long
   */
  public static long date2Minute(Date date) {
    Calendar todayStart = Calendar.getInstance();
    todayStart.set(Calendar.HOUR_OF_DAY, 0);
    todayStart.set(Calendar.MINUTE, 0);
    todayStart.set(Calendar.SECOND, 0);
    todayStart.set(Calendar.MILLISECOND, 0);
    return (date.getTime() - todayStart.getTime().getTime()) / (1000 * 60);
  }

  /**
   * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
   *
   * @param nowTime 当前时间
   * @param startTime 开始时间
   * @param endTime 结束时间
   */
  public static boolean ifOverlapDate(Date nowTime, Date startTime, Date endTime) {
    if (nowTime.getTime() == startTime.getTime()
        || nowTime.getTime() == endTime.getTime()) {
      return true;
    }

    Calendar date = Calendar.getInstance();
    date.setTime(nowTime);

    Calendar begin = Calendar.getInstance();
    begin.setTime(startTime);

    Calendar end = Calendar.getInstance();
    end.setTime(endTime);

    if (date.after(begin) && date.before(end)) {
      return true;
    } else {
      return false;
    }
  }

  public static boolean ifCurrentTime(Date date, String pattern) {
    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
    String param = sdf.format(date);//参数时间
    String now = sdf.format(new Date());//当前时间
    if (param.equals(now)) {
      return true;
    }
    return false;
  }

  public static void main(String[] args) throws ParseException {
    System.out.println(formatStrToStr("2008年09月15日", "yyyy年MM月dd日", "yyyy-MM-dd"));
  }
}
