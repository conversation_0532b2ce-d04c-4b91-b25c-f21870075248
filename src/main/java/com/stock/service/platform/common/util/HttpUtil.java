package com.stock.service.platform.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HttpUtil {

  private static final int TIMEOUT = 15 * 1000;

  private static String apikey;

  @Value("${cscInterface.apikey}")
  public  void setApikey(String apikey) {
    HttpUtil.apikey = apikey;
  }

  public static String getApikey() {
    return apikey;
  }

  /**
   *
   */
  public static String httpPostWithJSON(String url, String encoderJson) throws Exception {
    CloseableHttpClient httpClient = HttpClients.createDefault();
    //配置超时时间
    RequestConfig requestConfig = RequestConfig.custom().
        setConnectTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT)
        .setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();
    HttpPost httpPost = new HttpPost(url);
    httpPost.setConfig(requestConfig);
    httpPost.setHeader("apikey", getApikey());
    httpPost.setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE);
    StringEntity stringEntity = new StringEntity(encoderJson, "UTF-8");
    stringEntity.setContentType("text/json");
    stringEntity
        .setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE));
    httpPost.setEntity(stringEntity);
    CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
    HttpEntity entity = httpResponse.getEntity();
    String result = EntityUtils.toString(entity, "UTF-8");
    EntityUtils.consume(entity);
    httpClient.close();
    return result;
  }

  /**
   *
   */
  public static String httpPostWithNameValuePair(String url, List<NameValuePair> params)
      throws Exception {
    CloseableHttpClient httpClient = HttpClients.createDefault();
    //配置超时时间
    RequestConfig requestConfig = RequestConfig.custom().
        setConnectTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT)
        .setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();
    HttpPost httpPost = new HttpPost(url);
    httpPost.setConfig(requestConfig);
    httpPost
        .setHeader("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE + ";charset=UTF-8");
    HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
    httpPost.setEntity(entity);
    CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
    HttpEntity entityResult = httpResponse.getEntity();
    String result = EntityUtils.toString(entityResult, "UTF-8");
    EntityUtils.consume(entity);
    httpClient.close();
    return result;
  }

  public static String httpGet(String url, String... str) throws Exception {
    if (StringUtils.isNotEmpty(url)) {
      if (url.substring(0, 5).equalsIgnoreCase("https")) {
        return httpsGet(url, str);
      }
    }
    CloseableHttpClient httpClient = HttpClients.createDefault();
    //配置超时时间
    RequestConfig requestConfig = RequestConfig.custom().
        setConnectTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT)
        .setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();
    StringBuilder sb = new StringBuilder(url);
    if (null != str) {
      for (int i = 0; i < str.length; i++) {
        if (i == 0) {
          sb.append("?").append(str[i]);
        } else {
          sb.append("&").append(str[i]);
        }

      }
    }
    log.info(sb.toString());
    HttpGet httpGet = new HttpGet(sb.toString());
    httpGet.setConfig(requestConfig);
    httpGet.addHeader("apikey",getApikey());
    CloseableHttpResponse httpResponse = httpClient.execute(httpGet);
    HttpEntity entity = httpResponse.getEntity();
    String result = EntityUtils.toString(entity, "UTF-8");
    EntityUtils.consume(entity);
    httpClient.close();
    return result;
  }

  public static String httpsGet(String url, String... str) throws Exception {
    HttpClient httpClient = null;
    HttpGet httpGet = null;
    String result = null;
    try {
      StringBuilder sb = new StringBuilder(url);
      if (null != str) {
        for (int i = 0; i < str.length; i++) {
          if (i == 0) {
            sb.append("?").append(str[i]);
          } else {
            sb.append("&").append(str[i]);
          }

        }
      }
      httpClient = new SSLClient();
      RequestConfig requestConfig = RequestConfig.custom().
          setConnectTimeout(TIMEOUT).setConnectionRequestTimeout(TIMEOUT)
          .setSocketTimeout(TIMEOUT).setRedirectsEnabled(true).build();
      httpGet = new HttpGet(sb.toString());
      httpGet.setConfig(requestConfig);
      httpGet.addHeader("apikey",getApikey());
      HttpResponse httpResponse = httpClient.execute(httpGet);
      if (httpResponse != null) {
        HttpEntity resEntity = httpResponse.getEntity();
        if (resEntity != null) {
          result = EntityUtils.toString(resEntity, "UTF-8");
        }
        EntityUtils.consume(resEntity);
      }
    } catch (Exception ex) {
      ex.printStackTrace();
    }
    return result;
  }

  static class SSLClient extends DefaultHttpClient {

    public SSLClient() throws Exception {
      super();
      SSLContext ctx = SSLContext.getInstance("TLS");
      X509TrustManager tm = new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain,
            String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain,
            String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
          return null;
        }
      };
      ctx.init(null, new TrustManager[]{tm}, null);
      SSLSocketFactory ssf = new SSLSocketFactory(ctx,
          SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
      ClientConnectionManager ccm = this.getConnectionManager();
      SchemeRegistry sr = ccm.getSchemeRegistry();
      sr.register(new Scheme("https", 443, ssf));
    }
  }

  public static void main(String[] args) throws Exception {
    String str = httpGet("http://**************/inner/market/kline", "cqTyp=0",
        "symbol=300601", "marketCd=2", "klineType=10", "cntChart=50", "fromTime=202109140000",
        "toTime=202109140000");
    System.out.println(str);

    String url = "https://*************/inner/market/kline";
    System.out.println(url.substring(0, 5));
  }
}
