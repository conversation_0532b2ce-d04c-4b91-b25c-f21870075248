package com.stock.service.platform.common.system.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.Page;
import com.stock.core.dto.UserInfo;
import com.stock.core.service.BaseService;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.service.StockService;
import com.stock.service.platform.common.sysCodeConvert.CodeConvert;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.ComBoxDto;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.dto.SaUserDto;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.openapi.dao.OpenCompanyMapper;
import com.stock.service.platform.person.service.PersonService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SaCompanyService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(SaCompanyService.class);
    @Autowired
    private SaCompanyMapper saCompanyMapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private OrgBizMapper orgBizMapper;
    @Autowired
    private OpenCompanyMapper openCompanyMapper;
    @Resource
    private CompanyContactMapper companyContactMapper;
    @Resource
    private CompanyMapper companyMapper;
    @Autowired
    private TopicProducer topicProducer;
    @Autowired
    private SaSystemUtil saSystemUtil;
    @Autowired
    PersonService personService;
    @Resource
    private QuestionnaireService questionnaireService;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Resource
    private StockService stockService;

    @Autowired
    private Environment env;

    @Resource
    private LogService logService;

    public JsonResponse<List<ComBoxDto>> queryBusinessDepartmentOptions(String orgId, String orgType) {
        JsonResponse<List<ComBoxDto>> response = new JsonResponse<>();
        List<ComBoxDto> comboxList = new ArrayList<>();
        OrgExample example = new OrgExample();
        //营业部创建上市公司，只能选择本营业部
        if (orgType != null && "3".equals(orgType)) {
            example.createCriteria().andOrgTypeEqualTo("3").andIdEqualTo(orgId).andStatusEqualTo("1");
        } else if (orgType != null && "2".equals(orgType)) {
            //分公司创建上市公司，可以选择分公司下营业部
            example.createCriteria().andOrgTypeEqualTo("3").andPOrgIdEqualTo(orgId).andStatusEqualTo("1");
        } else if (orgType != null && "1".equals(orgType)) {
            //总部创建上市公司，可以选择所有营业部
            example.createCriteria().andOrgTypeEqualTo("3").andStatusEqualTo("1");
        }
        List<Org> orgs = orgMapper.selectByExample(example);
        Optional.ofNullable(orgs).orElse(new ArrayList<>()).forEach(dto -> {
            ComBoxDto combox = new ComBoxDto();
            combox.setValue(dto.getId());
            combox.setText(dto.getOrgName());
            comboxList.add(combox);
        });
        response.setResult(comboxList);
        return response;
    }

    public JsonResponse<String> deleteCompanyById(String id, String seriakey, UserInfo userInfo, String companyCode) {
        JsonResponse<String> response = new JsonResponse<>();
        //判断提交表单是否被篡改
        if (!StringUtils.isBlank(id)) {
            Map<String, String> checkParam = new HashMap<>();
            checkParam.put("id", id);
            checkParam.put("seriakey", seriakey);
            int checkFlag = saCompanyMapper.selectByIdSeriakey(checkParam);
            if (checkFlag == 0) {
                response.setErrorMsg("修改信息不存在");
                return response;
            }
        }
        //查询上市公司下是否已经配置人员
        List<SaUserDto> userList = saCompanyMapper.queryCompanyUsers(id);
        List<SaCompanyDto> companyDtoList = saCompanyMapper.queryCompanyByParentId(id);
        if (CollectionUtils.isEmpty(userList)) {
            if (CollectionUtils.isEmpty(companyDtoList)){
                SaCompanyDto param = new SaCompanyDto();
                param.setUpdateTime(new Date());
                param.setUpdateUser(userInfo.getUserId());
                SaCompanyDto companyOld = saCompanyMapper.getCompanyById(id);
                saCompanyMapper.updateByPrimaryKeys(id, "2");
                saCompanyMapper.updateSaOrgByPrimaryKeys(id, "2");
                //saCompanyMapper.deleteByPrimaryKeys(id, param);
                //saCompanyMapper.deleteSaOrgByPrimaryKeys(id, param);
                response.setResult("删除成功");
                //向其他微服发送消息
                String uid = UUID.randomUUID().toString();
                Map<String, Object> info = new HashMap<>();
                info.put("id", id);
                info.put("companyCode", companyCode);
                info.put("userId", getUserInfo().getInfo().get("id"));
                try {
                    MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                    send.setUid(uid);
                    send.setSystemType(saSystemUtil.getSysCode());
                    send.setBusinessType(TopicConstant.BUS_TYPE_COMPANY);
                    send.setOperationType(TopicConstant.OP_TYPE_DELETE);
                    send.setInfo(info);
                    saSystemUtil.insertMqMessage(send);
                    topicProducer.sendTopic(info, saSystemUtil.getSysCode(), TopicConstant.BUS_TYPE_COMPANY, TopicConstant.OP_TYPE_DELETE, uid);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                try {
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    HttpServletRequest request = attributes.getRequest();
                    String ip = CommonService.getIp(request);
                    logService.asyncSaveOperationLog(
                            new OperationLogEvent(id,
                                    null, "删除上市公司",
                                    "上市公司管理", "SaCompanyService.deleteCompanyById",
                                    "删除上市公司{" + companyOld.getZhName() + "}", ip, getUserInfo()));
                } catch (Exception e) {
                    logger.info(e.getMessage());
                }
            } else {
                response.setResult("已存在所属子公司,请先进行迁移再删除");
            }
        } else {
            response.setResult("已存在所属用户,请先进行迁移再删除");
        }
        return response;
    }

    public SaCompanyDto getCompanyById(String id) throws IllegalAccessException {
        SaCompanyDto company = saCompanyMapper.getCompanyById(id);
        StripTrailingZerosUtil.convertBigDecimalsToPlainString(company);
        CompanyContactExample example = new CompanyContactExample();
        example.createCriteria().andCompanyCodeEqualTo(company.getCompanyCode());
        List<CompanyContact> companyContacts = companyContactMapper.selectByExample(example);
        companyContacts.forEach(item -> {
            Profiles profiles1 = Profiles.of("dev", "prepub-sz");
            if (!env.acceptsProfiles(profiles1)) {
                item.setContactPhone(questionnaireService.deCodeAes(item.getContactPhone()));
            } else {
                item.setContactPhone(item.getContactPhone());
            }
        });
        company.setContacts(companyContacts);
        return company;
    }

    public JsonResponse<String> createOrUpdate(SaCompanyDto dto, UserInfo userInfo) throws Exception {
        Profiles profiles = Profiles.of("dev","prepub-sz");
        JsonResponse<String> response = new JsonResponse<>();
        //判断提交表单是否被篡改
        if (!StringUtils.isBlank(dto.getId())) {
            Map<String, String> checkParam = new HashMap<>();
            checkParam.put("id", dto.getId());
            checkParam.put("seriakey", dto.getSeriakey());
            int checkFlag = saCompanyMapper.selectByIdSeriakey(checkParam);
            if (checkFlag == 0) {
                response.setErrorMsg("修改信息不存在");
                return response;
            }
        }
        String companyId;
        String companyName;
        if (StringUtils.isBlank(dto.getId())) {
            SaCompanyDto insertCompany = new SaCompanyDto();
            BeanUtils.copyProperties(dto, insertCompany);
            insertCompany.setCompanyName(insertCompany.getZhName());
            insertCompany.setBusinessLicenseNumber(insertCompany.getSocialUnifiedCreditCode());
            insertCompany.setCompanyProperty(insertCompany.getOrgform());
            insertCompany.setCompanyRegisterDate(insertCompany.getRegisterDate());
            insertCompany.setCompanyListingDate(insertCompany.getListingDate());
            insertCompany.setRegisteredCapitalMoney(insertCompany.getRegisterCapital());
            insertCompany.setTotalCapitalStock(insertCompany.getCapitalStockTotal());
            insertCompany.setLegalPersonName(insertCompany.getCorporate());
            insertCompany.setLegalPersonIdNumber(insertCompany.getCorporateIdNumber());
            insertCompany.setBusinessDepartmentStr(insertCompany.getOrganisationNo());
            insertCompany.setCreateDate(new Date());
            insertCompany.setCreateBy(userInfo.getUserId());
            insertCompany.setModifiedDate(new Date());
            insertCompany.setModifiedBy(userInfo.getUserId());
            insertCompany.setCreateTime(new Date());
            insertCompany.setCreateUser(userInfo.getUserId());
            insertCompany.setUpdateTime(new Date());
            insertCompany.setUpdateUser(userInfo.getUserId());
            Map<String, String> subsystemCodes = CodeConvert.convertCode(insertCompany.getMarketStockType(), "marketStockType");
            insertCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));
            if (StringUtils.isEmpty(insertCompany.getCompanyCode())) {
                int codeSort = saCompanyMapper.getChildrenCompanyCode(insertCompany.getParentCompanyCode());
                insertCompany.setCompanyCode(insertCompany.getParentCompanyCode() + String.format("%04d", codeSort));
                dto.setCompanyCode(insertCompany.getCompanyCode());
            }
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(insertCompany);
            saCompanyMapper.insertSelective(insertCompany);
            companyId = insertCompany.getId();
            companyName = insertCompany.getZhName();
            if (StringUtils.isNotEmpty(insertCompany.getParentId())) {
                response.setResult("新增子公司成功");
            } else {
                response.setResult("新增上市公司成功");
            }
            int maxLevel = 0;
            String id = "";
            Map<String, Object> info = new HashMap<>();
            List<Org> orgList = new ArrayList<>();
            List<Org> recordList = new ArrayList<>();
            for (String pOrgId : dto.getOrganisationNo().split(",")) {
                // org表
                Org record = new Org();
                record.setCompanyId(companyId);
                record.setOrgName(companyName);
                record.setOrgType("4");
                record.setpOrgId(pOrgId);
                recordList.add(record);
                if (StringUtils.isNotEmpty(insertCompany.getParentId())) {
                    record.setChildrenCompanyFlag("1");
                } else {
                    record.setChildrenCompanyFlag("0");
                }
                orgMapper.insertSelective(record);
                id = record.getpOrgId();
                maxLevel = orgMapper.selOrgLevel();
                for (int i = 0; i <= maxLevel; i++) {
                    Org org = orgMapper.selParentById(id);
                    if (org == null) {
                        break;
                    }
                    orgList.add(org);
                    if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                        break;
                    }
                    id = org.getpOrgId();
                }
            }

            info.put("saCompany", insertCompany);
            info.put("org", recordList);
            info.put("orgP", orgList);
            info.put("userId", getUserInfo().getInfo().get("id"));
//            // 调用交易数据数据整合
//            if (StringUtils.isNotEmpty(insertCompany.getCompanyCode())) {
//                stockService.stockInfoMergeByCompanyCode(insertCompany.getCompanyCode());
//            }
            // 向其他微服发送消息
            String uid = UUID.randomUUID().toString();
            try {
                String systemType = saSystemUtil.getSysCode();
                if (StringUtils.isNotEmpty(insertCompany.getParentId())) {
                    systemType = "GQJL";
                }
                MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType(systemType);
                send.setBusinessType(TopicConstant.BUS_TYPE_COMPANY);
                send.setOperationType(TopicConstant.OP_TYPE_INSERT);
                send.setInfo(info);
                saSystemUtil.insertMqMessage(send);
                topicProducer.sendTopic(info, systemType, TopicConstant.BUS_TYPE_COMPANY, TopicConstant.OP_TYPE_INSERT, uid);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            //新增操作日志
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(insertCompany),
                                null, "新增上市公司",
                                "上市公司管理", "SaCompanyService.createOrUpdate",
                                "新增上市公司{" + insertCompany.getZhName() + "}", ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        } else {
            SaCompanyDto updateCompany = new SaCompanyDto();
            BeanUtils.copyProperties(dto, updateCompany);
            updateCompany.setCompanyName(updateCompany.getZhName());
            updateCompany.setBusinessLicenseNumber(updateCompany.getSocialUnifiedCreditCode());
            updateCompany.setCompanyProperty(updateCompany.getOrgform());
            updateCompany.setCompanyRegisterDate(updateCompany.getRegisterDate());
            updateCompany.setCompanyListingDate(updateCompany.getListingDate());
            updateCompany.setRegisteredCapitalMoney(updateCompany.getRegisterCapital());
            updateCompany.setTotalCapitalStock(updateCompany.getCapitalStockTotal());
            updateCompany.setLegalPersonName(updateCompany.getCorporate());
            updateCompany.setLegalPersonIdNumber(updateCompany.getCorporateIdNumber());
            updateCompany.setBusinessDepartmentStr(updateCompany.getOrganisationNo());
            updateCompany.setModifiedDate(new Date());
            updateCompany.setModifiedBy(userInfo.getUserId());
            updateCompany.setUpdateTime(new Date());
            updateCompany.setUpdateUser(userInfo.getUserId());
            Map<String, String> subsystemCodes = CodeConvert.convertCode(updateCompany.getMarketStockType(), "marketStockType");
            updateCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));
            companyId = updateCompany.getId();
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(updateCompany);
            SaCompanyDto saCompanyOld = saCompanyMapper.getCompanyById(updateCompany.getId());
            // 增加校验公司代码不能为空，并且不能修改
            if(StringUtils.isEmpty(updateCompany.getCompanyCode())){
                response.setErrorMsg("证券代码不能为空");
                return response;
            }
            if(!saCompanyOld.getCompanyCode().equals(updateCompany.getCompanyCode())){
                response.setErrorMsg("证券代码不允许修改");
                return response;
            }
            saCompanyMapper.updateByPrimaryKeySelective(updateCompany);
            response.setResult("更新上市公司成功");
            companyName = dto.getZhName();
            List<Org> orgList = new ArrayList<>();
            List<Org> recordList = new ArrayList<>();
//            if (StringUtils.isEmpty(updateCompany.getParentId())) {
                OrgExample orgExample = new OrgExample();
                orgExample.createCriteria().andCompanyIdEqualTo(dto.getId());
                orgMapper.deleteByExample(orgExample);
                for (String pOrgId : dto.getOrganisationNo().split(",")) {
                    // org表
                    Org record = new Org();
                    record.setCompanyId(companyId);
                    record.setOrgName(companyName);
                    record.setOrgType("4");
                    record.setpOrgId(pOrgId);
                    if (StringUtils.isNotEmpty(updateCompany.getParentId())) {
                        record.setChildrenCompanyFlag("1");
                    } else {
                        record.setChildrenCompanyFlag("0");
                    }
                    orgMapper.insertSelective(record);
                    recordList.add(record);
                    String id = record.getpOrgId();
                    int maxLevel = orgMapper.selOrgLevel();
                    for (int i = 0; i <= maxLevel; i++) {
                        Org org = orgMapper.selParentById(id);
                        if (org == null) {
                            break;
                        }
                        orgList.add(org);
                        if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                            break;
                        }
                        id = org.getpOrgId();
                    }
                }
//            }
            String ids = recordList.stream().map(Org::getId).collect(Collectors.joining(","));
            // 更新公司的时候把person表的company_code更新
            orgMapper.updatePersonOrg(ids,dto.getCompanyCode());
            // 更新公司的时候把user表的company_id更新
            orgMapper.updateUserOrg(ids,dto.getId());
            //向其他微服发送消息
            Map<String, Object> info = new HashMap<>();
            info.put("saCompany", updateCompany);
            info.put("org", recordList);
            info.put("orgP", orgList);
            info.put("userId", getUserInfo().getInfo().get("id"));
            String uid = UUID.randomUUID().toString();
            try {
                String systemType = saSystemUtil.getSysCode();
                if (StringUtils.isNotEmpty(updateCompany.getParentId())) {
                    systemType = "GQJL";
                }
                MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType(systemType);
                send.setBusinessType(TopicConstant.BUS_TYPE_COMPANY);
                send.setOperationType(TopicConstant.OP_TYPE_UPDATE);
                send.setInfo(info);
                saSystemUtil.insertMqMessage(send);
                topicProducer.sendTopic(info, systemType, TopicConstant.BUS_TYPE_COMPANY, TopicConstant.OP_TYPE_INSERT, uid);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            //修改操作日志
            try {
                MessageUtil messageUtil = new MessageUtil();
                MessageInfo messageInfo = messageUtil.getMessageContext(updateCompany, saCompanyOld);
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(updateCompany),
                                messageInfo.getOperationContext(), "编辑上市公司",
                                "上市公司管理", "SaCompanyService.createOrUpdate",
                                messageInfo.getOperationRemark(), ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        }
        // 修改联系人信息，先删后插
        CompanyContactExample example = new CompanyContactExample();
        example.createCriteria().andCompanyCodeEqualTo(dto.getCompanyCode());
        companyContactMapper.deleteByExample(example);

        if (CollectionUtils.isNotEmpty(dto.getContacts())) {
            dto.getContacts().forEach(item -> {
                item.setCompanyCode(dto.getCompanyCode());
                item.setUpdateTime(new Date());
                item.setUpdateUser(userInfo.getUserId());
                if(!env.acceptsProfiles(profiles)) {
                    // 加密手机号
                    item.setContactPhone(questionnaireService.enCodeAec(item.getContactPhone()));
                } else {
                    item.setContactPhone(item.getContactPhone());
                }
                companyContactMapper.insertSelective(item);
            });
        }
        return response;
    }

    public List<String> queryAllCompany() {
        return saCompanyMapper.selectAllCompany();
    }

    public List<String> queryAllChildCompany() {
        return saCompanyMapper.queryAllChildCompany();
    }

    public List<String> queryCompanyByBranchCode(String orgId) {
        List<String> companyCode = new ArrayList<>();
        List<String> businessDepartmentIds = saCompanyMapper.queryDepartmentIdByBranchCode(orgId);
        for (String id : businessDepartmentIds) {
            companyCode.addAll(saCompanyMapper.queryCompanyByDepartmentId(id));
        }
        return companyCode;
    }

    public List<String> queryCompanyByDepartment(String businessDepartmentId) {
        List<String> companyCode = new ArrayList<>();
        if (StringUtils.isNotEmpty(businessDepartmentId)) {
            String pid = orgBizMapper.checkBranchIfShare(businessDepartmentId);
            if (StringUtils.isNotEmpty(pid)) {
                OrgExample example = new OrgExample();
                example.createCriteria().andStatusEqualTo("1").andPOrgIdEqualTo(pid);
                List<Org> businessDepartments = orgMapper.selectByExample(example);
                if (null != businessDepartments && businessDepartments.size() > 0) {
                    for (Org org : businessDepartments) {
                        companyCode.addAll(saCompanyMapper.queryCompanyByDepartmentId(org.getId()));
                    }
                }
            } else {
                companyCode = saCompanyMapper.queryCompanyByDepartmentId(businessDepartmentId);
            }
        }
        return companyCode;
    }

    public String checkCompanyCodeConvert(String id, String companyCode) {
        try {
            if (StringUtils.isNotEmpty(companyCode)) {
                // 公司代码不是空
                // 查询旧代码在 北交所代码转换表 是否已刷新
                String oldCompanyCode = saCompanyMapper.selectOldCompanyCode(companyCode);
                if (StringUtils.isEmpty(oldCompanyCode)) {
                    return "";
                } else {
                    return "旧证券代码已失效，请使用变更后的证券代码";
                }
            }
            return "";
        } catch (Exception e) {
            logger.error("Error occurred while checking company code convert", e);
            return "系统出现异常，请稍后重试";
        }
    }

    public long checkBusDepCode(String id, String companyCode) {
        CompanyExample example = new CompanyExample();
        if (StringUtils.isNotEmpty(companyCode)) {
            if (StringUtils.isEmpty(id)) {
                // 新增
                example.createCriteria().andCompanyCodeEqualTo(companyCode);
            } else {
                // 更新
                example.createCriteria().andCompanyCodeEqualTo(companyCode).andIdNotEqualTo(id);
            }
            return companyMapper.countByExample(example);
        } else {
            return 0;
        }
    }

    public SaCompanyDto getCompanyByCode(String companyCode) {
        return saCompanyMapper.getCompanyByCode(companyCode);
    }

    public SaCompanyDto getCompanyInfoByCode(String companyCode) throws IllegalAccessException {
        SaCompanyDto saCompany = openCompanyMapper.getCompanyByCodePlate(companyCode);
        if (ObjectUtils.isEmpty(saCompany)) {
            saCompany = new SaCompanyDto();
            saCompany.setMarketStockType("99");
        }
        StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompany);
        return saCompany;
    }

    private void listToTree (List<SaCompanyDto> list, List<SaCompanyDto> companyChildList) throws IllegalAccessException {
        List<SaCompanyDto> saCompanyDtoList = new ArrayList<>();
        for (SaCompanyDto saCompanyDto : list) {
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompanyDto);
            saCompanyDtoList = companyChildList.stream().filter(a -> StringUtils.equals(a.getParentId(),saCompanyDto.getId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(saCompanyDtoList)) {
                saCompanyDto.setChildren(saCompanyDtoList);
                listToTree(saCompanyDtoList, companyChildList);
            }
        }
    }
    public long checkSocialUnifiedCreditCode(String id, String socialUnifiedCreditCode) {
        CompanyExample example = new CompanyExample();
        if (StringUtils.isEmpty(id)) {
            // 新增
            example.createCriteria().andSocialUnifiedCreditCodeEqualTo(socialUnifiedCreditCode);
        } else {
            // 更新
            example.createCriteria().andSocialUnifiedCreditCodeEqualTo(socialUnifiedCreditCode).andIdNotEqualTo(id);
        }
        return companyMapper.countByExample(example);
    }

    public Map<String, Object> queryPagingCompanyBySelective(SaCompanyDto dto) throws IllegalAccessException {
        dto.setStartRow((dto.getStartRow()-1)* dto.getPageSize());
        if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("isAdmin"),"1") || StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"1")) {
            // 超级管理员或者总部用户
            dto.setOrgIds(null);
        } else if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"2")) {
            // 分公司用户
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andStatusEqualTo("1").andOrgTypeLessThanOrEqualTo("3");
            List<Org> orgList = orgMapper.selectByExample(orgExample);
            List<Org> subsets = findSubsetsById(orgList, Arrays.asList(getUserInfo().getInfo().get("orgId").toString().split(",")), "3");
            List<String> orgIds = subsets.stream().map(Org::getId).collect(Collectors.toList());
            dto.setOrgIds(orgIds);
        } else if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"3")) {
            // 营业部用户
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andStatusEqualTo("1").andOrgTypeLessThanOrEqualTo("3");
            List<Org> orgList = orgMapper.selectByExample(orgExample);
            List<String> orgIds = new ArrayList<>(Arrays.asList(getUserInfo().getInfo().get("orgId").toString().split(",")));
            List<Org> orgListByOrgId = orgMapper.selectBySonIds(orgIds);
            for (Org org : orgListByOrgId) {
                // 判断该营业部所属分公司是否同意营业部互相查看
                if ("1".equals(org.getThresholdFlag())) {
                    List<Org> subsets = findSubsetsById(orgList, Arrays.asList(org.getId().split(",")), "3");
                    List<String> orgIdList = subsets.stream().map(Org::getId).collect(Collectors.toList());
                    orgIds.addAll(orgIdList);
                }
            }
            dto.setOrgIds(orgIds);
        }
        Page<SaCompanyDto> list = this.getPageList(dto.getStartRow(), dto.getPageSize(),
                "com.stock.service.platform.common.system.dao.SaCompanyMapper.queryPagingCompanyBySelective", dto);
        List<SaCompanyDto> companyList = list.getData();
        List<String> parentCompanyCodeList = companyList.stream()
                .map(SaCompanyDto::getCompanyCode) // 使用方法引用来获取parentCompanyCode
                .collect(Collectors.toList()); // 收集结果到一个新的List中
        List<SaCompanyDto> companyChildList = saCompanyMapper.getCompanyList(parentCompanyCodeList);
        if (CollectionUtils.isNotEmpty(companyChildList)) listToTree(companyList, companyChildList);
        Map<String, Object> result = new HashMap<>();
        result.put("tableData", companyList);
        result.put("total", list.getTotal());
        result.put("userOrgType", getUserInfo().getInfo().get("orgType"));
        return result;
    }

    public List<Org> findSubsetsById(List<Org> orgList, List<String> parentIds) {
        List<Org> subsets = new ArrayList<>();
        for (String parentId : parentIds) {
            subsets.addAll(findSubsetsById(orgList, parentId));
        }
        return subsets;
    }

    private List<Org> findSubsetsById(List<Org> orgList, String parentId) {
        List<Org> subsets = new ArrayList<>();
        for (Org org : orgList) {
            if (org.getpOrgId() != null && org.getpOrgId().equals(parentId)) {
                subsets.add(org);
                subsets.addAll(findSubsetsById(orgList, org.getId()));
            }
        }
        return subsets;
    }

    public List<Org> findSubsetsById(List<Org> orgList, List<String> parentIds, String orgType) {
        List<Org> subsets = findSubsetsById(orgList, parentIds);
        subsets.removeIf(org -> !org.getOrgType().equals(orgType));
        return subsets;
    }

    public Object queryTableData(Map<String, Object> param) {
        SystemConfigExample example = new SystemConfigExample();
        example.createCriteria().andSysCodeIsNotNull();
        return systemConfigMapper.selectByExample(example);
    }

    public static String getBeforeComma(String str) {
        if (str == null || !str.contains(",")) {
            return str; // 如果字符串为空或不含逗号，则返回整个字符串
        }
        int index = str.indexOf(","); // 获取第一个逗号的位置
        if (index != -1) {
            return str.substring(0, index); // 返回逗号前的子字符串
        }
        return str; // 如果没有找到逗号，返回整个字符串
    }
}
