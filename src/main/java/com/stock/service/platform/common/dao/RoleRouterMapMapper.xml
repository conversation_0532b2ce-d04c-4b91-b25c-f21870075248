<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.RoleRouterMapMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.RoleRouterMap">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="router_id" jdbcType="VARCHAR" property="routerId" />
    <result column="authority_flag" jdbcType="VARCHAR" property="authorityFlag" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, role_id, router_id, authority_flag, update_user, update_time
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.RoleRouterMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_role_router_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_role_router_map
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_role_router_map
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.RoleRouterMapExample">
    delete from sa_role_router_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.RoleRouterMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_role_router_map (id, role_id, router_id, 
      authority_flag, update_user, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, #{routerId,jdbcType=VARCHAR}, 
      #{authorityFlag,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.RoleRouterMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_role_router_map
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="roleId != null">
        role_id,
      </if>
      <if test="routerId != null">
        router_id,
      </if>
      <if test="authorityFlag != null">
        authority_flag,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="routerId != null">
        #{routerId,jdbcType=VARCHAR},
      </if>
      <if test="authorityFlag != null">
        #{authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.RoleRouterMapExample" resultType="java.lang.Long">
    select count(*) from sa_role_router_map
    <if test="_parameter != null">

    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_role_router_map
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.routerId != null">
        router_id = #{record.routerId,jdbcType=VARCHAR},
      </if>
      <if test="record.authorityFlag != null">
        authority_flag = #{record.authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_role_router_map
    set id = #{record.id,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      router_id = #{record.routerId,jdbcType=VARCHAR},
      authority_flag = #{record.authorityFlag,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.RoleRouterMap">
    update sa_role_router_map
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="routerId != null">
        router_id = #{routerId,jdbcType=VARCHAR},
      </if>
      <if test="authorityFlag != null">
        authority_flag = #{authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.RoleRouterMap">
    update sa_role_router_map
    set role_id = #{roleId,jdbcType=VARCHAR},
      router_id = #{routerId,jdbcType=VARCHAR},
      authority_flag = #{authorityFlag,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.RoleRouterMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_role_router_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
    <select id="getRouterIdListByRoleId" resultType="java.lang.String">
      SELECT sr.router_des
      FROM sa_role_router_map srrm
      LEFT JOIN sa_router sr ON sr.id = srrm.router_id
      WHERE role_id = #{roleId,jdbcType=VARCHAR}
      order by sr.router_des
    </select>
  <select id="getRouterDesByRouterIdList" resultType="java.lang.String">
    SELECT sr.router_des
    FROM sa_router sr
    <where>
      <if test="routerIdList != null">
        sr.id IN
        <foreach item="item" index="index" collection="routerIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by sr.router_des
  </select>
</mapper>