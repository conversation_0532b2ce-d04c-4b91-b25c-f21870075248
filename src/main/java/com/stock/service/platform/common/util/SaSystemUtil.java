package com.stock.service.platform.common.util;

import com.alibaba.druid.support.json.JSONUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stock.service.platform.common.dao.ActiveMqMapper;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SaSystemUtil {

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Resource
    private ActiveMqMapper activeMqMapper;

    public String getSysCode(){
        SystemConfigExample systemConfigExample = new SystemConfigExample();
        systemConfigExample.createCriteria();
        List<SystemConfig> systemConfigs = systemConfigMapper.selectByExample(systemConfigExample);
        String systemCode = "";
        for (SystemConfig systemConfig : systemConfigs) {
            systemCode += systemConfig.getSysCode()+",";
        }
        return systemCode.substring(0, systemCode.length() - 1);
    }

    public void insertMqMessage(MQMsgDto message){
        ObjectMapper objectMapper = new ObjectMapper();
        for (String s : message.getSystemType().split(",")) {
            try {
            Map<String,String> param = new HashMap<>();
            param.put("messageId", message.getUid());
            param.put("sysCode", s);
            param.put("info", objectMapper.writeValueAsString(message.getInfo()));
            // 0失败，1成功，2已发送
            param.put("isSuccess","2");
            param.put("businessType",message.getBusinessType());
            param.put("operationType", message.getOperationType());
            activeMqMapper.insertActivemqTopic(param);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
