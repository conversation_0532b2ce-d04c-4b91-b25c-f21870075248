<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.LogMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Log">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="log_type" jdbcType="VARCHAR" property="logType" />
    <result column="log_time" jdbcType="TIMESTAMP" property="logTime" />
    <result column="log_name" jdbcType="VARCHAR" property="logName" />
    <result column="log_content" jdbcType="VARCHAR" property="logContent" />
    <result column="log_ip" jdbcType="VARCHAR" property="logIp" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, log_type, log_time, log_name, log_content, log_ip, create_user, create_time, 
    update_user, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.LogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.LogExample">
    delete from sa_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Log">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_log (id, log_type, log_time, 
      log_name, log_content, log_ip, 
      create_user, create_time, update_user, 
      update_time, status)
    values (#{id,jdbcType=VARCHAR}, #{logType,jdbcType=VARCHAR}, #{logTime,jdbcType=TIMESTAMP}, 
      #{logName,jdbcType=VARCHAR}, #{logContent,jdbcType=VARCHAR}, #{logIp,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Log">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="logType != null">
        log_type,
      </if>
      <if test="logTime != null">
        log_time,
      </if>
      <if test="logName != null">
        log_name,
      </if>
      <if test="logContent != null">
        log_content,
      </if>
      <if test="logIp != null">
        log_ip,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="logType != null">
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logTime != null">
        #{logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logName != null">
        #{logName,jdbcType=VARCHAR},
      </if>
      <if test="logContent != null">
        #{logContent,jdbcType=VARCHAR},
      </if>
      <if test="logIp != null">
        #{logIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.LogExample" resultType="java.lang.Long">
    select count(*) from sa_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.logType != null">
        log_type = #{record.logType,jdbcType=VARCHAR},
      </if>
      <if test="record.logTime != null">
        log_time = #{record.logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.logName != null">
        log_name = #{record.logName,jdbcType=VARCHAR},
      </if>
      <if test="record.logContent != null">
        log_content = #{record.logContent,jdbcType=VARCHAR},
      </if>
      <if test="record.logIp != null">
        log_ip = #{record.logIp,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_log
    set id = #{record.id,jdbcType=VARCHAR},
      log_type = #{record.logType,jdbcType=VARCHAR},
      log_time = #{record.logTime,jdbcType=TIMESTAMP},
      log_name = #{record.logName,jdbcType=VARCHAR},
      log_content = #{record.logContent,jdbcType=VARCHAR},
      log_ip = #{record.logIp,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Log">
    update sa_log
    <set>
      <if test="logType != null">
        log_type = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logTime != null">
        log_time = #{logTime,jdbcType=TIMESTAMP},
      </if>
      <if test="logName != null">
        log_name = #{logName,jdbcType=VARCHAR},
      </if>
      <if test="logContent != null">
        log_content = #{logContent,jdbcType=VARCHAR},
      </if>
      <if test="logIp != null">
        log_ip = #{logIp,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Log">
    update sa_log
    set log_type = #{logType,jdbcType=VARCHAR},
      log_time = #{logTime,jdbcType=TIMESTAMP},
      log_name = #{logName,jdbcType=VARCHAR},
      log_content = #{logContent,jdbcType=VARCHAR},
      log_ip = #{logIp,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.LogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>