package com.stock.service.platform.common.service;

import com.stock.core.service.BaseService;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.SystemConfigConstant;
import com.stock.service.platform.common.dto.RouterDto;
import com.stock.service.platform.common.entity.SystemConfig;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class SystemConfigService extends BaseService {

    @Autowired
    CommonService commonService;

    public String replaceDynamicParam(HttpServletRequest request, String url) {
        if (!url.contains("?")) { return url; }
        StringBuilder result;
        try {
            result = new StringBuilder(url.substring(0, url.indexOf("?")));
            Map<String, String> map = getParamsInURL(url);
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StringUtils.isNotEmpty(value)) {
                    // 参数中值等于标识说明为动态参数
                    if (SystemConfigConstant.DYNAMIC_PARAM_SIGN.equals(value)) {
                        // 参数中值包含${说明为动态参数
                        String realValue = this.getDynamicParamValue(request, key);
                        if (StringUtils.isEmpty(realValue)) {
                            realValue = SystemConfigConstant.UNEXPECTED;
                        }
                        result.append("&").append(URLEncoder.encode(key, "UTF-8")).append("=").append(URLEncoder.encode(realValue, "UTF-8"));
                    } else {
                        // 参数中值不包含为手动设置的参数
                        result.append("&").append(URLEncoder.encode(key, "UTF-8")).append("=").append(URLEncoder.encode(value, "UTF-8"));
                    }
                }
            }
        }catch (Exception e) {
            result = new StringBuilder(url);
            e.printStackTrace();
        }
        // 将字符串中第一个&替换为?
        if (StringUtils.isNotEmpty(result.toString())) {
            return result.toString().replaceFirst("&", "?");
        }
        return result.toString();
    }

    /**
     * 获取url中参数的map
     * @param url 必须保证url未被编码
     * @return
     * @throws Exception
     */
    public static Map<String, String> getParamsInURL(String url) throws Exception {
        Map<String, String> result = new HashMap<>();
        URI uri = new URI(url);
        String query = uri.getQuery();
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            String key = idx > 0 ? pair.substring(0, idx) : pair;
            String value = idx > 0 && pair.length() > idx + 1 ? pair.substring(idx + 1) : null;
            result.put(key, value);
        }
        return result;
    }

    /**
     * 获取动态参数值
     * @param request
     * @param key
     * @return
     */
    public String getDynamicParamValue(HttpServletRequest request, String key) {
        // 判断是否可获取
        if(SystemConfigConstant.SYS_CONFIG_API_DYNAMIC_PARAM_CONTENT.contains(key)) {
            // 此部分方法需要根据不同券商公司进行自定义，因为可能每家券商能获取的动态参数不同
            if("companyCode".equals(key)) {
                return getUserInfo().getCompanyCode();
            }else if("userId".equals(key)) {
                return getUserInfo().getUserId();
            }else if("username".equals(key)) {
                return getUserInfo().getUsername();
            }else if("token".equals(key)) {
                return request.getHeader(HttpHeaders.AUTHORIZATION);
            } else if("access_token".equals(key)) {
                return request.getHeader(HttpHeaders.AUTHORIZATION);
            }
        }
        return null;
    }

    /**
     * 获取当前环境对应的配置
     * @param config
     * @return
     */
    public Map<String, Object> getCurrentEnvironmentConfig(SystemConfig config) {
        Map<String, Object> map = JsonUtil.fromJson(config.getSysConfig(), HashMap.class);
        String curEnv = commonService.getCurrentEnvironment();
        Map<String, Object> result = null;
        if(map.get(curEnv) != null) {
            result = (Map<String, Object>) map.get(curEnv);
        }
        return result;
    }

    public RouterDto getOpenedSystemRouter(HttpServletRequest request, SystemConfig config, int type) {
        if(StringUtils.isNotEmpty(config.getSysConfig())) {
            // 获取当前环境对应的配置
            Map<String, Object> configMap = this.getCurrentEnvironmentConfig(config);
            String url;
            if (type > 3) {
                url = MapUtils.getString(configMap, SystemConfigConstant.SYS_CONFIG_API_LOGIN_NAME_OUT);
            } else {
                url = MapUtils.getString(configMap, SystemConfigConstant.SYS_CONFIG_API_LOGIN_NAME);
            }
            if(StringUtils.isNotEmpty(url)) {
                String id = UUID.randomUUID().toString();
                RouterDto router = new RouterDto();
                router.setId(id);
                router.setMenuDes(config.getSysName());
                router.setMenuList(new ArrayList<>());
                router.setMenuName(config.getSysCode());
                router.setMenuType(CodeTableConstant.DATA_STATUS_0);
                router.setSortNo(config.getSysOrder());
                router.setMeta("{\"title\":\"" + config.getSysName() + "\",\"id\":\"" + id + "\"}");
                Map<String, Object> metaData = new HashMap<>();
                metaData.put("id", id);
                metaData.put("title", config.getSysName());
                router.setMetaData(metaData);
                // url中可能包含动态参数，将动态参数替换
                url = this.replaceDynamicParam(request, url);
                router.setPath(url);
                return router;
            }
        }
        return null;
    }


}
