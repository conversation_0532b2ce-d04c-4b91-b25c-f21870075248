package com.stock.service.platform.common.log;

import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.Subscribe;
import com.stock.service.platform.common.entity.SaOperationLogWithBLOBs;
import com.stock.service.platform.common.service.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class OperationLogListener {

  @Autowired
  private AsyncEventBus asyncEventBus;

  @Resource
  private LogService logService;

  @PostConstruct
  public void register() {
    asyncEventBus.register(this);
  }

  @AllowConcurrentEvents//线程安全
  @Subscribe
  public void saveOperationLog(OperationLogEvent operationLogEvent) throws ClassNotFoundException {
    // 日志入库
    SaOperationLogWithBLOBs insertOperationLogParam = new SaOperationLogWithBLOBs();
    insertOperationLogParam.setParameterJson(operationLogEvent.getParameter());
    insertOperationLogParam.setResultJson(operationLogEvent.getResult());
    insertOperationLogParam.setOperationTypeDetail(operationLogEvent.getOperationTypeDetail());
    insertOperationLogParam.setRouter(operationLogEvent.getRouter());
    insertOperationLogParam.setMethod(operationLogEvent.getMethod());
    insertOperationLogParam.setRemark(operationLogEvent.getRemark());
    insertOperationLogParam.setIp(operationLogEvent.getIp());
    logService.createOperationLog(insertOperationLogParam, operationLogEvent.getUserInfo());
  }
}
