package com.stock.service.platform.common.dto;

import com.stock.core.util.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: (这里用一句话描述这个类的作用)
 * @author: xiazhengwei
 * @date:
 */
@Data
public class RouterDto implements Serializable {

    private static final long serialVersionUID = -5885346264620142023L;

    private String id;
    private String pMenuId;
    private String resourceId;
    private String menuName;
    private String menuDes;
    private String menuType;
    private Integer menuFolderFlag;
    private Integer sortNo;
    private String menuIcon;
    private String meta;
    private String component;
    private String path;
    private String redirect;
    private String userId;
    private String authorityFlag;
    private String para;

    private String systemType;

    /**
     * 菜单元数据
     */
    private Map<String, Object> metaData;

    /**
     * 菜单集合
     */
    private List<RouterDto> menuList;

    public Map<String, Object> getMetaData() {
        if(StringUtils.isNotBlank(this.meta)) {
            return JsonUtil.fromJson(this.meta, Map.class);
        }else {
            return null;
        }
    }

    public void setMetaData(Map<String, Object> metaData) {
        this.metaData = metaData;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpMenuId() {
        return pMenuId;
    }

    public void setpMenuId(String pMenuId) {
        this.pMenuId = pMenuId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public Integer getMenuFolderFlag() {
        return menuFolderFlag;
    }

    public void setMenuFolderFlag(Integer menuFolderFlag) {
        this.menuFolderFlag = menuFolderFlag;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public String getMenuIcon() {
        return menuIcon;
    }

    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public List<RouterDto> getMenuList() {
        return menuList;
    }

    public void setMenuList(List<RouterDto> menuList) {
        this.menuList = menuList;
    }

    public String getMenuDes() {
        return menuDes;
    }

    public void setMenuDes(String menuDes) {
        this.menuDes = menuDes;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAuthorityFlag() {
        return authorityFlag;
    }

    public void setAuthorityFlag(String authorityFlag) {
        this.authorityFlag = authorityFlag;
    }

    public String getPara() {
        return para;
    }

    public void setPara(String para) {
        this.para = para;
    }
}
