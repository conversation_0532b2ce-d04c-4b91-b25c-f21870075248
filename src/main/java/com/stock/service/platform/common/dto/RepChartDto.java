package com.stock.service.platform.common.dto;

import java.io.Serializable;

public class RepChartDto implements Serializable {


    private String begTransactionDate;//年初的第一个交易日

    private String nowTransactionDate;//当前日期前的交易日

    private String year;

    private String[] data;

    private String unifyPurpose;

    public void setData(String[] data) {
        this.data = data;
    }

    public String[] getData() {
        return data;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getUnifyPurpose() {
        return unifyPurpose;
    }

    public void setUnifyPurpose(String unifyPurpose) {
        this.unifyPurpose = unifyPurpose;
    }

    public String getBegTransactionDate() {
        return begTransactionDate;
    }

    public void setBegTransactionDate(String begTransactionDate) {
        this.begTransactionDate = begTransactionDate;
    }

    public String getNowTransactionDate() {
        return nowTransactionDate;
    }

    public void setNowTransactionDate(String nowTransactionDate) {
        this.nowTransactionDate = nowTransactionDate;
    }

}
