package com.stock.service.platform.common.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.ParameterMapper;
import com.stock.service.platform.common.entity.Parameter;
import com.stock.service.platform.common.entity.ParameterExample;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 短信发送服务
 */
@Service
public class MessageService extends BaseService {
//    @Value("${broker.message.url}")
//    private String MSG_URL;
//
//    @Value("${broker.message.corpId}")
//    private String CORP_ID;
//
//    @Value("${broker.message.corpPwd}")
//    private String CORP_PWD;
//
//    @Value("${broker.message.corpService}")
//    private String CORP_SERVICE;

    @Autowired
    private ParameterMapper parameterMapper;

    private static Logger logger = LoggerFactory.getLogger(MessageService.class);

    /**
     * 发送短信接口调用
     * @param mobile
     * @param content
     * @param msgId
     * @param msgExt
     * @return
     */
    public Map<String, Object> sendMessage(String mobile, String content, String msgId, String msgExt) {
//        if(this.getSendMsgFlag()) {
            Map<String, Object> map = new HashMap<>();
//            String[] msgUrlArr = MSG_URL.split(",");
//            HttpClient client = new HttpClient();
//            client.getHttpConnectionManager().getParams().setConnectionTimeout(5 * 1000);
//            // "http://IP:8080/sms_send2.do"
//            NameValuePair[] data = {
//                    new NameValuePair("corp_id", CORP_ID),
//                    new NameValuePair("corp_pwd", CORP_PWD),
//                    new NameValuePair("corp_service",CORP_SERVICE),
//                    new NameValuePair("mobile",mobile),
//                    new NameValuePair("msg_content",content),
//                    new NameValuePair("corp_msg_id ",msgId),
//                    new NameValuePair("ext ",msgExt)
//            };
//            try {
//                PostMethod post = new PostMethod(msgUrlArr[0]);
//                post.addRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=utf-8");//在头文件中设置转码
//                post.setRequestBody(data);
//                client.executeMethod(post);
//                /*org.apache.commons.httpclient.Header[] headers = post.getResponseHeaders();
//                for(org.apache.commons.httpclient.Header h : headers){
//                    System.out.println(h.toString());
//                }*/
//                int statusCode = post.getStatusCode();
//                map.put("statusCode", statusCode);
//                map.put("resultStr", post.getResponseBodyAsString());
//                post.releaseConnection();
//                return map;
//            } catch (IOException e) {
//                if(msgUrlArr.length > 1) {
//                    try {
//                        PostMethod post = new PostMethod(msgUrlArr[1]);
//                        post.addRequestHeader("Content-Type","application/x-www-form-urlencoded;charset=utf-8");//在头文件中设置转码
//                        post.setRequestBody(data);
//                        int statusCode = post.getStatusCode();
//                        map.put("statusCode", statusCode);
//                        map.put("resultStr", post.getResponseBodyAsString());
//                        post.releaseConnection();
//                        return map;
//                    } catch (IOException e1) {
//                        logger.error("调用短信接口1报错，原因：{}", e1.getMessage());
//                    }
//                }else {
//                    logger.error("调用短信接口报错，原因：{}", e.getMessage());
//                }
//            }
//        }else {
//            logger.error("发送短信取消，原因：短信开关未定义或未打开");
//        }

        return new HashMap<>();
    }

    public Map<String, Object> sendMessage(String mobile, String content) {
        return this.sendMessage(mobile, content, "", "");
    }

    public Map<String, Object> sendMailMessage(String mail, String content) {
        Map<String, Object> map = new HashMap<>();
        map.put("mail",mail);
        map.put("content",content);
        return map;
    }
}