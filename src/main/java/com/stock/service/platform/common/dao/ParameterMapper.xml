<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ParameterMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Parameter">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="param_code" jdbcType="VARCHAR" property="paramCode" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_content" jdbcType="VARCHAR" property="paramContent" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, param_code, param_desc, param_value, param_name, param_content, create_user, 
    create_time, update_user, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.ParameterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_parameter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_parameter
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_parameter
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.ParameterExample">
    delete from sa_parameter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Parameter">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_parameter (id, param_code, param_desc, 
      param_value, param_name, param_content, 
      create_user, create_time, update_user, 
      update_time, status)
    values (#{id,jdbcType=VARCHAR}, #{paramCode,jdbcType=VARCHAR}, #{paramDesc,jdbcType=VARCHAR}, 
      #{paramValue,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramContent,jdbcType=VARCHAR}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Parameter">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_parameter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="paramCode != null">
        param_code,
      </if>
      <if test="paramDesc != null">
        param_desc,
      </if>
      <if test="paramValue != null">
        param_value,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramContent != null">
        param_content,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="paramCode != null">
        #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null">
        #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramContent != null">
        #{paramContent,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.ParameterExample" resultType="java.lang.Long">
    select count(*) from sa_parameter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_parameter
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.paramCode != null">
        param_code = #{record.paramCode,jdbcType=VARCHAR},
      </if>
      <if test="record.paramDesc != null">
        param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.paramValue != null">
        param_value = #{record.paramValue,jdbcType=VARCHAR},
      </if>
      <if test="record.paramName != null">
        param_name = #{record.paramName,jdbcType=VARCHAR},
      </if>
      <if test="record.paramContent != null">
        param_content = #{record.paramContent,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_parameter
    set id = #{record.id,jdbcType=VARCHAR},
      param_code = #{record.paramCode,jdbcType=VARCHAR},
      param_desc = #{record.paramDesc,jdbcType=VARCHAR},
      param_value = #{record.paramValue,jdbcType=VARCHAR},
      param_name = #{record.paramName,jdbcType=VARCHAR},
      param_content = #{record.paramContent,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Parameter">
    update sa_parameter
    <set>
      <if test="paramCode != null">
        param_code = #{paramCode,jdbcType=VARCHAR},
      </if>
      <if test="paramDesc != null">
        param_desc = #{paramDesc,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null">
        param_value = #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        param_name = #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramContent != null">
        param_content = #{paramContent,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Parameter">
    update sa_parameter
    set param_code = #{paramCode,jdbcType=VARCHAR},
      param_desc = #{paramDesc,jdbcType=VARCHAR},
      param_value = #{paramValue,jdbcType=VARCHAR},
      param_name = #{paramName,jdbcType=VARCHAR},
      param_content = #{paramContent,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.ParameterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_parameter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>