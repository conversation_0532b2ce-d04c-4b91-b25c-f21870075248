package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class PersonWithBLOBs extends Person implements Serializable {
    private String resume;

    private String remark;

    private String searchString;
    private String userLockStatus;
    private String userAvailableStatus;
    private String userName;
    private String orgNames;
    private String orgLimitList;
    private List<Map<String,String>> personIds;
    private List<Map<String,String>> personIdSeriakeys;
    private String orgType;
    private String userType;
    private String isMgr;
    private String seriakey;
    private List<String> orgIds;

    private static final long serialVersionUID = 1L;

    public String getSearchString() {
        return searchString;
    }

    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }

    public String getUserLockStatus() {
        return userLockStatus;
    }

    public void setUserLockStatus(String userLockStatus) {
        this.userLockStatus = userLockStatus;
    }

    public String getUserAvailableStatus() {
        return userAvailableStatus;
    }

    public void setUserAvailableStatus(String userAvailableStatus) {
        this.userAvailableStatus = userAvailableStatus;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getOrgLimitList() {
        return orgLimitList;
    }

    public void setOrgLimitList(String orgLimitList) {
        this.orgLimitList = orgLimitList;
    }

    public List<Map<String, String>> getPersonIds() {
        return personIds;
    }

    public void setPersonIds(List<Map<String, String>> personIds) {
        this.personIds = personIds;
    }

    public List<Map<String, String>> getPersonIdSeriakeys() {
        return personIdSeriakeys;
    }

    public void setPersonIdSeriakeys(List<Map<String, String>> personIdSeriakeys) {
        this.personIdSeriakeys = personIdSeriakeys;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getIsMgr() {
        return isMgr;
    }

    public void setIsMgr(String isMgr) {
        this.isMgr = isMgr;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public String getResume() {
        return resume;
    }

    public void setResume(String resume) {
        this.resume = resume == null ? null : resume.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}