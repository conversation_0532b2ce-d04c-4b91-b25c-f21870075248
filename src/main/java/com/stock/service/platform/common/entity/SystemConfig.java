package com.stock.service.platform.common.entity;

import com.stock.service.platform.common.util.Message;

import java.io.Serializable;
import java.util.Date;

public class SystemConfig implements Serializable {
    private String id;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String sysCode;

    @Message(title = "系统名称", primary = "true")
    private String sysName;

    private Integer sysOrder;

    @Message(title = "内外网跳转路径", primary = "true")
    private String sysConfig;

    private String status;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode == null ? null : sysCode.trim();
    }

    public String getSysName() {
        return sysName;
    }

    public void setSysName(String sysName) {
        this.sysName = sysName == null ? null : sysName.trim();
    }

    public Integer getSysOrder() {
        return sysOrder;
    }

    public void setSysOrder(Integer sysOrder) {
        this.sysOrder = sysOrder;
    }

    public String getSysConfig() {
        return sysConfig;
    }

    public void setSysConfig(String sysConfig) {
        this.sysConfig = sysConfig == null ? null : sysConfig.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}