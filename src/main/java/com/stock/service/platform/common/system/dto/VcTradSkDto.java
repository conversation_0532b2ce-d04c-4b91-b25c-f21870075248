package com.stock.service.platform.common.system.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class VcTradSkDto {

    private String id;

    public String idSource;

    public String securityCd;

    public String securityMm;

    public String tradeDate;

    public Double lclose;

    public Double  NEW;

    public String today;// 今日

    public String companyCode;// 公司代码

    public int indexType;//0目的及方式查板块 1价格及规模

    public Double newAvg;

    public Double CHG;

    public String ipoDate;

    public String belongsPlate;//bankuai

    public String zhSortName;

    public String zhName;

    private BigDecimal repStartDateNum;
    private BigDecimal repDateNum;
    private BigDecimal repEndDateNum;
    private BigDecimal ZSZ;
    private BigDecimal shareNum;
    private BigDecimal totalShares;
    private BigDecimal ffShares;
    private BigDecimal asharer;
    private String xData; //是否显示报告走势横坐标

    private BigDecimal TVALCNY;

    private BigDecimal CJL;

    public BigDecimal getTotalShares() {
        return totalShares;
    }

    public void setTotalShares(BigDecimal totalShares) {
        this.totalShares = totalShares;
    }

    public BigDecimal getFfShares() {
        return ffShares;
    }

    public void setFfShares(BigDecimal ffShares) {
        this.ffShares = ffShares;
    }

    public BigDecimal getAsharer() {
        return asharer;
    }

    public void setAsharer(BigDecimal asharer) {
        this.asharer = asharer;
    }

    public BigDecimal getShareNum() {
        return shareNum;
    }

    public void setShareNum(BigDecimal shareNum) {
        this.shareNum = shareNum;
        this.shareNum = shareNum;
    }

    public BigDecimal getZSZ() {
        return ZSZ;
    }

    public void setZSZ(BigDecimal ZSZ) {
        this.ZSZ = ZSZ;
    }

    public BigDecimal getRepStartDateNum() {
        return repStartDateNum;
    }

    public void setRepStartDateNum(BigDecimal repStartDateNum) {
        this.repStartDateNum = repStartDateNum;
    }

    public BigDecimal getRepDateNum() {
        return repDateNum;
    }

    public void setRepDateNum(BigDecimal repDateNum) {
        this.repDateNum = repDateNum;
    }

    public BigDecimal getRepEndDateNum() {
        return repEndDateNum;
    }

    public void setRepEndDateNum(BigDecimal repEndDateNum) {
        this.repEndDateNum = repEndDateNum;
    }

    public int getIndexType() {
        return indexType;
    }

    public void setIndexType(int indexType) {
        this.indexType = indexType;
    }

    public String getToday() {
        return today;
    }

    public void setToday(String today) {
        this.today = today;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdSource() {
        return idSource;
    }

    public void setIdSource(String idSource) {
        this.idSource = idSource;
    }

    public String getSecurityCd() {
        return securityCd;
    }

    public void setSecurityCd(String securityCd) {
        this.securityCd = securityCd;
    }

    public String getSecurityMm() {
        return securityMm;
    }

    public void setSecurityMm(String securityMm) {
        this.securityMm = securityMm;
    }

    public String getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(String tradeDate) {
        this.tradeDate = tradeDate;
    }

    public Double getLclose() {
        return lclose;
    }

    public void setLclose(Double lclose) {
        this.lclose = lclose;
    }

    public Double getNEW() {
        return NEW;
    }

    public void setNEW(Double NEW) {
        this.NEW = NEW;
    }

    public Double getNewAvg() {
        return newAvg;
    }

    public void setNewAvg(Double newAvg) {
        this.newAvg = newAvg;
    }

    public Double getCHG() {
        return CHG;
    }

    public void setCHG(Double CHG) {
        this.CHG = CHG;
    }

    public String getIpoDate() {
        return ipoDate;
    }

    public void setIpoDate(String ipoDate) {
        this.ipoDate = ipoDate;
    }

    public String getBelongsPlate() {
        return belongsPlate;
    }

    public void setBelongsPlate(String belongsPlate) {
        this.belongsPlate = belongsPlate;
    }

    public String getZhSortName() {
        return zhSortName;
    }

    public void setZhSortName(String zhSortName) {
        this.zhSortName = zhSortName;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }

    public String getxData() {
        return xData;
    }

    public void setxData(String xData) {
        this.xData = xData;
    }
}

