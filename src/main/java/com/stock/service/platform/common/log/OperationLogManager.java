package com.stock.service.platform.common.log;

import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.dao.OperationLogMapper;
import com.stock.service.platform.common.entity.SaOperationLogWithBLOBs;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class OperationLogManager {

  @Resource
  private OperationLogMapper operationLogMapper;

  @Transactional
  public Boolean createOperationLog(SaOperationLogWithBLOBs insertOperationLog, UserInfo userInfo) {
    insertOperationLog.setCreateUser(userInfo.getUserId());
    insertOperationLog.setCreateTime(new Date());
    operationLogMapper.insertSelective(insertOperationLog);
    return true;
  }

}
