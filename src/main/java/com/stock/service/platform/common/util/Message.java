package com.stock.service.platform.common.util;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Message {

  //字段标题
  String title() default "";

  //类型 object 实体类 list 列表
  String type() default "";

  //增加删除的标志字段 true 检查 false 不检查
  String primary() default "";

  //必填or非必填  true 必填 false 非必填
  String required() default "";
}