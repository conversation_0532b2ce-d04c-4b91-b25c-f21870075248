<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.RouterMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Router">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="p_router_id" jdbcType="VARCHAR" property="pRouterId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="router_name" jdbcType="VARCHAR" property="routerName" />
    <result column="router_des" jdbcType="VARCHAR" property="routerDes" />
    <result column="router_type" jdbcType="VARCHAR" property="routerType" />
    <result column="curr_status" jdbcType="VARCHAR" property="currStatus" />
    <result column="router_folder_flag" jdbcType="INTEGER" property="routerFolderFlag" />
    <result column="para" jdbcType="VARCHAR" property="para" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="router_icon" jdbcType="VARCHAR" property="routerIcon" />
    <result column="meta" jdbcType="VARCHAR" property="meta" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="redirect" jdbcType="VARCHAR" property="redirect" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, p_router_id, resource_id, router_name, router_des, router_type, curr_status, 
    router_folder_flag, para, sort_no, remark, router_icon, meta, component, path, company_id, 
    redirect, create_user, create_time, update_user, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.RouterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_router
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_router
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.RouterExample">
    delete from sa_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Router">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_router (id, p_router_id, resource_id, 
      router_name, router_des, router_type, 
      curr_status, router_folder_flag, para, 
      sort_no, remark, router_icon, 
      meta, component, path, 
      company_id, redirect, create_user, 
      create_time, update_user, update_time, 
      status)
    values (#{id,jdbcType=VARCHAR}, #{pRouterId,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, 
      #{routerName,jdbcType=VARCHAR}, #{routerDes,jdbcType=VARCHAR}, #{routerType,jdbcType=VARCHAR}, 
      #{currStatus,jdbcType=VARCHAR}, #{routerFolderFlag,jdbcType=INTEGER}, #{para,jdbcType=VARCHAR}, 
      #{sortNo,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{routerIcon,jdbcType=VARCHAR}, 
      #{meta,jdbcType=VARCHAR}, #{component,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=VARCHAR}, #{redirect,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Router">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_router
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="pRouterId != null">
        p_router_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="routerName != null">
        router_name,
      </if>
      <if test="routerDes != null">
        router_des,
      </if>
      <if test="routerType != null">
        router_type,
      </if>
      <if test="currStatus != null">
        curr_status,
      </if>
      <if test="routerFolderFlag != null">
        router_folder_flag,
      </if>
      <if test="para != null">
        para,
      </if>
      <if test="sortNo != null">
        sort_no,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="routerIcon != null">
        router_icon,
      </if>
      <if test="meta != null">
        meta,
      </if>
      <if test="component != null">
        component,
      </if>
      <if test="path != null">
        path,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="redirect != null">
        redirect,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="pRouterId != null">
        #{pRouterId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="routerName != null">
        #{routerName,jdbcType=VARCHAR},
      </if>
      <if test="routerDes != null">
        #{routerDes,jdbcType=VARCHAR},
      </if>
      <if test="routerType != null">
        #{routerType,jdbcType=VARCHAR},
      </if>
      <if test="currStatus != null">
        #{currStatus,jdbcType=VARCHAR},
      </if>
      <if test="routerFolderFlag != null">
        #{routerFolderFlag,jdbcType=INTEGER},
      </if>
      <if test="para != null">
        #{para,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        #{sortNo,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="routerIcon != null">
        #{routerIcon,jdbcType=VARCHAR},
      </if>
      <if test="meta != null">
        #{meta,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        #{component,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        #{path,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="redirect != null">
        #{redirect,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.RouterExample" resultType="java.lang.Long">
    select count(*) from sa_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_router
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.pRouterId != null">
        p_router_id = #{record.pRouterId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.routerName != null">
        router_name = #{record.routerName,jdbcType=VARCHAR},
      </if>
      <if test="record.routerDes != null">
        router_des = #{record.routerDes,jdbcType=VARCHAR},
      </if>
      <if test="record.routerType != null">
        router_type = #{record.routerType,jdbcType=VARCHAR},
      </if>
      <if test="record.currStatus != null">
        curr_status = #{record.currStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.routerFolderFlag != null">
        router_folder_flag = #{record.routerFolderFlag,jdbcType=INTEGER},
      </if>
      <if test="record.para != null">
        para = #{record.para,jdbcType=VARCHAR},
      </if>
      <if test="record.sortNo != null">
        sort_no = #{record.sortNo,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.routerIcon != null">
        router_icon = #{record.routerIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.meta != null">
        meta = #{record.meta,jdbcType=VARCHAR},
      </if>
      <if test="record.component != null">
        component = #{record.component,jdbcType=VARCHAR},
      </if>
      <if test="record.path != null">
        path = #{record.path,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.redirect != null">
        redirect = #{record.redirect,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_router
    set id = #{record.id,jdbcType=VARCHAR},
      p_router_id = #{record.pRouterId,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      router_name = #{record.routerName,jdbcType=VARCHAR},
      router_des = #{record.routerDes,jdbcType=VARCHAR},
      router_type = #{record.routerType,jdbcType=VARCHAR},
      curr_status = #{record.currStatus,jdbcType=VARCHAR},
      router_folder_flag = #{record.routerFolderFlag,jdbcType=INTEGER},
      para = #{record.para,jdbcType=VARCHAR},
      sort_no = #{record.sortNo,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      router_icon = #{record.routerIcon,jdbcType=VARCHAR},
      meta = #{record.meta,jdbcType=VARCHAR},
      component = #{record.component,jdbcType=VARCHAR},
      path = #{record.path,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      redirect = #{record.redirect,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Router">
    update sa_router
    <set>
      <if test="pRouterId != null">
        p_router_id = #{pRouterId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="routerName != null">
        router_name = #{routerName,jdbcType=VARCHAR},
      </if>
      <if test="routerDes != null">
        router_des = #{routerDes,jdbcType=VARCHAR},
      </if>
      <if test="routerType != null">
        router_type = #{routerType,jdbcType=VARCHAR},
      </if>
      <if test="currStatus != null">
        curr_status = #{currStatus,jdbcType=VARCHAR},
      </if>
      <if test="routerFolderFlag != null">
        router_folder_flag = #{routerFolderFlag,jdbcType=INTEGER},
      </if>
      <if test="para != null">
        para = #{para,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        sort_no = #{sortNo,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="routerIcon != null">
        router_icon = #{routerIcon,jdbcType=VARCHAR},
      </if>
      <if test="meta != null">
        meta = #{meta,jdbcType=VARCHAR},
      </if>
      <if test="component != null">
        component = #{component,jdbcType=VARCHAR},
      </if>
      <if test="path != null">
        path = #{path,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="redirect != null">
        redirect = #{redirect,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Router">
    update sa_router
    set p_router_id = #{pRouterId,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      router_name = #{routerName,jdbcType=VARCHAR},
      router_des = #{routerDes,jdbcType=VARCHAR},
      router_type = #{routerType,jdbcType=VARCHAR},
      curr_status = #{currStatus,jdbcType=VARCHAR},
      router_folder_flag = #{routerFolderFlag,jdbcType=INTEGER},
      para = #{para,jdbcType=VARCHAR},
      sort_no = #{sortNo,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      router_icon = #{routerIcon,jdbcType=VARCHAR},
      meta = #{meta,jdbcType=VARCHAR},
      component = #{component,jdbcType=VARCHAR},
      path = #{path,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      redirect = #{redirect,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.RouterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>