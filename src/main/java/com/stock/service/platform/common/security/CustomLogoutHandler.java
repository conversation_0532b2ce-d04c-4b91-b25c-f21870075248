package com.stock.service.platform.common.security;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.constant.RedisKeyConstant;
import com.stock.service.platform.common.dao.LogMapper;
import com.stock.service.platform.common.entity.Log;
import com.stock.service.platform.common.service.CommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.Map;

/**
 * @Description: 退出处理器
 * @author: xiazhengwei
 * @date:
 */
@Component
public class CustomLogoutHandler implements LogoutHandler {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    LogMapper logMapper;

    @Autowired
    private RedisDao redisDao;

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        Assert.notNull(request, "HttpServletRequest required");

        // 获取对应Session
        HttpSession session = request.getSession(false);

        // 插入日志
        String loginType = (String)this.redisDao.getObject("access_token_login_type_" + request.getHeader(HttpHeaders.AUTHORIZATION));
        UserInfo userInfo = (UserInfo)this.redisDao.getObject("access_token:" + request.getHeader(HttpHeaders.AUTHORIZATION));

        Map<String, String> param = Maps.newLinkedHashMap();
        param.put("登录用户ID", userInfo.getUserId());
        param.put("登录用户名", userInfo.getUsername());
        param.put("会话ID", session != null && StringUtils.isNotEmpty(session.getId()) ?session.getId(): "");
        Log log = new Log();
        log.setLogType(loginType);
        log.setLogTime(new Date());
        log.setLogName("用户登出");
        log.setLogContent(Joiner.on(",").withKeyValueSeparator("=").join(param));
        log.setLogIp(CommonService.getIp(request));
        log.setCreateUser(userInfo.getUserId());
        log.setUpdateUser(userInfo.getUserId());
        log.setCreateTime(new Date());
        log.setUpdateTime(new Date());
        log.setStatus("1");

        logMapper.insertSelective(log);

        // 清理Session
        if (session != null) {
            logger.debug("Invalidating session: " + session.getId());
            session.invalidate();
        }

        // 清理redis，用户信息在登出成功拦截器清理
        this.redisDao.delObject("access_token_login_type_" + request.getHeader(HttpHeaders.AUTHORIZATION));

        // 清理手机验证码
        this.redisDao.delObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + userInfo.getUsername());

        // 清空SecurityContext
        SecurityContextHolder.clearContext();
    }
}
