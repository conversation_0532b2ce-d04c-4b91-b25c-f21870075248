<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.SystemConfigMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.SystemConfig">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="sys_name" jdbcType="VARCHAR" property="sysName" />
    <result column="sys_order" jdbcType="INTEGER" property="sysOrder" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.stock.service.platform.common.entity.SystemConfig">
    <result column="sys_config" jdbcType="LONGVARCHAR" property="sysConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_user, create_time, update_user, update_time, sys_code, sys_name, sys_order, status
  </sql>
  <sql id="Blob_Column_List">
    sys_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.stock.service.platform.common.entity.SystemConfigExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_system_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.SystemConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_system_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_system_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.stock.service.platform.common.entity.SystemConfig">
    update sa_system_config
    set create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      sys_code = #{sysCode,jdbcType=VARCHAR},
      sys_name = #{sysName,jdbcType=VARCHAR},
      sys_order = #{sysOrder,jdbcType=INTEGER},
      sys_config = #{sysConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="lockStatusClick">
    update sa_system_config set status = #{status,jdbcType=INTEGER}, update_time = now() where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>