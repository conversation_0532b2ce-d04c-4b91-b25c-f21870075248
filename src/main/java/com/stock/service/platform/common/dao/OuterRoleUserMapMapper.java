package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.OuterRoleUserMap;
import com.stock.service.platform.common.entity.OuterRoleUserMapExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface OuterRoleUserMapMapper {
    long countByExample(OuterRoleUserMapExample example);

    int deleteByExample(OuterRoleUserMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(OuterRoleUserMap record);

    int insertSelective(OuterRoleUserMap record);

    List<OuterRoleUserMap> selectByExampleWithRowbounds(OuterRoleUserMapExample example, RowBounds rowBounds);

    List<OuterRoleUserMap> selectByExample(OuterRoleUserMapExample example);

    OuterRoleUserMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") OuterRoleUserMap record, @Param("example") OuterRoleUserMapExample example);

    int updateByExample(@Param("record") OuterRoleUserMap record, @Param("example") OuterRoleUserMapExample example);

    int updateByPrimaryKeySelective(OuterRoleUserMap record);

    int updateByPrimaryKey(OuterRoleUserMap record);

    String getCompanyCodeByUserId(String userId);

    List<String> getSystemStateName(List<String> systemState);

    String getUserSystem(String userId);

    void updateMergeFlag(String userId);
}