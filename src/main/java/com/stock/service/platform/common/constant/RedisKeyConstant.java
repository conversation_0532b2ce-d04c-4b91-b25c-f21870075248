package com.stock.service.platform.common.constant;

/**
 * <AUTHOR>
 * <p>
 * Redis的key常量接口类
 */
public class RedisKeyConstant {

    /**
     * 手机验证码前缀
     */
    public static final String TELEPHONE_VERIFY_CODE_PREFIX = "telephone:verify:code:";

    /**
     * 邮箱验证码前缀
     */
    public static final String MAIL_VERIFY_CODE_PREFIX = "mail:verify:code:";

    public static final String LOGIN_CAPTCHA_MSG_RANDOM_KEY = "messageRandom";
    /**
     * 数据字典前缀
     */
    public static final String SA_CODE_PREFIX = "sa:code:";
    /**
     * 数据字典过期时间
     */
    public static final long SA_CODE_EXPIRE_TIME = 1000 * 60 * 60 * 24;


    /**
     * 分布式锁获取超时时间
     */
    public static final long DISTRIBUTE_ACQUIRE_TIMEOUT = 2000L;


    /**
     * 分布式锁执行超时时间
     */
    public static final int DISTRIBUTE_LOCK_TIMEOUT = 20;

    /**
     * 文件异步打包前缀
     */
    public static final String FILE_PACKAGE_ASYNC_PREFIX = "upload:file:async:";


    public static final String LOGIN_CAPTCHA_RANDOM_KEY = "random";


    public static final String LOGIN_CAPTCHA_PREFIX = "equity:captcha:code:";
}
