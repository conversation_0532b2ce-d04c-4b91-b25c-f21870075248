package com.stock.service.platform.common.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.StockService;
import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import com.stock.service.platform.common.system.service.TimedTasksService;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("system/timedTasks")
public class TimedTasksController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(TimedTasksController.class);

    @Autowired
    private TimedTasksService timedTasksService;

    @Autowired
    private TopicProducer topicProducer;

    @Autowired
    private TimedTasksMapper timedTasksMapper;

    @Resource
    private StockService stockService;

    @Resource
    private LogService logService;

    @PostMapping("getTasksList")
    public JsonResponse<Map<String, Object>> getTasksList(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        try {
            response.setResult(timedTasksService.getTasksList(param));
        }catch (Exception e) {
            response.setErrorMsg("获取定时任务列表失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("getTasksGroupList")
    public JsonResponse<Map<String, Object>> getTasksGroupList(@RequestBody Map<String, Object> param) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        try {
            response.setResult(timedTasksService.getTasksGroupList(param));
        }catch (Exception e) {
            response.setErrorMsg("获取定时任务列表失败");
        }
        return response;
    }

    @PostMapping("repeat")
    public JsonResponse<Object> repeat(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            String sysCode = (String) param.get("sysCode");
            if ("PLATFORM".equals(sysCode)) {
                param.put("isSuccess", "2");
                timedTasksMapper.updateIsSuccessP(param);
                String name = MapUtils.getString(param, "name");
                if ("vcTradSkDailyMerge".equals(name)){
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    HttpServletRequest request = attributes.getRequest();
                    String ip = CommonService.getIp(request);
                    stockService.repeat(param, ip, getUserInfo());
                }
            }
            response.setResult(true);
        }catch (Exception e) {
            response.setErrorMsg("重新发送失败");
            e.printStackTrace();
        }
        return response;
    }
}
