<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OuterRouterMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.OuterRouter">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="router_name" jdbcType="VARCHAR" property="routerName" />
    <result column="router_desc" jdbcType="VARCHAR" property="routerDesc" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sys_code, source, router_name, router_desc, sort_no
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.OuterRouterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_outer_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_outer_router
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_outer_router
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.OuterRouterExample">
    delete from sa_outer_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.OuterRouter">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_outer_router (id, sys_code, source, 
      router_name, router_desc, sort_no
      )
    values (#{id,jdbcType=VARCHAR}, #{sysCode,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, 
      #{routerName,jdbcType=VARCHAR}, #{routerDesc,jdbcType=VARCHAR}, #{sortNo,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.OuterRouter">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_outer_router
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="sysCode != null">
        sys_code,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="routerName != null">
        router_name,
      </if>
      <if test="routerDesc != null">
        router_desc,
      </if>
      <if test="sortNo != null">
        sort_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="sysCode != null">
        #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="routerName != null">
        #{routerName,jdbcType=VARCHAR},
      </if>
      <if test="routerDesc != null">
        #{routerDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        #{sortNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.OuterRouterExample" resultType="java.lang.Long">
    select count(*) from sa_outer_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_outer_router
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sysCode != null">
        sys_code = #{record.sysCode,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.routerName != null">
        router_name = #{record.routerName,jdbcType=VARCHAR},
      </if>
      <if test="record.routerDesc != null">
        router_desc = #{record.routerDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.sortNo != null">
        sort_no = #{record.sortNo,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_outer_router
    set id = #{record.id,jdbcType=VARCHAR},
      sys_code = #{record.sysCode,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      router_name = #{record.routerName,jdbcType=VARCHAR},
      router_desc = #{record.routerDesc,jdbcType=VARCHAR},
      sort_no = #{record.sortNo,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.OuterRouter">
    update sa_outer_router
    <set>
      <if test="sysCode != null">
        sys_code = #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="routerName != null">
        router_name = #{routerName,jdbcType=VARCHAR},
      </if>
      <if test="routerDesc != null">
        router_desc = #{routerDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        sort_no = #{sortNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.OuterRouter">
    update sa_outer_router
    set sys_code = #{sysCode,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      router_name = #{routerName,jdbcType=VARCHAR},
      router_desc = #{routerDesc,jdbcType=VARCHAR},
      sort_no = #{sortNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.OuterRouterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_outer_router
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>