package com.stock.service.platform.common.config;

import com.stock.service.platform.file.service.CancelPackageSubscriberService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * <AUTHOR>
 * 数据源相关的配置
 */
@Configuration
public class PersistenceConfig {

    @Bean
    public CancelPackageSubscriberService cancelPackageSubscriberService(){
        return new CancelPackageSubscriberService();
    }

    @Bean
    MessageListenerAdapter messageListener(CancelPackageSubscriberService cancelPackageSubscriberService) {
        return new MessageListenerAdapter(cancelPackageSubscriberService);
    }

    @Bean
    RedisMessageListenerContainer redisContainer(RedisConnectionFactory factory,MessageListenerAdapter messageListener) {
        final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(factory);
        container.addMessageListener(messageListener, new ChannelTopic("cancelPackage"));
        return container;
    }


}
