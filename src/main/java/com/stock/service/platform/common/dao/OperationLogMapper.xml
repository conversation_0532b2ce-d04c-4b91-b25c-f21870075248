<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OperationLogMapper">
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.SaOperationLogWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="ip != null">
        ip,
      </if>
      <if test="operationTypeDetail != null">
        operation_type_detail,
      </if>
      <if test="router != null">
        router,
      </if>
      <if test="method != null">
        method,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="parameterJson != null">
        parameter_json,
      </if>
      <if test="resultJson != null">
        result_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="operationTypeDetail != null">
        #{operationTypeDetail,jdbcType=VARCHAR},
      </if>
      <if test="router != null">
        #{router,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="parameterJson != null">
        #{parameterJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="resultJson != null">
        #{resultJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>