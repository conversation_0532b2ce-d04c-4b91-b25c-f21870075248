<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.service.platform.common.system.dao.SaPersonMapper">

    <insert id="insertSelective" parameterType="com.stock.service.platform.common.system.dto.SaPersonDto">
        insert into sa_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            hidden_key,
            <if test="id != null">
                id,
            </if>
            <if test="personType != null">
                person_type,
            </if>
            <if test="personName != null">
                person_name,
            </if>
            <if test="jobName != null">
                job_name,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="mail != null">
                mail,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="post != null">
                post,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            uuid(),
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="personType != null">
                #{personType,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="jobName != null">
                #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="post != null">
                #{post,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=TIMESTAMP},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=TIMESTAMP},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=TIMESTAMP},
            </if>
            <if test="approveStatus != null">
                #{approveStatus,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updatePerson" parameterType="com.stock.service.platform.common.system.dto.SaPersonDto">
        UPDATE sa_person
        <set>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="personName != null">
                person_name = #{personName,jdbcType=VARCHAR},
            </if>
            <if test="personType != null">
                person_type = #{personType,jdbcType=VARCHAR},
            </if>
            <if test="jobName != null">
                job_name = #{jobName,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                telephone = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                mail = #{mail,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                fax = #{fax,jdbcType=VARCHAR},
            </if>
            <if test="post != null">
                post = #{post,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="approveStatus != null">
                approve_status = #{approveStatus,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>