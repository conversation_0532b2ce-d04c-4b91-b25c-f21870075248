package com.stock.service.platform.common.service;

import cn.tass.hsmApi.extendFunction.Result;
import cn.tass.hsmApi.extendFunction.extendFunction;
import cn.tass.kits.Forms;
import com.stock.core.service.BaseService;
import com.stock.service.platform.block.controller.RepManageController;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.regex.Pattern;

@Service
@Transactional(rollbackFor = {Exception.class})
public class QuestionnaireService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(RepManageController.class);

    @Value("${aesMachine.machineFlag}")
    private String machineFlag;

    @Value("${aesMachine.machinePath}")
    private String machinePath;

    @Value("${aesMachine.aexLogger}")
    private String aexLogger;

    @Value("${aesMachine.aexHost1}")
    private String aexHost1;

    public static final String STR_MACHINEFLAG_ON = "1";

    /**
     * 加密机加密
     *
     * @param inParam
     * @return
     */
    public String enCodeAec(String inParam) {
        try {
            if (StringUtils.isBlank(inParam)) {
                return "";
            }
            //如果不是11为纯数字，则为非法手机号不做机密处理
            String pattern = "^1[\\d]{10}";
            boolean isMatch = Pattern.matches(pattern, inParam);
            if (!isMatch) {
                return inParam;
            }
            if (STR_MACHINEFLAG_ON.equals(machineFlag) && StringUtils.isNotBlank(machinePath)) {
                String[] paths = machinePath.split(",");
                if (paths != null && paths.length > 0) {
                    try {
                        extendFunction instance = extendFunction.getInstance(createAexInfo(paths));
                        int keyIndex = 1;
                        String deriveFactor = "";//没有分散因子
                        int sessionKeyFlag = 0;
                        String sessionKeyFactor = "";
                        byte[] inData = inParam.getBytes();
                        Result EncData = instance.packetDataEnc(keyIndex, deriveFactor, sessionKeyFlag, sessionKeyFactor, inData);
                        inParam = Forms.byteToHexString(EncData.getData());
                    } catch (Exception ex) {
                        logger.error(ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("解密异常，解密参数" + inParam + ex.getMessage());
        }
        return inParam;
    }

    /**
     * 加密机解密
     *
     * @param inParam
     * @return
     */
    public String deCodeAes(String inParam) {
        try {
            if (StringUtils.isBlank(inParam)) {
                return "";
            }
            String pattern = "^1[\\d]{10}";
            boolean isMatch = Pattern.matches(pattern, inParam);
            if (isMatch || inParam.length() < 15) {
                return inParam;
            }
            if (STR_MACHINEFLAG_ON.equals(machineFlag) && StringUtils.isNotBlank(machinePath)) {
                String[] paths = machinePath.split(",");
                if (paths != null && paths.length > 0) {
                    try {
                        extendFunction instance = extendFunction.getInstance(createAexInfo(paths));
                        int keyIndex = 1;
                        String deriveFactor = "";//没有分散因子
                        int sessionKeyFlag = 0;
                        String sessionKeyFactor = "";
                        byte[] inData = inParam.getBytes();
                        Result r = instance.packetDataDec(keyIndex, deriveFactor, sessionKeyFlag, sessionKeyFactor, Forms.hexStringToByte(inParam));
                        inParam = new String(r.getData());
                    } catch (Exception ex) {
                        logger.error(ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage());
        }
        return inParam;
    }

    private String createAexInfo(String[] paths) {
        StringBuffer sbAex = new StringBuffer();
        sbAex.append("{");
        sbAex.append("[LOGGER];").append(aexLogger);
        for (int i = 0; i < paths.length; i++) {
            String[] strIpPort = paths[i].split(":");
            sbAex.append("[HOST " + (i + 1) + "];").append(aexHost1);
            sbAex.append("host = ").append(strIpPort[0]).append(";");
            sbAex.append("port = ").append(strIpPort[1]).append(";");
        }
        sbAex.append("}");
        return sbAex.toString();
    }
}
