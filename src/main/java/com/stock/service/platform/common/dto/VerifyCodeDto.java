package com.stock.service.platform.common.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * 验证码dto
 * 可能增加邮箱验证码验证
 */
public class VerifyCodeDto {

    /**
     * 手机号
     * 十一位，1开头
     */
    @NotBlank(message = "手机号为空")
    @Pattern(message = "手机号格式错误",regexp = "1\\d{10}")
    private String telephone;


    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
}
