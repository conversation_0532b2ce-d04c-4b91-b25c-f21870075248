package com.stock.service.platform.common.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 股票日行情
 * vc_trad_sk_daily
 */
@Data
public class VcTradSkDailyDto implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 源ID
     */
    private String idSource;

    /**
     * 证券代码
     */
    private String securityCd;

    /**
     * 交易日期
     */
    private Date tradeDate;

    /**
     * 今日收盘
     */
    private BigDecimal NEW;

    /**
     * 涨跌幅
     */
    private BigDecimal chg;

    /**
     * 成交量（股）
     */
    private BigDecimal cjl;

    /**
     * 成交金额(元)
     */
    private BigDecimal tvalcny;

    /**
     * 总市值
     */
    private BigDecimal zsz;

    /**
     * 市盈率(TTM)
     */
    private BigDecimal pe9;

    /**
     * 市净率(最新报告期MRQ)
     */
    private BigDecimal pb8;

    /**
     * A股流通市值
     */
    private BigDecimal agszbhxs;

    /**
     * B股流通市值(建投不分A/B,数据存到A,B存空)
     */
    private BigDecimal bgszbhxszrm;

    /**
     * 股东人数
     */
    private Long shareNum;

    /**
     * 总股本（A+B+H）
     */
    private BigDecimal totalshares;

    /**
     * 自由流通股本
     */
    private BigDecimal ffshares;

    /**
     * 有限售条件股份
     */
    private Long rshare;

    /**
     * 流通股份(与自由流通股本相同)
     */
    private Long cshare;

    /**
     * 人民币普通股（A股）
     */
    private Long ashare;

    /**
     * 限售流通A股
     */
    private Long asharer;

    /**
     * 境内上市外资股（B股）
     */
    private Long bshare;

    /**
     * 限售流通B股
     */
    private Long bsharer;

    /**
     * 境外上市外资股（H股）
     */
    private Long hshare;

    /**
     * 限售流通H股
     */
    private Long hsharer;

    /**
     * 插入时间
     */
    private Date inserttime;

    private static final long serialVersionUID = 1L;
}