package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class SaOperationLog implements Serializable {
    private String id;

    private String ip;

    private String operationTypeDetail;

    private String router;

    private String method;

    private String remark;

    private Date createTime;

    private String createUser;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOperationTypeDetail() {
        return operationTypeDetail;
    }

    public void setOperationTypeDetail(String operationTypeDetail) {
        this.operationTypeDetail = operationTypeDetail;
    }

    public String getRouter() {
        return router;
    }

    public void setRouter(String router) {
        this.router = router;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}