<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.system.dao.InfoSyncMapper">


    <insert id="insertOrgSelective" parameterType="com.stock.service.platform.common.entity.Org">
        insert into sa_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="companyId != null">
                company_id,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="pOrgId != null">
                p_org_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="level != null">
                level,
            </if>
            <if test="orgType != null">
                org_type,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="thresholdFlag != null">
                threshold_flag,
            </if>
            <if test="childrenCompanyFlag != null">
                children_company_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="pOrgId != null">
                #{pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=INTEGER},
            </if>
            <if test="level != null">
                #{level},
            </if>
            <if test="orgType != null">
                #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="thresholdFlag != null">
                #{thresholdFlag,jdbcType=VARCHAR},
            </if>
            <if test="childrenCompanyFlag != null">
                #{childrenCompanyFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="insertRoleSelective" parameterType="com.stock.service.platform.common.entity.OuterRole">
        insert into sa_outer_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="sysCode != null">
                sys_code,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleDesc != null">
                role_desc,
            </if>
            <if test="roleType != null">
                role_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="sysCode != null">
                #{sysCode,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDesc != null">
                #{roleDesc,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                #{roleType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <insert id="insertPersonSelective" parameterType="com.stock.service.platform.common.entity.PersonWithBLOBs">

        insert into sa_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="employeeNumber != null">
                employee_number,
            </if>
            <if test="personType != null">
                person_type,
            </if>
            <if test="personName != null">
                person_name,
            </if>
            <if test="foreignName != null">
                foreign_name,
            </if>
            <if test="titleName != null">
                title_name,
            </if>
            <if test="jobId != null">
                job_id,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="telephoneSpare1 != null">
                telephone_spare1,
            </if>
            <if test="telephoneSpare2 != null">
                telephone_spare2,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="mail != null">
                mail,
            </if>
            <if test="mailSpare1 != null">
                mail_spare1,
            </if>
            <if test="mailSpare2 != null">
                mail_spare2,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="post != null">
                post,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="nationality != null">
                nationality,
            </if>
            <if test="certificateType != null">
                certificate_type,
            </if>
            <if test="certificate != null">
                certificate,
            </if>
            <if test="pictureUrl != null">
                picture_url,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="resumeStatus != null">
                resume_status,
            </if>
            <if test="activeStatus != null">
                active_status,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="identityType != null">
                identity_type,
            </if>
            <if test="shareTradable != null">
                share_tradable,
            </if>
            <if test="shareCount != null">
                share_count,
            </if>
            <if test="companyShareholder != null">
                company_shareholder,
            </if>
            <if test="eid != null">
                eid,
            </if>
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="source != null">
                source,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="hiddenKey != null">
                hidden_key,
            </if>
            <if test="weChatId != null">
                we_chat_id,
            </if>
            <if test="jobs != null">
                jobs,
            </if>
            <if test="resume != null">
                resume,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="employeeNumber != null">
                #{employeeNumber,jdbcType=VARCHAR},
            </if>
            <if test="personType != null">
                #{personType,jdbcType=VARCHAR},
            </if>
            <if test="personName != null">
                #{personName,jdbcType=VARCHAR},
            </if>
            <if test="foreignName != null">
                #{foreignName,jdbcType=VARCHAR},
            </if>
            <if test="titleName != null">
                #{titleName,jdbcType=VARCHAR},
            </if>
            <if test="jobId != null">
                #{jobId,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="telephoneSpare1 != null">
                #{telephoneSpare1,jdbcType=VARCHAR},
            </if>
            <if test="telephoneSpare2 != null">
                #{telephoneSpare2,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="mailSpare1 != null">
                #{mailSpare1,jdbcType=VARCHAR},
            </if>
            <if test="mailSpare2 != null">
                #{mailSpare2,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="post != null">
                #{post,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="nationality != null">
                #{nationality,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                #{certificateType,jdbcType=VARCHAR},
            </if>
            <if test="certificate != null">
                #{certificate,jdbcType=VARCHAR},
            </if>
            <if test="pictureUrl != null">
                #{pictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="resumeStatus != null">
                #{resumeStatus,jdbcType=VARCHAR},
            </if>
            <if test="activeStatus != null">
                #{activeStatus,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="identityType != null">
                #{identityType,jdbcType=VARCHAR},
            </if>
            <if test="shareTradable != null">
                #{shareTradable,jdbcType=DECIMAL},
            </if>
            <if test="shareCount != null">
                #{shareCount,jdbcType=DECIMAL},
            </if>
            <if test="companyShareholder != null">
                #{companyShareholder,jdbcType=VARCHAR},
            </if>
            <if test="eid != null">
                #{eid,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="hiddenKey != null">
                #{hiddenKey,jdbcType=VARCHAR},
            </if>
            <if test="weChatId != null">
                #{weChatId,jdbcType=VARCHAR},
            </if>
            <if test="jobs != null">
                #{jobs,jdbcType=VARCHAR},
            </if>
            <if test="resume != null">
                #{resume,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>


    <insert id="insertUserSelective" parameterType="com.stock.service.platform.common.entity.SaUser">
        insert into sa_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="companyId != null">
                company_id,
            </if>
            <if test="orgNo != null">
                org_no,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="realName != null">
                real_name,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="mail != null">
                mail,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="jobs != null">
                jobs,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="passwordUpdateTime != null">
                password_update_time,
            </if>
            <if test="unlockTime != null">
                unlock_time,
            </if>
            <if test="errorNum != null">
                error_num,
            </if>
            <if test="lockType != null">
                lock_type,
            </if>
            <if test="lockState != null">
                lock_state,
            </if>
            <if test="isAdmin != null">
                is_admin,
            </if>
            <if test="reserveDepPermission != null">
                reserve_dep_permission,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="hiddenKey != null">
                hidden_key,
            </if>
            <if test="isFirst != null">
                is_first,
            </if>
            <if test="sort != null">
                sort,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgNo != null">
                #{orgNo,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="jobs != null">
                #{jobs,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="passwordUpdateTime != null">
                #{passwordUpdateTime,jdbcType=DATE},
            </if>
            <if test="unlockTime != null">
                #{unlockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="errorNum != null">
                #{errorNum,jdbcType=INTEGER},
            </if>
            <if test="lockType != null">
                #{lockType,jdbcType=VARCHAR},
            </if>
            <if test="lockState != null">
                #{lockState,jdbcType=VARCHAR},
            </if>
            <if test="isAdmin != null">
                #{isAdmin,jdbcType=VARCHAR},
            </if>
            <if test="reserveDepPermission != null">
                #{reserveDepPermission,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="hiddenKey != null">
                #{hiddenKey,jdbcType=VARCHAR},
            </if>
            <if test="isFirst != null">
                #{isFirst,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!--查上市公司列表-->
    <select id="selectCompanyList" resultType="com.stock.service.platform.common.system.dto.CompanyOrgDto">
        SELECT
            sc.company_code AS companyCode,
            sc.id AS companyId,
            GROUP_CONCAT( so.id ORDER BY so.id ASC SEPARATOR ',' ) AS orgId
        FROM
            sa_company sc
                LEFT JOIN sa_org so ON so.company_id = sc.id
        GROUP BY
            sc.id;
    </select>

    <insert id="insertCompanySelective" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        insert into sa_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            hidden_key,
            legal_person_id_type,
            company_status,
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="zhName != null">
                zh_name,
            </if>
            <if test="zhSortName != null">
                zh_sort_name,
            </if>
            <if test="corporate != null">
                corporate,
            </if>
            <if test="organisationNo != null">
                organisation_no,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerDate != null">
                register_date,
            </if>
            <if test="companyMail != null">
                company_mail,
            </if>
            <if test="registerCapital != null">
                register_capital,
            </if>

            <if test="phone != null">
                phone,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteflag != null">
                deleteflag,
            </if>
            <if test="orgform != null">
                orgform,
            </if>
            <if test="companyStockName != null">
                company_stock_name,
            </if>
            <if test="corporateIdNumber != null">
                corporate_id_number,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="marketStockType != null">
                market_stock_type,
            </if>
            <if test="listingDate != null">
                listing_date,
            </if>
            <if test="issuePrice != null">
                issue_price,
            </if>
            <if test="capitalStockA != null">
                capital_stock_a,
            </if>
            <if test="capitalStockB != null">
                capital_stock_b,
            </if>
            <if test="capitalStockH != null">
                capital_stock_h,
            </if>
            <if test="capitalStockTotal != null">
                capital_stock_total,
            </if>
            <if test="socialUnifiedCreditCode != null">
                social_unified_credit_code,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="parentCompanyCode != null">
                parent_company_code,
            </if>
            <if test="market != null">
                market,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            uuid(),
            0,
            1,
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="zhName != null">
                #{zhName,jdbcType=VARCHAR},
            </if>
            <if test="zhSortName != null">
                #{zhSortName,jdbcType=VARCHAR},
            </if>
            <if test="corporate != null">
                #{corporate,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyMail != null">
                #{companyMail,jdbcType=VARCHAR},
            </if>
            <if test="registerCapital != null">
                #{registerCapital,jdbcType=DECIMAL},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteflag != null">
                #{deleteflag,jdbcType=VARCHAR},
            </if>
            <if test="orgform != null">
                #{orgform,jdbcType=VARCHAR},
            </if>
            <if test="companyStockName != null">
                #{companyStockName,jdbcType=VARCHAR},
            </if>
            <if test="corporateIdNumber != null">
                #{corporateIdNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="marketStockType != null">
                #{marketStockType,jdbcType=VARCHAR},
            </if>
            <if test="listingDate != null">
                #{listingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="issuePrice != null">
                #{issuePrice,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockA != null">
                #{capitalStockA,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockB != null">
                #{capitalStockB,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockH != null">
                #{capitalStockH,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockTotal != null">
                #{capitalStockTotal,jdbcType=DECIMAL},
            </if>
            <if test="socialUnifiedCreditCode != null">
                #{socialUnifiedCreditCode,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="parentCompanyCode != null">
                #{parentCompanyCode,jdbcType=VARCHAR},
            </if>
            <if test="market != null">
                #{market,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


</mapper>