package com.stock.service.platform.common.entity;

import java.io.Serializable;

public class MessageInfo implements Serializable {

  //操作内容（json）
  private String operationContext;
  //操作备注
  private String operationRemark;

  public String getOperationContext() {
    return operationContext;
  }

  public void setOperationContext(String operationContext) {
    this.operationContext = operationContext;
  }

  public String getOperationRemark() {
    return operationRemark;
  }

  public void setOperationRemark(String operationRemark) {
    this.operationRemark = operationRemark;
  }
}