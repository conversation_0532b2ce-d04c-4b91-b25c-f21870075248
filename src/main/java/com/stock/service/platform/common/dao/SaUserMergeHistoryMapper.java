package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.SaUserMergeHistory;
import com.stock.service.platform.common.entity.SaUserMergeHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SaUserMergeHistoryMapper {
    long countByExample(SaUserMergeHistoryExample example);

    int deleteByExample(SaUserMergeHistoryExample example);

    int deleteByPrimaryKey(String id);

    int insert(SaUserMergeHistory record);

    int insertSelective(SaUserMergeHistory record);

    List<SaUserMergeHistory> selectByExample(SaUserMergeHistoryExample example);

    SaUserMergeHistory selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") SaUserMergeHistory record, @Param("example") SaUserMergeHistoryExample example);

    int updateByExample(@Param("record") SaUserMergeHistory record, @Param("example") SaUserMergeHistoryExample example);

    int updateByPrimaryKeySelective(SaUserMergeHistory record);

    int updateByPrimaryKey(SaUserMergeHistory record);
}