package com.stock.service.platform.common.system.dao;



import com.stock.service.platform.common.system.dto.MenuDto;
import com.stock.service.platform.common.system.dto.SaUserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SaUserMapper {
    List<SaUserDto> queryUser(SaUserDto dto);

    List<SaUserDto> queryInnerUserButAdmin(SaUserDto dto);

    List<SaUserDto> queryBranchAndDepUser(SaUserDto dto);

    List<SaUserDto> queryDepAndCompanyUser(SaUserDto dto);

    void insertSelective(SaUserDto dto);

    int updateUser(SaUserDto dto);

    List<String> findUserOfRolesByUserId(@Param("userId") String userId);

    void deleteUserRoleMapByUserId(String userId);

    void createUserRolesMap(Map<String, Object> map);

    List<String> findUserByRoleId(String id);
    //按照删除用户下合规角色
    void deleteUserRoleMapByRoleId(Map<String, Object> map);
    //获取用户权限菜单
    List<MenuDto> getRootMenu(List<String> resourceId);        //返回根菜单
    List<MenuDto> findMenuByParentId(String parentid);//根据父一级菜单，返回所有子菜单
    //通过角色id，获取资源id
    List<String> findResourceByRoleId(List<String> roleId);
    //通过roleId获取按钮id
    List<String> getButtonIdByRoleId(List<String> roleId);
    //通过按钮id获取按钮code
    List<String> getButtonResourceById(List<String> buttonId);
}