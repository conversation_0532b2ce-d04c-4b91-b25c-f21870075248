package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.OrgExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface OrgMapper {
    long countByExample(OrgExample example);

    int deleteByExample(OrgExample example);

    int deleteByPrimaryKey(String id);


    int insertSelective(Org record);

    List<Org> selectByExampleWithRowbounds(OrgExample example, RowBounds rowBounds);

    List<Org> selectByExample(OrgExample example);

    Org selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Org record, @Param("example") OrgExample example);

    int updateByExample(@Param("record") Org record, @Param("example") OrgExample example);

    int updateByPrimaryKeySelective(Org record);

    int updateByPrimaryKey(Org record);

    List<Map<String,Object>> selectOrgDictByIsMgr(@Param("params") Map<String, String> params);

    String selectOrgIdByBranch(@Param("orgId") String orgId);

    String selectOrgIdByDept(@Param("orgId") String orgId);

    String selectOrgIdByBranchBesb(@Param("orgId") String orgId);

    String checkBranchIfShare(@Param("orgId") String orgId);

    // 仅可用于外部公司
    Org selectByCompanyId(@Param("companyId") String companyId);

    Org selectByUserId(@Param("userId") String userId);

    Org selParentById (@Param("id") String id);

    @Select("select max(level) level from sa_org")
    int selOrgLevel();

    void updateByPrimaryKeys(String id, String status,Integer order);

    void deleteByPrimaryKeys(String id);

    List<Org> selectBySonIds(List<String> orgIds);

    void updatePersonOrg(String orgId, String companyCode);
    void updateUserOrg(String orgId, String companyId);
}