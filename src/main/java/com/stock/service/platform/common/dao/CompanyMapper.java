package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.Company;
import com.stock.service.platform.common.entity.CompanyExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CompanyMapper {
    long countByExample(CompanyExample example);

    //int deleteByExample(CompanyExample example);

    //int deleteByPrimaryKey(String id);

    //int insert(Company record);

    //int insertSelective(Company record);

    //List<Company> selectByExampleWithRowbounds(CompanyExample example, RowBounds rowBounds);

    List<Company> selectByExample(CompanyExample example);

    Company selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Company record, @Param("example") CompanyExample example);

    //int updateByExample(@Param("record") Company record, @Param("example") CompanyExample example);

    int updateByPrimaryKeySelective(Company record);

    //int updateByPrimaryKey(Company record);
}