<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.PersonMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Person">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="employee_number" jdbcType="VARCHAR" property="employeeNumber" />
    <result column="person_type" jdbcType="VARCHAR" property="personType" />
    <result column="person_name" jdbcType="VARCHAR" property="personName" />
    <result column="foreign_name" jdbcType="VARCHAR" property="foreignName" />
    <result column="title_name" jdbcType="VARCHAR" property="titleName" />
    <result column="job_id" jdbcType="VARCHAR" property="jobId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="sex" jdbcType="VARCHAR" property="sex" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="telephone_spare1" jdbcType="VARCHAR" property="telephoneSpare1" />
    <result column="telephone_spare2" jdbcType="VARCHAR" property="telephoneSpare2" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="mail" jdbcType="VARCHAR" property="mail" />
    <result column="mail_spare1" jdbcType="VARCHAR" property="mailSpare1" />
    <result column="mail_spare2" jdbcType="VARCHAR" property="mailSpare2" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="post" jdbcType="VARCHAR" property="post" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="nationality" jdbcType="VARCHAR" property="nationality" />
    <result column="certificate_type" jdbcType="VARCHAR" property="certificateType" />
    <result column="certificate" jdbcType="VARCHAR" property="certificate" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="resume_status" jdbcType="VARCHAR" property="resumeStatus" />
    <result column="active_status" jdbcType="VARCHAR" property="activeStatus" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="identity_type" jdbcType="VARCHAR" property="identityType" />
    <result column="share_tradable" jdbcType="DECIMAL" property="shareTradable" />
    <result column="share_count" jdbcType="DECIMAL" property="shareCount" />
    <result column="company_shareholder" jdbcType="VARCHAR" property="companyShareholder" />
    <result column="eid" jdbcType="VARCHAR" property="eid" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="hidden_key" jdbcType="VARCHAR" property="hiddenKey" />
    <result column="we_chat_id" jdbcType="VARCHAR" property="weChatId" />
    <result column="jobs" jdbcType="VARCHAR" property="jobs" />
    <result column="children_user_name" jdbcType="VARCHAR" property="childrenUserName" />
    <result column="children_org" jdbcType="VARCHAR" property="childrenOrg" />
    <result column="children_role" jdbcType="VARCHAR" property="childrenRole" />
    <result column="children_lock" jdbcType="VARCHAR" property="childrenLock" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.stock.service.platform.common.entity.PersonWithBLOBs">
    <result column="resume" jdbcType="LONGVARCHAR" property="resume" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, employee_number, person_type, person_name, foreign_name, title_name, job_id,
    org_name, org_id, sex, birthday, telephone, telephone_spare1, telephone_spare2, phone,
    mail, mail_spare1, mail_spare2, fax, post, address, nationality, certificate_type,
    certificate, picture_url, user_id, resume_status, active_status, company_name, identity_type,
    share_tradable, share_count, company_shareholder, eid, company_code, source, create_user,
    create_time, update_user, update_time, status, hidden_key, we_chat_id, jobs, children_user_name,
    children_org, children_role, children_lock
  </sql>
  <sql id="Blob_Column_List">
    resume, remark
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.stock.service.platform.common.entity.PersonExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.PersonExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectByUserId" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>

  <select id="selectByIdSeriakey" parameterType="java.util.Map" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    where id = #{id,jdbcType=VARCHAR}
    and hidden_key = #{seriakey,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_person
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.PersonExample">
    delete from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <delete id="deleteCompanyMapByUserId">
    delete from sa_user_company_map where user_id = #{id}
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.PersonWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into sa_person (id, employee_number, person_type,
    person_name, foreign_name, title_name,
    job_id, org_name, org_id,
    sex, birthday, telephone,
    telephone_spare1, telephone_spare2, phone,
    mail, mail_spare1, mail_spare2,
    fax, post, address,
    nationality, certificate_type, certificate,
    picture_url, user_id, resume_status,
    active_status, company_name, identity_type,
    share_tradable, share_count, company_shareholder,
    eid, company_code, source,
    create_user, create_time, update_user,
    update_time, status, resume,
    remark,hidden_key, we_chat_id, jobs)
    values (#{id,jdbcType=VARCHAR}, #{employeeNumber,jdbcType=VARCHAR}, #{personType,jdbcType=VARCHAR},
    #{personName,jdbcType=VARCHAR}, #{foreignName,jdbcType=VARCHAR}, #{titleName,jdbcType=VARCHAR},
    #{jobId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR},
    #{sex,jdbcType=VARCHAR}, #{birthday,jdbcType=DATE}, #{telephone,jdbcType=VARCHAR},
    #{telephoneSpare1,jdbcType=VARCHAR}, #{telephoneSpare2,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
    #{mail,jdbcType=VARCHAR}, #{mailSpare1,jdbcType=VARCHAR}, #{mailSpare2,jdbcType=VARCHAR},
    #{fax,jdbcType=VARCHAR}, #{post,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
    #{nationality,jdbcType=VARCHAR}, #{certificateType,jdbcType=VARCHAR}, #{certificate,jdbcType=VARCHAR},
    #{pictureUrl,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, #{resumeStatus,jdbcType=VARCHAR},
    #{activeStatus,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{identityType,jdbcType=VARCHAR},
    #{shareTradable,jdbcType=DECIMAL}, #{shareCount,jdbcType=DECIMAL}, #{companyShareholder,jdbcType=VARCHAR},
    #{eid,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR},
    #{createUser,jdbcType=VARCHAR}, now(), #{updateUser,jdbcType=VARCHAR},
    #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, #{resume,jdbcType=LONGVARCHAR},
    #{remark,jdbcType=LONGVARCHAR},uuid(), #{weChatId,jdbcType=VARCHAR}, #{jobs,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.PersonWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into sa_person
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      hidden_key,
      <if test="employeeNumber != null">
        employee_number,
      </if>
      <if test="personType != null">
        person_type,
      </if>
      <if test="personName != null">
        person_name,
      </if>
      <if test="foreignName != null">
        foreign_name,
      </if>
      <if test="titleName != null">
        title_name,
      </if>
      <if test="jobId != null">
        job_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="telephoneSpare1 != null">
        telephone_spare1,
      </if>
      <if test="telephoneSpare2 != null">
        telephone_spare2,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="mail != null">
        mail,
      </if>
      <if test="mailSpare1 != null">
        mail_spare1,
      </if>
      <if test="mailSpare2 != null">
        mail_spare2,
      </if>
      <if test="fax != null">
        fax,
      </if>
      <if test="post != null">
        post,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="nationality != null">
        nationality,
      </if>
      <if test="certificateType != null">
        certificate_type,
      </if>
      <if test="certificate != null">
        certificate,
      </if>
      <if test="pictureUrl != null">
        picture_url,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="resumeStatus != null">
        resume_status,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="identityType != null">
        identity_type,
      </if>
      <if test="shareTradable != null">
        share_tradable,
      </if>
      <if test="shareCount != null">
        share_count,
      </if>
      <if test="companyShareholder != null">
        company_shareholder,
      </if>
      <if test="eid != null">
        eid,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="resume != null">
        resume,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      uuid(),
      <if test="employeeNumber != null">
        #{employeeNumber,jdbcType=VARCHAR},
      </if>
      <if test="personType != null">
        #{personType,jdbcType=VARCHAR},
      </if>
      <if test="personName != null">
        #{personName,jdbcType=VARCHAR},
      </if>
      <if test="foreignName != null">
        #{foreignName,jdbcType=VARCHAR},
      </if>
      <if test="titleName != null">
        #{titleName,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="telephoneSpare1 != null">
        #{telephoneSpare1,jdbcType=VARCHAR},
      </if>
      <if test="telephoneSpare2 != null">
        #{telephoneSpare2,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        #{mail,jdbcType=VARCHAR},
      </if>
      <if test="mailSpare1 != null">
        #{mailSpare1,jdbcType=VARCHAR},
      </if>
      <if test="mailSpare2 != null">
        #{mailSpare2,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="post != null">
        #{post,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="nationality != null">
        #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="certificateType != null">
        #{certificateType,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="resumeStatus != null">
        #{resumeStatus,jdbcType=VARCHAR},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="identityType != null">
        #{identityType,jdbcType=VARCHAR},
      </if>
      <if test="shareTradable != null">
        #{shareTradable,jdbcType=DECIMAL},
      </if>
      <if test="shareCount != null">
        #{shareCount,jdbcType=DECIMAL},
      </if>
      <if test="companyShareholder != null">
        #{companyShareholder,jdbcType=VARCHAR},
      </if>
      <if test="eid != null">
        #{eid,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="resume != null">
        #{resume,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.PersonExample" resultType="java.lang.Long">
    select count(*) from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_person
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeNumber != null">
        employee_number = #{record.employeeNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.personType != null">
        person_type = #{record.personType,jdbcType=VARCHAR},
      </if>
      <if test="record.personName != null">
        person_name = #{record.personName,jdbcType=VARCHAR},
      </if>
      <if test="record.foreignName != null">
        foreign_name = #{record.foreignName,jdbcType=VARCHAR},
      </if>
      <if test="record.titleName != null">
        title_name = #{record.titleName,jdbcType=VARCHAR},
      </if>
      <if test="record.jobId != null">
        job_id = #{record.jobId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.sex != null">
        sex = #{record.sex,jdbcType=VARCHAR},
      </if>
      <if test="record.birthday != null">
        birthday = #{record.birthday,jdbcType=DATE},
      </if>
      <if test="record.telephone != null">
        telephone = #{record.telephone,jdbcType=VARCHAR},
      </if>
      <if test="record.telephoneSpare1 != null">
        telephone_spare1 = #{record.telephoneSpare1,jdbcType=VARCHAR},
      </if>
      <if test="record.telephoneSpare2 != null">
        telephone_spare2 = #{record.telephoneSpare2,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.mail != null">
        mail = #{record.mail,jdbcType=VARCHAR},
      </if>
      <if test="record.mailSpare1 != null">
        mail_spare1 = #{record.mailSpare1,jdbcType=VARCHAR},
      </if>
      <if test="record.mailSpare2 != null">
        mail_spare2 = #{record.mailSpare2,jdbcType=VARCHAR},
      </if>
      <if test="record.fax != null">
        fax = #{record.fax,jdbcType=VARCHAR},
      </if>
      <if test="record.post != null">
        post = #{record.post,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.nationality != null">
        nationality = #{record.nationality,jdbcType=VARCHAR},
      </if>
      <if test="record.certificateType != null">
        certificate_type = #{record.certificateType,jdbcType=VARCHAR},
      </if>
      <if test="record.certificate != null">
        certificate = #{record.certificate,jdbcType=VARCHAR},
      </if>
      <if test="record.pictureUrl != null">
        picture_url = #{record.pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.resumeStatus != null">
        resume_status = #{record.resumeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.identityType != null">
        identity_type = #{record.identityType,jdbcType=VARCHAR},
      </if>
      <if test="record.shareTradable != null">
        share_tradable = #{record.shareTradable,jdbcType=DECIMAL},
      </if>
      <if test="record.shareCount != null">
        share_count = #{record.shareCount,jdbcType=DECIMAL},
      </if>
      <if test="record.companyShareholder != null">
        company_shareholder = #{record.companyShareholder,jdbcType=VARCHAR},
      </if>
      <if test="record.eid != null">
        eid = #{record.eid,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.resume != null">
        resume = #{record.resume,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update sa_person
    set id = #{record.id,jdbcType=VARCHAR},
    employee_number = #{record.employeeNumber,jdbcType=VARCHAR},
    person_type = #{record.personType,jdbcType=VARCHAR},
    person_name = #{record.personName,jdbcType=VARCHAR},
    foreign_name = #{record.foreignName,jdbcType=VARCHAR},
    title_name = #{record.titleName,jdbcType=VARCHAR},
    job_id = #{record.jobId,jdbcType=VARCHAR},
    org_name = #{record.orgName,jdbcType=VARCHAR},
    org_id = #{record.orgId,jdbcType=VARCHAR},
    sex = #{record.sex,jdbcType=VARCHAR},
    birthday = #{record.birthday,jdbcType=DATE},
    telephone = #{record.telephone,jdbcType=VARCHAR},
    telephone_spare1 = #{record.telephoneSpare1,jdbcType=VARCHAR},
    telephone_spare2 = #{record.telephoneSpare2,jdbcType=VARCHAR},
    phone = #{record.phone,jdbcType=VARCHAR},
    mail = #{record.mail,jdbcType=VARCHAR},
    mail_spare1 = #{record.mailSpare1,jdbcType=VARCHAR},
    mail_spare2 = #{record.mailSpare2,jdbcType=VARCHAR},
    fax = #{record.fax,jdbcType=VARCHAR},
    post = #{record.post,jdbcType=VARCHAR},
    address = #{record.address,jdbcType=VARCHAR},
    nationality = #{record.nationality,jdbcType=VARCHAR},
    certificate_type = #{record.certificateType,jdbcType=VARCHAR},
    certificate = #{record.certificate,jdbcType=VARCHAR},
    picture_url = #{record.pictureUrl,jdbcType=VARCHAR},
    user_id = #{record.userId,jdbcType=VARCHAR},
    resume_status = #{record.resumeStatus,jdbcType=VARCHAR},
    active_status = #{record.activeStatus,jdbcType=VARCHAR},
    company_name = #{record.companyName,jdbcType=VARCHAR},
    identity_type = #{record.identityType,jdbcType=VARCHAR},
    share_tradable = #{record.shareTradable,jdbcType=DECIMAL},
    share_count = #{record.shareCount,jdbcType=DECIMAL},
    company_shareholder = #{record.companyShareholder,jdbcType=VARCHAR},
    eid = #{record.eid,jdbcType=VARCHAR},
    company_code = #{record.companyCode,jdbcType=VARCHAR},
    source = #{record.source,jdbcType=VARCHAR},
    create_user = #{record.createUser,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_user = #{record.updateUser,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    status = #{record.status,jdbcType=VARCHAR},
    resume = #{record.resume,jdbcType=LONGVARCHAR},
    remark = #{record.remark,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_person
    set id = #{record.id,jdbcType=VARCHAR},
    employee_number = #{record.employeeNumber,jdbcType=VARCHAR},
    person_type = #{record.personType,jdbcType=VARCHAR},
    person_name = #{record.personName,jdbcType=VARCHAR},
    foreign_name = #{record.foreignName,jdbcType=VARCHAR},
    title_name = #{record.titleName,jdbcType=VARCHAR},
    job_id = #{record.jobId,jdbcType=VARCHAR},
    org_name = #{record.orgName,jdbcType=VARCHAR},
    org_id = #{record.orgId,jdbcType=VARCHAR},
    sex = #{record.sex,jdbcType=VARCHAR},
    birthday = #{record.birthday,jdbcType=DATE},
    telephone = #{record.telephone,jdbcType=VARCHAR},
    telephone_spare1 = #{record.telephoneSpare1,jdbcType=VARCHAR},
    telephone_spare2 = #{record.telephoneSpare2,jdbcType=VARCHAR},
    phone = #{record.phone,jdbcType=VARCHAR},
    mail = #{record.mail,jdbcType=VARCHAR},
    mail_spare1 = #{record.mailSpare1,jdbcType=VARCHAR},
    mail_spare2 = #{record.mailSpare2,jdbcType=VARCHAR},
    fax = #{record.fax,jdbcType=VARCHAR},
    post = #{record.post,jdbcType=VARCHAR},
    address = #{record.address,jdbcType=VARCHAR},
    nationality = #{record.nationality,jdbcType=VARCHAR},
    certificate_type = #{record.certificateType,jdbcType=VARCHAR},
    certificate = #{record.certificate,jdbcType=VARCHAR},
    picture_url = #{record.pictureUrl,jdbcType=VARCHAR},
    user_id = #{record.userId,jdbcType=VARCHAR},
    resume_status = #{record.resumeStatus,jdbcType=VARCHAR},
    active_status = #{record.activeStatus,jdbcType=VARCHAR},
    company_name = #{record.companyName,jdbcType=VARCHAR},
    identity_type = #{record.identityType,jdbcType=VARCHAR},
    share_tradable = #{record.shareTradable,jdbcType=DECIMAL},
    share_count = #{record.shareCount,jdbcType=DECIMAL},
    company_shareholder = #{record.companyShareholder,jdbcType=VARCHAR},
    eid = #{record.eid,jdbcType=VARCHAR},
    company_code = #{record.companyCode,jdbcType=VARCHAR},
    source = #{record.source,jdbcType=VARCHAR},
    create_user = #{record.createUser,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_user = #{record.updateUser,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.PersonWithBLOBs">
    update sa_person
    <set>
      <if test="employeeNumber != null">
        employee_number = #{employeeNumber,jdbcType=VARCHAR},
      </if>
      <if test="personType != null">
        person_type = #{personType,jdbcType=VARCHAR},
      </if>
      <if test="personName != null">
        person_name = #{personName,jdbcType=VARCHAR},
      </if>
      <if test="foreignName != null">
        foreign_name = #{foreignName,jdbcType=VARCHAR},
      </if>
      <if test="titleName != null">
        title_name = #{titleName,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        job_id = #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="telephoneSpare1 != null">
        telephone_spare1 = #{telephoneSpare1,jdbcType=VARCHAR},
      </if>
      <if test="telephoneSpare2 != null">
        telephone_spare2 = #{telephoneSpare2,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        mail = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="mailSpare1 != null">
        mail_spare1 = #{mailSpare1,jdbcType=VARCHAR},
      </if>
      <if test="mailSpare2 != null">
        mail_spare2 = #{mailSpare2,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        fax = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="post != null">
        post = #{post,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="nationality != null">
        nationality = #{nationality,jdbcType=VARCHAR},
      </if>
      <if test="certificateType != null">
        certificate_type = #{certificateType,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        certificate = #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="resumeStatus != null">
        resume_status = #{resumeStatus,jdbcType=VARCHAR},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="identityType != null">
        identity_type = #{identityType,jdbcType=VARCHAR},
      </if>
      <if test="shareTradable != null">
        share_tradable = #{shareTradable,jdbcType=DECIMAL},
      </if>
      <if test="shareCount != null">
        share_count = #{shareCount,jdbcType=DECIMAL},
      </if>
      <if test="companyShareholder != null">
        company_shareholder = #{companyShareholder,jdbcType=VARCHAR},
      </if>
      <if test="eid != null">
        eid = #{eid,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="resume != null">
        resume = #{resume,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="weChatId != null">
        we_chat_id = #{weChatId, jdbcType=VARCHAR},
      </if>
      <if test="jobs != null">
        jobs = #{jobs, jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.stock.service.platform.common.entity.PersonWithBLOBs">
    update sa_person
    set employee_number = #{employeeNumber,jdbcType=VARCHAR},
        person_type = #{personType,jdbcType=VARCHAR},
        person_name = #{personName,jdbcType=VARCHAR},
        foreign_name = #{foreignName,jdbcType=VARCHAR},
        title_name = #{titleName,jdbcType=VARCHAR},
        job_id = #{jobId,jdbcType=VARCHAR},
        org_name = #{orgName,jdbcType=VARCHAR},
        org_id = #{orgId,jdbcType=VARCHAR},
        sex = #{sex,jdbcType=VARCHAR},
        birthday = #{birthday,jdbcType=DATE},
        telephone = #{telephone,jdbcType=VARCHAR},
        telephone_spare1 = #{telephoneSpare1,jdbcType=VARCHAR},
        telephone_spare2 = #{telephoneSpare2,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        mail = #{mail,jdbcType=VARCHAR},
        mail_spare1 = #{mailSpare1,jdbcType=VARCHAR},
        mail_spare2 = #{mailSpare2,jdbcType=VARCHAR},
        fax = #{fax,jdbcType=VARCHAR},
        post = #{post,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        nationality = #{nationality,jdbcType=VARCHAR},
        certificate_type = #{certificateType,jdbcType=VARCHAR},
        certificate = #{certificate,jdbcType=VARCHAR},
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        resume_status = #{resumeStatus,jdbcType=VARCHAR},
        active_status = #{activeStatus,jdbcType=VARCHAR},
        company_name = #{companyName,jdbcType=VARCHAR},
        identity_type = #{identityType,jdbcType=VARCHAR},
        share_tradable = #{shareTradable,jdbcType=DECIMAL},
        share_count = #{shareCount,jdbcType=DECIMAL},
        company_shareholder = #{companyShareholder,jdbcType=VARCHAR},
        eid = #{eid,jdbcType=VARCHAR},
        company_code = #{companyCode,jdbcType=VARCHAR},
        source = #{source,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR},
        resume = #{resume,jdbcType=LONGVARCHAR},
        remark = #{remark,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Person">
    update sa_person
    set employee_number = #{employeeNumber,jdbcType=VARCHAR},
        person_type = #{personType,jdbcType=VARCHAR},
        person_name = #{personName,jdbcType=VARCHAR},
        foreign_name = #{foreignName,jdbcType=VARCHAR},
        title_name = #{titleName,jdbcType=VARCHAR},
        job_id = #{jobId,jdbcType=VARCHAR},
        org_name = #{orgName,jdbcType=VARCHAR},
        org_id = #{orgId,jdbcType=VARCHAR},
        sex = #{sex,jdbcType=VARCHAR},
        birthday = #{birthday,jdbcType=DATE},
        telephone = #{telephone,jdbcType=VARCHAR},
        telephone_spare1 = #{telephoneSpare1,jdbcType=VARCHAR},
        telephone_spare2 = #{telephoneSpare2,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        mail = #{mail,jdbcType=VARCHAR},
        mail_spare1 = #{mailSpare1,jdbcType=VARCHAR},
        mail_spare2 = #{mailSpare2,jdbcType=VARCHAR},
        fax = #{fax,jdbcType=VARCHAR},
        post = #{post,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        nationality = #{nationality,jdbcType=VARCHAR},
        certificate_type = #{certificateType,jdbcType=VARCHAR},
        certificate = #{certificate,jdbcType=VARCHAR},
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        resume_status = #{resumeStatus,jdbcType=VARCHAR},
        active_status = #{activeStatus,jdbcType=VARCHAR},
        company_name = #{companyName,jdbcType=VARCHAR},
        identity_type = #{identityType,jdbcType=VARCHAR},
        share_tradable = #{shareTradable,jdbcType=DECIMAL},
        share_count = #{shareCount,jdbcType=DECIMAL},
        company_shareholder = #{companyShareholder,jdbcType=VARCHAR},
        eid = #{eid,jdbcType=VARCHAR},
        company_code = #{companyCode,jdbcType=VARCHAR},
        source = #{source,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.stock.service.platform.common.entity.PersonExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.PersonExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_person
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <insert id="insertUserCompanyMap">
    insert into sa_user_company_map(id,user_id,company_id,update_user,update_time)
    values
    <foreach collection="ruleUserCompanyMapList" item="item" index="index" separator=",">
      (
      uuid_short(),
      #{item.userId},
      #{item.companyId},
      #{item.updateUser},
      now()
      )
    </foreach>
  </insert>

  <select id="selectMaxSort" resultType="java.lang.Integer">
    select
      ifnull(max(su.sort), 0) + 1
    from
      sa_user su
    where  su.user_name like concat('%',#{companyCode},'%')
  </select>
  <update id="deletingByPersonId">
    update sa_person set status = #{status} where id = #{personId}
  </update>
  <update id="updateUserById">
    update sa_user
    <set>
      <if test="orgId != null">
        org_no = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        mail = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=LONGVARCHAR},
      </if>
      <if test="hiddenKey != null">
        hidden_key = #{hiddenKey,jdbcType=LONGVARCHAR},
      </if>
      <if test="mergeStatus != null">
        merge_status = #{mergeStatus,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateStatusByPrimaryKey">
    update sa_person set status = #{status} where id = #{personId}
  </update>
  <update id="updateUserStatus">
    update sa_person set status = #{status} where id = #{id}
  </update>
  <select id="selectPersonByTelephone" resultType="com.stock.service.platform.common.entity.Person">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sa_person
    where telephone = #{telephone}
  </select>
  <select id="selectCompanyCode" resultType="java.lang.String">
    select sc.company_code companyCode
    from sa_org so
    left join sa_company sc on so.company_id = sc.id
    where so.id = #{orgId}
  </select>
  <select id="getCompanyId" resultType="java.lang.String">
    select id from sa_company where company_code = #{companyCode}
  </select>
  <select id="getOrgIdsByPersonId" resultType="java.lang.String">
    select company_id from sa_user_company_map where user_id = #{id}
  </select>
  <select id="getCompanyIdByOrgId" resultType="java.lang.String">
    select company_id from sa_org where id = #{orgId}
  </select>
  <select id="getOldChildrenUserName" resultType="java.lang.String">
    select children_user_name from sa_person where id = #{id}
  </select>
  <select id="getOrgIdList" resultType="java.lang.String">
    SELECT GROUP_CONCAT(id)
    FROM sa_org
    WHERE company_id = (SELECT company_id FROM sa_org WHERE id = #{orgId});
  </select>
  <select id="selectByOrgId" resultType="com.stock.service.platform.common.entity.Person">
    select
    <include refid="Base_Column_List" />
    from sa_person
    where org_id like concat('%', #{id}, '%') or children_org like concat('%', #{id}, '%')
  </select>
  <select id="getOrgNameByOrgIds" resultType="java.lang.String">
    SELECT so.org_name
    FROM sa_org so
    <where>
      <if test="orgIdList != null">
        so.id IN
        <foreach item="item" index="index" collection="orgIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by so.org_name
  </select>
  <!--  <select id="getUserInfoByPersonId" resultType="com.stock.service.platform.user.dto.UserDto">-->
<!--    select id,-->
<!--           company_id,-->
<!--           org_no,-->
<!--           user_name,-->
<!--           password,-->
<!--           real_name,-->
<!--           -->
<!--    from sa_user where id = #{id}-->
<!--  </select>-->

</mapper>