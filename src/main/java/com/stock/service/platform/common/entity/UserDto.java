package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * sa_user
 * <AUTHOR>
public class UserDto implements Serializable {
    /**
     * 主键
     */
    private String id;

    private String companyId;

    /**
     * 所属部门ID
     */
    private String orgNo;

    /**
     * 登陆账号
     */
    private String userName;

    /**
     * 登陆密码
     */
    private String password;

    /**
     * 用户姓名
     */
    private String realName;

    /**
     * 移动电话
     */
    private String telephone;

    /**
     * 固定电话
     */
    private String phone;

    /**
     * 邮件
     */
    private String mail;

    /**
     * 传真
     */
    private String fax;

    /**
     * 岗位
     */
    private String jobs;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码修改时间
     */
    private Date passwordUpdateTime;

    /**
     * 锁定时间
     */
    private Date unlockTime;

    /**
     * 错误次数
     */
    private Integer errorNum;

    /**
     * 锁定类型
     */
    private String lockType;

    /**
     * 用户锁定状态（1:锁定）
     */
    private String lockState;

    /**
     * 是否为超级管理员标志
     */
    private String isAdmin;

    /**
     * 是否保留部门权限 1:保留
     */
    private String reserveDepPermission;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态：0删除、1有效、2删除中
     */
    private String status;

    private String userType;

    private String hiddenKey;

    private String isFirst;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否合并  空是未合并   1合并了别人  2被别人合并
     */
    private String mergeStatus;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getJobs() {
        return jobs;
    }

    public void setJobs(String jobs) {
        this.jobs = jobs;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getPasswordUpdateTime() {
        return passwordUpdateTime;
    }

    public void setPasswordUpdateTime(Date passwordUpdateTime) {
        this.passwordUpdateTime = passwordUpdateTime;
    }

    public Date getUnlockTime() {
        return unlockTime;
    }

    public void setUnlockTime(Date unlockTime) {
        this.unlockTime = unlockTime;
    }

    public Integer getErrorNum() {
        return errorNum;
    }

    public void setErrorNum(Integer errorNum) {
        this.errorNum = errorNum;
    }

    public String getLockType() {
        return lockType;
    }

    public void setLockType(String lockType) {
        this.lockType = lockType;
    }

    public String getLockState() {
        return lockState;
    }

    public void setLockState(String lockState) {
        this.lockState = lockState;
    }

    public String getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(String isAdmin) {
        this.isAdmin = isAdmin;
    }

    public String getReserveDepPermission() {
        return reserveDepPermission;
    }

    public void setReserveDepPermission(String reserveDepPermission) {
        this.reserveDepPermission = reserveDepPermission;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getHiddenKey() {
        return hiddenKey;
    }

    public void setHiddenKey(String hiddenKey) {
        this.hiddenKey = hiddenKey;
    }

    public String getIsFirst() {
        return isFirst;
    }

    public void setIsFirst(String isFirst) {
        this.isFirst = isFirst;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(String mergeStatus) {
        this.mergeStatus = mergeStatus;
    }
}