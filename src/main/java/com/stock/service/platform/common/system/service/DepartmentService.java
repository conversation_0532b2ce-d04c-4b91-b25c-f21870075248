package com.stock.service.platform.common.system.service;


import com.alibaba.fastjson.JSONObject;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.dao.PersonMapper;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.system.dao.DepartmentBizMapper;
import com.stock.service.platform.common.system.dto.DepartmentDto;
import com.stock.service.platform.common.tree.inter.TreeNode;
import com.stock.service.platform.common.tree.service.AbstractTreeService;
import com.stock.service.platform.common.util.CommonUtil;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.organization.dao.OrganizationBizMapper;
import com.stock.service.platform.organization.dto.OrgDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DepartmentService extends AbstractTreeService {

    private static final Logger logger = LoggerFactory.getLogger(DepartmentService.class);
    @Autowired
    private DepartmentBizMapper mapper;
    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private OrganizationBizMapper organizationBizMapper;


    @Autowired
    private SaSystemUtil saSystemUtil;

    @Autowired
    private TopicProducer topicProducer;

    @Resource
    private LogService logService;

    /**
     * 获取组织机构树
     *
     * @return
     */
    public Map<String,Object> listOrgTree(OrgDto orgDto) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrgDto> orgList = organizationBizMapper.getOrgList(orgDto);
        orgList = orgList.stream().map(data -> {
            if(StringUtils.equals(data.getOrgType(),"4")) {
                data.setDisabled(true);
            } else {
                data.setCompanyId(null);
            }
            return data;
        }).collect(Collectors.toList());
        map.put("treeSize",orgList.size());
        map.put("tree", CommonUtil.listToTree("0", orgList, "id", "pOrgId"));
        return map;
    }
    @Override
    public List<DepartmentDto> listNode(String treeId) {
        List<DepartmentDto> list = mapper.listDepartmentByCompanyId(treeId);
        List<DepartmentDto> treeList = CommonUtil.listToTree("0", list, "id", "pOrgId");
        return treeList;
    }

    @Override
    public  DepartmentDto getNodeByOrderLevelAndTreeId(String treeId,String treeLevel,Integer treeOrder,String pid) {

        return  mapper.getNodeByOrderLevelAndTreeId(treeId,treeOrder,treeLevel,pid);
    }

    @Override
    public void deleteRelation(TreeNode node) {
        PersonExample personExample = new PersonExample();
        personExample.createCriteria().andOrgIdEqualTo(node.getId());
        PersonWithBLOBs person = new PersonWithBLOBs();
        person.setStatus("0");
        personMapper.updateByExampleSelective(person,personExample);
    }

    @Override
    public void insertNode(TreeNode node) {
        DepartmentDto departmentDto = (DepartmentDto) node;
        Org org = new Org();
        BeanUtils.copyProperties(departmentDto,org);
        orgMapper.insertSelective(org);
        departmentDto.setId(org.getId());

        //向其他微服发送消息
        Map<String,Object> info = new HashMap<>();
        if ("2".equals(org.getOrgType())) {
            org.setBranchName(org.getOrgName());
            org.setBranchCode(org.getOrgCode());
        }
        if ("3".equals(org.getOrgType())) {
            org.setBusinessDepartment(org.getOrgName());
            org.setDepCode(org.getOrgCode());
            org.setTradeOrgId(org.getpOrgId());
        }
        List<Org> orgList = new ArrayList<>();
        String id = org.getpOrgId();
        int maxLevel = orgMapper.selOrgLevel();;
        for (int i = 0; i <= maxLevel; i++) {
            Org org1 = orgMapper.selParentById(id);
            if (org1 == null) {
                break;
            }
            orgList.add(org1);
            if ("1".equals(org1.getLevel()) || "1".equals(org1.getOrgType())) {
                break;
            }
            id = org1.getpOrgId();
        }
        info.put("org", org);
        info.put("orgP", orgList);
        info.put("userId", getUserInfo().getInfo().get("id"));
        info.put("isBranchCompany", "2".equals(org.getOrgType()) ? "1" : "0"); // 1 分公司, 0 营业部
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String,Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType("HGGL,GQJL");
        send.setBusinessType(TopicConstant.BUS_TYPE_ORG);
        send.setOperationType(TopicConstant.OP_TYPE_INSERT);
        send.setInfo(info);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(info, "HGGL,GQJL", TopicConstant.BUS_TYPE_ORG, TopicConstant.OP_TYPE_INSERT,uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(org),
                            null, "新增组织架构",
                            "系统管理/组织架构", "DepartmentService.insertNode",
                            "新增组织架构{" + org.getOrgName() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }

    @Override
    public void deleteNodeById(String id) {
        Org org = new Org();
        org.setId(id);
        org.setStatus("2");
        Org orgOld = orgMapper.selectByPrimaryKey(org.getId());
        orgMapper.updateByPrimaryKeySelective(org);

        //向其他微服发送消息
        Map<String,Object> info = new HashMap<>();
        if ("2".equals(org.getOrgType())) {
            org.setBranchName(org.getOrgName());
            org.setBranchCode(org.getOrgCode());
        }
        if ("3".equals(org.getOrgType())) {
            org.setBusinessDepartment(org.getOrgName());
            org.setDepCode(org.getOrgCode());
            org.setTradeOrgId(org.getpOrgId());
        }
        info.put("org", org);
        info.put("userId", getUserInfo().getInfo().get("id"));
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andIdEqualTo(id);
        List<Org> orgList = orgMapper.selectByExample(orgExample);
        info.put("isBranchCompany", "2".equals(orgList.get(0).getOrgType()) ? "1" : "0"); // 1 分公司, 0 营业部
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String,Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType("HGGL,GQJL");
        send.setBusinessType(TopicConstant.BUS_TYPE_ORG);
        send.setOperationType(TopicConstant.OP_TYPE_DELETE);
        send.setInfo(info);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(info, "HGGL,GQJL", TopicConstant.BUS_TYPE_ORG, TopicConstant.OP_TYPE_DELETE,uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(id,
                            null, "删除组织架构",
                            "系统管理/组织架构", "DepartmentService.deleteNodeById",
                            "删除组织架构{" + orgOld.getOrgName() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }

    @Override
    public TreeNode getNodeById(String from) {
        Org org = orgMapper.selectByPrimaryKey(from);
        DepartmentDto departmentDto = new DepartmentDto();
        BeanUtils.copyProperties(org,departmentDto);
        return departmentDto;
    }

    @Override
    public void updateNodeById(TreeNode node) {
        DepartmentDto departmentDto = (DepartmentDto) node;
        Org org = new Org();
        BeanUtils.copyProperties(departmentDto,org);
        Org orgOld = orgMapper.selectByPrimaryKey(org.getId());
        orgMapper.updateByPrimaryKey(org);

        //向其他微服发送消息
        Map<String,Object> info = new HashMap<>();
        if ("2".equals(org.getOrgType())) {
            org.setBranchName(org.getOrgName());
            org.setBranchCode(org.getOrgCode());
        }
        if ("3".equals(org.getOrgType())) {
            org.setBusinessDepartment(org.getOrgName());
            org.setDepCode(org.getOrgCode());
            org.setTradeOrgId(org.getpOrgId());
        }
        List<Org> orgList = new ArrayList<>();
        String id = org.getpOrgId();
        int maxLevel = orgMapper.selOrgLevel();
        for (int j = 0; j <= maxLevel; j++) {
            Org org1 = orgMapper.selParentById(id);
            if (org1 == null) break;
            orgList.add(org1);
            if ("1".equals(org1.getLevel()) || "1".equals(org1.getOrgType())) break;
            id = org1.getpOrgId();
        }
        info.put("org",org);
        info.put("userId", getUserInfo().getInfo().get("id"));
        info.put("isBranchCompany", "2".equals(org.getOrgType()) ? "1" : "0"); // 1 分公司, 0 营业部
        info.put("orgP", orgList);
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String,Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType("HGGL,GQJL");
        send.setBusinessType(TopicConstant.BUS_TYPE_ORG);
        send.setOperationType(TopicConstant.OP_TYPE_UPDATE);
        send.setInfo(info);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(info, "HGGL,GQJL", TopicConstant.BUS_TYPE_ORG, TopicConstant.OP_TYPE_INSERT,uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            MessageUtil messageUtil = new MessageUtil();
            org.setPOrgId(orgMapper.selectByPrimaryKey(org.getPOrgId()).getOrgName());
            orgOld.setPOrgId(orgMapper.selectByPrimaryKey(orgOld.getPOrgId()).getOrgName());
            MessageInfo messageInfo = messageUtil.getMessageContext(org, orgOld);
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(org),
                            messageInfo.getOperationContext(), "编辑组织架构",
                            "系统管理/组织架构", "DepartmentService.updateNodeById",
                            messageInfo.getOperationRemark(), ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
    }

    @Override
    public int getSameLevelNextOrder(String treeId, String pid) {
        return mapper.countByTreeIdAndPid(treeId,pid) + 1;
    }

    @Override
    public List<DepartmentDto> listChildNodesByPIdAndTreeId(String treeId, String id) {
        return mapper.listNodeByPidAndTreeId(treeId,id);
    }

    @Override
    protected List<DepartmentDto> listSameLevelGtOrder(String treeId, String pid, String level, Integer order) {
        return mapper.listGtNodeByPidAndTreeIdAndLevel(treeId,pid,level,order);
    }

    @Override
    protected void addNodeAttribute(Object attribute) {

    }

    @Override
    protected void setOrderName(List<? extends TreeNode> list) {

    }
    public  boolean checkOrgHasPerson(String ids) {
        //PersonExample personExample = new PersonExample();
        //personExample.createCriteria().andOrgIdLike(ids).andStatusEqualTo("1");
        //List<Person> personList = personMapper.selectByExample(personExample);
        List<Person> personList = personMapper.selectByOrgId(ids);
        return personList.size() > 0;
    }
    public boolean checkRepeatOrgCode(String id,String orgCode,Integer type) {
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andOrgCodeEqualTo(orgCode).andStatusEqualTo("1");
        List<Org> orgs = orgMapper.selectByExample(orgExample);
        if(orgs.size() > 0 && type == 1) {
            return true;
        }else if(orgs.size() > 0 && type == 0) {//编辑的时候,允许修改
            if(orgs.size() > 1) {
                return true;
            }else if(orgs.size() == 1) {
                if(!StringUtils.equals(orgs.get(0).getOrgCode(),orgMapper.selectByPrimaryKey(id).getOrgCode())) {
                    return true;
                }
            }
        }
        return false;
    }
    public boolean checkRepeatOrgName(String id,String orgName,Integer type) {
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andOrgNameEqualTo(orgName).andStatusEqualTo("1");
        List<Org> orgs = orgMapper.selectByExample(orgExample);
        if(orgs.size() > 0 && type == 1) {
            return true;
        }else if(orgs.size() > 0 && type == 0) {//编辑的时候,允许修改
            if(orgs.size() > 1) {
                return true;
            }else if(orgs.size() == 1) {
                if(!StringUtils.equals(orgs.get(0).getOrgName(),orgMapper.selectByPrimaryKey(id).getOrgName())) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<OrgDto> addFirstNode() {
        List<OrgDto> orgList = new ArrayList<>();
        Org org = new Org();
        org.setStatus("1");
        org.setOrgName("总部");
        org.setLevel("0");
        org.setOrderId(1);
        org.setCompanyId(getUserInfo().getCompanyId());
        org.setpOrgId("0");
        orgMapper.insertSelective(org);
        OrgDto orgDto = new OrgDto();
        BeanUtils.copyProperties(org, orgDto);
        orgList.add(orgDto);
        return orgList;
    }

    public List<Org> getOrgList(Org org) {
        OrgExample example = new OrgExample();
//        example.createCriteria().andIdEqualTo(org.getId());
        example.createCriteria().andIdIn(org.getIds()).andStatusEqualTo("1");
        List<Org> orgList = orgMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(orgList)) {
            int level = Integer.parseInt(orgList.get(0).getLevel());
            for (int i = 0; i < 4 - level; i++) {
                List<String> orgIds = orgList.stream()
                        .map(Org::getId)
                        .collect(Collectors.toList());
                example.clear();
                example.createCriteria().andPOrgIdIn(orgIds).andStatusEqualTo("1");
                orgList = orgMapper.selectByExample(example);
            }
        }
        orgList = new ArrayList<>(orgList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(Org::getCompanyId, Function.identity(), (existing, replacement) -> existing),
                        Map::values
                )));
        return orgList;
    }


    public List<OrgDto> getOrgListByPId(OrgDto orgDto) {
        return organizationBizMapper.getOrgListByPId(orgDto);
    }
}
