package com.stock.service.platform.common.sysCodeConvert;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-01-30 18:11
 */
public enum GQJLSystem implements CodeMapper{
    INSTANCE;

    private static final Map<String, Map<String, String>> codeMaps = new HashMap<>();

    static {
        // 添加市场码值映射
        Map<String, String> marketStockTypeMap = new HashMap<>();
        marketStockTypeMap.put("00", "0"); // 深市
        marketStockTypeMap.put("01", "0"); // 深市
        marketStockTypeMap.put("02", "0"); // 深市
        marketStockTypeMap.put("04", "1"); // 沪市
        marketStockTypeMap.put("07", "1"); // 沪市
        marketStockTypeMap.put("09", "2"); // 北交所
        marketStockTypeMap.put("05", "3"); // 新三板
        marketStockTypeMap.put("99", "4"); // 非上市公司
        codeMaps.put("marketStockType", marketStockTypeMap);
    }

    @Override
    public String mapCode(String platformCode, String type) {
        Map<String, String> typeMap = codeMaps.getOrDefault(type, new HashMap<>());
        return typeMap.getOrDefault(platformCode, "");
    }
}
