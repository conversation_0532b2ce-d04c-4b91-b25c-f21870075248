package com.stock.service.platform.common.system.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExcelSaLoginLogManageDto implements Serializable {


    //操作人姓名
    @ExcelProperty("操作人姓名")
    @ColumnWidth(10)
    private String userName;

    //ip
    @ExcelProperty("操作ip")
    @ColumnWidth(20)
    private String logIP;


    //操作类型
    @ExcelProperty("操作类型")
    @ColumnWidth(13)
    private String logName;


    //修改时间
    @ExcelProperty("操作时间")
    @ColumnWidth(20)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date logTime;
}
