package com.stock.service.platform.common.system.dao;

import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.system.dto.CompanyOrgDto;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InfoSyncMapper {

    /**
     * 新增org表
     */
    int insertOrgSelective(Org record);

    /**
     * 新增角色
     */
    int insertRoleSelective(OuterRole record);

    // 新增person表
    int insertPersonSelective(PersonWithBLOBs record);

    // 新增user表
    int insertUserSelective(SaUser record);

    /**
     * 查上市公司和org表关联关系
     */
  List<CompanyOrgDto> selectCompanyList();


    int insertCompanySelective(SaCompanyDto insertCompany);

}
