package com.stock.service.platform.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024-01-15 14:47
 */
@Data
public class ActivemqTopicDto implements Serializable {
    private String id;
    private String messageId;
    private String sysCode;
    private String info;
    private String isSuccess;
    private String messageType;
    private String errorMessage;
    private String businessType;
    private String operationType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
}
