<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.User">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="org_no" jdbcType="VARCHAR" property="orgNo" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="mail" jdbcType="VARCHAR" property="mail" />
    <result column="fax" jdbcType="VARCHAR" property="fax" />
    <result column="jobs" jdbcType="VARCHAR" property="jobs" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="password_update_time" jdbcType="DATE" property="passwordUpdateTime" />
    <result column="unlock_time" jdbcType="TIMESTAMP" property="unlockTime" />
    <result column="error_num" jdbcType="INTEGER" property="errorNum" />
    <result column="lock_type" jdbcType="VARCHAR" property="lockType" />
    <result column="lock_state" jdbcType="VARCHAR" property="lockState" />
    <result column="is_admin" jdbcType="VARCHAR" property="isAdmin" />
    <result column="reserve_dep_permission" jdbcType="VARCHAR" property="reserveDepPermission" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="is_first" jdbcType="VARCHAR" property="isFirst" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, org_no, user_name, password, real_name, telephone, phone, mail, fax,
    jobs, remark, password_update_time, unlock_time, error_num, lock_type, lock_state,
    is_admin, reserve_dep_permission, create_user, create_time, update_user, update_time,
    status,is_first, user_type
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from sa_user
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByIdSeriakey" parameterType="java.util.Map" resultType="java.lang.Integer">
    select
      count(1)
    from sa_person
    where id = #{id,jdbcType=VARCHAR}
      and hidden_key = #{seriakey,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_user
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.UserExample">
    delete from sa_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.User">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into sa_user (id, company_id, org_no,
    user_name, password, real_name,
    telephone, phone, mail,
    fax, jobs, remark,
    password_update_time, unlock_time, error_num,
    lock_type, lock_state, is_admin,
    reserve_dep_permission, create_user, create_time,
    update_user, update_time, status
    )
    values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{orgNo,jdbcType=VARCHAR},
    #{userName,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR},
    #{telephone,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{mail,jdbcType=VARCHAR},
    #{fax,jdbcType=VARCHAR}, #{jobs,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
    #{passwordUpdateTime,jdbcType=DATE}, #{unlockTime,jdbcType=TIMESTAMP}, #{errorNum,jdbcType=INTEGER},
    #{lockType,jdbcType=VARCHAR}, #{lockState,jdbcType=VARCHAR}, #{isAdmin,jdbcType=VARCHAR},
    #{reserveDepPermission,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.User">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into sa_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,

      <if test="companyId != null">
        company_id,
      </if>
      <if test="orgNo != null">
        org_no,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="mail != null">
        mail,
      </if>
      <if test="fax != null">
        fax,
      </if>
      <if test="jobs != null">
        jobs,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="passwordUpdateTime != null">
        password_update_time,
      </if>
      <if test="unlockTime != null">
        unlock_time,
      </if>
      <if test="errorNum != null">
        error_num,
      </if>
      <if test="lockType != null">
        lock_type,
      </if>
      <if test="lockState != null">
        lock_state,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="reserveDepPermission != null">
        reserve_dep_permission,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},

      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgNo != null">
        #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        #{mail,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="jobs != null">
        #{jobs,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="passwordUpdateTime != null">
        #{passwordUpdateTime,jdbcType=DATE},
      </if>
      <if test="unlockTime != null">
        #{unlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorNum != null">
        #{errorNum,jdbcType=INTEGER},
      </if>
      <if test="lockType != null">
        #{lockType,jdbcType=VARCHAR},
      </if>
      <if test="lockState != null">
        #{lockState,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=VARCHAR},
      </if>
      <if test="reserveDepPermission != null">
        #{reserveDepPermission,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.UserExample" resultType="java.lang.Long">
    select count(*) from sa_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgNo != null">
        org_no = #{record.orgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.telephone != null">
        telephone = #{record.telephone,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.mail != null">
        mail = #{record.mail,jdbcType=VARCHAR},
      </if>
      <if test="record.fax != null">
        fax = #{record.fax,jdbcType=VARCHAR},
      </if>
      <if test="record.jobs != null">
        jobs = #{record.jobs,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.passwordUpdateTime != null">
        password_update_time = #{record.passwordUpdateTime,jdbcType=DATE},
      </if>
      <if test="record.unlockTime != null">
        unlock_time = #{record.unlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.errorNum != null">
        error_num = #{record.errorNum,jdbcType=INTEGER},
      </if>
      <if test="record.lockType != null">
        lock_type = #{record.lockType,jdbcType=VARCHAR},
      </if>
      <if test="record.lockState != null">
        lock_state = #{record.lockState,jdbcType=VARCHAR},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveDepPermission != null">
        reserve_dep_permission = #{record.reserveDepPermission,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_user
    set id = #{record.id,jdbcType=VARCHAR},
    company_id = #{record.companyId,jdbcType=VARCHAR},
    org_no = #{record.orgNo,jdbcType=VARCHAR},
    user_name = #{record.userName,jdbcType=VARCHAR},
    password = #{record.password,jdbcType=VARCHAR},
    real_name = #{record.realName,jdbcType=VARCHAR},
    telephone = #{record.telephone,jdbcType=VARCHAR},
    phone = #{record.phone,jdbcType=VARCHAR},
    mail = #{record.mail,jdbcType=VARCHAR},
    fax = #{record.fax,jdbcType=VARCHAR},
    jobs = #{record.jobs,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
    password_update_time = #{record.passwordUpdateTime,jdbcType=DATE},
    unlock_time = #{record.unlockTime,jdbcType=TIMESTAMP},
    error_num = #{record.errorNum,jdbcType=INTEGER},
    lock_type = #{record.lockType,jdbcType=VARCHAR},
    lock_state = #{record.lockState,jdbcType=VARCHAR},
    is_admin = #{record.isAdmin,jdbcType=VARCHAR},
    reserve_dep_permission = #{record.reserveDepPermission,jdbcType=VARCHAR},
    create_user = #{record.createUser,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_user = #{record.updateUser,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.User">
    update sa_user
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgNo != null">
        org_no = #{orgNo,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null">
        mail = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        fax = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="jobs != null">
        jobs = #{jobs,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="passwordUpdateTime != null">
        password_update_time = #{passwordUpdateTime,jdbcType=DATE},
      </if>
      <if test="unlockTime != null">
        unlock_time = #{unlockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorNum != null">
        error_num = #{errorNum,jdbcType=INTEGER},
      </if>
      <if test="lockType != null">
        lock_type = #{lockType,jdbcType=VARCHAR},
      </if>
      <if test="lockState != null">
        lock_state = #{lockState,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=VARCHAR},
      </if>
      <if test="reserveDepPermission != null">
        reserve_dep_permission = #{reserveDepPermission,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.User">
    update sa_user
    set company_id = #{companyId,jdbcType=VARCHAR},
        org_no = #{orgNo,jdbcType=VARCHAR},
        user_name = #{userName,jdbcType=VARCHAR},
        password = #{password,jdbcType=VARCHAR},
        real_name = #{realName,jdbcType=VARCHAR},
        telephone = #{telephone,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        mail = #{mail,jdbcType=VARCHAR},
        fax = #{fax,jdbcType=VARCHAR},
        jobs = #{jobs,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        password_update_time = #{passwordUpdateTime,jdbcType=DATE},
        unlock_time = #{unlockTime,jdbcType=TIMESTAMP},
        error_num = #{errorNum,jdbcType=INTEGER},
        lock_type = #{lockType,jdbcType=VARCHAR},
        lock_state = #{lockState,jdbcType=VARCHAR},
        is_admin = #{isAdmin,jdbcType=VARCHAR},
        reserve_dep_permission = #{reserveDepPermission,jdbcType=VARCHAR},
        create_user = #{createUser,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        status = #{status,jdbcType=VARCHAR},
        is_first = #{isFirst,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.UserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByUserName" resultType="com.stock.service.platform.common.entity.User">
    select
    <include refid="Base_Column_List" />
    from sa_user
    where user_name = #{userName,jdbcType=VARCHAR}
  </select>
  <select id="getUserIdByUserName" resultType="java.lang.String">
    select id from sa_user where user_name = #{userName}
  </select>
    <select id="getUserTypeByUserName" resultType="java.lang.String">
      select sp.person_type
      from sa_person sp
      left join sa_user su on su.id = sp.id
      where su.user_name = #{userName}
    </select>

    <update id="deletingByUserId">
    update sa_user set lock_state = #{lockState}, status = #{status} where id = #{userId}
  </update>
</mapper>