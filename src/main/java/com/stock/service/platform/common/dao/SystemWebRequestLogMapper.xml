<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.SystemWebRequestLogMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.SystemWebRequestLog">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ip_address" jdbcType="VARCHAR" property="ipAddress" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="request_type" jdbcType="VARCHAR" property="requestType" />
    <result column="clazz_name" jdbcType="VARCHAR" property="clazzName" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs">
    <result column="request_url" jdbcType="LONGVARCHAR" property="requestUrl" />
    <result column="method_param" jdbcType="LONGVARCHAR" property="methodParam" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ip_address, user_id, request_type, clazz_name, method_name, create_time
  </sql>
  <sql id="Blob_Column_List">
    request_url, method_param
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from system_web_request_log
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from system_web_request_log
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample">
    delete from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into system_web_request_log (id, ip_address, user_id, 
      request_type, clazz_name, method_name, 
      create_time, system_name, request_url, method_param
      )
    values (#{id,jdbcType=VARCHAR}, #{ipAddress,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{requestType,jdbcType=VARCHAR}, #{clazzName,jdbcType=VARCHAR}, #{methodName,jdbcType=VARCHAR},
        #{createTime,jdbcType=LONGVARCHAR}, #{systemName,jdbcType=LONGVARCHAR}, #{requestUrl,jdbcType=LONGVARCHAR}, #{methodParam,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into system_web_request_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="ipAddress != null">
        ip_address,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="requestType != null">
        request_type,
      </if>
      <if test="clazzName != null">
        clazz_name,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="methodParam != null">
        method_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="ipAddress != null">
        #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="requestType != null">
        #{requestType,jdbcType=VARCHAR},
      </if>
      <if test="clazzName != null">
        #{clazzName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="methodParam != null">
        #{methodParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample" resultType="java.lang.Long">
    select count(*) from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_web_request_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.ipAddress != null">
        ip_address = #{record.ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.requestType != null">
        request_type = #{record.requestType,jdbcType=VARCHAR},
      </if>
      <if test="record.clazzName != null">
        clazz_name = #{record.clazzName,jdbcType=VARCHAR},
      </if>
      <if test="record.methodName != null">
        method_name = #{record.methodName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.methodParam != null">
        method_param = #{record.methodParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update system_web_request_log
    set id = #{record.id,jdbcType=VARCHAR},
      ip_address = #{record.ipAddress,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      request_type = #{record.requestType,jdbcType=VARCHAR},
      clazz_name = #{record.clazzName,jdbcType=VARCHAR},
      method_name = #{record.methodName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      request_url = #{record.requestUrl,jdbcType=LONGVARCHAR},
      method_param = #{record.methodParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_web_request_log
    set id = #{record.id,jdbcType=VARCHAR},
      ip_address = #{record.ipAddress,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      request_type = #{record.requestType,jdbcType=VARCHAR},
      clazz_name = #{record.clazzName,jdbcType=VARCHAR},
      method_name = #{record.methodName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs">
    update system_web_request_log
    <set>
      <if test="ipAddress != null">
        ip_address = #{ipAddress,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="requestType != null">
        request_type = #{requestType,jdbcType=VARCHAR},
      </if>
      <if test="clazzName != null">
        clazz_name = #{clazzName,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="methodParam != null">
        method_param = #{methodParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs">
    update system_web_request_log
    set ip_address = #{ipAddress,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      request_type = #{requestType,jdbcType=VARCHAR},
      clazz_name = #{clazzName,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      request_url = #{requestUrl,jdbcType=LONGVARCHAR},
      method_param = #{methodParam,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLog">
    update system_web_request_log
    set ip_address = #{ipAddress,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      request_type = #{requestType,jdbcType=VARCHAR},
      clazz_name = #{clazzName,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.SystemWebRequestLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_web_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>