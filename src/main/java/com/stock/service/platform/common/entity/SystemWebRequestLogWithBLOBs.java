package com.stock.service.platform.common.entity;

import java.io.Serializable;

public class SystemWebRequestLogWithBLOBs extends SystemWebRequestLog implements Serializable {
    private String requestUrl;

    private String methodParam;

    private static final long serialVersionUID = 1L;

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl == null ? null : requestUrl.trim();
    }

    public String getMethodParam() {
        return methodParam;
    }

    public void setMethodParam(String methodParam) {
        this.methodParam = methodParam == null ? null : methodParam.trim();
    }
}