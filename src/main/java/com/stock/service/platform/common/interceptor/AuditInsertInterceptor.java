package com.stock.service.platform.common.interceptor;

import com.stock.core.misc.DefaultIdWorker;
import com.stock.core.misc.IdWorker;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Properties;


/**
 * 自动注入主键id
 *
 * <AUTHOR>
 */
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class AuditInsertInterceptor implements Interceptor {

    private final IdWorker idWorker = new DefaultIdWorker();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        Object parameter = invocation.getArgs()[1];


        // 处理每一条
        if (parameter instanceof MapperMethod.ParamMap) {
            // 循环遍历paramMap 每一个键值，然后处理
            ((MapperMethod.ParamMap<?>) parameter).forEach((k, v) -> {
                if (v instanceof Collection) {
                    ((Collection) v).forEach(perV -> {
                        doEachParam(sqlCommandType, perV);
                    });
                } else {
                    doEachParam(sqlCommandType, v);
                }
            });
        } else {
            doEachParam(sqlCommandType, parameter);
        }


        return invocation.proceed();
    }

    private void doEachParam(SqlCommandType sqlCommandType, Object parameter) {
        if (SqlCommandType.INSERT == sqlCommandType) {
            setIdValue(parameter);
        }
    }

    /**
     * 只拦截Executor
     *
     * @param target 拦截对象
     * @return Object
     */
    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }

    /**
     * 设置插件参数
     */
    @Override
    public void setProperties(Properties p) {
    }

    /**
     * 设置审计字段值
     *
     * @param parameter 参数
     */
    private void setIdValue(Object parameter) {
        if (parameter == null) {
            return;
        }
        Field updateUserField = ReflectionUtils.findField(parameter.getClass(), "id");
        if (updateUserField != null) {
            ReflectionUtils.makeAccessible(updateUserField);
            // 不存在为空，才会插入新的id
            if (ReflectionUtils.getField(updateUserField, parameter) == null) {
                ReflectionUtils.setField(updateUserField, parameter, idWorker.generateId());
            }
        }
    }
}
