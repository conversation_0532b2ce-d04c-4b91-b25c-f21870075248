<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OuterRoleBizMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.OuterRole">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_desc" jdbcType="VARCHAR" property="roleDesc" />
  </resultMap>

  <sql id="Base_Column_List">
    id, sys_code, source, role_name, role_desc
  </sql>
  <select id="getStockRoleList" resultMap="BaseResultMap">
    select id,
           sys_code,
           source,
           role_name,
           role_desc,
           role_type
    from sa_outer_role
    where sys_code = 'GQJL'
  </select>

</mapper>