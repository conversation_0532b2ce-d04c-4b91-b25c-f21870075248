package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * sa_user
 * <AUTHOR>
@Data
public class SaUser implements Serializable {
    /**
     * 主键
     */
    private String id;

    private String companyId;

    /**
     * 所属部门ID
     */
    private String orgNo;

    /**
     * 登陆账号
     */
    private String userName;

    /**
     * 登陆密码
     */
    private String password;

    /**
     * 用户姓名
     */
    private String realName;

    /**
     * 移动电话
     */
    private String telephone;

    /**
     * 固定电话
     */
    private String phone;

    /**
     * 邮件
     */
    private String mail;

    /**
     * 传真
     */
    private String fax;

    /**
     * 岗位
     */
    private String jobs;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码修改时间
     */
    private Date passwordUpdateTime;

    /**
     * 锁定时间
     */
    private Date unlockTime;

    /**
     * 错误次数
     */
    private Integer errorNum;

    /**
     * 锁定类型
     */
    private String lockType;

    /**
     * 用户锁定状态（1:锁定）
     */
    private String lockState;

    /**
     * 是否为超级管理员标志
     */
    private String isAdmin;

    /**
     * 是否保留部门权限 1:保留
     */
    private String reserveDepPermission;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态：0删除、1有效、2删除中
     */
    private String status;

    private String userType;

    private String hiddenKey;

    private String isFirst;

    /**
     * 排序
     */
    private Integer sort;

    private static final long serialVersionUID = 1L;
}