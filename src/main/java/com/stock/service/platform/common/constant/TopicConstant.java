package com.stock.service.platform.common.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TopicConstant {
    // 业务类型
    public static final String BUS_TYPE_COMPANY = "company";// 公司
    public static final Map<String,String> BUS_TYPE_COMPANY_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_COMPANY); put("label", "公司");
    }};
    public static final String BUS_TYPE_LOGOUT = "logout";// 退出
    public static final Map<String,String> BUS_TYPE_LOGOUT_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_LOGOUT); put("label", "退出");
    }};
    public static final String BUS_TYPE_ROLE = "role"; // 角色
    public static final Map<String,String> BUS_TYPE_ROLE_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_ROLE); put("label", "角色");
    }};
    public static final String BUS_TYPE_USER = "user"; // 内外部用户
    public static final Map<String,String> BUS_TYPE_USER_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_USER); put("label", "用户");
    }};
    public static final String BUS_TYPE_PERMISSION = "permission"; // 用户授权（用户对应角色）
    public static final Map<String,String> BUS_TYPE_PERMISSION_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_PERMISSION); put("label", "用户授权");
    }};
    public static final String BUS_TYPE_ORG = "org"; // 组织架构
    public static final Map<String,String> BUS_TYPE_ORG_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_ORG); put("label", "组织架构");
    }};
    public static final String BUS_TYPE_LOG = "log"; // 日志
    public static final Map<String,String> BUS_TYPE_LOG_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_LOG); put("label", "日志");
    }};
    public static final String BUS_TYPE_TIMED_TASKS = "timedTasks"; // 定时任务
    public static final Map<String,String> BUS_TYPE_TIMED_TASKS_MAP = new HashMap<String,String>() {{
        put("value", BUS_TYPE_TIMED_TASKS); put("label", "定时任务");
    }};

    public static final List<String> BUS_TYPE_LIST = new ArrayList<String>() {{
        add(BUS_TYPE_COMPANY); add(BUS_TYPE_ROLE);
        add(BUS_TYPE_USER); add(BUS_TYPE_PERMISSION); add(BUS_TYPE_ORG); add(BUS_TYPE_TIMED_TASKS); add(BUS_TYPE_LOG);
    }};
    public static final List<Map<String,String>> BUS_TYPE_LIST_MAP = new ArrayList<Map<String,String>>() {{
        add(BUS_TYPE_COMPANY_MAP); add(BUS_TYPE_ROLE_MAP);add(BUS_TYPE_USER_MAP); add(BUS_TYPE_PERMISSION_MAP);
        add(BUS_TYPE_ORG_MAP); add(BUS_TYPE_TIMED_TASKS_MAP); add(BUS_TYPE_LOG_MAP);
    }};
    // 操作类型
    public static final String OP_TYPE_INSERT = "insert";
    public static final Map<String,String> OP_TYPE_INSERT_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_INSERT); put("label", "新增");
    }};
    public static final String OP_TYPE_LOCK = "lock";
    public static final Map<String,String> OP_TYPE_LOCK_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_LOCK); put("label", "锁定");
    }};
    public static final String OP_TYPE_OUT_USER = "outUser";
    public static final Map<String,String> OP_TYPE_OUT_USER_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_OUT_USER); put("label", "合规外部用户导入");
    }};
    public static final String OP_TYPE_DELETE = "delete";
    public static final Map<String,String> OP_TYPE_DELETE_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_DELETE); put("label", "删除");
    }};
    public static final String OP_TYPE_ALL = "ALL";
    public static final String OP_TYPE_LOGOUT = "logout";
    public static final String OP_TYPE_UPDATE = "update";
    public static final Map<String,String> OP_TYPE_UPDATE_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_UPDATE); put("label", "更新");
    }};
    public static final String OP_TYPE_SETUSER = "setuser";
    public static final Map<String,String> OP_TYPE_SETUSER_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_SETUSER); put("label", "设置用户(回购专用)");
    }};
    public static final String OP_TYPE_MERGE = "merge";
    public static final Map<String,String> OP_TYPE_MERGE_MAP = new HashMap<String,String>() {{
        put("value", OP_TYPE_MERGE); put("label", "外部用户合并");
    }};

    public static final String OP_TYPE_I = "I";
    public static final String OP_TYPE_U = "U";
    public static final String OP_TYPE_REPEAT = "repeat";

    public static final List<String> OP_TYPE_LIST = new ArrayList<String>() {{
        add(OP_TYPE_INSERT); add(OP_TYPE_DELETE); add(OP_TYPE_UPDATE); add(OP_TYPE_LOCK); add(OP_TYPE_SETUSER);
        add(OP_TYPE_MERGE);add(OP_TYPE_OUT_USER);
        add(OP_TYPE_REPEAT); add(OP_TYPE_I); add(OP_TYPE_U);
    }};

    public static final List<Map<String,String>> OP_TYPE_LIST_MAP = new ArrayList<Map<String,String>>() {{
        add(OP_TYPE_INSERT_MAP); add(OP_TYPE_DELETE_MAP); add(OP_TYPE_UPDATE_MAP); add(OP_TYPE_LOCK_MAP);
        add(OP_TYPE_SETUSER_MAP); add(OP_TYPE_MERGE_MAP); add(OP_TYPE_OUT_USER_MAP);
    }};

}
