package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.OuterRoleRouterMap;
import com.stock.service.platform.common.entity.OuterRoleRouterMapExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface OuterRoleRouterMapMapper {
    long countByExample(OuterRoleRouterMapExample example);

    int deleteByExample(OuterRoleRouterMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(OuterRoleRouterMap record);

    int insertSelective(OuterRoleRouterMap record);

    List<OuterRoleRouterMap> selectByExampleWithRowbounds(OuterRoleRouterMapExample example, RowBounds rowBounds);

    List<OuterRoleRouterMap> selectByExample(OuterRoleRouterMapExample example);

    OuterRoleRouterMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") OuterRoleRouterMap record, @Param("example") OuterRoleRouterMapExample example);

    int updateByExample(@Param("record") OuterRoleRouterMap record, @Param("example") OuterRoleRouterMapExample example);

    int updateByPrimaryKeySelective(OuterRoleRouterMap record);

    int updateByPrimaryKey(OuterRoleRouterMap record);
}