package com.stock.service.platform.common.entity;

import java.io.Serializable;

public class ChatContentWithBLOBs extends ChatContent implements Serializable {
    private String questionContent;

    private String answerContent;

    private String citeContent;

    private String prompt;

    private static final long serialVersionUID = 1L;

    public String getQuestionContent() {
        return questionContent;
    }

    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent == null ? null : questionContent.trim();
    }

    public String getAnswerContent() {
        return answerContent;
    }

    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent == null ? null : answerContent.trim();
    }

    public String getCiteContent() {
        return citeContent;
    }

    public void setCiteContent(String citeContent) {
        this.citeContent = citeContent == null ? null : citeContent.trim();
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt == null ? null : prompt.trim();
    }
}