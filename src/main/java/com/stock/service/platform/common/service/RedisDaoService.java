package com.stock.service.platform.common.service;

import com.stock.core.dao.RedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class RedisDaoService extends RedisDao {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public boolean lock(String lockKey,String lockValue,long lockExpireTime) {
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, lockExpireTime, TimeUnit.MILLISECONDS);
        return success != null && success;
    }

    public boolean unlock(String lockKey,String lockValue) {
        Object valueObj = redisTemplate.opsForValue().get(lockKey);
        if (valueObj instanceof String) {
            String currentLockValue = (String) redisTemplate.opsForValue().get(lockKey);
            if (lockValue.equals(currentLockValue)) {
                redisTemplate.delete(lockKey);
                return true;
            }
        }
        return false;
    }

    public boolean renewLock(String lockKey,String lockValue,long lockExpireTime) {
        Object valueObj = redisTemplate.opsForValue().get(lockKey);
        if (valueObj instanceof String) {
            String currentLockValue = (String) redisTemplate.opsForValue().get(lockKey);
            if (lockValue.equals(currentLockValue)) {
                redisTemplate.expire(lockKey, lockExpireTime, TimeUnit.MILLISECONDS);
                return true;
            }
        }
        return false;
    }
}
