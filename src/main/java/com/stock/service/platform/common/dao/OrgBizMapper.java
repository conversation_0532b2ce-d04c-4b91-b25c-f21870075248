package com.stock.service.platform.common.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface OrgBizMapper {
    //List<Map<String,String>> selectOrgDictByIsMgr(@Param("params") Map params);

    String selectOrgIdByBranch(@Param("orgId") String orgId);

    String selectOrgIdByDept(@Param("orgId") String orgId);

    String selectOrgIdByBranchBesb(@Param("orgId") String orgId);

    String checkBranchIfShare(@Param("orgId") String orgId);

    // 仅可用于外部公司
    //Org selectByCompanyId(@Param("companyId") String companyId);

    //Org selectByUserId(@Param("userId") String userId);
}
