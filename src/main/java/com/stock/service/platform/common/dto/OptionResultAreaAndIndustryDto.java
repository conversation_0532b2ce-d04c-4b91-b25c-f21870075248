package com.stock.service.platform.common.dto;

import com.google.common.collect.Lists;

import java.io.Serializable;
import java.util.List;

public class OptionResultAreaAndIndustryDto implements Serializable {


    private static final long serialVersionUID = 7682844141337830615L;

    private String label;

    private List<OptionResultAreaAndIndustryDto> items;

    private String initial;

    private String value;

    private String name;

    private String id;

    private String pId;

    private List<OptionResultAreaAndIndustryDto> children = Lists.newArrayList();

    private String areaGroup;//地区分组

    public String getAreaGroup() {
        return areaGroup;
    }

    public void setAreaGroup(String areaGroup) {
        this.areaGroup = areaGroup;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<OptionResultAreaAndIndustryDto> getItems() {
        return items;
    }

    public void setItems(List<OptionResultAreaAndIndustryDto> items) {
        this.items = items;
    }

    public String getInitial() {
        return initial;
    }

    public void setInitial(String initial) {
        this.initial = initial;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpId() {
        return pId;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

    public List<OptionResultAreaAndIndustryDto> getChildren() {
        return children;
    }

    public void setChildren(List<OptionResultAreaAndIndustryDto> children) {
        this.children = children;
    }
}
