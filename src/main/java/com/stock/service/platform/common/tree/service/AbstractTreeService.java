package com.stock.service.platform.common.tree.service;

import com.google.common.collect.Lists;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.system.dao.DepartmentBizMapper;
import com.stock.service.platform.common.tree.inter.TreeNode;
import com.stock.service.platform.common.tree.inter.TreeService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public abstract class AbstractTreeService extends BaseService implements TreeService {

    @Resource
    private DepartmentBizMapper mapper;

    @Override
    public  <T extends TreeNode> T addNode(T node) {
        int order = mapper.countByPid(node.getPid()) + 1;
        node.setTreeOrder(order);
        insertNode(node);
        // 下面两个方法是空的
        addNodeAttribute(node.getAttribute());
        setOrderName(Lists.newArrayList(node));
        return node;
    }




    @Override
    public  <T extends TreeNode> List<T> moveDownNode(T node) {
        synchronized (AbstractTreeService.class) {
            T nextNode = getSameLevelNextNode(node);
            if (nextNode == null) {
                throw new RuntimeException("当前节点为最底层节点,不能再向下移动了");
            }
            node.setTreeOrder(node.getTreeOrder() + 1);
            nextNode.setTreeOrder(nextNode.getTreeOrder() - 1);
            updateNodeById(node);
            updateNodeById(nextNode);
            setOrderName(Lists.newArrayList(node, nextNode));
            List<T> list = new ArrayList<>();
            list.add(node);
            list.add(nextNode);
            Lists.newArrayList(node, nextNode);
            return list;
        }
    }


    @Override
    public <T extends TreeNode> List<T>  moveUpNode(T node) {
        synchronized (AbstractTreeService.class) {
            T preNode = getSameLevelPreNode(node);
            if(preNode == null )  {
                throw new RuntimeException("当前节点为最顶级节点,不能再向上移动了");
            }
            node.setTreeOrder(node.getTreeOrder() - 1);
            preNode.setTreeOrder(preNode.getTreeOrder() + 1);
            updateNodeById(node);
            updateNodeById(preNode);
            setOrderName(Lists.newArrayList(node,preNode));
            ArrayList<T> list = new ArrayList<>();
            list.add(node);
            list.add(preNode);
            return list;
        }
    }



    @Override
    public <T extends TreeNode> T addChildNode(T node) {
        int order = mapper.countByPid(node.getId()) + 1;
        node.setPid(node.getId());
        node.setId(null);
        node.setTreeLevel(String.valueOf(Integer.parseInt(node.getTreeLevel()) + 1));
        node.setTreeOrder(order);
        insertNode(node);
        addNodeAttribute(node);
        setOrderName(Lists.newArrayList(node));
        return node;
    }

    @Override
    public List<TreeNode> adjustNode(String from,String to,String type) {
        TreeNode fromNode = getNodeById(from);
        String treeId = fromNode.getTreeId();
        String pidOld = fromNode.getPid();
        Integer orderOld = fromNode.getTreeOrder();
        String levelOld = fromNode.getTreeLevel();

        if (StringUtils.equals(type, "0")) {//调整到子节点
            TreeNode toNode = getNodeById(to);
            //移动到子节点
            fromNode.setPid(toNode.getId());
            fromNode.setTreeLevel(String.valueOf(Integer.parseInt(toNode.getTreeLevel()) + 1));
            fromNode.setTreeOrder(getSameLevelNextOrder(toNode.getTreeId(),toNode.getId()));

        }
        //else {//调整到顶级节点
        //    fromNode.setPid("0");
        //    fromNode.setTreeLevel("0");
        //    Integer order = getSameLevelNextOrder(treeId, "0");
        //    fromNode.setTreeOrder(order);
        //}
        updateNodeById(fromNode);
        //移动之后子节点重新排序
        //listToTreeOrganizeLevel(fromNode.getTreeLevel(),listChildNodesByPIdAndTreeId(fromNode.getTreeId(),fromNode.getId()));
        //调整节点,在未移动之前,在此节点之后的节点重新排序
        organizeTreeOrder(treeId,pidOld,levelOld,orderOld);
        return listNode(treeId);
    }



    /**
     * @Title: organizeTreeOrder
     * @Description: 在当前级别中所有大于order的节点向上移动一步
     * @param: @param treeId
     * @param: @param pidOld
     * @param: @param levelOld
     * @param: @param orderOld
     * @return: void
     * @throws
     */
    protected void organizeTreeOrder(String treeId, String pid, String level, Integer order) {
        List<? extends TreeNode> nodeList = listSameLevelGtOrder(treeId, pid, level, order);
        for (TreeNode node : nodeList) {
            node.setTreeOrder(node.getTreeOrder() - 1);
            updateNodeById(node);
        }
    }


    /**
     * 功能描述: <br>递归更新子节点的级别
     * @Param: [level, lists]
     * @Return: void
     * @Author: DarkSql
     * @Date: 2020/7/6 15:58
     */
    public void listToTreeOrganizeLevel(String level, List<? extends TreeNode> lists) {
        for (TreeNode node : lists) {
            node.setTreeLevel((Integer.parseInt(level) + 1) + "");
            updateNodeById(node);
            List<? extends TreeNode> nodeList = listChildNodesByPIdAndTreeId(node.getTreeId(), node.getId());
            if (nodeList.size() > 0) {
                listToTreeOrganizeLevel(node.getTreeLevel(),nodeList);
            }
        }
    }
    @Override
    public List<TreeNode> delete(String nodeId) {
        TreeNode node = getNodeById(nodeId);
        listToDeleteNode(Lists.newArrayList(node));
        organizeTreeOrder(node.getTreeId(), node.getPid(), node.getTreeLevel(), node.getTreeOrder());
        List<TreeNode> nodeList = listChildNodesByPIdAndTreeId(node.getTreeId(),node.getPid());
        setOrderName(nodeList);
        return  nodeList;
    }

    public void listToDeleteNode(List<? extends TreeNode> list) {

        for (TreeNode node : list) {
            deleteNodeById(node.getId());
            deleteRelation(node);
            List<? extends TreeNode> childNodes = listChildNodesByPIdAndTreeId(node.getTreeId(),node.getId());
            if (childNodes.size() > 0) {
                listToDeleteNode(childNodes);
            }
        }
    }


    <T extends TreeNode> T getSameLevelNextNode(T node) {
        return getNodeByOrderLevelAndTreeId(node.getTreeId(),node.getTreeLevel(),node.getTreeOrder() + 1,node.getPid());
    }
    <T extends TreeNode> T getSameLevelPreNode(T node) {
        return getNodeByOrderLevelAndTreeId(node.getTreeId(),node.getTreeLevel(),node.getTreeOrder() - 1,node.getPid());
    }
    protected abstract <T extends TreeNode> T getNodeByOrderLevelAndTreeId(String treeId,String treeLevel,Integer treeOrder,String pid);
    protected abstract void deleteRelation(TreeNode node);
    protected abstract void insertNode(TreeNode node);
    protected abstract void deleteNodeById(String id);
    protected abstract TreeNode getNodeById(String from);
    protected abstract void updateNodeById(TreeNode node);
    protected abstract int getSameLevelNextOrder(String treeId, String pid);
    protected abstract <T extends TreeNode> List<T> listChildNodesByPIdAndTreeId(String treeId, String id);
    protected abstract List<? extends TreeNode> listSameLevelGtOrder(String treeId, String pid, String level, Integer order);
    protected abstract void addNodeAttribute(Object attribute);
    protected abstract void setOrderName(List<? extends TreeNode> list);


}
