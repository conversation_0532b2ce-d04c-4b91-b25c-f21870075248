package com.stock.service.platform.common.manager;

import com.alibaba.fastjson.JSON;
import com.stock.service.platform.common.dto.MessageDto;
import com.stock.service.platform.common.util.HttpUtil;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MessageManager {

  @Value("${cscInterface.smsUrl}")
  private String smsUrl;

  @Value("${cscInterface.smsUserId}")
  private String smsUserId;

  @Value("${cscInterface.smsServiceCode}")
  private String smsServiceCode;

  @Value("${cscInterface.smsSign}")
  private String smsSign;


  /**
   * 发送短信
   */
  public MessageDto sendSms(List<String> phoneList, String smsContent) throws Exception {
    List<NameValuePair> params = new ArrayList<>();
    params.add(new BasicNameValuePair("user_id", smsUserId));
    params.add(new BasicNameValuePair("service_code", smsServiceCode));
    params.add(new BasicNameValuePair("sign", smsSign));
    params.add(new BasicNameValuePair("mobile", String.join(",", phoneList)));
    params.add(new BasicNameValuePair("msg_content", smsContent));
    params.add(new BasicNameValuePair("valid_time", "5"));

    return JSON.parseObject(HttpUtil.httpPostWithNameValuePair(smsUrl, params), MessageDto.class);
  }
}
