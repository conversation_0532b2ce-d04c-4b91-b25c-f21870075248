package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.RoleRouterMap;
import com.stock.service.platform.common.entity.RoleRouterMapExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.HashSet;
import java.util.List;

public interface RoleRouterMapMapper {
    long countByExample(RoleRouterMapExample example);

    int deleteByExample(RoleRouterMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(RoleRouterMap record);

    int insertSelective(RoleRouterMap record);

    List<RoleRouterMap> selectByExampleWithRowbounds(RoleRouterMapExample example, RowBounds rowBounds);

    List<RoleRouterMap> selectByExample(RoleRouterMapExample example);

    RoleRouterMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") RoleRouterMap record, @Param("example") RoleRouterMapExample example);

    int updateByExample(@Param("record") RoleRouterMap record, @Param("example") RoleRouterMapExample example);

    int updateByPrimaryKeySelective(RoleRouterMap record);

    int updateByPrimaryKey(RoleRouterMap record);

    List<String> getRouterIdListByRoleId(String roleId);

    List<String> getRouterDesByRouterIdList(HashSet<String> routerIdList);
}