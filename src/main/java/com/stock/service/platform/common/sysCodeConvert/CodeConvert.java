package com.stock.service.platform.common.sysCodeConvert;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-01-30 18:07
 */
public class CodeConvert {
    private static final Map<String, CodeMapper> systemMappers = new HashMap<>();

    static {
        systemMappers.put("GQJL", GQJLSystem.INSTANCE);
    }

    public static Map<String, String> convertCode(String platformCode, String type) {
        Map<String, String> resultMap = new HashMap<>();
        for (Map.Entry<String, CodeMapper> entry : systemMappers.entrySet()) {
            resultMap.put(entry.getKey(), entry.getValue().mapCode(platformCode, type));
        }
        return resultMap;
    }

    public static void main(String[] args) {
        String platformCode = "00";
        String type = "marketStockType"; // 例如，ORG表示组织架构码值，COMPANY_TYPE表示公司性质码值
        Map<String, String> subsystemCodes = convertCode(platformCode, type);
        subsystemCodes.forEach((system, code) -> System.out.println("System: " + system + ", Code: " + code));
    }
}
