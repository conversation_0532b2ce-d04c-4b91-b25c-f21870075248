package com.stock.service.platform.common.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;

public class PasswordUtil {


    private static final int MIN_NUMBER = 33;
    private static final int MAX_NUMBER = 126;
    private static final int BOUND = MAX_NUMBER - MIN_NUMBER + 1;

    private static final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String CHAR_UPPER = CHAR_LOWER.toUpperCase();
    private static final String NUMBER = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$";

    /**
     * 随机生成指定位数的密码
     *
     * char[33,126]，可表示数字、大小写字母、特殊字符
     *
     * @param length 密码长度
     * @return
     */
    public static String randomPassword(int length) {
        StringBuilder builder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            char value = (char) (random.nextInt(BOUND) + MIN_NUMBER);
            builder.append(value);
        }
        return builder.toString();
    }


    public static String genRandomNum(int pwd_len){
        //35是因为数组是从0开始的，26个字母+10个数字
        final int  maxNum = 65;
        int i;  //生成的随机数
        int count = 0; //生成的密码的长度
        char[] str = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
                'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w',
                'x', 'y', 'z','A','B','C','D','E','F','G','H','I','J','K','L',
                'M','N','P','Q','R','S','T','U','V','W','X','Y','Z','1', '2', '3', '4', '5', '6', '7', '8', '9','#','$','%','^','&' };
        StringBuffer pwd = new StringBuffer("");
        Random r = new Random();
        while(count < pwd_len){
            //生成随机数，取绝对值，防止生成负数，
            i = Math.abs(r.nextInt(maxNum));  //生成的数最大为36-1
            if (i >= 0 && i < str.length) {
                pwd.append(str[i]);
                count ++;
            }
        }
        return pwd.toString();
    }

    /**
     * 随机生成8位密码，包含大小写字母、数字和特殊字符
     * @return
     */
    public static String generatePassword() {
        // 确保密码中至少包含一个大写字母、一个小写字母、一个数字和一个特殊字符
        List<Character> passwordChars = new ArrayList<>();
        passwordChars.add(randomCharFrom(CHAR_LOWER));  // 添加一个小写字母
        passwordChars.add(randomCharFrom(CHAR_UPPER));  // 添加一个大写字母
        passwordChars.add(randomCharFrom(NUMBER));       // 添加一个数字
        passwordChars.add(randomCharFrom(SPECIAL_CHARS)); // 添加一个特殊字符

        // 填充剩余的字符，确保密码总长度为8位
        while (passwordChars.size() < 8) {
            passwordChars.add(randomCharFrom(CHAR_LOWER + CHAR_UPPER + NUMBER + SPECIAL_CHARS));
        }

        // 打乱顺序以增加随机性
        Collections.shuffle(passwordChars);

        StringBuilder passwordBuilder = new StringBuilder();
        for (Character c : passwordChars) {
            passwordBuilder.append(c);
        }

        return passwordBuilder.toString();
    }

    private static char randomCharFrom(String source) {
        Random rand = new Random();
        int index = rand.nextInt(source.length());
        return source.charAt(index);
    }

}
