package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.ChatLoginRecord;
import com.stock.service.platform.common.entity.ChatLoginRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ChatLoginRecordMapper {
    long countByExample(ChatLoginRecordExample example);

    int deleteByExample(ChatLoginRecordExample example);

    int deleteByPrimaryKey(String id);

    int insert(ChatLoginRecord record);

    int insertSelective(ChatLoginRecord record);

    List<ChatLoginRecord> selectByExampleWithRowbounds(ChatLoginRecordExample example, RowBounds rowBounds);

    List<ChatLoginRecord> selectByExample(ChatLoginRecordExample example);

    ChatLoginRecord selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") ChatLoginRecord record, @Param("example") ChatLoginRecordExample example);

    int updateByExample(@Param("record") ChatLoginRecord record, @Param("example") ChatLoginRecordExample example);

    int updateByPrimaryKeySelective(ChatLoginRecord record);

    int updateByPrimaryKey(ChatLoginRecord record);
}