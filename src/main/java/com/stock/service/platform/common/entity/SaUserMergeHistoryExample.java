package com.stock.service.platform.common.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SaUserMergeHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SaUserMergeHistoryExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdIsNull() {
            addCriterion("subsidiary_user_id is null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdIsNotNull() {
            addCriterion("subsidiary_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdEqualTo(String value) {
            addCriterion("subsidiary_user_id =", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdNotEqualTo(String value) {
            addCriterion("subsidiary_user_id <>", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdGreaterThan(String value) {
            addCriterion("subsidiary_user_id >", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("subsidiary_user_id >=", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdLessThan(String value) {
            addCriterion("subsidiary_user_id <", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdLessThanOrEqualTo(String value) {
            addCriterion("subsidiary_user_id <=", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdLike(String value) {
            addCriterion("subsidiary_user_id like", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdNotLike(String value) {
            addCriterion("subsidiary_user_id not like", value, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdIn(List<String> values) {
            addCriterion("subsidiary_user_id in", values, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdNotIn(List<String> values) {
            addCriterion("subsidiary_user_id not in", values, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdBetween(String value1, String value2) {
            addCriterion("subsidiary_user_id between", value1, value2, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andSubsidiaryUserIdNotBetween(String value1, String value2) {
            addCriterion("subsidiary_user_id not between", value1, value2, "subsidiaryUserId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdIsNull() {
            addCriterion("main_user_marge_role_id is null");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdIsNotNull() {
            addCriterion("main_user_marge_role_id is not null");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdEqualTo(String value) {
            addCriterion("main_user_marge_role_id =", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdNotEqualTo(String value) {
            addCriterion("main_user_marge_role_id <>", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdGreaterThan(String value) {
            addCriterion("main_user_marge_role_id >", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdGreaterThanOrEqualTo(String value) {
            addCriterion("main_user_marge_role_id >=", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdLessThan(String value) {
            addCriterion("main_user_marge_role_id <", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdLessThanOrEqualTo(String value) {
            addCriterion("main_user_marge_role_id <=", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdLike(String value) {
            addCriterion("main_user_marge_role_id like", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdNotLike(String value) {
            addCriterion("main_user_marge_role_id not like", value, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdIn(List<String> values) {
            addCriterion("main_user_marge_role_id in", values, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdNotIn(List<String> values) {
            addCriterion("main_user_marge_role_id not in", values, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdBetween(String value1, String value2) {
            addCriterion("main_user_marge_role_id between", value1, value2, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMainUserMargeRoleIdNotBetween(String value1, String value2) {
            addCriterion("main_user_marge_role_id not between", value1, value2, "mainUserMargeRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdIsNull() {
            addCriterion("merged_role_id is null");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdIsNotNull() {
            addCriterion("merged_role_id is not null");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdEqualTo(String value) {
            addCriterion("merged_role_id =", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdNotEqualTo(String value) {
            addCriterion("merged_role_id <>", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdGreaterThan(String value) {
            addCriterion("merged_role_id >", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdGreaterThanOrEqualTo(String value) {
            addCriterion("merged_role_id >=", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdLessThan(String value) {
            addCriterion("merged_role_id <", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdLessThanOrEqualTo(String value) {
            addCriterion("merged_role_id <=", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdLike(String value) {
            addCriterion("merged_role_id like", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdNotLike(String value) {
            addCriterion("merged_role_id not like", value, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdIn(List<String> values) {
            addCriterion("merged_role_id in", values, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdNotIn(List<String> values) {
            addCriterion("merged_role_id not in", values, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdBetween(String value1, String value2) {
            addCriterion("merged_role_id between", value1, value2, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andMergedRoleIdNotBetween(String value1, String value2) {
            addCriterion("merged_role_id not between", value1, value2, "mergedRoleId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}