package com.stock.service.platform.common.config;

import com.stock.service.platform.common.interceptor.ReplaceDataBaseNameInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2024-08-12 17:50
 */
@Configuration
public class MyBatisConfig {

    @Bean
    public ReplaceDataBaseNameInterceptor replaceDataBaseNameInterceptor() {
        return new ReplaceDataBaseNameInterceptor();
    }
}
