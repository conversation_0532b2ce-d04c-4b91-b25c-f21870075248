package com.stock.service.platform.common.system.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExcelSaOperationLogManageDto implements Serializable {

    //账号
    @ExcelProperty("操作账号")
    @ColumnWidth(12)
    private String userName;

    //ip
    @ExcelProperty("操作ip")
    @ColumnWidth(15)
    private String ip;

    //公司名称
    @ExcelProperty("用户类别")
    @ColumnWidth(13)
    private String userType;

    //ip
    @ExcelProperty("所属营业部")
    @ColumnWidth(20)
    private String orgName;

    //url
    @ExcelProperty("所属上市公司")
    @ColumnWidth(20)
    private String companyName;

    //方法类型
    @ExcelProperty("操作路径")
    @ColumnWidth(30)
    private String router;

    //方法名
    @ExcelProperty("操作内容")
    @ColumnWidth(20)
    private String operationTypeDetail;

    //参数
    @ExcelProperty("备注")
    @ColumnWidth(50)
    private String remark;

    //修改时间
    @ExcelProperty("操作时间")
    @ColumnWidth(25)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
