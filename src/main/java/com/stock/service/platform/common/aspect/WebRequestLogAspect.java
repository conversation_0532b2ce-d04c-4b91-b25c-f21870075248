package com.stock.service.platform.common.aspect;

import com.alibaba.fastjson.JSONObject;
import com.stock.service.platform.common.constant.CompanyConstant;
import com.stock.service.platform.common.controller.CommonController;
import com.stock.service.platform.common.dao.SystemWebRequestLogMapper;
import com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs;
import com.stock.service.platform.common.service.CommonService;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestWrapper;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Aspect
@Component
public class WebRequestLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(WebRequestLogAspect.class);
    private static List<String> IGNORE_CLAZZ = new ArrayList<>();
    private static List<String> IGNORE_METHOD_PATH = new ArrayList<>();

    static {
        IGNORE_CLAZZ.add("com.stock.service.platform.common.controller.ContentNegotiatingErrorController");
        IGNORE_METHOD_PATH.add("com.stock.service.platform.user.controller.UserController.userInfo");
    }
    @Autowired
    private CommonController commonController;
    @Autowired
    private SystemWebRequestLogMapper systemWebRequestLogMapper;


    // Scan path
    @Pointcut("execution(public * com.stock.service.platform..*Controller.*(..))")
    public void requestLog() {  }

    @Before("requestLog()")
    public void doBefore(JoinPoint joinPoint)  {
        String requestUrl = "";
        try {
            // Get request content
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            // Current user
            if (commonController.getUserInfo() != null) {
                String userId = commonController.getUserInfo().getUserId();
                if (request != null && StringUtils.isNotEmpty(userId)) {
                    requestUrl = request.getRequestURL().toString();
                    String clazzName = joinPoint.getTarget().getClass().getName();
                    String requestType = request.getMethod();
                    String methodPath = clazzName + "." + joinPoint.getSignature().getName();
                    // 只保存POST请求
                    if("POST".equals(requestType)
                            && !IGNORE_CLAZZ.contains(clazzName) && !IGNORE_METHOD_PATH.contains(methodPath)
                            && userId != null) {
                        // 插入日志
                        SystemWebRequestLogWithBLOBs requestLog = new SystemWebRequestLogWithBLOBs();
                        requestLog.setIpAddress(CommonService.getIp(request));
                        requestLog.setUserId(userId);
                        requestLog.setRequestUrl(requestUrl);
                        requestLog.setRequestType(requestType);
                        requestLog.setClazzName(clazzName.substring(CompanyConstant.SPILT_CLASS_NAME_BEG_INDEX));
                        requestLog.setMethodName(joinPoint.getSignature().getName());

                        requestLog.setSystemName("PLATFORM");

                        Date currentTime = new Date();
                        requestLog.setCreateTime(currentTime);

                        StringBuilder methodParam = new StringBuilder();
                        for (Object obj : joinPoint.getArgs()) {
                            // SecurityContextHolderAwareRequestWrapper类型的不能转成JSON
                            if (obj != null && !(obj instanceof SecurityContextHolderAwareRequestWrapper)) {
                                try {
                                    methodParam.append(JSONObject.toJSONString(obj));
                                } catch (Exception e) {
                                    logger.info("Insert web request log error! Reason:{}",e.toString());
                                    // 处理异常
                                }
                            }
                        }
                        //for(Object obj : joinPoint.getArgs()){
                        //    if(obj!=null){ methodParam.append(JSONObject.toJSONString(obj)); }
                        //}
                        requestLog.setMethodParam(methodParam.toString());
                        systemWebRequestLogMapper.insert(requestLog);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("Insert web request log error! Reason:{}",e.toString());
        }
    }
}
