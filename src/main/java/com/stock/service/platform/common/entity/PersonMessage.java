package com.stock.service.platform.common.entity;


import com.stock.service.platform.common.util.Message;
import lombok.Data;

import java.io.Serializable;

@Data
public class PersonMessage implements Serializable {


    @Message(title = "姓名", primary = "true")
    private String personName;

    @Message(title = "职务", primary = "true")
    private String jobs;

    @Message(title = "所属机构", primary = "true")
    private String orgId;

    @Message(title = "手机", primary = "true")
    private String telephone;

    @Message(title = "电话", primary = "true")
    private String phone;

    @Message(title = "邮箱", primary = "true")
    private String mail;

    @Message(title = "传真", primary = "true")
    private String fax;

    @Message(title = "地址", primary = "true")
    private String address;

    @Message(title = "证件号码", primary = "true")
    private String certificate;

    @Message(title = "客户号(合规)", primary = "true")
    private String weChatId;

    @Message(title = "备注", primary = "true")
    private String remark;

    @Message(title = "用户名", primary = "true")
    private String userName;

    //@Message(title = "所属部门", primary = "true")
    private String isMgr;

    @Message(title = "子账号名", primary = "true")
    private String childrenUserName;

    @Message(title = "子账号营业部", primary = "true")
    private String childrenOrg;

    @Message(title = "子账号角色", primary = "true")
    private String childrenRole;

    @Message(title = "子账号锁定状态", primary = "true")
    private String childrenLock;
}
