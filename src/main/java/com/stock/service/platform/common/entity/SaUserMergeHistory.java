package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class SaUserMergeHistory implements Serializable {
    private String id;

    private String userId;

    private String subsidiaryUserId;

    private String mainUserMargeRoleId;

    private String mergedRoleId;

    private String mergeFlag;

    private Date createTime;

    private String createUser;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getSubsidiaryUserId() {
        return subsidiaryUserId;
    }

    public void setSubsidiaryUserId(String subsidiaryUserId) {
        this.subsidiaryUserId = subsidiaryUserId == null ? null : subsidiaryUserId.trim();
    }

    public String getMainUserMargeRoleId() {
        return mainUserMargeRoleId;
    }

    public void setMainUserMargeRoleId(String mainUserMargeRoleId) {
        this.mainUserMargeRoleId = mainUserMargeRoleId == null ? null : mainUserMargeRoleId.trim();
    }

    public String getMergedRoleId() {
        return mergedRoleId;
    }

    public void setMergedRoleId(String mergedRoleId) {
        this.mergedRoleId = mergedRoleId == null ? null : mergedRoleId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public String getMergeFlag() {
        return mergeFlag;
    }

    public void setMergeFlag(String mergedFlag) {
        this.mergeFlag = mergedFlag;
    }
}