package com.stock.service.platform.common.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RouterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RouterExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPRouterIdIsNull() {
            addCriterion("p_router_id is null");
            return (Criteria) this;
        }

        public Criteria andPRouterIdIsNotNull() {
            addCriterion("p_router_id is not null");
            return (Criteria) this;
        }

        public Criteria andPRouterIdEqualTo(String value) {
            addCriterion("p_router_id =", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdNotEqualTo(String value) {
            addCriterion("p_router_id <>", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdGreaterThan(String value) {
            addCriterion("p_router_id >", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdGreaterThanOrEqualTo(String value) {
            addCriterion("p_router_id >=", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdLessThan(String value) {
            addCriterion("p_router_id <", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdLessThanOrEqualTo(String value) {
            addCriterion("p_router_id <=", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdLike(String value) {
            addCriterion("p_router_id like", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdNotLike(String value) {
            addCriterion("p_router_id not like", value, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdIn(List<String> values) {
            addCriterion("p_router_id in", values, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdNotIn(List<String> values) {
            addCriterion("p_router_id not in", values, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdBetween(String value1, String value2) {
            addCriterion("p_router_id between", value1, value2, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andPRouterIdNotBetween(String value1, String value2) {
            addCriterion("p_router_id not between", value1, value2, "pRouterId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNull() {
            addCriterion("resource_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNotNull() {
            addCriterion("resource_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIdEqualTo(String value) {
            addCriterion("resource_id =", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotEqualTo(String value) {
            addCriterion("resource_id <>", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThan(String value) {
            addCriterion("resource_id >", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThanOrEqualTo(String value) {
            addCriterion("resource_id >=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThan(String value) {
            addCriterion("resource_id <", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThanOrEqualTo(String value) {
            addCriterion("resource_id <=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLike(String value) {
            addCriterion("resource_id like", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotLike(String value) {
            addCriterion("resource_id not like", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIn(List<String> values) {
            addCriterion("resource_id in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotIn(List<String> values) {
            addCriterion("resource_id not in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdBetween(String value1, String value2) {
            addCriterion("resource_id between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotBetween(String value1, String value2) {
            addCriterion("resource_id not between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andRouterNameIsNull() {
            addCriterion("router_name is null");
            return (Criteria) this;
        }

        public Criteria andRouterNameIsNotNull() {
            addCriterion("router_name is not null");
            return (Criteria) this;
        }

        public Criteria andRouterNameEqualTo(String value) {
            addCriterion("router_name =", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameNotEqualTo(String value) {
            addCriterion("router_name <>", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameGreaterThan(String value) {
            addCriterion("router_name >", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameGreaterThanOrEqualTo(String value) {
            addCriterion("router_name >=", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameLessThan(String value) {
            addCriterion("router_name <", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameLessThanOrEqualTo(String value) {
            addCriterion("router_name <=", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameLike(String value) {
            addCriterion("router_name like", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameNotLike(String value) {
            addCriterion("router_name not like", value, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameIn(List<String> values) {
            addCriterion("router_name in", values, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameNotIn(List<String> values) {
            addCriterion("router_name not in", values, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameBetween(String value1, String value2) {
            addCriterion("router_name between", value1, value2, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterNameNotBetween(String value1, String value2) {
            addCriterion("router_name not between", value1, value2, "routerName");
            return (Criteria) this;
        }

        public Criteria andRouterDesIsNull() {
            addCriterion("router_des is null");
            return (Criteria) this;
        }

        public Criteria andRouterDesIsNotNull() {
            addCriterion("router_des is not null");
            return (Criteria) this;
        }

        public Criteria andRouterDesEqualTo(String value) {
            addCriterion("router_des =", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesNotEqualTo(String value) {
            addCriterion("router_des <>", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesGreaterThan(String value) {
            addCriterion("router_des >", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesGreaterThanOrEqualTo(String value) {
            addCriterion("router_des >=", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesLessThan(String value) {
            addCriterion("router_des <", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesLessThanOrEqualTo(String value) {
            addCriterion("router_des <=", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesLike(String value) {
            addCriterion("router_des like", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesNotLike(String value) {
            addCriterion("router_des not like", value, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesIn(List<String> values) {
            addCriterion("router_des in", values, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesNotIn(List<String> values) {
            addCriterion("router_des not in", values, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesBetween(String value1, String value2) {
            addCriterion("router_des between", value1, value2, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterDesNotBetween(String value1, String value2) {
            addCriterion("router_des not between", value1, value2, "routerDes");
            return (Criteria) this;
        }

        public Criteria andRouterTypeIsNull() {
            addCriterion("router_type is null");
            return (Criteria) this;
        }

        public Criteria andRouterTypeIsNotNull() {
            addCriterion("router_type is not null");
            return (Criteria) this;
        }

        public Criteria andRouterTypeEqualTo(String value) {
            addCriterion("router_type =", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeNotEqualTo(String value) {
            addCriterion("router_type <>", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeGreaterThan(String value) {
            addCriterion("router_type >", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeGreaterThanOrEqualTo(String value) {
            addCriterion("router_type >=", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeLessThan(String value) {
            addCriterion("router_type <", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeLessThanOrEqualTo(String value) {
            addCriterion("router_type <=", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeLike(String value) {
            addCriterion("router_type like", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeNotLike(String value) {
            addCriterion("router_type not like", value, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeIn(List<String> values) {
            addCriterion("router_type in", values, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeNotIn(List<String> values) {
            addCriterion("router_type not in", values, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeBetween(String value1, String value2) {
            addCriterion("router_type between", value1, value2, "routerType");
            return (Criteria) this;
        }

        public Criteria andRouterTypeNotBetween(String value1, String value2) {
            addCriterion("router_type not between", value1, value2, "routerType");
            return (Criteria) this;
        }

        public Criteria andCurrStatusIsNull() {
            addCriterion("curr_status is null");
            return (Criteria) this;
        }

        public Criteria andCurrStatusIsNotNull() {
            addCriterion("curr_status is not null");
            return (Criteria) this;
        }

        public Criteria andCurrStatusEqualTo(String value) {
            addCriterion("curr_status =", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusNotEqualTo(String value) {
            addCriterion("curr_status <>", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusGreaterThan(String value) {
            addCriterion("curr_status >", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusGreaterThanOrEqualTo(String value) {
            addCriterion("curr_status >=", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusLessThan(String value) {
            addCriterion("curr_status <", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusLessThanOrEqualTo(String value) {
            addCriterion("curr_status <=", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusLike(String value) {
            addCriterion("curr_status like", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusNotLike(String value) {
            addCriterion("curr_status not like", value, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusIn(List<String> values) {
            addCriterion("curr_status in", values, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusNotIn(List<String> values) {
            addCriterion("curr_status not in", values, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusBetween(String value1, String value2) {
            addCriterion("curr_status between", value1, value2, "currStatus");
            return (Criteria) this;
        }

        public Criteria andCurrStatusNotBetween(String value1, String value2) {
            addCriterion("curr_status not between", value1, value2, "currStatus");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagIsNull() {
            addCriterion("router_folder_flag is null");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagIsNotNull() {
            addCriterion("router_folder_flag is not null");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagEqualTo(Integer value) {
            addCriterion("router_folder_flag =", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagNotEqualTo(Integer value) {
            addCriterion("router_folder_flag <>", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagGreaterThan(Integer value) {
            addCriterion("router_folder_flag >", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("router_folder_flag >=", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagLessThan(Integer value) {
            addCriterion("router_folder_flag <", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagLessThanOrEqualTo(Integer value) {
            addCriterion("router_folder_flag <=", value, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagIn(List<Integer> values) {
            addCriterion("router_folder_flag in", values, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagNotIn(List<Integer> values) {
            addCriterion("router_folder_flag not in", values, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagBetween(Integer value1, Integer value2) {
            addCriterion("router_folder_flag between", value1, value2, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andRouterFolderFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("router_folder_flag not between", value1, value2, "routerFolderFlag");
            return (Criteria) this;
        }

        public Criteria andParaIsNull() {
            addCriterion("para is null");
            return (Criteria) this;
        }

        public Criteria andParaIsNotNull() {
            addCriterion("para is not null");
            return (Criteria) this;
        }

        public Criteria andParaEqualTo(String value) {
            addCriterion("para =", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaNotEqualTo(String value) {
            addCriterion("para <>", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaGreaterThan(String value) {
            addCriterion("para >", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaGreaterThanOrEqualTo(String value) {
            addCriterion("para >=", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaLessThan(String value) {
            addCriterion("para <", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaLessThanOrEqualTo(String value) {
            addCriterion("para <=", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaLike(String value) {
            addCriterion("para like", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaNotLike(String value) {
            addCriterion("para not like", value, "para");
            return (Criteria) this;
        }

        public Criteria andParaIn(List<String> values) {
            addCriterion("para in", values, "para");
            return (Criteria) this;
        }

        public Criteria andParaNotIn(List<String> values) {
            addCriterion("para not in", values, "para");
            return (Criteria) this;
        }

        public Criteria andParaBetween(String value1, String value2) {
            addCriterion("para between", value1, value2, "para");
            return (Criteria) this;
        }

        public Criteria andParaNotBetween(String value1, String value2) {
            addCriterion("para not between", value1, value2, "para");
            return (Criteria) this;
        }

        public Criteria andSortNoIsNull() {
            addCriterion("sort_no is null");
            return (Criteria) this;
        }

        public Criteria andSortNoIsNotNull() {
            addCriterion("sort_no is not null");
            return (Criteria) this;
        }

        public Criteria andSortNoEqualTo(Integer value) {
            addCriterion("sort_no =", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoNotEqualTo(Integer value) {
            addCriterion("sort_no <>", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoGreaterThan(Integer value) {
            addCriterion("sort_no >", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort_no >=", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoLessThan(Integer value) {
            addCriterion("sort_no <", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoLessThanOrEqualTo(Integer value) {
            addCriterion("sort_no <=", value, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoIn(List<Integer> values) {
            addCriterion("sort_no in", values, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoNotIn(List<Integer> values) {
            addCriterion("sort_no not in", values, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoBetween(Integer value1, Integer value2) {
            addCriterion("sort_no between", value1, value2, "sortNo");
            return (Criteria) this;
        }

        public Criteria andSortNoNotBetween(Integer value1, Integer value2) {
            addCriterion("sort_no not between", value1, value2, "sortNo");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRouterIconIsNull() {
            addCriterion("router_icon is null");
            return (Criteria) this;
        }

        public Criteria andRouterIconIsNotNull() {
            addCriterion("router_icon is not null");
            return (Criteria) this;
        }

        public Criteria andRouterIconEqualTo(String value) {
            addCriterion("router_icon =", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconNotEqualTo(String value) {
            addCriterion("router_icon <>", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconGreaterThan(String value) {
            addCriterion("router_icon >", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconGreaterThanOrEqualTo(String value) {
            addCriterion("router_icon >=", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconLessThan(String value) {
            addCriterion("router_icon <", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconLessThanOrEqualTo(String value) {
            addCriterion("router_icon <=", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconLike(String value) {
            addCriterion("router_icon like", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconNotLike(String value) {
            addCriterion("router_icon not like", value, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconIn(List<String> values) {
            addCriterion("router_icon in", values, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconNotIn(List<String> values) {
            addCriterion("router_icon not in", values, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconBetween(String value1, String value2) {
            addCriterion("router_icon between", value1, value2, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andRouterIconNotBetween(String value1, String value2) {
            addCriterion("router_icon not between", value1, value2, "routerIcon");
            return (Criteria) this;
        }

        public Criteria andMetaIsNull() {
            addCriterion("meta is null");
            return (Criteria) this;
        }

        public Criteria andMetaIsNotNull() {
            addCriterion("meta is not null");
            return (Criteria) this;
        }

        public Criteria andMetaEqualTo(String value) {
            addCriterion("meta =", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaNotEqualTo(String value) {
            addCriterion("meta <>", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaGreaterThan(String value) {
            addCriterion("meta >", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaGreaterThanOrEqualTo(String value) {
            addCriterion("meta >=", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaLessThan(String value) {
            addCriterion("meta <", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaLessThanOrEqualTo(String value) {
            addCriterion("meta <=", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaLike(String value) {
            addCriterion("meta like", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaNotLike(String value) {
            addCriterion("meta not like", value, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaIn(List<String> values) {
            addCriterion("meta in", values, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaNotIn(List<String> values) {
            addCriterion("meta not in", values, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaBetween(String value1, String value2) {
            addCriterion("meta between", value1, value2, "meta");
            return (Criteria) this;
        }

        public Criteria andMetaNotBetween(String value1, String value2) {
            addCriterion("meta not between", value1, value2, "meta");
            return (Criteria) this;
        }

        public Criteria andComponentIsNull() {
            addCriterion("component is null");
            return (Criteria) this;
        }

        public Criteria andComponentIsNotNull() {
            addCriterion("component is not null");
            return (Criteria) this;
        }

        public Criteria andComponentEqualTo(String value) {
            addCriterion("component =", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotEqualTo(String value) {
            addCriterion("component <>", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThan(String value) {
            addCriterion("component >", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentGreaterThanOrEqualTo(String value) {
            addCriterion("component >=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThan(String value) {
            addCriterion("component <", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLessThanOrEqualTo(String value) {
            addCriterion("component <=", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentLike(String value) {
            addCriterion("component like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotLike(String value) {
            addCriterion("component not like", value, "component");
            return (Criteria) this;
        }

        public Criteria andComponentIn(List<String> values) {
            addCriterion("component in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotIn(List<String> values) {
            addCriterion("component not in", values, "component");
            return (Criteria) this;
        }

        public Criteria andComponentBetween(String value1, String value2) {
            addCriterion("component between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andComponentNotBetween(String value1, String value2) {
            addCriterion("component not between", value1, value2, "component");
            return (Criteria) this;
        }

        public Criteria andPathIsNull() {
            addCriterion("path is null");
            return (Criteria) this;
        }

        public Criteria andPathIsNotNull() {
            addCriterion("path is not null");
            return (Criteria) this;
        }

        public Criteria andPathEqualTo(String value) {
            addCriterion("path =", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotEqualTo(String value) {
            addCriterion("path <>", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathGreaterThan(String value) {
            addCriterion("path >", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathGreaterThanOrEqualTo(String value) {
            addCriterion("path >=", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLessThan(String value) {
            addCriterion("path <", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLessThanOrEqualTo(String value) {
            addCriterion("path <=", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathLike(String value) {
            addCriterion("path like", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotLike(String value) {
            addCriterion("path not like", value, "path");
            return (Criteria) this;
        }

        public Criteria andPathIn(List<String> values) {
            addCriterion("path in", values, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotIn(List<String> values) {
            addCriterion("path not in", values, "path");
            return (Criteria) this;
        }

        public Criteria andPathBetween(String value1, String value2) {
            addCriterion("path between", value1, value2, "path");
            return (Criteria) this;
        }

        public Criteria andPathNotBetween(String value1, String value2) {
            addCriterion("path not between", value1, value2, "path");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andRedirectIsNull() {
            addCriterion("redirect is null");
            return (Criteria) this;
        }

        public Criteria andRedirectIsNotNull() {
            addCriterion("redirect is not null");
            return (Criteria) this;
        }

        public Criteria andRedirectEqualTo(String value) {
            addCriterion("redirect =", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectNotEqualTo(String value) {
            addCriterion("redirect <>", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectGreaterThan(String value) {
            addCriterion("redirect >", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectGreaterThanOrEqualTo(String value) {
            addCriterion("redirect >=", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectLessThan(String value) {
            addCriterion("redirect <", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectLessThanOrEqualTo(String value) {
            addCriterion("redirect <=", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectLike(String value) {
            addCriterion("redirect like", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectNotLike(String value) {
            addCriterion("redirect not like", value, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectIn(List<String> values) {
            addCriterion("redirect in", values, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectNotIn(List<String> values) {
            addCriterion("redirect not in", values, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectBetween(String value1, String value2) {
            addCriterion("redirect between", value1, value2, "redirect");
            return (Criteria) this;
        }

        public Criteria andRedirectNotBetween(String value1, String value2) {
            addCriterion("redirect not between", value1, value2, "redirect");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}