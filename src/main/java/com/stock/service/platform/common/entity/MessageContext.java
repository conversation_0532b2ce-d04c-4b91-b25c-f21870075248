package com.stock.service.platform.common.entity;

import java.io.Serializable;

public class MessageContext implements Serializable {
  //标题
  private String title;
  //操作类型
  private String operationType;
  //旧内容
  private String oldText;
  //新内容
  private String newText;
  //备注
  private String remark;
  //字段
  private String privateWord;
  //路径
  private String classPath;

  public MessageContext(String title, String operationType, String oldText, String newText,
                        String privateWord, String classPath) {
    this.title = title;
    this.operationType = operationType;
    this.oldText = oldText;
    this.newText = newText;
    this.privateWord = privateWord;
    this.classPath = classPath;
  }

  public MessageContext() {
  }

  public String getOperationType() {
    return operationType;
  }

  public void setOperationType(String operationType) {
    this.operationType = operationType;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public String getClassPath() {
    return classPath;
  }

  public void setClassPath(String classPath) {
    this.classPath = classPath;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }


  public String getOldText() {
    return oldText;
  }

  public void setOldText(String oldText) {
    this.oldText = oldText;
  }

  public String getNewText() {
    return newText;
  }

  public void setNewText(String newText) {
    this.newText = newText;
  }

  public String getPrivateWord() {
    return privateWord;
  }

  public void setPrivateWord(String privateWord) {
    this.privateWord = privateWord;
  }
}