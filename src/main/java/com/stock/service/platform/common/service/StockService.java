package com.stock.service.platform.common.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.dto.UserInfo;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.StockBizMapper;
import com.stock.service.platform.common.dto.VcTradSkDailyDto;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2024-03-14 15:48
 */
@Service
public class StockService extends BaseService {

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(StockService.class);

    @Resource
    private StockBizMapper stockBizMapper;

    @Resource
    private TimedTasksMapper timedTasksMapper;

    @Resource
    private LogService logService;

    /**
     * 将四张表的全部交易数据整合
     */
    public void stockInfoMergeFirst() throws Exception {
        stockBizMapper.deleteVcTradSkDailyData();
        List<String> allCompanyCode = stockBizMapper.getAllCompanyCode();
        if (CollectionUtils.isNotEmpty(allCompanyCode)) {
            for (String companyCode : allCompanyCode) {
                stockInfoMergeByCompanyCodeManual(companyCode);
            }
        }
    }

    /**
     * 四合一整合指定日期的交易数据
     * @param date 指定的日期字符串，格式为 "yyyy-MM-dd"
     */
    public void stockInfoMerge(String date) throws Exception {
        try {
            stockBizMapper.deleteVcTradSkDailyDataByDate(date);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd"); // 日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
            List<VcTradSkDailyDto> vcTradSkDailyList = new ArrayList<>();
            // 获取全部companyCode
            List<String> allCompanyCode = stockBizMapper.getAllCompanyCodeByDate(date+ " 00:00:00");
            if (CollectionUtils.isNotEmpty(allCompanyCode)) {
                allCompanyCode = allCompanyCode.stream().distinct().collect(Collectors.toList());
                // 获取四个表的交易数据
                List<VcTradSkDailyDto> stockPerformanceList = stockBizMapper.getStockPerformance(date);
                if (CollectionUtils.isEmpty(stockPerformanceList)) {
                    throw new Exception("状态表成功,但stock_performance表数据为空");
                }
                List<VcTradSkDailyDto> stockEvaluationList = stockBizMapper.getStockEvaluation(date);
                if (CollectionUtils.isEmpty(stockEvaluationList)) {
                    throw new Exception("状态表成功,但stock_evaluation表数据为空");
                }
                Map<String, VcTradSkDailyDto> stockPerformanceMap = convertToListMap(stockPerformanceList);
                Map<String, VcTradSkDailyDto> stockEvaluationMap = convertToListMap(stockEvaluationList);
                // 将四个交易表数据合并为一个
                for (String companyCode : allCompanyCode) {
                    VcTradSkDailyDto vcTradSkDaily = new VcTradSkDailyDto();
                    LocalDateTime now = LocalDateTime.now();
                    vcTradSkDaily.setIdSource(now.format(formatter));
                    vcTradSkDaily.setSecurityCd(companyCode);
                    vcTradSkDaily.setTradeDate(dateFormat.parse(date));
                    // 从stockPerformanceMap获取数据
                    VcTradSkDailyDto performanceData = stockPerformanceMap.get(companyCode);
                    if (performanceData != null) {
                        vcTradSkDaily.setNEW(performanceData.getNEW()); // 今日收盘
                        vcTradSkDaily.setChg(performanceData.getChg()); // 涨跌幅
                        // 数交推送的时候已经乘10000了，这里就不乘了
                        vcTradSkDaily.setCjl(performanceData.getCjl()); // 成交量(股)
                        vcTradSkDaily.setTvalcny(performanceData.getTvalcny()); // 成交金额(元)
                        //vcTradSkDaily.setCjl(performanceData.getCjl().multiply(BigDecimal.valueOf(10000))); // 成交量(股)
                        //vcTradSkDaily.setTvalcny(performanceData.getTvalcny().multiply(BigDecimal.valueOf(10000))); // 成交金额(元)
                        if (ObjectUtils.isEmpty(vcTradSkDaily.getCjl())) continue;
                    } else {
                        continue;
                    }
                    // 从stockEvaluationMap获取数据
                    VcTradSkDailyDto evaluationData = stockEvaluationMap.get(companyCode);
                    if (evaluationData != null) {
                        vcTradSkDaily.setZsz(evaluationData.getZsz()); // 总市值
                        vcTradSkDaily.setPe9(evaluationData.getPe9()); // 市盈率(TTM)
                        vcTradSkDaily.setPb8(evaluationData.getPb8()); // 市净率(最新报告期MRQ)
                        vcTradSkDaily.setAgszbhxs(evaluationData.getAgszbhxs()); // A股流通市值
                        vcTradSkDaily.setBgszbhxszrm(BigDecimal.valueOf(0)); // B股流通市值(建投不分A/B,数据存到A,B存空)
                    }
                    // 从stockHoldersMap获取数据
                    VcTradSkDailyDto holdersData = stockBizMapper.getStockHoldersByCodeAndDate(companyCode, date);
                    if (holdersData != null) {
                        vcTradSkDaily.setShareNum(holdersData.getShareNum()); // 股东人数
                    }
                    // 从stockSharesMap获取数据
                    VcTradSkDailyDto sharesData = stockBizMapper.getStockSharesByCodeAndDate(companyCode, date);
                    if (sharesData != null) {
                        vcTradSkDaily.setTotalshares(sharesData.getTotalshares()); // 总股本（A+B+H）
                        vcTradSkDaily.setFfshares(sharesData.getFfshares()); // 自由流通股本
                        vcTradSkDaily.setRshare(sharesData.getRshare()); // 有限售条件股份
                        vcTradSkDaily.setCshare(sharesData.getCshare()); // 流通股份(与自由流通股本相同)
                        vcTradSkDaily.setAshare(sharesData.getAshare()); // 流通A股
                        vcTradSkDaily.setAsharer(sharesData.getAsharer()); // 非流通A股
                        vcTradSkDaily.setBshare(sharesData.getBshare()); // 流通B股
                        vcTradSkDaily.setBsharer(sharesData.getBsharer()); // 非流通B股
                        vcTradSkDaily.setHshare(sharesData.getHshare()); // 流通H股
                        vcTradSkDaily.setHsharer(sharesData.getHsharer()); // 非流通H股
                    }
                    vcTradSkDailyList.add(vcTradSkDaily);
                }
                if (CollectionUtils.isNotEmpty(vcTradSkDailyList)) {
                    // 将合并完的数据插入到vc_trad_sk_daily, 一次插入200条
                    int InsertSize = 200;
                    // 需要插入多少次, 这个算法就是防止不是在整除的时候少了一次
                    int limit = (vcTradSkDailyList.size() + InsertSize - 1) / InsertSize;
                    // 根据起始值seed(0)，每次生成一个指定递增值（n+1）的数，limit(limit)用于截断流的长度,也就是进行limit次的里面的操作
                    Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
                        // skip就是跳过前面（a * InsertSize）条数据，因为 a是从0开始，到limit，skip(0)的时候是空的不插入 .limit（InsertSize）->限制每次插入数据的200条
                        List<VcTradSkDailyDto> tempVcTradSkDailyList = vcTradSkDailyList.stream().skip(a * InsertSize).limit(InsertSize).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(tempVcTradSkDailyList)) {
                            stockBizMapper.insertVcTradSkDaily(tempVcTradSkDailyList);
                        }
                    });
                }
            }
        } catch (Exception e) {
            if ("stock_performance表数据为空".equals(e.getMessage()) || "stock_evaluation表数据为空".equals(e.getMessage())) {
                throw new Exception(e);
            } else {
                throw new Exception("定时任务执行失败");
            }
        }
    }

    /**
     * 整合指定公司的交易数据
     * @param companyCode 公司代码字符串
     */
    public void stockInfoMergeByCompanyCode(String companyCode) throws Exception {
        stockBizMapper.deleteVcTradSkDailyDataByCompanyCode(companyCode);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<VcTradSkDailyDto> vcTradSkDailyList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        LocalDate currentDate = today.minusDays(1);
        // 获取1年前的日期
        LocalDate oneYearAgo = currentDate.minusYears(1);
        List<String> allTradeDate = timedTasksMapper.getOneYearTradingDay(oneYearAgo.format(formatter2), currentDate.format(formatter2));
        if (CollectionUtils.isNotEmpty(allTradeDate)) {
            allTradeDate = allTradeDate.stream().distinct().collect(Collectors.toList());
            // 将日期转换为指定格式的字符串
            String startDateStr = oneYearAgo.format(formatter2);
            String endDateStr = currentDate.format(formatter2);
            // 获取四个表的交易数据
            Map<String, VcTradSkDailyDto> stockPerformanceMap = convertToListMapByTradeDate(stockBizMapper.getStockPerformanceByCompanyCode(companyCode, startDateStr, endDateStr));
            Map<String, VcTradSkDailyDto> stockEvaluationMap = convertToListMapByTradeDate(stockBizMapper.getStockEvaluationByCompanyCode(companyCode, startDateStr, endDateStr));
            // 下面两个表不使用日期坐条件查询,传了但是没用
            Map<String, VcTradSkDailyDto> stockHoldersMap = convertToListMapByTradeDate(stockBizMapper.getStockHoldersByCompanyCode(companyCode, startDateStr, endDateStr));
            Map<String, VcTradSkDailyDto> stockSharesMap = convertToListMapByTradeDate(stockBizMapper.getStockSharesByCompanyCode(companyCode, startDateStr, endDateStr));
            // 将四个交易表数据合并为一个
            for (String tradeDate : allTradeDate) {
                VcTradSkDailyDto vcTradSkDaily = new VcTradSkDailyDto();
                LocalDateTime now = LocalDateTime.now();
                vcTradSkDaily.setIdSource(now.format(formatter));
                vcTradSkDaily.setSecurityCd(companyCode);
                vcTradSkDaily.setTradeDate(dateFormat.parse(tradeDate));
                // 从stockPerformanceMap获取数据
                VcTradSkDailyDto performanceData = stockPerformanceMap.get(tradeDate);
                if (performanceData != null) {
                    vcTradSkDaily.setNEW(performanceData.getNEW()); // 今日收盘
                    vcTradSkDaily.setChg(performanceData.getChg()); // 涨跌幅
                    vcTradSkDaily.setCjl(performanceData.getCjl()); // 成交量(股)
                    vcTradSkDaily.setTvalcny(performanceData.getTvalcny()); // 成交金额(元)
                    if (ObjectUtils.isEmpty(vcTradSkDaily.getCjl())) continue;
                } else {
                    continue;
                }
                // 从stockEvaluationMap获取数据
                VcTradSkDailyDto evaluationData = stockEvaluationMap.get(tradeDate);
                if (evaluationData != null) {
                    vcTradSkDaily.setZsz(evaluationData.getZsz()); // 总市值
                    vcTradSkDaily.setPe9(evaluationData.getPe9()); // 市盈率(TTM)
                    vcTradSkDaily.setPb8(evaluationData.getPb8()); // 市净率(最新报告期MRQ)
                    vcTradSkDaily.setAgszbhxs(evaluationData.getAgszbhxs()); // A股流通市值
                    vcTradSkDaily.setBgszbhxszrm(BigDecimal.valueOf(0)); // B股流通市值(建投不分A/B,数据存到A,B存空)
                }
                // 从stockHoldersMap获取数据
                VcTradSkDailyDto holdersData = getLatestDtoBeforeOrOnDate(stockHoldersMap, tradeDate);
                if (holdersData != null) {
                    vcTradSkDaily.setShareNum(holdersData.getShareNum()); // 股东人数
                }
                // 从stockSharesMap获取数据
                VcTradSkDailyDto sharesData = getLatestDtoBeforeOrOnDate(stockSharesMap, tradeDate);
                if (sharesData != null) {
                    vcTradSkDaily.setTotalshares(sharesData.getTotalshares()); // 总股本（A+B+H）
                    vcTradSkDaily.setFfshares(sharesData.getFfshares()); // 自由流通股本
                    vcTradSkDaily.setRshare(sharesData.getRshare()); // 有限售条件股份
                    vcTradSkDaily.setCshare(sharesData.getCshare()); // 流通股份(与自由流通股本相同)
                    vcTradSkDaily.setAshare(sharesData.getAshare()); // 流通A股
                    vcTradSkDaily.setAsharer(sharesData.getAsharer()); // 非流通A股
                    vcTradSkDaily.setBshare(sharesData.getBshare()); // 流通B股
                    vcTradSkDaily.setBsharer(sharesData.getBsharer()); // 非流通B股
                    vcTradSkDaily.setHshare(sharesData.getHshare()); // 流通H股
                    vcTradSkDaily.setHsharer(sharesData.getHsharer()); // 非流通H股
                }
                vcTradSkDailyList.add(vcTradSkDaily);
            }
            if (CollectionUtils.isNotEmpty(vcTradSkDailyList)) {
                // 将合并完的数据插入到vc_trad_sk_daily, 一次插入200条
                int InsertSize = 200;
                // 需要插入多少次, 这个算法就是防止不是在整除的时候少了一次
                int limit = (vcTradSkDailyList.size() + InsertSize - 1) / InsertSize;
                // 根据起始值seed(0)，每次生成一个指定递增值（n+1）的数，limit(limit)用于截断流的长度,也就是进行limit次的里面的操作
                Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
                    // skip就是跳过前面（a * InsertSize）条数据，因为 a是从0开始，到limit，skip(0)的时候是空的不插入 .limit（InsertSize）->限制每次插入数据的200条
                    List<VcTradSkDailyDto> tempVcTradSkDailyList = vcTradSkDailyList.stream().skip(a * InsertSize).limit(InsertSize).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tempVcTradSkDailyList)) {
                        stockBizMapper.insertVcTradSkDaily(tempVcTradSkDailyList);
                    }
                });
            }
        }
    }

    /**
     * 整合指定公司的交易数据
     * @param companyCode 公司代码字符串
     */
    public void stockInfoMergeByCompanyCodeManual(String companyCode) throws Exception {
        stockBizMapper.deleteVcTradSkDailyDataByCompanyCode(companyCode);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<VcTradSkDailyDto> vcTradSkDailyList = new ArrayList<>();
        // 获取当前日期
        LocalDate today = LocalDate.now();
        LocalDate currentDate = today.minusDays(1);
        // 手动执行写死 "2023-05-22"
        //LocalDate oneYearAgo = currentDate.minusYears(1);
        List<String> allTradeDate = timedTasksMapper.getOneYearTradingDay("2023-05-22", currentDate.format(formatter2));
        if (CollectionUtils.isNotEmpty(allTradeDate)) {
            allTradeDate = allTradeDate.stream().distinct().collect(Collectors.toList());
            // 将日期转换为指定格式的字符串, 手动执行写死 "2023-05-22"
            //String startDateStr = oneYearAgo.format(formatter2);
            String startDateStr = "2023-05-22";
            String endDateStr = currentDate.format(formatter2);
            // 获取四个表的交易数据
            Map<String, VcTradSkDailyDto> stockPerformanceMap = convertToListMapByTradeDate(stockBizMapper.getStockPerformanceByCompanyCode(companyCode, startDateStr, endDateStr));
            Map<String, VcTradSkDailyDto> stockEvaluationMap = convertToListMapByTradeDate(stockBizMapper.getStockEvaluationByCompanyCode(companyCode, startDateStr, endDateStr));
            // 下面两个表不使用日期坐条件查询,传了但是没用
            Map<String, VcTradSkDailyDto> stockHoldersMap = convertToListMapByTradeDate(stockBizMapper.getStockHoldersByCompanyCode(companyCode, startDateStr, endDateStr));
            Map<String, VcTradSkDailyDto> stockSharesMap = convertToListMapByTradeDate(stockBizMapper.getStockSharesByCompanyCode(companyCode, startDateStr, endDateStr));
            // 将四个交易表数据合并为一个
            for (String tradeDate : allTradeDate) {
                VcTradSkDailyDto vcTradSkDaily = new VcTradSkDailyDto();
                LocalDateTime now = LocalDateTime.now();
                vcTradSkDaily.setIdSource(now.format(formatter));
                vcTradSkDaily.setSecurityCd(companyCode);
                vcTradSkDaily.setTradeDate(dateFormat.parse(tradeDate));
                // 从stockPerformanceMap获取数据
                VcTradSkDailyDto performanceData = stockPerformanceMap.get(tradeDate);
                if (performanceData != null) {
                    vcTradSkDaily.setNEW(performanceData.getNEW()); // 今日收盘
                    vcTradSkDaily.setChg(performanceData.getChg()); // 涨跌幅
                    vcTradSkDaily.setCjl(performanceData.getCjl()); // 成交量(股)
                    vcTradSkDaily.setTvalcny(performanceData.getTvalcny()); // 成交金额(元)
                    if (ObjectUtils.isEmpty(vcTradSkDaily.getCjl())) continue;
                } else {
                    continue;
                }
                // 从stockEvaluationMap获取数据
                VcTradSkDailyDto evaluationData = stockEvaluationMap.get(tradeDate);
                if (evaluationData != null) {
                    vcTradSkDaily.setZsz(evaluationData.getZsz()); // 总市值
                    vcTradSkDaily.setPe9(evaluationData.getPe9()); // 市盈率(TTM)
                    vcTradSkDaily.setPb8(evaluationData.getPb8()); // 市净率(最新报告期MRQ)
                    vcTradSkDaily.setAgszbhxs(evaluationData.getAgszbhxs()); // A股流通市值
                    vcTradSkDaily.setBgszbhxszrm(BigDecimal.valueOf(0)); // B股流通市值(建投不分A/B,数据存到A,B存空)
                }
                // 从stockHoldersMap获取数据
                VcTradSkDailyDto holdersData = getLatestDtoBeforeOrOnDate(stockHoldersMap, tradeDate);
                if (holdersData != null) {
                    vcTradSkDaily.setShareNum(holdersData.getShareNum()); // 股东人数
                }
                // 从stockSharesMap获取数据
                VcTradSkDailyDto sharesData = getLatestDtoBeforeOrOnDate(stockSharesMap, tradeDate);
                if (sharesData != null) {
                    vcTradSkDaily.setTotalshares(sharesData.getTotalshares()); // 总股本（A+B+H）
                    vcTradSkDaily.setFfshares(sharesData.getFfshares()); // 自由流通股本
                    vcTradSkDaily.setRshare(sharesData.getRshare()); // 有限售条件股份
                    vcTradSkDaily.setCshare(sharesData.getCshare()); // 流通股份(与自由流通股本相同)
                    vcTradSkDaily.setAshare(sharesData.getAshare()); // 流通A股
                    vcTradSkDaily.setAsharer(sharesData.getAsharer()); // 非流通A股
                    vcTradSkDaily.setBshare(sharesData.getBshare()); // 流通B股
                    vcTradSkDaily.setBsharer(sharesData.getBsharer()); // 非流通B股
                    vcTradSkDaily.setHshare(sharesData.getHshare()); // 流通H股
                    vcTradSkDaily.setHsharer(sharesData.getHsharer()); // 非流通H股
                }
                vcTradSkDailyList.add(vcTradSkDaily);
            }
            if (CollectionUtils.isNotEmpty(vcTradSkDailyList)) {
                // 将合并完的数据插入到vc_trad_sk_daily, 一次插入200条
                int InsertSize = 200;
                // 需要插入多少次, 这个算法就是防止不是在整除的时候少了一次
                int limit = (vcTradSkDailyList.size() + InsertSize - 1) / InsertSize;
                // 根据起始值seed(0)，每次生成一个指定递增值（n+1）的数，limit(limit)用于截断流的长度,也就是进行limit次的里面的操作
                Stream.iterate(0, n -> n + 1).limit(limit).forEach(a -> {
                    // skip就是跳过前面（a * InsertSize）条数据，因为 a是从0开始，到limit，skip(0)的时候是空的不插入 .limit（InsertSize）->限制每次插入数据的200条
                    List<VcTradSkDailyDto> tempVcTradSkDailyList = vcTradSkDailyList.stream().skip(a * InsertSize).limit(InsertSize).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(tempVcTradSkDailyList)) {
                        stockBizMapper.insertVcTradSkDaily(tempVcTradSkDailyList);
                    }
                });
            }
        }
    }

    /**
     * 将列表转换为以公司代码为键的Map
     * @param list 要转换的列表
     * @return 公司代码到数据的映射
     */
    private Map<String, VcTradSkDailyDto> convertToListMap(List<VcTradSkDailyDto> list) {
        Map<String, VcTradSkDailyDto> map = new HashMap<>();
        for (VcTradSkDailyDto dto : list) {
            map.put(dto.getSecurityCd(), dto);
        }
        return map;
    }

    /**
     * 将列表转换为以TradeDate为键的Map
     * @param list 要转换的列表
     * @return TradeDate到数据的映射
     */
    private Map<String, VcTradSkDailyDto> convertToListMapByTradeDate(List<VcTradSkDailyDto> list) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, VcTradSkDailyDto> map = new HashMap<>();
        for (VcTradSkDailyDto dto : list) {
            map.put(sdf.format(dto.getTradeDate()), dto);
        }
        return map;
    }

    /**
     * 从map中找到小于指定日期的最新日期数据
     * @param dataMap 数据map<日期, 数据>
     * @param targetDateStr 指定日期,格式"yyyy-MM-dd"
     * @return 数据
     */
    public static VcTradSkDailyDto getLatestDtoBeforeOrOnDate(Map<String, VcTradSkDailyDto> dataMap, String targetDateStr) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd"); // 日期格式
        Date targetDate = dateFormat.parse(targetDateStr); // 目标日期字符串转换为日期对象
        String latestKey = null;
        Date latestDate = null;
        // 遍历Map中的键
        for (String dateStr : dataMap.keySet()) {
            Date currentDate = dateFormat.parse(dateStr); // 当前键转换为日期对象
            // 如果当前日期小于等于目标日期，并且是迄今为止找到的最大日期
            if (currentDate.compareTo(targetDate) <= 0 && (latestDate == null || currentDate.compareTo(latestDate) > 0)) {
                latestDate = currentDate;
                latestKey = dateStr;
            }
        }

        // 使用找到的最大日期的键来获取对应的值
        return latestKey != null ? dataMap.get(latestKey) : null;
    }

    @Async
    public void repeat(Map<String, Object> param, String ip, UserInfo userInfo) {
        try {
            String tradeDate = param.get("tradeDate").toString();
            // 判断数交推送是否成功
            if (timedTasksMapper.getSuccessCount(tradeDate.replaceAll("-","")) == 4) {
                this.stockInfoMerge(tradeDate);
                param.put("isSuccess", "1");
                param.put("errorMessage", "重新执行成功");
            }  else {
                param.put("isSuccess", "0");
                param.put("errorMessage", "数据交换系统任务推送状态表有失败的");
            }
            timedTasksMapper.updateIsSuccessP(param);
        } catch (Exception e) {
            param.put("isSuccess", "0");
            param.put("errorMessage", e.getMessage());
            timedTasksMapper.updateIsSuccessP(param);
        } finally {
            try {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(param),
                                null, "定时任务重新执行",
                                "系统管理/定时任务管理", "TimedTasksController.repeat",
                                "定时任务{" + MapUtils.getString(param, "zhName") + "(" + formatter.format(new Date(MapUtils.getLong(param, "createTime"))) + ")}重新执行", ip, userInfo));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        }
    }
}
