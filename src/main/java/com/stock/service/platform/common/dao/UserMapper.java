package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.entity.UserExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UserMapper {
    long countByExample(UserExample example);

    int deleteByExample(UserExample example);

    int deleteByPrimaryKey(String id);

    int insert(User record);

    int insertSelective(User record);

    List<User> selectByExampleWithRowbounds(UserExample example, RowBounds rowBounds);

    List<User> selectByExample(UserExample example);

    User selectByPrimaryKey(String id);

    int selectByIdSeriakey(Map<String,String> userDto);

    int updateByExampleSelective(@Param("record") User record, @Param("example") UserExample example);

    int updateByExample(@Param("record") User record, @Param("example") UserExample example);

    int updateByPrimaryKeySelective(User record);

    int updateByPrimaryKey(User record);

    void deletingByUserId(String userId, String lockState, String status);

    User selectByUserName(String userName);

    String getUserIdByUserName(String userName);

    String getUserTypeByUserName(String userName);
}