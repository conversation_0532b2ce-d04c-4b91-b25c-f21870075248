package com.stock.service.platform.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class StringUtil extends StringUtils {

  public static boolean isEmpty(CharSequence cs) {
    return cs == null || cs.length() == 0 || "undefined".equals(cs) || "null".equals(cs);
  }

  public static boolean isNotEmpty(CharSequence cs) {
    return !isEmpty(cs);
  }

  public static boolean isEmpty(Object cs) {
    if (cs instanceof String) {
      return isEmpty((String) cs);
    } else {
      return isEmpty(String.valueOf(cs));
    }
  }

  /**
   * 拼接带{}的字符串
   */
  public static String initString(String source, String... param) {
    Object[] argArray = param;
    return MessageFormatter.arrayFormat(source, argArray).getMessage();
  }

  /**
   * 任一个相等都通过
   */
  public static boolean equalsAny(CharSequence source, CharSequence... searchStrs) {
    if (searchStrs == null) {
      return source == null;
    }
    for (CharSequence str : searchStrs) {
      if (str.equals(source)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 去掉空格
   */
  public static String replaceBlank(String str) {
    String dest = "";
    if (str != null) {
      Pattern p = Pattern.compile("\\s*|\t|\r|\n");
      Matcher m = p.matcher(str);
      dest = m.replaceAll("");
    }
    return dest;
  }
}
