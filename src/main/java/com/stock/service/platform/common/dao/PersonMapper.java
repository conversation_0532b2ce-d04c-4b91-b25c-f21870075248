package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.Person;
import com.stock.service.platform.common.entity.PersonExample;
import com.stock.service.platform.common.entity.PersonWithBLOBs;
import com.stock.service.platform.common.entity.UserCompanyMap;
import com.stock.service.platform.user.dto.UserDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface PersonMapper {
    long countByExample(PersonExample example);

    int deleteByExample(PersonExample example);

    int deleteByPrimaryKey(String id);

    int insert(PersonWithBLOBs record);

    int insertSelective(PersonWithBLOBs record);

    List<PersonWithBLOBs> selectByExampleWithBLOBsWithRowbounds(PersonExample example, RowBounds rowBounds);

    List<PersonWithBLOBs> selectByExampleWithBLOBs(PersonExample example);

    List<Person> selectByExampleWithRowbounds(PersonExample example, RowBounds rowBounds);

    List<Person> selectByExample(PersonExample example);

    PersonWithBLOBs selectByPrimaryKey(String id);
    PersonWithBLOBs selectByIdSeriakey(Map<String,String> param);

    int updateByExampleSelective(@Param("record") PersonWithBLOBs record, @Param("example") PersonExample example);

    int updateByExampleWithBLOBs(@Param("record") PersonWithBLOBs record, @Param("example") PersonExample example);

    int updateByExample(@Param("record") Person record, @Param("example") PersonExample example);

    int updateByPrimaryKeySelective(PersonWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(PersonWithBLOBs record);

    int updateByPrimaryKey(Person record);

    PersonWithBLOBs selectByUserId(String userId);

    int insertUserCompanyMap(List<UserCompanyMap> ruleUserCompanyMapList);

    int selectMaxSort(String companyCode);

    String selectCompanyCode(String orgId);
    void deletingByPersonId(String personId, String status);

    Person selectPersonByTelephone(String telephone);

    String getCompanyId(String companyCode);

    List<String> getOrgIdsByPersonId(String id);

    String getCompanyIdByOrgId(String orgId);

    void deleteCompanyMapByUserId(String id);

    void updateUserById(UserDto userDto);

    void updateStatusByPrimaryKey(Person person);

    void updateUserStatus(String id, String status);

    String getOldChildrenUserName(String id);

    String getOrgIdList(String orgId);

    List<Person> selectByOrgId(String id);

    List<String> getOrgNameByOrgIds(List<String> orgIdList);

    //UserDto getUserInfoByPersonId(String id);
}