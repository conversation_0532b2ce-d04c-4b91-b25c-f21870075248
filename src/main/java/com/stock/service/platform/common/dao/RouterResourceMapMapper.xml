<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.RouterResourceMapMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.RouterResourceMap">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="router_id" jdbcType="VARCHAR" property="routerId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="authority_flag" jdbcType="VARCHAR" property="authorityFlag" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, router_id, resource_id, authority_flag, update_user, update_time, create_time, 
    create_user, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.RouterResourceMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_router_resource_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_router_resource_map
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_router_resource_map
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.RouterResourceMapExample">
    delete from sa_router_resource_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.RouterResourceMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_router_resource_map (id, router_id, resource_id, 
      authority_flag, update_user, update_time, 
      create_time, create_user, status
      )
    values (#{id,jdbcType=VARCHAR}, #{routerId,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, 
      #{authorityFlag,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.RouterResourceMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_router_resource_map
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="routerId != null">
        router_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="authorityFlag != null">
        authority_flag,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="routerId != null">
        #{routerId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="authorityFlag != null">
        #{authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.RouterResourceMapExample" resultType="java.lang.Long">
    select count(*) from sa_router_resource_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_router_resource_map
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.routerId != null">
        router_id = #{record.routerId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.authorityFlag != null">
        authority_flag = #{record.authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_router_resource_map
    set id = #{record.id,jdbcType=VARCHAR},
      router_id = #{record.routerId,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      authority_flag = #{record.authorityFlag,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.RouterResourceMap">
    update sa_router_resource_map
    <set>
      <if test="routerId != null">
        router_id = #{routerId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="authorityFlag != null">
        authority_flag = #{authorityFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.RouterResourceMap">
    update sa_router_resource_map
    set router_id = #{routerId,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      authority_flag = #{authorityFlag,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.RouterResourceMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_router_resource_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>