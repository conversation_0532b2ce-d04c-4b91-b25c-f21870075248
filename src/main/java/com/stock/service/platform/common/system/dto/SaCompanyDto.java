package com.stock.service.platform.common.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stock.service.platform.common.entity.CompanyContact;
import com.stock.service.platform.common.util.Message;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SaCompanyDto implements Serializable {
    private String id;

    @Message(title = "证劵代码", primary = "true")
    private String companyCode;

    @Message(title = "公司名称", primary = "true")
    private String zhName;

    @Message(title = "公司简称", primary = "true")
    private String zhSortName;

    @Message(title = "法定代表人", primary = "true")
    private String corporate;

    private String taxNo;

    @Message(title = "服务营业部", primary = "true")
    private String organisationNo;

    @Message(title = "注册地址", primary = "true")
    private String registerAddress;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Message(title = "公司上市日期", primary = "true")
    private Date listingDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Message(title = "公司登记日期", primary = "true")
    private Date registerDate;

    @Message(title = "公司邮箱", primary = "true")
    private String companyMail;

    @Message(title = "公司注册资金", primary = "true")
    private BigDecimal registerCapital;

    @Message(title = "发行价格", primary = "true")
    private BigDecimal issuePrice;

    @Message(title = "A股股本", primary = "true")
    private BigDecimal capitalStockA;

    @Message(title = "B股股本", primary = "true")
    private BigDecimal capitalStockB;

    @Message(title = "H股股本", primary = "true")
    private BigDecimal capitalStockH;

    @Message(title = "总股本", primary = "true")
    private BigDecimal capitalStockTotal;

    private String hiddenKey;

    private String source;

    @Message(title = "社会统一信用代码", primary = "true")
    private String socialUnifiedCreditCode;

    private String superiorCompanyCode;

    private String superiorCompanyName;

    private String legalPersonIdType;

    private String parentId;

    private String parentCompanyCode;

    private String companyStatus;

    @Message(title = "公司电话", primary = "true")
    private String phone;

    private String belongsPlate;

    private String city;

    private String relaCode;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String stockType;

    private String liststate;

    private String deleteflag;

    private String statusName;

    @Message(title = "公司性质", primary = "true")
    private String orgform;

    private String businessDepartmentName;

    @Message(title = "证劵名称", primary = "true")
    private String companyStockName;

    @Message(title = "法人证件号码", primary = "true")
    private String corporateIdNumber;

    @Message(title = "备注", primary = "true")
    private String remark;

    @Message(title = "板块", primary = "true")
    private String marketStockType;

    private List<String> companyCodeList;

    private String seriakey;

    private String industryCSRCStr;

    private String industrySWStr;
    private String logoUrl;
    private String compprofileDc;
    private String controStr;
    private String comStatus;
    private String systemState;

    private String systemStateName;

    private List<CompanyContact> contacts;

    private List<String> ids;

    private String userId;

    private List<String> orgIds;

    private String orgId;

    private String companyWebsite;

    @Message(title = "市场", primary = "true")
    private String market;

    /**
     * mq发送字段
     */
    private String companyName; // 公司名称
    private String businessLicenseNumber; // 社会统一信用代码
    private String companyProperty; // 公司性质
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date companyRegisterDate; // 公司注册日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date companyListingDate; // 公司上市日期
    private BigDecimal registeredCapitalMoney; // 公司注册资金（元）
    private BigDecimal totalCapitalStock; // 总股本
    private String legalPersonName; // 法定代表人
    private String legalPersonIdNumber; // 法人证件号码
    private String companyPhone; // 公司电话
    private String companyContactName; // 联系人
    private String companyContactPhone; // 联系人手机号
    private String companyContactEmail; // 联系人Email
    private String businessDepartmentStr; // 合作营业部（勾选多个营业部逗号分割）
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate; // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifiedDate; // 更新时间
    private String createBy; // 创建人ID
    private String modifiedBy; // 更新人ID

    private int startRow;

    private int pageSize;

    private List<SaCompanyDto> children;

    private String marketStockTypeGQJL;

    private static final long serialVersionUID = 1L;

    public String getMarketStockTypeGQJL() {
        return marketStockTypeGQJL;
    }

    public void setMarketStockTypeGQJL(String marketStockTypeGQJL) {
        this.marketStockTypeGQJL = marketStockTypeGQJL;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getBusinessLicenseNumber() {
        return businessLicenseNumber;
    }

    public void setBusinessLicenseNumber(String businessLicenseNumber) {
        this.businessLicenseNumber = businessLicenseNumber;
    }

    public String getCompanyProperty() {
        return companyProperty;
    }

    public void setCompanyProperty(String companyProperty) {
        this.companyProperty = companyProperty;
    }

    public Date getCompanyRegisterDate() {
        return companyRegisterDate;
    }

    public void setCompanyRegisterDate(Date companyRegisterDate) {
        this.companyRegisterDate = companyRegisterDate;
    }

    public Date getCompanyListingDate() {
        return companyListingDate;
    }

    public void setCompanyListingDate(Date companyListingDate) {
        this.companyListingDate = companyListingDate;
    }

    public BigDecimal getRegisteredCapitalMoney() {
        return registeredCapitalMoney;
    }

    public void setRegisteredCapitalMoney(BigDecimal registeredCapitalMoney) {
        this.registeredCapitalMoney = registeredCapitalMoney;
    }

    public BigDecimal getTotalCapitalStock() {
        return totalCapitalStock;
    }

    public void setTotalCapitalStock(BigDecimal totalCapitalStock) {
        this.totalCapitalStock = totalCapitalStock;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLegalPersonIdNumber() {
        return legalPersonIdNumber;
    }

    public void setLegalPersonIdNumber(String legalPersonIdNumber) {
        this.legalPersonIdNumber = legalPersonIdNumber;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyContactName() {
        return companyContactName;
    }

    public void setCompanyContactName(String companyContactName) {
        this.companyContactName = companyContactName;
    }

    public String getCompanyContactPhone() {
        return companyContactPhone;
    }

    public void setCompanyContactPhone(String companyContactPhone) {
        this.companyContactPhone = companyContactPhone;
    }

    public String getCompanyContactEmail() {
        return companyContactEmail;
    }

    public void setCompanyContactEmail(String companyContactEmail) {
        this.companyContactEmail = companyContactEmail;
    }

    public String getBusinessDepartmentStr() {
        return businessDepartmentStr;
    }

    public void setBusinessDepartmentStr(String businessDepartmentStr) {
        this.businessDepartmentStr = businessDepartmentStr;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public Date getListingDate() {
        return listingDate;
    }

    public void setListingDate(Date listingDate) {
        this.listingDate = listingDate;
    }

    public BigDecimal getIssuePrice() {
        return issuePrice;
    }

    public void setIssuePrice(BigDecimal issuePrice) {
        this.issuePrice = issuePrice;
    }

    public BigDecimal getCapitalStockA() {
        return capitalStockA;
    }

    public void setCapitalStockA(BigDecimal capitalStockA) {
        this.capitalStockA = capitalStockA;
    }

    public BigDecimal getCapitalStockB() {
        return capitalStockB;
    }

    public void setCapitalStockB(BigDecimal capitalStockB) {
        this.capitalStockB = capitalStockB;
    }

    public BigDecimal getCapitalStockH() {
        return capitalStockH;
    }

    public void setCapitalStockH(BigDecimal capitalStockH) {
        this.capitalStockH = capitalStockH;
    }

    public BigDecimal getCapitalStockTotal() {
        return capitalStockTotal;
    }

    public void setCapitalStockTotal(BigDecimal capitalStockTotal) {
        this.capitalStockTotal = capitalStockTotal;
    }

    public String getHiddenKey() {
        return hiddenKey;
    }

    public void setHiddenKey(String hiddenKey) {
        this.hiddenKey = hiddenKey;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSocialUnifiedCreditCode() {
        return socialUnifiedCreditCode;
    }

    public void setSocialUnifiedCreditCode(String socialUnifiedCreditCode) {
        this.socialUnifiedCreditCode = socialUnifiedCreditCode;
    }

    public String getSuperiorCompanyCode() {
        return superiorCompanyCode;
    }

    public void setSuperiorCompanyCode(String superiorCompanyCode) {
        this.superiorCompanyCode = superiorCompanyCode;
    }

    public String getSuperiorCompanyName() {
        return superiorCompanyName;
    }

    public void setSuperiorCompanyName(String superiorCompanyName) {
        this.superiorCompanyName = superiorCompanyName;
    }

    public String getLegalPersonIdType() {
        return legalPersonIdType;
    }

    public void setLegalPersonIdType(String legalPersonIdType) {
        this.legalPersonIdType = legalPersonIdType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentCompanyCode() {
        return parentCompanyCode;
    }

    public void setParentCompanyCode(String parentCompanyCode) {
        this.parentCompanyCode = parentCompanyCode;
    }

    public String getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(String companyStatus) {
        this.companyStatus = companyStatus;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName == null ? null : zhName.trim();
    }

    public String getZhSortName() {
        return zhSortName;
    }

    public void setZhSortName(String zhSortName) {
        this.zhSortName = zhSortName == null ? null : zhSortName.trim();
    }

    public String getCorporate() {
        return corporate;
    }

    public void setCorporate(String corporate) {
        this.corporate = corporate == null ? null : corporate.trim();
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo == null ? null : taxNo.trim();
    }

    public String getOrganisationNo() {
        return organisationNo;
    }

    public void setOrganisationNo(String organisationNo) {
        this.organisationNo = organisationNo == null ? null : organisationNo.trim();
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress == null ? null : registerAddress.trim();
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public String getCompanyMail() {
        return companyMail;
    }

    public void setCompanyMail(String companyMail) {
        this.companyMail = companyMail == null ? null : companyMail.trim();
    }

    public BigDecimal getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(BigDecimal registerCapital) {
        this.registerCapital = registerCapital;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getBusinessDepartmentName() {
        return businessDepartmentName;
    }

    public void setBusinessDepartmentName(String businessDepartmentName) {
        this.businessDepartmentName = businessDepartmentName;
    }

    public String getBelongsPlate() {
        return belongsPlate;
    }

    public void setBelongsPlate(String belongsPlate) {
        this.belongsPlate = belongsPlate == null ? null : belongsPlate.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getRelaCode() {
        return relaCode;
    }

    public void setRelaCode(String relaCode) {
        this.relaCode = relaCode == null ? null : relaCode.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType == null ? null : stockType.trim();
    }

    public String getListstate() {
        return liststate;
    }

    public void setListstate(String liststate) {
        this.liststate = liststate == null ? null : liststate.trim();
    }

    public String getDeleteflag() {
        return deleteflag;
    }

    public void setDeleteflag(String deleteflag) {
        this.deleteflag = deleteflag == null ? null : deleteflag.trim();
    }

    public String getOrgform() {
        return orgform;
    }

    public void setOrgform(String orgform) {
        this.orgform = orgform == null ? null : orgform.trim();
    }

    public String getCompanyStockName() {
        return companyStockName;
    }

    public void setCompanyStockName(String companyStockName) {
        this.companyStockName = companyStockName == null ? null : companyStockName.trim();
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber == null ? null : corporateIdNumber.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMarketStockType() {
        return marketStockType;
    }

    public void setMarketStockType(String marketStockType) {
        this.marketStockType = marketStockType == null ? null : marketStockType.trim();
    }

    public List<String> getCompanyCodeList() {
        return companyCodeList;
    }

    public void setCompanyCodeList(List<String> companyCodeList) {
        this.companyCodeList = companyCodeList;
    }

    public String getIndustryCSRCStr() {
        return industryCSRCStr;
    }

    public void setIndustryCSRCStr(String industryCSRCStr) {
        this.industryCSRCStr = industryCSRCStr;
    }

    public String getIndustrySWStr() {
        return industrySWStr;
    }

    public void setIndustrySWStr(String industrySWStr) {
        this.industrySWStr = industrySWStr;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getCompprofileDc() {
        return compprofileDc;
    }

    public void setCompprofileDc(String compprofileDc) {
        this.compprofileDc = compprofileDc;
    }

    public String getControStr() {
        return controStr;
    }

    public void setControStr(String controStr) {
        this.controStr = controStr;
    }

    public String getComStatus() {
        return comStatus;
    }

    public void setComStatus(String comStatus) {
        this.comStatus = comStatus;
    }

    public String getSystemState() {
        return systemState;
    }

    public void setSystemState(String systemState) {
        this.systemState = systemState;
    }

    public String getSystemStateName() {
        return systemStateName;
    }

    public void setSystemStateName(String systemStateName) {
        this.systemStateName = systemStateName;
    }

    public List<CompanyContact> getContacts() {
        return contacts;
    }

    public void setContacts(List<CompanyContact> contacts) {
        this.contacts = contacts;
    }

    public int getStartRow() {
        return startRow;
    }

    public void setStartRow(int startRow) {
        this.startRow = startRow;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public List<SaCompanyDto> getChildren() {
        return children;
    }

    public void setChildren(List<SaCompanyDto> children) {
        this.children = children;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public String getCompanyWebsite() {
        return companyWebsite;
    }

    public void setCompanyWebsite(String companyWebsite) {
        this.companyWebsite = companyWebsite;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}