package com.stock.service.platform.common.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CompanyExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CompanyExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeleteflagIsNull() {
            addCriterion("deleteflag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteflagIsNotNull() {
            addCriterion("deleteflag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteflagEqualTo(String value) {
            addCriterion("deleteflag =", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagNotEqualTo(String value) {
            addCriterion("deleteflag <>", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagGreaterThan(String value) {
            addCriterion("deleteflag >", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagGreaterThanOrEqualTo(String value) {
            addCriterion("deleteflag >=", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagLessThan(String value) {
            addCriterion("deleteflag <", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagLessThanOrEqualTo(String value) {
            addCriterion("deleteflag <=", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagLike(String value) {
            addCriterion("deleteflag like", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagNotLike(String value) {
            addCriterion("deleteflag not like", value, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagIn(List<String> values) {
            addCriterion("deleteflag in", values, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagNotIn(List<String> values) {
            addCriterion("deleteflag not in", values, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagBetween(String value1, String value2) {
            addCriterion("deleteflag between", value1, value2, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andDeleteflagNotBetween(String value1, String value2) {
            addCriterion("deleteflag not between", value1, value2, "deleteflag");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }


        public Criteria andSocialUnifiedCreditCodeEqualTo(String value) {
            addCriterion("social_unified_credit_code =", value, "socialUnifiedCreditCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andZhNameIsNull() {
            addCriterion("zh_name is null");
            return (Criteria) this;
        }

        public Criteria andZhNameIsNotNull() {
            addCriterion("zh_name is not null");
            return (Criteria) this;
        }

        public Criteria andZhNameEqualTo(String value) {
            addCriterion("zh_name =", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameNotEqualTo(String value) {
            addCriterion("zh_name <>", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameGreaterThan(String value) {
            addCriterion("zh_name >", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameGreaterThanOrEqualTo(String value) {
            addCriterion("zh_name >=", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameLessThan(String value) {
            addCriterion("zh_name <", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameLessThanOrEqualTo(String value) {
            addCriterion("zh_name <=", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameLike(String value) {
            addCriterion("zh_name like", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameNotLike(String value) {
            addCriterion("zh_name not like", value, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameIn(List<String> values) {
            addCriterion("zh_name in", values, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameNotIn(List<String> values) {
            addCriterion("zh_name not in", values, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameBetween(String value1, String value2) {
            addCriterion("zh_name between", value1, value2, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhNameNotBetween(String value1, String value2) {
            addCriterion("zh_name not between", value1, value2, "zhName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameIsNull() {
            addCriterion("zh_sort_name is null");
            return (Criteria) this;
        }

        public Criteria andZhSortNameIsNotNull() {
            addCriterion("zh_sort_name is not null");
            return (Criteria) this;
        }

        public Criteria andZhSortNameEqualTo(String value) {
            addCriterion("zh_sort_name =", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameNotEqualTo(String value) {
            addCriterion("zh_sort_name <>", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameGreaterThan(String value) {
            addCriterion("zh_sort_name >", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameGreaterThanOrEqualTo(String value) {
            addCriterion("zh_sort_name >=", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameLessThan(String value) {
            addCriterion("zh_sort_name <", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameLessThanOrEqualTo(String value) {
            addCriterion("zh_sort_name <=", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameLike(String value) {
            addCriterion("zh_sort_name like", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameNotLike(String value) {
            addCriterion("zh_sort_name not like", value, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameIn(List<String> values) {
            addCriterion("zh_sort_name in", values, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameNotIn(List<String> values) {
            addCriterion("zh_sort_name not in", values, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameBetween(String value1, String value2) {
            addCriterion("zh_sort_name between", value1, value2, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andZhSortNameNotBetween(String value1, String value2) {
            addCriterion("zh_sort_name not between", value1, value2, "zhSortName");
            return (Criteria) this;
        }

        public Criteria andCorporateIsNull() {
            addCriterion("corporate is null");
            return (Criteria) this;
        }

        public Criteria andCorporateIsNotNull() {
            addCriterion("corporate is not null");
            return (Criteria) this;
        }

        public Criteria andCorporateEqualTo(String value) {
            addCriterion("corporate =", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateNotEqualTo(String value) {
            addCriterion("corporate <>", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateGreaterThan(String value) {
            addCriterion("corporate >", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateGreaterThanOrEqualTo(String value) {
            addCriterion("corporate >=", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateLessThan(String value) {
            addCriterion("corporate <", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateLessThanOrEqualTo(String value) {
            addCriterion("corporate <=", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateLike(String value) {
            addCriterion("corporate like", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateNotLike(String value) {
            addCriterion("corporate not like", value, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateIn(List<String> values) {
            addCriterion("corporate in", values, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateNotIn(List<String> values) {
            addCriterion("corporate not in", values, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateBetween(String value1, String value2) {
            addCriterion("corporate between", value1, value2, "corporate");
            return (Criteria) this;
        }

        public Criteria andCorporateNotBetween(String value1, String value2) {
            addCriterion("corporate not between", value1, value2, "corporate");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoIsNull() {
            addCriterion("organisation_no is null");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoIsNotNull() {
            addCriterion("organisation_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoEqualTo(String value) {
            addCriterion("organisation_no =", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoNotEqualTo(String value) {
            addCriterion("organisation_no <>", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoGreaterThan(String value) {
            addCriterion("organisation_no >", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoGreaterThanOrEqualTo(String value) {
            addCriterion("organisation_no >=", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoLessThan(String value) {
            addCriterion("organisation_no <", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoLessThanOrEqualTo(String value) {
            addCriterion("organisation_no <=", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoLike(String value) {
            addCriterion("organisation_no like", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoNotLike(String value) {
            addCriterion("organisation_no not like", value, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoIn(List<String> values) {
            addCriterion("organisation_no in", values, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoNotIn(List<String> values) {
            addCriterion("organisation_no not in", values, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoBetween(String value1, String value2) {
            addCriterion("organisation_no between", value1, value2, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andOrganisationNoNotBetween(String value1, String value2) {
            addCriterion("organisation_no not between", value1, value2, "organisationNo");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressIsNull() {
            addCriterion("register_address is null");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressIsNotNull() {
            addCriterion("register_address is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressEqualTo(String value) {
            addCriterion("register_address =", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressNotEqualTo(String value) {
            addCriterion("register_address <>", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressGreaterThan(String value) {
            addCriterion("register_address >", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressGreaterThanOrEqualTo(String value) {
            addCriterion("register_address >=", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressLessThan(String value) {
            addCriterion("register_address <", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressLessThanOrEqualTo(String value) {
            addCriterion("register_address <=", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressLike(String value) {
            addCriterion("register_address like", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressNotLike(String value) {
            addCriterion("register_address not like", value, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressIn(List<String> values) {
            addCriterion("register_address in", values, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressNotIn(List<String> values) {
            addCriterion("register_address not in", values, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressBetween(String value1, String value2) {
            addCriterion("register_address between", value1, value2, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterAddressNotBetween(String value1, String value2) {
            addCriterion("register_address not between", value1, value2, "registerAddress");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(Date value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(Date value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(Date value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(Date value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(Date value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(Date value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<Date> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<Date> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(Date value1, Date value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(Date value1, Date value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andCompanyMailIsNull() {
            addCriterion("company_mail is null");
            return (Criteria) this;
        }

        public Criteria andCompanyMailIsNotNull() {
            addCriterion("company_mail is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyMailEqualTo(String value) {
            addCriterion("company_mail =", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailNotEqualTo(String value) {
            addCriterion("company_mail <>", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailGreaterThan(String value) {
            addCriterion("company_mail >", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailGreaterThanOrEqualTo(String value) {
            addCriterion("company_mail >=", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailLessThan(String value) {
            addCriterion("company_mail <", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailLessThanOrEqualTo(String value) {
            addCriterion("company_mail <=", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailLike(String value) {
            addCriterion("company_mail like", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailNotLike(String value) {
            addCriterion("company_mail not like", value, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailIn(List<String> values) {
            addCriterion("company_mail in", values, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailNotIn(List<String> values) {
            addCriterion("company_mail not in", values, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailBetween(String value1, String value2) {
            addCriterion("company_mail between", value1, value2, "companyMail");
            return (Criteria) this;
        }

        public Criteria andCompanyMailNotBetween(String value1, String value2) {
            addCriterion("company_mail not between", value1, value2, "companyMail");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalIsNull() {
            addCriterion("register_capital is null");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalIsNotNull() {
            addCriterion("register_capital is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalEqualTo(BigDecimal value) {
            addCriterion("register_capital =", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalNotEqualTo(BigDecimal value) {
            addCriterion("register_capital <>", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalGreaterThan(BigDecimal value) {
            addCriterion("register_capital >", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("register_capital >=", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalLessThan(BigDecimal value) {
            addCriterion("register_capital <", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalLessThanOrEqualTo(BigDecimal value) {
            addCriterion("register_capital <=", value, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalIn(List<BigDecimal> values) {
            addCriterion("register_capital in", values, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalNotIn(List<BigDecimal> values) {
            addCriterion("register_capital not in", values, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_capital between", value1, value2, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andRegisterCapitalNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("register_capital not between", value1, value2, "registerCapital");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andOrgformIsNull() {
            addCriterion("orgform is null");
            return (Criteria) this;
        }

        public Criteria andOrgformIsNotNull() {
            addCriterion("orgform is not null");
            return (Criteria) this;
        }

        public Criteria andOrgformEqualTo(String value) {
            addCriterion("orgform =", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformNotEqualTo(String value) {
            addCriterion("orgform <>", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformGreaterThan(String value) {
            addCriterion("orgform >", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformGreaterThanOrEqualTo(String value) {
            addCriterion("orgform >=", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformLessThan(String value) {
            addCriterion("orgform <", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformLessThanOrEqualTo(String value) {
            addCriterion("orgform <=", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformLike(String value) {
            addCriterion("orgform like", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformNotLike(String value) {
            addCriterion("orgform not like", value, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformIn(List<String> values) {
            addCriterion("orgform in", values, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformNotIn(List<String> values) {
            addCriterion("orgform not in", values, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformBetween(String value1, String value2) {
            addCriterion("orgform between", value1, value2, "orgform");
            return (Criteria) this;
        }

        public Criteria andOrgformNotBetween(String value1, String value2) {
            addCriterion("orgform not between", value1, value2, "orgform");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameIsNull() {
            addCriterion("company_stock_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameIsNotNull() {
            addCriterion("company_stock_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameEqualTo(String value) {
            addCriterion("company_stock_name =", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameNotEqualTo(String value) {
            addCriterion("company_stock_name <>", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameGreaterThan(String value) {
            addCriterion("company_stock_name >", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_stock_name >=", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameLessThan(String value) {
            addCriterion("company_stock_name <", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameLessThanOrEqualTo(String value) {
            addCriterion("company_stock_name <=", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameLike(String value) {
            addCriterion("company_stock_name like", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameNotLike(String value) {
            addCriterion("company_stock_name not like", value, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameIn(List<String> values) {
            addCriterion("company_stock_name in", values, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameNotIn(List<String> values) {
            addCriterion("company_stock_name not in", values, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameBetween(String value1, String value2) {
            addCriterion("company_stock_name between", value1, value2, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCompanyStockNameNotBetween(String value1, String value2) {
            addCriterion("company_stock_name not between", value1, value2, "companyStockName");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberIsNull() {
            addCriterion("corporate_id_number is null");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberIsNotNull() {
            addCriterion("corporate_id_number is not null");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberEqualTo(String value) {
            addCriterion("corporate_id_number =", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberNotEqualTo(String value) {
            addCriterion("corporate_id_number <>", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberGreaterThan(String value) {
            addCriterion("corporate_id_number >", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberGreaterThanOrEqualTo(String value) {
            addCriterion("corporate_id_number >=", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberLessThan(String value) {
            addCriterion("corporate_id_number <", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberLessThanOrEqualTo(String value) {
            addCriterion("corporate_id_number <=", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberLike(String value) {
            addCriterion("corporate_id_number like", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberNotLike(String value) {
            addCriterion("corporate_id_number not like", value, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberIn(List<String> values) {
            addCriterion("corporate_id_number in", values, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberNotIn(List<String> values) {
            addCriterion("corporate_id_number not in", values, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberBetween(String value1, String value2) {
            addCriterion("corporate_id_number between", value1, value2, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCorporateIdNumberNotBetween(String value1, String value2) {
            addCriterion("corporate_id_number not between", value1, value2, "corporateIdNumber");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameIsNull() {
            addCriterion("company_contact_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameIsNotNull() {
            addCriterion("company_contact_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameEqualTo(String value) {
            addCriterion("company_contact_name =", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameNotEqualTo(String value) {
            addCriterion("company_contact_name <>", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameGreaterThan(String value) {
            addCriterion("company_contact_name >", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_contact_name >=", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameLessThan(String value) {
            addCriterion("company_contact_name <", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameLessThanOrEqualTo(String value) {
            addCriterion("company_contact_name <=", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameLike(String value) {
            addCriterion("company_contact_name like", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameNotLike(String value) {
            addCriterion("company_contact_name not like", value, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameIn(List<String> values) {
            addCriterion("company_contact_name in", values, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameNotIn(List<String> values) {
            addCriterion("company_contact_name not in", values, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameBetween(String value1, String value2) {
            addCriterion("company_contact_name between", value1, value2, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactNameNotBetween(String value1, String value2) {
            addCriterion("company_contact_name not between", value1, value2, "companyContactName");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneIsNull() {
            addCriterion("company_contact_phone is null");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneIsNotNull() {
            addCriterion("company_contact_phone is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneEqualTo(String value) {
            addCriterion("company_contact_phone =", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneNotEqualTo(String value) {
            addCriterion("company_contact_phone <>", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneGreaterThan(String value) {
            addCriterion("company_contact_phone >", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("company_contact_phone >=", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneLessThan(String value) {
            addCriterion("company_contact_phone <", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("company_contact_phone <=", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneLike(String value) {
            addCriterion("company_contact_phone like", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneNotLike(String value) {
            addCriterion("company_contact_phone not like", value, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneIn(List<String> values) {
            addCriterion("company_contact_phone in", values, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneNotIn(List<String> values) {
            addCriterion("company_contact_phone not in", values, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneBetween(String value1, String value2) {
            addCriterion("company_contact_phone between", value1, value2, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andCompanyContactPhoneNotBetween(String value1, String value2) {
            addCriterion("company_contact_phone not between", value1, value2, "companyContactPhone");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeIsNull() {
            addCriterion("market_stock_type is null");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeIsNotNull() {
            addCriterion("market_stock_type is not null");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeEqualTo(String value) {
            addCriterion("market_stock_type =", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeNotEqualTo(String value) {
            addCriterion("market_stock_type <>", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeGreaterThan(String value) {
            addCriterion("market_stock_type >", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeGreaterThanOrEqualTo(String value) {
            addCriterion("market_stock_type >=", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeLessThan(String value) {
            addCriterion("market_stock_type <", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeLessThanOrEqualTo(String value) {
            addCriterion("market_stock_type <=", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeLike(String value) {
            addCriterion("market_stock_type like", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeNotLike(String value) {
            addCriterion("market_stock_type not like", value, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeIn(List<String> values) {
            addCriterion("market_stock_type in", values, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeNotIn(List<String> values) {
            addCriterion("market_stock_type not in", values, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeBetween(String value1, String value2) {
            addCriterion("market_stock_type between", value1, value2, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andMarketStockTypeNotBetween(String value1, String value2) {
            addCriterion("market_stock_type not between", value1, value2, "marketStockType");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyIsNull() {
            addCriterion("hidden_key is null");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyIsNotNull() {
            addCriterion("hidden_key is not null");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyEqualTo(String value) {
            addCriterion("hidden_key =", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyNotEqualTo(String value) {
            addCriterion("hidden_key <>", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyGreaterThan(String value) {
            addCriterion("hidden_key >", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyGreaterThanOrEqualTo(String value) {
            addCriterion("hidden_key >=", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyLessThan(String value) {
            addCriterion("hidden_key <", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyLessThanOrEqualTo(String value) {
            addCriterion("hidden_key <=", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyLike(String value) {
            addCriterion("hidden_key like", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyNotLike(String value) {
            addCriterion("hidden_key not like", value, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyIn(List<String> values) {
            addCriterion("hidden_key in", values, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyNotIn(List<String> values) {
            addCriterion("hidden_key not in", values, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyBetween(String value1, String value2) {
            addCriterion("hidden_key between", value1, value2, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andHiddenKeyNotBetween(String value1, String value2) {
            addCriterion("hidden_key not between", value1, value2, "hiddenKey");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSystemStateIsNull() {
            addCriterion("system_state is null");
            return (Criteria) this;
        }

        public Criteria andSystemStateIsNotNull() {
            addCriterion("system_state is not null");
            return (Criteria) this;
        }

        public Criteria andSystemStateEqualTo(String value) {
            addCriterion("system_state =", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateNotEqualTo(String value) {
            addCriterion("system_state <>", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateGreaterThan(String value) {
            addCriterion("system_state >", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateGreaterThanOrEqualTo(String value) {
            addCriterion("system_state >=", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateLessThan(String value) {
            addCriterion("system_state <", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateLessThanOrEqualTo(String value) {
            addCriterion("system_state <=", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateLike(String value) {
            addCriterion("system_state like", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateNotLike(String value) {
            addCriterion("system_state not like", value, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateIn(List<String> values) {
            addCriterion("system_state in", values, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateNotIn(List<String> values) {
            addCriterion("system_state not in", values, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateBetween(String value1, String value2) {
            addCriterion("system_state between", value1, value2, "systemState");
            return (Criteria) this;
        }

        public Criteria andSystemStateNotBetween(String value1, String value2) {
            addCriterion("system_state not between", value1, value2, "systemState");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}