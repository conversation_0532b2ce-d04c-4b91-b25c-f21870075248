package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.Parameter;
import com.stock.service.platform.common.entity.ParameterExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface ParameterMapper {
    long countByExample(ParameterExample example);

    int deleteByExample(ParameterExample example);

    int deleteByPrimaryKey(String id);

    int insert(Parameter record);

    int insertSelective(Parameter record);

    List<Parameter> selectByExampleWithRowbounds(ParameterExample example, RowBounds rowBounds);

    List<Parameter> selectByExample(ParameterExample example);

    Parameter selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Parameter record, @Param("example") ParameterExample example);

    int updateByExample(@Param("record") Parameter record, @Param("example") ParameterExample example);

    int updateByPrimaryKeySelective(Parameter record);

    int updateByPrimaryKey(Parameter record);
}