<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.system.dao.TimedTasksMapper">

    <select id="getTasksList" resultType="map">
        select message_id messageId,
               sys_code sysCode,
               info info,
               is_success isSuccess,
               error_message errorMessage,
               trade_date tradeDate,
               update_time updateTime
        from activemq_topic
        where message_type = '1' and sys_code = #{sysCode}
        ORDER BY update_time Desc
        LIMIT #{startRow}, #{pageSize}
    </select>

    <insert id="insertActivemqTopic01" parameterType="java.util.Map">
        INSERT INTO activemq_topic (id,message_id,sys_code, info, is_success,
                                    message_type, business_type,
                                    operation_type, error_message, create_time, update_time)
        VALUES (CONCAT(uuid_short(), ''), #{messageId},#{sysCode},
                #{info}, #{isSuccess}, #{messageType},
                #{businessType}, #{operationType}, #{errorMessage}, #{updateTime}, #{updateTime})
    </insert>

    <update id="updateIsSuccess" parameterType="java.util.Map">
        update activemq_topic
        <set>
            is_success = #{isSuccess},
            <if test="errorMessage != null">
                error_message = #{errorMessage},
            </if>
        </set>
        where message_id = #{messageId} and sys_code = #{sysCode}
    </update>

    <insert id="insertTasksP" parameterType="com.stock.service.platform.common.system.dto.TasksDto">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        INSERT INTO timed_tasks (id, name, zh_name, is_success, error_message,trade_date, create_time)
        VALUES (#{id,jdbcType=VARCHAR}, #{name},#{zhName}, #{isSuccess}, #{errorMessage},#{tradeDate}, now())
    </insert>

    <select id="getTasksPList" resultType="map">
        select id,
               name,
               zh_name zhName,
               is_success isSuccess,
               error_message errorMessage,
               trade_date tradeDate,
               create_time createTime,
               update_time updateTime
        from timed_tasks
        where name = #{name}
        ORDER BY create_time desc
        LIMIT #{startRow}, #{pageSize}
    </select>
    <select id="getTasksPCount" resultType="java.lang.Integer">
        select count(*) from timed_tasks where name = #{name}
    </select>
    <select id="getTasksPGroupList" resultType="java.util.Map">
        SELECT
            t1.name,
            t1.zh_name zhName,
            t1.is_success isSuccess,
            t1.create_time createTime
        FROM
            `timed_tasks` t1
        INNER JOIN (
            SELECT
                name,
                MAX(create_time) AS max_create_time
            FROM
                `timed_tasks`
            GROUP BY
                name
        ) t2 ON t1.name = t2.name AND t1.create_time = t2.max_create_time
        ORDER BY t1.name desc
        LIMIT #{startRow}, #{pageSize}
    </select>
    <select id="getTasksPGroupCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            `timed_tasks` t1
                INNER JOIN (
                SELECT
                    name,
                    MAX(create_time) AS max_create_time
                FROM
                    `timed_tasks`
                GROUP BY
                    name
            ) t2 ON t1.name = t2.name AND t1.create_time = t2.max_create_time
    </select>
    <select id="isTradingDay" resultType="java.lang.Integer">
        select count(*) from `capital-repmanage-jt`.trade_date where band_type = 'A' and sisdel = '0' and valid_flag = '0' and trade_date = #{date}
    </select>
    <select id="getSuccessCount" resultType="java.lang.Integer">
        select count(*) from t_dxs_task_status where biz_date = #{date} and job_status = 'SUCCESS'
    </select>
    <select id="getOneYearTradingDay" resultType="java.lang.String">
        select trade_date
        from `capital-repmanage-jt`.trade_date
        where band_type = 'A'
          and sisdel = '0'
          and valid_flag = '0'
          and trade_date between #{oneYearAgo} and #{currentDate}
        order by trade_date
    </select>
    <select id="getMaxTradeDate" resultType="java.lang.String">
        SELECT
        trade_date
        FROM
        `capital-repmanage-jt`.`trade_date`
        WHERE
        band_type = 'A'
        AND sisdel = '0'
        AND valid_flag = '0'
        AND trade_date &lt; DATE (#{date,jdbcType=VARCHAR})
        ORDER BY trade_date DESC
        LIMIT 1
    </select>
    <select id="getTimedTasksByTradeDate" resultType="com.stock.service.platform.common.system.dto.TasksDto">
        SELECT
            id
        FROM
            timed_tasks
        WHERE
            trade_date = #{tradeDate}
    </select>
    <update id="updateIsSuccessP" parameterType="java.util.Map">
        update timed_tasks
        <set>
            is_success = #{isSuccess},
            <if test="errorMessage != null">
                error_message = #{errorMessage},
            </if>
            update_time = now()
        </set>
        where id = #{id}
    </update>
</mapper>