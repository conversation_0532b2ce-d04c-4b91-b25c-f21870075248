package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class Person implements Serializable {
    private String id;

    private String employeeNumber;

    private String personType;

    private String personName;

    private String foreignName;

    private String titleName;

    private String jobId;

    private String jobs;

    private String orgName;

    private String orgId;

    private String sex;

    private Date birthday;

    private String telephone;

    private String telephoneSpare1;

    private String telephoneSpare2;

    private String phone;

    private String mail;

    private String mailSpare1;

    private String mailSpare2;

    private String fax;

    private String post;

    private String address;

    private String nationality;

    private String certificateType;

    private String certificate;

    private String pictureUrl;

    private String userId;

    private String resumeStatus;

    private String activeStatus;

    private String companyName;

    private String identityType;

    private Long shareTradable;

    private Long shareCount;

    private String companyShareholder;

    private String eid;

    private String companyCode;

    private String source;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String status;

    private String hiddenKey;

    private String weChatId;

    private String childrenUserName;

    private String childrenOrg;

    private String childrenRole;

    private String childrenLock;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber == null ? null : employeeNumber.trim();
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType == null ? null : personType.trim();
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName == null ? null : personName.trim();
    }

    public String getForeignName() {
        return foreignName;
    }

    public void setForeignName(String foreignName) {
        this.foreignName = foreignName == null ? null : foreignName.trim();
    }

    public String getTitleName() {
        return titleName;
    }

    public void setTitleName(String titleName) {
        this.titleName = titleName == null ? null : titleName.trim();
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId == null ? null : jobId.trim();
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex == null ? null : sex.trim();
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public String getTelephoneSpare1() {
        return telephoneSpare1;
    }

    public void setTelephoneSpare1(String telephoneSpare1) {
        this.telephoneSpare1 = telephoneSpare1 == null ? null : telephoneSpare1.trim();
    }

    public String getTelephoneSpare2() {
        return telephoneSpare2;
    }

    public void setTelephoneSpare2(String telephoneSpare2) {
        this.telephoneSpare2 = telephoneSpare2 == null ? null : telephoneSpare2.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail == null ? null : mail.trim();
    }

    public String getMailSpare1() {
        return mailSpare1;
    }

    public void setMailSpare1(String mailSpare1) {
        this.mailSpare1 = mailSpare1 == null ? null : mailSpare1.trim();
    }

    public String getMailSpare2() {
        return mailSpare2;
    }

    public void setMailSpare2(String mailSpare2) {
        this.mailSpare2 = mailSpare2 == null ? null : mailSpare2.trim();
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax == null ? null : fax.trim();
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post == null ? null : post.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality == null ? null : nationality.trim();
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType == null ? null : certificateType.trim();
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate == null ? null : certificate.trim();
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl == null ? null : pictureUrl.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getResumeStatus() {
        return resumeStatus;
    }

    public void setResumeStatus(String resumeStatus) {
        this.resumeStatus = resumeStatus == null ? null : resumeStatus.trim();
    }

    public String getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(String activeStatus) {
        this.activeStatus = activeStatus == null ? null : activeStatus.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getIdentityType() {
        return identityType;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType == null ? null : identityType.trim();
    }

    public Long getShareTradable() {
        return shareTradable;
    }

    public void setShareTradable(Long shareTradable) {
        this.shareTradable = shareTradable;
    }

    public Long getShareCount() {
        return shareCount;
    }

    public void setShareCount(Long shareCount) {
        this.shareCount = shareCount;
    }

    public String getCompanyShareholder() {
        return companyShareholder;
    }

    public void setCompanyShareholder(String companyShareholder) {
        this.companyShareholder = companyShareholder == null ? null : companyShareholder.trim();
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid == null ? null : eid.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getHiddenKey() {
        return hiddenKey;
    }

    public void setHiddenKey(String hiddenKey) {
        this.hiddenKey = hiddenKey == null ? null : hiddenKey.trim();
    }

    public String getWeChatId() {
        return weChatId;
    }

    public void setWeChatId(String weChatId) {
        this.weChatId = weChatId;
    }

    public String getJobs() {
        return jobs;
    }

    public void setJobs(String jobs) {
        this.jobs = jobs;
    }

    public String getChildrenUserName() {
        return childrenUserName;
    }

    public void setChildrenUserName(String childrenUserName) {
        this.childrenUserName = childrenUserName;
    }

    public String getChildrenOrg() {
        return childrenOrg;
    }

    public void setChildrenOrg(String childrenOrg) {
        this.childrenOrg = childrenOrg;
    }

    public String getChildrenRole() {
        return childrenRole;
    }

    public void setChildrenRole(String childrenRole) {
        this.childrenRole = childrenRole;
    }

    public String getChildrenLock() {
        return childrenLock;
    }

    public void setChildrenLock(String childrenLock) {
        this.childrenLock = childrenLock;
    }
}