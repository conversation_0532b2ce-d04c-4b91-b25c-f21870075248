package com.stock.service.platform.common.system.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.system.dao.SaUserMapper;
import com.stock.service.platform.common.system.dto.MenuDto;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.person.service.PersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SaUserService extends BaseService {
    @Autowired
    private SaUserMapper userMapper;
    @Autowired
    private SaCompanyService companyService;
    @Autowired
    PersonService personService;
    @Autowired
    OrgMapper orgMapper;

    public List<String> getUserOfRolesByUserId(String userId) {
        return userMapper.findUserOfRolesByUserId(userId);
    }

    /**
     * 查询当前登陆人下可查看的上市公司
     * 返回companyCodeList
     */
    public List<String> subsidiaryCompany() {
        List<String> codeList = new ArrayList<>();
        String userId = getUserInfo().getUserId();
        PersonDto user = personService.getPersonInfoById(userId);
        String orgId =user.getOrgId();
        Org org = orgMapper.selectByPrimaryKey(orgId);
        String orgType = org.getOrgType();

        if ("1".equals(orgType)) {// 总部人员
            codeList = companyService.queryAllCompany();
        } else if ("2".equals(orgType) ) {// 分公司人员
            codeList = companyService.queryCompanyByBranchCode(orgId);
        } else if ("3".equals(orgType)) {// 营业部人员
            codeList.addAll(companyService.queryCompanyByDepartment(orgId));
        } else {// 外部上市公司人员
            codeList.add(getUserInfo().getCompanyCode());
        }
        if(codeList.size() == 0){
            codeList.add("");
        }
        return codeList;
    }

    /**
     * @param
     * @description: 加密
     * @return:
     * @author: jiangshiyou
     * @time: 2020/6/15 14:28
     */
    public static byte[] encrypt(byte[] data, String sKey) {
        try {
            byte[] key = sKey.getBytes();

            IvParameterSpec iv = new IvParameterSpec(key);
            DESKeySpec desKey = new DESKeySpec(key);

            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey securekey = keyFactory.generateSecret(desKey);

            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

            cipher.init(Cipher.ENCRYPT_MODE, securekey);

            return cipher.doFinal(data);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /*获取角色菜单*/
    public Map<String,Object> getMenu(){
        Map<String,Object> resultMap = new HashMap<>();
        List<MenuDto> resultList = new ArrayList<>();//菜单结果集
        List<String> buttonResource = new ArrayList<>();

        List<String> rolesList = getUserOfRolesByUserId(getUserInfo().getUserId());
        if(rolesList.size() > 0){
            //获取资源id
            List<String> resourceId = userMapper.findResourceByRoleId(rolesList);
            if(resourceId.size() > 0){
                //获取菜单资源
                resultList = userMapper.getRootMenu(resourceId);
            }
            //通过roleId获取按钮id
            List<String> buttonId = userMapper.getButtonIdByRoleId(rolesList);
            if(buttonId.size() > 0 ){
                //获取按钮资源
                buttonResource = userMapper.getButtonResourceById(buttonId);
            }

        }

        resultMap.put("menuField",resultList);
        resultMap.put("roles",buttonResource);
        resultMap.put("companyCode",getUserInfo().getCompanyCode());
        resultMap.put("companyName",getUserInfo().getInfo().get("companyZhName"));
        resultMap.put("userType",getUserInfo().getInfo().get("userType"));
        resultMap.put("isMgr",getUserInfo().getInfo().get("isMgr"));
        resultMap.put("userType",getUserInfo().getInfo().get("userType"));
        return resultMap;
    }
}
