<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.service.platform.common.system.dao.SaUserMapper">

    <select id="queryUser" resultType="com.stock.service.platform.common.system.dto.SaUserDto"
            parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        SELECT
        sa_person.id id,
        sa_person.person_type personType,
        sa_person.person_name personName,
        sa_person.telephone telephone,
        sa_person.phone phone,
        sa_person.mail mail,
        sa_person.fax fax,
        sa_person.post post,
        sa_person.address address,
        sa_person.`status` status,
        sa_user.user_name userName,
        sa_user.`password` password,
        sa_user.real_name realName,
        sa_user.company_id companyId,
        sa_person.company_code companyCode
        FROM
        sa_person ,
        sa_user
        WHERE sa_person.id = sa_user.id
          and  sa_person.`status` = '1'
          and sa_user.`status` = '1'
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="branchCompanyName != null and branchCompanyName != ''">
            and sa_user.company_name like concat('%',#{branchCompanyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        <if test="id != null and id != ''">
            and sa_user.id = #{id}
        </if>
        <if test="companyCodeList != null and companyCodeList.size !=0">
            and sa_person.company_code in
            <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY sa_user.update_time DESC
    </select>

    <select id="queryInnerUserButAdmin" resultType="com.stock.service.platform.common.system.dto.SaUserDto"
            parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        SELECT
        sa_person.id id,
        sa_person.person_type personType,
        sa_person.person_name personName,
        sa_person.telephone telephone,
        sa_person.phone phone,
--         sa_person.job_name jobName,
        sa_person.mail mail,
        sa_person.fax fax,
        sa_person.post post,
        sa_person.address address,
        sa_person.`status` status,
        sa_user.user_name userName,
        sa_user.`password` password,
        sa_user.real_name realName,
        sa_user.company_id companyId,
        sa_person.company_code companyCode,
        sa_user.is_mgr isMgr,
        sa_user.user_type userType,
        sa_user.company_name companyName,
        sa_user.approve_status approveStatus
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and sa_user.user_type = '0' and sa_user.user_name != 'sadmin'
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="branchCompanyName != null and branchCompanyName != ''">
            and sa_user.company_name like concat('%',#{branchCompanyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        <if test="id != null and id != ''">
            and sa_user.id = #{id}
        </if>
        <if test="companyCodeList != null and companyCodeList.size !=0">
            and sa_person.company_code in
            <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--<if test="businessDepartmentIds != null and businessDepartmentIds.size !=0">
            and sa_user.business_department_id in
            <foreach collection="businessDepartmentIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>-->
        ORDER BY sa_user.update_time DESC
    </select>
    <sql id="common_col" >
        sa_person.id,
        sa_person.person_type,
        sa_person.person_name,
        sa_person.telephone,
        sa_person.phone,
--         sa_person.job_name,
        sa_person.mail,
        sa_person.fax,
        sa_person.post,
        sa_person.address,
        sa_person.`status`,
        sa_user.user_name,
        sa_user.`password`,
        sa_user.real_name,
        sa_user.company_id,
        ,
        sa_user.is_mgr,
        sa_user.user_type,
        sa_user.company_name,
        sa_user.update_time,
        sa_user.approve_status
    </sql>
    <select id="queryBranchAndDepUser" resultType="com.stock.service.platform.common.system.dto.SaUserDto"
            parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        SELECT
        temp.id id,
        temp.person_type personType,
        temp.person_name personName,
        temp.telephone telephone,
        temp.phone phone,
        temp.job_name jobName,
        temp.mail mail,
        temp.fax fax,
        temp.post post,
        temp.address address,
        temp.`status` status,
        temp.user_name userName,
        temp.`password` password,
        temp.real_name realName,
        temp.company_id companyId,
        temp.company_code companyCode,
        temp.is_mgr isMgr,
        temp.user_type userType,
        temp.company_name companyName,
        temp.update_time updateTime,
        temp.approve_status approveStatus
        FROM (
        SELECT
        <include refid="common_col" />
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and sa_user.user_type = '0' and  sa_user.is_mgr = '0'
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        union all
        SELECT
        <include refid="common_col"/>
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        <if test="companyCodeList != null and companyCodeList.size !=0">
            and sa_user.company_code in
            <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        union all
        SELECT
        <include refid="common_col" />
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        and sa_user.user_type = '0' and sa_user.is_mgr = '2'
        <if test="companyCode != null and companyCode != ''">
            and sa_user.company_code = #{companyCode}
        </if>
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        ) temp
        ORDER BY temp.update_time DESC
    </select>

    <select id="queryDepAndCompanyUser" resultType="com.stock.service.platform.common.system.dto.SaUserDto"
            parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        SELECT
        temp.id id,
        temp.person_type personType,
        temp.person_name personName,
        temp.telephone telephone,
        temp.phone phone,
        temp.job_name jobName,
        temp.mail mail,
        temp.fax fax,
        temp.post post,
        temp.address address,
        temp.`status` status,
        temp.user_name userName,
        temp.`password` password,
        temp.real_name realName,
        temp.company_id companyId,
        temp.company_code companyCode,
        temp.is_mgr isMgr,
        temp.user_type userType,
        temp.company_name companyName,
        temp.update_time updateTime,
        temp.approve_status approveStatus
        FROM (
        SELECT
        <include refid="common_col"/>
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        <if test="companyCodeList != null and companyCodeList.size !=0">
            and sa_user.company_code in
            <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        union all
        SELECT
        <include refid="common_col"/>
        FROM
        sa_person,sa_user
        WHERE sa_person.id = sa_user.id
        and  sa_person.`status` = '1'
        and sa_user.`status` = '1'
        <if test="companyName != null and companyName != ''">
            and sa_user.company_name like concat('%',#{companyName},'%')
        </if>
        <if test="organisationNo != null and organisationNo != ''">
            <choose>
                <when test="organisationNo == '总部'.toString()">
                    and sa_user.is_mgr = '1'
                </when>
                <otherwise>
                    and sa_user.company_name LIKE concat('%', #{organisationNo}, '%')
                    and sa_user.is_mgr = '2'
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            and sa_user.user_name like concat('%',#{userName},'%')
        </if>
        ) temp
        ORDER BY temp.update_time DESC
    </select>

    <insert id="insertSelective" parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into sa_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="realName != null">
                real_name,
            </if>
            <if test="telephone != null">
                telephone,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="mail != null">
                mail,
            </if>
            <if test="fax != null">
                fax,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isMgr != null">
                is_Mgr,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                #{mail,jdbcType=VARCHAR},
            </if>
            <if test="fax != null">
                #{fax,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="isMgr != null">
                #{isMgr,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="businessDepartmentId != null">
                #{businessDepartmentId,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="approveStatus != null">
                #{approveStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateUser" parameterType="com.stock.service.platform.common.system.dto.SaUserDto">
        UPDATE sa_user
        <set>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                user_type = #{userType,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="companyCode != null">
                company_code = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="ssoUserName != null">
                sso_user_name = #{ssoUserName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="isMgr != null">
                is_mgr = #{isMgr,jdbcType=CHAR},
            </if>
            <if test="errorNum != null">
                error_num = #{errorNum,jdbcType=CHAR},
            </if>
            <if test="lockType != null">
                lock_type = #{lockType,jdbcType=CHAR},
            </if>
            <if test="approveStatus != null">
                approve_status = #{approveStatus,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="findUserOfRolesByUserId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT ROLE_ID
        FROM SA_USER_ROLE_MAP
        WHERE 1=1
        AND USER_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="findUserByRoleId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT ROLE_ID
        FROM SA_USER_ROLE_MAP
        WHERE 1=1
        AND role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <!-- 删除用户所有角色 -->
    <delete id="deleteUserRoleMapByUserId" parameterType="java.lang.String">
    DELETE FROM SA_USER_ROLE_MAP
    WHERE USER_ID = #{userId,jdbcType=VARCHAR}
  </delete>

    <!-- 新建用户角色 -->
    <insert id="createUserRolesMap" parameterType="map">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        INSERT INTO SA_USER_ROLE_MAP(
        ID,
        USER_ID,
        ROLE_ID
        )
        VALUES(
        #{id,jdbcType=VARCHAR},
        #{userId,jdbcType=VARCHAR},
        #{roleId,jdbcType=VARCHAR}
        )
    </insert>
        <!--删除该用户下的，合规交易角色-->
    <delete id="deleteUserRoleMapByRoleId" parameterType="map">
        DELETE FROM SA_USER_ROLE_MAP
        WHERE USER_ID = #{userId,jdbcType=VARCHAR}
        and   role_id = #{roleId,jdbcType=VARCHAR}
      </delete>
    <!--获取用户角色菜单-->
    <resultMap id="menuMap" type="com.stock.service.platform.common.system.dto.MenuDto">
        <id column="id" property="id"/>
        <result column="resource_name" property="name"/>
        <result column="p_id" property="pMenuId"/>
        <result column="resource_des" property="menuName"/>
        <result column="resource_url" property="path"/>
        <collection property="menuField" ofType="com.stock.service.platform.common.system.dto.MenuDto">
            <id column="o_id" property="id"/>
            <result column="o_resource_name" property="name"/>
            <result column="o_p_id" property="pMenuId"/>
            <result column="o_resource_des" property="menuName"/>
            <result column="o_resource_url" property="path"/>
        </collection>
    </resultMap>
    <select id="getRootMenu" resultMap="menuMap" parameterType="java.util.List">
        select
        a.id,
        c.resource_name,
        a.menu_name as resource_des,
        ifnull(b.id,'') as o_id,
        b.menu_name as o_resource_des,
        c.id as o_p_id,
        c.resource_url as o_resource_url
        FROM sa_menu a
        left join sa_menu b on b.p_menu_id = a.id
        left join sa_resource c on b.resource_id = c.id
        where a.top_menu_type = '1' and a.id != b.id
        and c.id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        order by b.sort_no
    </select>
    <select id="findMenuByParentId" parameterType="java.lang.String" resultMap="menuMap">
        SELECT
        id,
        resource_name,
        p_id,
        resource_des,
        resource_url
        from sa_resource where p_id = #{value}
    </select>
    <!--获取用户角色菜单，结束！！！-->
    <!--通过用户id获取resourceid-->
    <select id="findResourceByRoleId" parameterType="java.util.List" resultType="java.lang.String">
        SELECT distinct resource_id
        FROM sa_role_resource_map
        WHERE 1=1
        AND role_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
             #{item}
        </foreach>

    </select>
    <!--通过用户id获取resourceid，结束-->
    <!--通过roleId获取按钮id-->
    <select id="getButtonIdByRoleId" parameterType="java.util.List" resultType="java.lang.String">
        SELECT distinct button_id
        FROM sa_menu_button_permission
        WHERE 1=1
        AND role_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>
    <!--通过roleId获取按钮id,结束！！！-->
    <!--通过按钮id获取按钮资源-->
    <select id="getButtonResourceById" parameterType="java.util.List" resultType="java.lang.String">
        SELECT  button_code
        FROM sa_menu_button
        WHERE 1=1
        AND id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>

    </select>

</mapper>