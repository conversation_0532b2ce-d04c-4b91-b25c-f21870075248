package com.stock.service.platform.common.util;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Charsets;
import com.google.common.base.Throwables;
import com.stock.core.dto.TreeDto;
import com.stock.core.util.CryptoUtil;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CommonUtil {
    private static final String AES_ALGORITHM = "AES";
    public static final String AES_CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS5Padding";
    private static final String ENCRYPT_KEY = "hNkYmsBUvrTd3C3o";
    private static final String IV = "mLZT7OIx1qOHZaPX";

    /**
     * 树类型转换
     *
     * @param list
     * @param parentRoot
     * @return
     */
    public static List<TreeDto> createTreeStructure(List<TreeDto> list, String parentRoot) {
        Map<String, List<TreeDto>> rootList = list.stream().filter(treeDto -> !treeDto.getParentId().equals(parentRoot)).collect(Collectors.groupingBy(treeDto -> treeDto.getParentId()));
        list.forEach(treeDto -> treeDto.setChildren(rootList.get(treeDto.getId())));
        return list.stream().filter(treeDto -> treeDto.getParentId().equals(parentRoot)).collect(Collectors.toList());
    }
    /**
     * 取得文件名
     *
     * @param str
     * @return
     */
    public static String getExportFileName(String str) {
        Random random = new Random();
        int i = (int) (random.nextDouble() * (99999 - 10000 + 1)) + 10000;
        return str.concat("_").concat(String.valueOf(i));
    }
    /**
     * 使用AES算法加密
     *
     * @param text 需要加密的内容
     * @param encryptKey 加密密钥
     */
    public static String encryptCBC(String text, String encryptKey) {
        try {
            byte[] key = Base64Utils.decodeFromString(encryptKey);
            byte[] content = text.getBytes(Charsets.UTF_8);
            SecretKeySpec keySpec = new SecretKeySpec(key, AES_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM_CBC);
            final byte[] iv = new byte[16];
            Arrays.fill(iv, (byte) 0x00);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec);
            // 加密
            byte[] encrypted = cipher.doFinal(content);
            // 使用BASE64对加密后的字符串进行编码
            return Base64Utils.encodeToString(encrypted);
        } catch (Exception e) {
            log.error("AES encrypt error, caused by : {}", Throwables.getStackTraceAsString(e));
        }
        return null;
    }
    /**
     * 功能描述: 通用listToTree.......<br>
     * @Param: [list, allList, childIdName, parentIdName]
     * @Return: void
     * @Author: DarkSql
     * @Date: 2021/12/15 15:34
     */
    public static  <T> List<T>  listToTree(String parentId ,List<T> allList,String childIdName,String parentIdName) {
        List<T> list = allList.stream().filter(data->{
            Field parentIdField = null;
            try {
                parentIdField = getClassFiled(data.getClass(),parentIdName);
                parentIdField.setAccessible(true);
                if(StringUtils.equals((String)parentIdField.get(data),parentId) ) {
                    return true;
                }

            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            return false;
        }).collect(Collectors.toList());//取第一层数据

        for (T t : list) {
            List<T> childList = allList.stream().filter(data ->
            {
                try {
                    Field parentIdFiled = getClassFiled(t.getClass(),childIdName);
                    Field childIdField = getClassFiled(data.getClass(),parentIdName);
                    childIdField.setAccessible(true);
                    parentIdFiled.setAccessible(true);
                    String pId = (String)parentIdFiled.get(t);
                    String childId = (String)childIdField.get(data);
                    return StringUtils.equals(pId,childId);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }
                return false;
            }).collect(Collectors.toList());
            if (childList.size() > 0) {
                try {
                    Field children = t.getClass().getDeclaredField("children");
                    children.setAccessible(true);
                    children.set(t,childList);
                    Field parentIdFiled = getClassFiled(t.getClass(),childIdName);
                    parentIdFiled.setAccessible(true);
                    String pId = (String)parentIdFiled.get(t);
                    listToTree(pId,allList,childIdName,parentIdName);
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                }

            }

        }
        return list;
    }

    private static Field getClassFiled(Class<?> aClass, String childIdName) throws NoSuchFieldException {
        if(StringUtils.equals(aClass.getName(),"Object.class")) {
            throw new NoSuchFieldException();
        }
        try {
            Field declaredField = aClass.getDeclaredField(childIdName);
            return declaredField;
        } catch (NoSuchFieldException e) {
            return getClassFiled(aClass.getSuperclass(),childIdName);
        }

    }

    public static <T> List<T> treeToList(List<T> allList) {
        Objects.requireNonNull(allList, "not Null");
        try {
            for (int i = 0; i < allList.size(); i++) {
                T t = allList.get(i);

                Field children = t.getClass().getDeclaredField("children");
                children.setAccessible(true);

                Object fieldValue = getFieldValue(t.getClass(), children, t);
                List<T> childrenList = (List<T>) fieldValue;
                children.set(t, null);
                if (childrenList != null) {
                    allList.addAll(childrenList);
                    treeToList(childrenList);
                }
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return allList;
    }

    private static <T> Object getFieldValue(Class<?> clazz, Field field, T t) {
        Object o = null;
        String fieldName = field.getName();
        String getMethodName = "get" +
                fieldName.substring(0, 1).toUpperCase() +
                fieldName.substring(1);
        try {
            Method method = clazz.getMethod(getMethodName);
            o = method.invoke(t);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return o;
    }

    public static <T>  T decrypt(String encryptData,Class<T> clazz){
        //encryptData = "\""+encryptData+"\"";
        encryptData = encryptData.substring(1, encryptData.length() - 1);
        String iv = Base64Utils.encodeToString(IV.getBytes(Charsets.UTF_8));
        String encryptKey = Base64Utils.encodeToString(ENCRYPT_KEY.getBytes(Charsets.UTF_8));
        String key = ENCRYPT_KEY + IV;
        String rtn = encryptData.replace(key, "");
        rtn = CryptoUtil.decrypt(rtn, encryptKey, CryptoUtil.AES_CIPHER_ALGORITHM_CBC, iv);
        log.info("查看加密结果=======rtn:" + rtn);
        return JSON.parseObject(rtn, clazz);

    }

    /**
     * @param digit 位数
     * @return 随机生成digit位数的数字
     */
    public static long getNum(int digit) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < digit; i++) {
            if (i == 0 && digit > 1)
                str.append(new Random().nextInt(9) + 1);
            else
                str.append(new Random().nextInt(10));
        }
        return Long.valueOf(str.toString());
    }

    //查询sa_system_config.sys_code
}
