<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ChatContentMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.ChatContent">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="record_id" jdbcType="VARCHAR" property="recordId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="tokens" jdbcType="VARCHAR" property="tokens" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="ai_type" jdbcType="VARCHAR" property="aiType" />
    <result column="question_time" jdbcType="TIMESTAMP" property="questionTime" />
    <result column="answer_time" jdbcType="TIMESTAMP" property="answerTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="data_order" jdbcType="VARCHAR" property="dataOrder" />
    <result column="feedback_type" jdbcType="VARCHAR" property="feedbackType" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.stock.service.platform.common.entity.ChatContentWithBLOBs">
    <result column="question_content" jdbcType="LONGVARCHAR" property="questionContent" />
    <result column="answer_content" jdbcType="LONGVARCHAR" property="answerContent" />
    <result column="cite_content" jdbcType="LONGVARCHAR" property="citeContent" />
    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, record_id, user_id, tokens, user_type, user_name, ai_type, question_time, answer_time, 
    create_user, create_time, update_user, update_time, data_order, feedback_type, ip, 
    status
  </sql>
  <sql id="Blob_Column_List">
    question_content, answer_content, cite_content, prompt
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.stock.service.platform.common.entity.ChatContentExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.ChatContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_content
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from chat_content
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.ChatContentExample">
    delete from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.ChatContentWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into chat_content (id, record_id, user_id, 
      tokens, user_type, user_name, 
      ai_type, question_time, answer_time, 
      create_user, create_time, update_user, 
      update_time, data_order, feedback_type, 
      ip, status, question_content, 
      answer_content, cite_content, prompt
      )
    values (#{id,jdbcType=VARCHAR}, #{recordId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{tokens,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{aiType,jdbcType=VARCHAR}, #{questionTime,jdbcType=TIMESTAMP}, #{answerTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{dataOrder,jdbcType=VARCHAR}, #{feedbackType,jdbcType=VARCHAR}, 
      #{ip,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{questionContent,jdbcType=LONGVARCHAR}, 
      #{answerContent,jdbcType=LONGVARCHAR}, #{citeContent,jdbcType=LONGVARCHAR}, #{prompt,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.ChatContentWithBLOBs">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into chat_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="recordId != null">
        record_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="tokens != null">
        tokens,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="aiType != null">
        ai_type,
      </if>
      <if test="questionTime != null">
        question_time,
      </if>
      <if test="answerTime != null">
        answer_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dataOrder != null">
        data_order,
      </if>
      <if test="feedbackType != null">
        feedback_type,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="questionContent != null">
        question_content,
      </if>
      <if test="answerContent != null">
        answer_content,
      </if>
      <if test="citeContent != null">
        cite_content,
      </if>
      <if test="prompt != null">
        prompt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="recordId != null">
        #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tokens != null">
        #{tokens,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="aiType != null">
        #{aiType,jdbcType=VARCHAR},
      </if>
      <if test="questionTime != null">
        #{questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answerTime != null">
        #{answerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOrder != null">
        #{dataOrder,jdbcType=VARCHAR},
      </if>
      <if test="feedbackType != null">
        #{feedbackType,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null">
        #{questionContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="answerContent != null">
        #{answerContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="citeContent != null">
        #{citeContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="prompt != null">
        #{prompt,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.ChatContentExample" resultType="java.lang.Long">
    select count(*) from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.recordId != null">
        record_id = #{record.recordId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.tokens != null">
        tokens = #{record.tokens,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.aiType != null">
        ai_type = #{record.aiType,jdbcType=VARCHAR},
      </if>
      <if test="record.questionTime != null">
        question_time = #{record.questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.answerTime != null">
        answer_time = #{record.answerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataOrder != null">
        data_order = #{record.dataOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.feedbackType != null">
        feedback_type = #{record.feedbackType,jdbcType=VARCHAR},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.questionContent != null">
        question_content = #{record.questionContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.answerContent != null">
        answer_content = #{record.answerContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.citeContent != null">
        cite_content = #{record.citeContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.prompt != null">
        prompt = #{record.prompt,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update chat_content
    set id = #{record.id,jdbcType=VARCHAR},
      record_id = #{record.recordId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      tokens = #{record.tokens,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      ai_type = #{record.aiType,jdbcType=VARCHAR},
      question_time = #{record.questionTime,jdbcType=TIMESTAMP},
      answer_time = #{record.answerTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      data_order = #{record.dataOrder,jdbcType=VARCHAR},
      feedback_type = #{record.feedbackType,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      question_content = #{record.questionContent,jdbcType=LONGVARCHAR},
      answer_content = #{record.answerContent,jdbcType=LONGVARCHAR},
      cite_content = #{record.citeContent,jdbcType=LONGVARCHAR},
      prompt = #{record.prompt,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_content
    set id = #{record.id,jdbcType=VARCHAR},
      record_id = #{record.recordId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      tokens = #{record.tokens,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      ai_type = #{record.aiType,jdbcType=VARCHAR},
      question_time = #{record.questionTime,jdbcType=TIMESTAMP},
      answer_time = #{record.answerTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      data_order = #{record.dataOrder,jdbcType=VARCHAR},
      feedback_type = #{record.feedbackType,jdbcType=VARCHAR},
      ip = #{record.ip,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.ChatContentWithBLOBs">
    update chat_content
    <set>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tokens != null">
        tokens = #{tokens,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="aiType != null">
        ai_type = #{aiType,jdbcType=VARCHAR},
      </if>
      <if test="questionTime != null">
        question_time = #{questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answerTime != null">
        answer_time = #{answerTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataOrder != null">
        data_order = #{dataOrder,jdbcType=VARCHAR},
      </if>
      <if test="feedbackType != null">
        feedback_type = #{feedbackType,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="questionContent != null">
        question_content = #{questionContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="answerContent != null">
        answer_content = #{answerContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="citeContent != null">
        cite_content = #{citeContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="prompt != null">
        prompt = #{prompt,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.stock.service.platform.common.entity.ChatContentWithBLOBs">
    update chat_content
    set record_id = #{recordId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      tokens = #{tokens,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      ai_type = #{aiType,jdbcType=VARCHAR},
      question_time = #{questionTime,jdbcType=TIMESTAMP},
      answer_time = #{answerTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      data_order = #{dataOrder,jdbcType=VARCHAR},
      feedback_type = #{feedbackType,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      question_content = #{questionContent,jdbcType=LONGVARCHAR},
      answer_content = #{answerContent,jdbcType=LONGVARCHAR},
      cite_content = #{citeContent,jdbcType=LONGVARCHAR},
      prompt = #{prompt,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.ChatContent">
    update chat_content
    set record_id = #{recordId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      tokens = #{tokens,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      ai_type = #{aiType,jdbcType=VARCHAR},
      question_time = #{questionTime,jdbcType=TIMESTAMP},
      answer_time = #{answerTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      data_order = #{dataOrder,jdbcType=VARCHAR},
      feedback_type = #{feedbackType,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.stock.service.platform.common.entity.ChatContentExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.ChatContentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>