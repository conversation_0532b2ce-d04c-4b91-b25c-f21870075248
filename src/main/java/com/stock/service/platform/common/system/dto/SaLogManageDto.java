package com.stock.service.platform.common.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SaLogManageDto implements Serializable {

    private static final long serialVersionUID = -2411065663677535056L;

    //公司编码
    private String companyCode;

    //公司名称
    private String companyName;

    //账号
    private String userName;

    //ip
    private String logIP;

    //url
    private String logURL;

    //方法类型
    private String logType;

    //方法名
    private String methodName;

    //参数
    private String param;

    //修改时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    //开始时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startTime;

    //结束时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endTime;

    private String systemName;

}
