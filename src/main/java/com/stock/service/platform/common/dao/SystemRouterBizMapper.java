package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.dto.RouterDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SystemRouterBizMapper {


    List<RouterDto> selectByCondition(String userId);

    /**
     * 查询用户角色对应的资源ID
     * @param userId
     * @return
     */
    List<String> selectResourcesByUserId(String userId);

}