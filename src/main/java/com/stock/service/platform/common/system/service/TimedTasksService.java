package com.stock.service.platform.common.system.service;

import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TimedTasksService {

    @Autowired
    private TimedTasksMapper timedTasksMapper;

    public Map<String, Object> getTasksList(Map<String, Object> param) {
        Integer startRow = (Integer) param.get("startRow");
        Integer pageSize = (Integer) param.get("pageSize");
        param.put("startRow", (startRow-1)*pageSize);
        Map<String, Object> result = new HashMap<>();
        result.put("total", 0);
        result.put("tableData", null);

        String sysCode = MapUtils.getString(param, "sysCode");

        List<Map<String, Object>> tasksList = new ArrayList<>();
        int tasksCount = 0;
        if ("PLATFORM".equals(sysCode)) {
            tasksList = timedTasksMapper.getTasksPList(param);
            tasksCount = timedTasksMapper.getTasksPCount(param);
        } else {
            tasksList = timedTasksMapper.getTasksList(param);
            tasksCount = timedTasksMapper.getTasksCount(param);
        }



        result.put("total", tasksCount);
        result.put("tableData", tasksList);
        return result;
    }

    public Map<String, Object> getTasksGroupList(Map<String, Object> param) {
        Integer startRow = (Integer) param.get("startRow");
        Integer pageSize = (Integer) param.get("pageSize");
        param.put("startRow", (startRow-1)*pageSize);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> tasksList = timedTasksMapper.getTasksPGroupList(param);
        int tasksCount = timedTasksMapper.getTasksPGroupCount();
        result.put("total", tasksCount);
        result.put("tableData", tasksList);
        return result;
    }

}
