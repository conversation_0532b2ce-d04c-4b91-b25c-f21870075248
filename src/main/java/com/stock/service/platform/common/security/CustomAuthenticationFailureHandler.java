package com.stock.service.platform.common.security;

import com.stock.core.Constant;
import com.stock.core.dto.JsonResponse;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.constant.LogicConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Description: 登录失败处理器
 * @author: xiazhengwei
 * @date:
 */
@Component
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationFailureHandler.class);

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        JsonResponse<String> result = new JsonResponse<>();
        String returnMessage = exception.getMessage();
        if (request.getParameter(LogicConstant.LOGIN_TYPE_NAME) != null && LogicConstant.USERNAME_PASSWORD_LOGIN_TYPE.equals(request.getParameter(LogicConstant.LOGIN_TYPE_NAME))) {
            String unLockHours;
            if (exception instanceof BadCredentialsException) {
                Integer leftTimes = (Integer)request.getAttribute(Constant.LOGIN_LEFT_TIMES);
                Integer maxLoginTimes = (Integer)request.getAttribute(Constant.MAX_LOGIN_TIMES);
                if (leftTimes != null && maxLoginTimes != null) {
                    returnMessage = exception.getMessage() + "，输入错误" + maxLoginTimes + "次将冻结账号，还有" + leftTimes + "次机会.";
                    if (leftTimes == 0) {
                        unLockHours = (String)request.getAttribute("unLockHours");
                        if (unLockHours != null) {
                            returnMessage = "用户已锁定，距离解锁还有" + unLockHours + "，请稍后再试.";
                        }
                    }
                }
            }else if (exception instanceof LockedException) {
                unLockHours = (String)request.getAttribute("unLockHours");
                if (unLockHours != null) {
                    returnMessage = "用户已锁定，距离解锁还有" + unLockHours + "，请稍后再试.";

                }
            }
        }else if(request.getParameter(LogicConstant.LOGIN_TYPE_NAME) != null && LogicConstant.TELEPHONE_VERIFY_CODE_LOGIN_TYPE.equals(request.getParameter(LogicConstant.LOGIN_TYPE_NAME))) {
            if (exception instanceof LockedException) {
                String unLockHours = (String)request.getAttribute("unLockHours");
                if (unLockHours != null) {
                    returnMessage = "用户已锁定，距离解锁还有" + unLockHours + "，请稍后再试.";
                }
            }
        }

        result.setErrorCode("401");
        result.setErrorMsg(returnMessage);
        PrintWriter out = response.getWriter();
        out.print(JsonUtil.toJson(result));
        out.flush();
        out.close();

    }

}
