package com.stock.service.platform.common.util;

import org.apache.commons.lang3.RandomUtils;

public class PrimaryKeyUtils {
    /**
     * 生成13位可做主键的数字
     * @return
     */
    public static Long getPrimaryKey() {
       return System.currentTimeMillis() + RandomUtils.nextLong(1000000, 10000000);
    }

    /**
     * 生成20位可做主键的字符串
     * @return
     */
    public static String getPrimaryKeyForString() {
        return "" + System.currentTimeMillis() + RandomUtils.nextInt(1000000, 10000000);
    }
}
