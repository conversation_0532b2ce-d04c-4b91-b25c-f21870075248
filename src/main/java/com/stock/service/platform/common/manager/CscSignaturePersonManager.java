package com.stock.service.platform.common.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.stock.service.platform.common.dto.CscPushSendMessageParam;
import com.stock.service.platform.common.exception.BusinessException;
import com.stock.service.platform.common.util.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


import java.util.*;

@Component
public class CscSignaturePersonManager {

    @Value("${cscInterface.pushProviderId}")
    private String pushProviderId;
    @Value("${cscInterface.pushAuditorId}")
    private String pushAuditorId;
    @Value("${cscInterface.pushChannelMail}")
    private String pushChannelMail;
    @Value("${cscInterface.pushTopicId}")
    private String pushTopicId;
    @Value("${cscInterface.pushSendMessage}")
    private String pushSendMessageUrl;

    /***
     * 推送信息
     * @param destList 推送目标id，邮件传邮箱
     * @param messageContent 邮件标题
     * @param longtext 邮件推送内容
     * @param destType 推送目标类型 101:邮箱
     * @return
     */
    public void pushSendMessage(List<String> destList, String messageContent,String longtext, int destType) {
        try {
            String resultStr;
            String[] arry = listToArray(destList);
            CscPushSendMessageParam param = new CscPushSendMessageParam();
            param.setProviderId(pushProviderId);
            param.setAuditorId(pushAuditorId);
            param.setContent(messageContent);
            if (StringUtils.isNotEmpty(longtext)){
                param.setLongtext(longtext);
            }
            param.setDestType(destType);
            param.setDestIds(arry);
            if (destType == 101) {//邮件
                param.setChannels(pushChannelMail);
                param.setTopicId(pushTopicId);
            }
            resultStr = HttpUtil
                    .httpPostWithJSON(pushSendMessageUrl, JSON.toJSONString(param));
            JSONObject resultJson = JSON.parseObject(resultStr, JSONObject.class);
            if (null != resultJson && !"".equals(resultJson)) {
                if (resultJson.containsKey("errmsg")) {
                    String errmsg = resultJson.getString("errmsg");
                    if (destType == 101) {//邮件
                        throw new BusinessException("邮件推送信息失败：" + errmsg);
                    }
                }
            } else {
                if (destType == 101) {//邮件
                    throw new BusinessException("邮件推送信息失败");
                }
            }
        } catch (BusinessException e) {
            throw new BusinessException(e);
        } catch (Exception e) {
            if (destType == 101) {//邮件
                throw new BusinessException("邮件推送信息失败：" + e.getMessage());
            }
        }
    }

    public String[] listToArray(List<String> list) {
        String[] array = list.toArray(new String[list.size()]);
        return array;
    }
}
