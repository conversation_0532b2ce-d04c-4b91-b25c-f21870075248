package com.stock.service.platform.common.util;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Required {
    /**
     * 是否必输
     * @return
     */
    boolean required() default false;

    /**
     * 校验信息
     * @return
     */
    String value() default "";

    /**
     * 多个属性联合不能全为空
     * @return
     */
    String jointNotNull() default "";
}