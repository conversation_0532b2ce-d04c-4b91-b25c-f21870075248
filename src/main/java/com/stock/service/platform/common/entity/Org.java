package com.stock.service.platform.common.entity;

import com.stock.service.platform.common.util.Message;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class Org implements Serializable {
    private String id;

    private String companyId;

    @Message(title = "机构代码", primary = "true")
    private String orgCode;

    @Message(title = "机构名称", primary = "true")
    private String orgName;

    @Message(title = "父级", primary = "true")
    private String pOrgId;

    @Message(title = "排序", primary = "true")
    private Integer orderId;

    private String level;

    private String orgType;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String status;

    @Message(title = "是否允许所属营业部相互查看", primary = "true")
    private String thresholdFlag;

    private String source;

    private String childrenCompanyFlag;

    private String branchName;

    private List<String> ids;

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBusinessDepartment() {
        return businessDepartment;
    }

    public void setBusinessDepartment(String businessDepartment) {
        this.businessDepartment = businessDepartment;
    }

    public String getDepCode() {
        return depCode;
    }

    public void setDepCode(String depCode) {
        this.depCode = depCode;
    }

    public String getTradeOrgId() {
        return tradeOrgId;
    }

    public void setTradeOrgId(String tradeOrgId) {
        this.tradeOrgId = tradeOrgId;
    }

    private String branchCode;

    private String businessDepartment;

    private String depCode;

    private String tradeOrgId;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getpOrgId() {
        return pOrgId;
    }

    public void setpOrgId(String pOrgId) {
        this.pOrgId = pOrgId == null ? null : pOrgId.trim();
    }

    public String getPOrgId() {
        return pOrgId;
    }

    public void setPOrgId(String pOrgId) {
        this.pOrgId = pOrgId == null ? null : pOrgId.trim();
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType == null ? null : orgType.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getThresholdFlag() {
        return thresholdFlag;
    }

    public void setThresholdFlag(String thresholdFlag) {
        this.thresholdFlag = thresholdFlag == null ? null : thresholdFlag.trim();
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source == null ? null : source.trim();
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public String getChildrenCompanyFlag() {
        return childrenCompanyFlag;
    }

    public void setChildrenCompanyFlag(String childrenCompanyFlag) {
        this.childrenCompanyFlag = childrenCompanyFlag;
    }
}