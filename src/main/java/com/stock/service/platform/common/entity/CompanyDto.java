package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * sa_company
 * <AUTHOR>
public class CompanyDto implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 1 删除 0 未删除 2删除中
     */
    private String deleteflag;

    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 公司中文名称
     */
    private String zhName;

    /**
     * 公司简称
     */
    private String zhSortName;

    /**
     * 法定代表人
     */
    private String corporate;

    /**
     * 所属营业部
     */
    private String organisationNo;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 上市日期
     */
    private Date listingDate;

    /**
     * 登记日期
     */
    private Date registerDate;

    /**
     * 公司邮箱
     */
    private String companyMail;

    /**
     * 注册资本
     */
    private BigDecimal registerCapital;

    /**
     * 发行价格
     */
    private BigDecimal issuePrice;

    /**
     * A股股本
     */
    private BigDecimal capitalStockA;

    /**
     * B股股本
     */
    private BigDecimal capitalStockB;

    /**
     * H股股本
     */
    private BigDecimal capitalStockH;

    /**
     * 总股本
     */
    private BigDecimal capitalStockTotal;

    /**
     * 电话
     */
    private String phone;

    /**
     * 公司性质
     */
    private String orgform;

    /**
     * 证券名称
     */
    private String companyStockName;

    /**
     * 法人证件号码
     */
    private String corporateIdNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 板块  0 深市  1 沪市
     */
    private String marketStockType;

    /**
     * 建投交易市场码值
     */
    private String market;

    /**
     * 安全键
     */
    private String hiddenKey;

    /**
     * 接入系统来源（对应接入系统的来源）
     */
    private String source;

    /**
     * 公司各系统平台显示状态
     */
    private String systemState;

    /**
     * 社会统一信用代码
     */
    private String socialUnifiedCreditCode;

    /**
     * 上级公司代码(暂时不用)
     */
    private String superiorCompanyCode;

    /**
     * 上级公司名称(暂时不用)
     */
    private String superiorCompanyName;

    /**
     * 法人证件类型，默认存0（身份证）
     */
    private Integer legalPersonIdType;

    /**
     * 主公司关联Id(上级的id)
     */
    private String parentId;

    /**
     * 主公司code(最上级的code)
     */
    private String parentCompanyCode;

    /**
     * 状态(0锁定，1正常)默认存1
     */
    private Integer companyStatus;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDeleteflag() {
        return deleteflag;
    }

    public void setDeleteflag(String deleteflag) {
        this.deleteflag = deleteflag;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }

    public String getZhSortName() {
        return zhSortName;
    }

    public void setZhSortName(String zhSortName) {
        this.zhSortName = zhSortName;
    }

    public String getCorporate() {
        return corporate;
    }

    public void setCorporate(String corporate) {
        this.corporate = corporate;
    }

    public String getOrganisationNo() {
        return organisationNo;
    }

    public void setOrganisationNo(String organisationNo) {
        this.organisationNo = organisationNo;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public Date getListingDate() {
        return listingDate;
    }

    public void setListingDate(Date listingDate) {
        this.listingDate = listingDate;
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public String getCompanyMail() {
        return companyMail;
    }

    public void setCompanyMail(String companyMail) {
        this.companyMail = companyMail;
    }

    public BigDecimal getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(BigDecimal registerCapital) {
        this.registerCapital = registerCapital;
    }

    public BigDecimal getIssuePrice() {
        return issuePrice;
    }

    public void setIssuePrice(BigDecimal issuePrice) {
        this.issuePrice = issuePrice;
    }

    public BigDecimal getCapitalStockA() {
        return capitalStockA;
    }

    public void setCapitalStockA(BigDecimal capitalStockA) {
        this.capitalStockA = capitalStockA;
    }

    public BigDecimal getCapitalStockB() {
        return capitalStockB;
    }

    public void setCapitalStockB(BigDecimal capitalStockB) {
        this.capitalStockB = capitalStockB;
    }

    public BigDecimal getCapitalStockH() {
        return capitalStockH;
    }

    public void setCapitalStockH(BigDecimal capitalStockH) {
        this.capitalStockH = capitalStockH;
    }

    public BigDecimal getCapitalStockTotal() {
        return capitalStockTotal;
    }

    public void setCapitalStockTotal(BigDecimal capitalStockTotal) {
        this.capitalStockTotal = capitalStockTotal;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOrgform() {
        return orgform;
    }

    public void setOrgform(String orgform) {
        this.orgform = orgform;
    }

    public String getCompanyStockName() {
        return companyStockName;
    }

    public void setCompanyStockName(String companyStockName) {
        this.companyStockName = companyStockName;
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMarketStockType() {
        return marketStockType;
    }

    public void setMarketStockType(String marketStockType) {
        this.marketStockType = marketStockType;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getHiddenKey() {
        return hiddenKey;
    }

    public void setHiddenKey(String hiddenKey) {
        this.hiddenKey = hiddenKey;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSystemState() {
        return systemState;
    }

    public void setSystemState(String systemState) {
        this.systemState = systemState;
    }

    public String getSocialUnifiedCreditCode() {
        return socialUnifiedCreditCode;
    }

    public void setSocialUnifiedCreditCode(String socialUnifiedCreditCode) {
        this.socialUnifiedCreditCode = socialUnifiedCreditCode;
    }

    public String getSuperiorCompanyCode() {
        return superiorCompanyCode;
    }

    public void setSuperiorCompanyCode(String superiorCompanyCode) {
        this.superiorCompanyCode = superiorCompanyCode;
    }

    public String getSuperiorCompanyName() {
        return superiorCompanyName;
    }

    public void setSuperiorCompanyName(String superiorCompanyName) {
        this.superiorCompanyName = superiorCompanyName;
    }

    public Integer getLegalPersonIdType() {
        return legalPersonIdType;
    }

    public void setLegalPersonIdType(Integer legalPersonIdType) {
        this.legalPersonIdType = legalPersonIdType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentCompanyCode() {
        return parentCompanyCode;
    }

    public void setParentCompanyCode(String parentCompanyCode) {
        this.parentCompanyCode = parentCompanyCode;
    }

    public Integer getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(Integer companyStatus) {
        this.companyStatus = companyStatus;
    }
}