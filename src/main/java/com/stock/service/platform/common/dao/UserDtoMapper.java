package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.UserDto;
import com.stock.service.platform.common.entity.UserDtoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserDtoMapper {
    long countByExample(UserDtoExample example);

    int deleteByExample(UserDtoExample example);

    int deleteByPrimaryKey(String id);

    int insert(UserDto record);

    int insertSelective(UserDto record);

    List<UserDto> selectByExample(UserDtoExample example);

    UserDto selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") UserDto record, @Param("example") UserDtoExample example);

    int updateByExample(@Param("record") UserDto record, @Param("example") UserDtoExample example);

    int updateByPrimaryKeySelective(UserDto record);

    int updateByPrimaryKey(UserDto record);
}