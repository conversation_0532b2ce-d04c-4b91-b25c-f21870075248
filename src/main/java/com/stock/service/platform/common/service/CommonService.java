package com.stock.service.platform.common.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.ApplicationException;
import com.stock.core.service.BaseService;
import com.stock.core.util.CryptoUtil;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.*;
import com.stock.service.platform.common.dao.CommonBizMapper;
import com.stock.service.platform.common.dao.CompanyMapper;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.dao.UserDtoMapper;
import com.stock.service.platform.common.dto.*;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.enums.NumberEnum;
import com.stock.service.platform.common.exception.BusinessException;
import com.stock.service.platform.common.manager.CscSignaturePersonManager;
import com.stock.service.platform.common.manager.MessageManager;
import com.stock.service.platform.common.util.SaSystemUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 通用的service
 */
@Service
public class CommonService extends BaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonService.class);

    @Resource
    private RedisDao redisDao;
    @Resource
    private CommonBizMapper commonBizMapper;

    @Value("${service.gui.baseUrl}")
    private String baseUrl;

    @Value("${service.gui.clientId}")
    private String clientId;

    @Value("${service.gui.clientSecret}")
    private String clientSecret;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MessageService messageService;

    @Resource
    private Environment env;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Resource
    private CompanyMapper companyMapper;

    @Resource
    private SystemConfigService systemConfigService;

    @Resource
    private Environment environment;

    @Resource
    private SaSystemUtil saSystemUtil;

    @Resource
    private TopicProducer topicProducer;

    @Resource
    private CscSignaturePersonManager cscSignaturePersonManager;

    @Resource
    private MessageManager messageManager;

    @Autowired
    private UserDtoMapper userDtoMapper;

    @Resource
    private QuestionnaireService questionnaireService;

    @Resource
    private PasswordEncoder passwordEncoder;

    public String getCurrentEnvironment() {
        return String.join(",", environment.getActiveProfiles());
    }

    /**
     *
     * 取得gui的访问令牌
     *
     * @return
     *
     */
    public String getGuiAccessToken() {
        Map<String, String> accessToken;
        String result = null;
        String url = baseUrl + "/oauth/token?grant_type=client_credentials&response_type=token&client_id="+clientId+"&client_secret="+clientSecret;
        ParameterizedTypeReference<Map<String, String>> responseType = new ParameterizedTypeReference<Map<String, String>>() {
        };
        try {
            ResponseEntity<Map<String, String>> responseEntity = restTemplate.exchange(url, HttpMethod.GET, null, responseType);
            accessToken = responseEntity.getBody();
            result = accessToken.get("access_token");
        } catch(Exception e) {
            LOGGER.info("get gui access token error. caused by {}", Throwables.getStackTraceAsString(e));
        }
        return result;
    }

    /**
     *
     * 生成租户信息的jwt
     *
     * @return
     *
     */
    public String getTenantInfoChangeCompanyCode() {
        Map<String, String> tenantInfo = Maps.newHashMap();
        tenantInfo.put("companyId", getUserInfo().getCompanyId());
        tenantInfo.put("companyName", getUserInfo().getCompanyShortName());
        tenantInfo.put("companyCode", getUserInfo().getCompanyCode());
        tenantInfo.put("userId", getUserInfo().getUserId());
        tenantInfo.put("username", getUserInfo().getUsername());
        tenantInfo.put("identityType", getUserInfo().getIdentityType());
        tenantInfo.put("personName", getUserInfo().getPersonName());
        // 租户来源：国信 （只显示方案设计）
        tenantInfo.put("fromtype", "");
        String encrypted = CryptoUtil.encryptCBC(JsonUtil.toJson(tenantInfo), Base64Utils.encodeToString(clientSecret.getBytes()));
        return Base64Utils.encodeToUrlSafeString(Base64Utils.decodeFromString(encrypted));
    }


    public JsonResponse<Boolean> getCurrentAudith(Map<String,String> map){
        JsonResponse<Boolean> response = new JsonResponse<Boolean>();
        response.setResult(true);
        map.put("id",getUserInfo().getUserId());
        int num = commonBizMapper.getCurrentAudith(map);
        if(num==0){
            response.setResult(false);
        }
        return response;
    }


    public JsonResponse<Boolean> getDynamicCode(HttpServletRequest request, String phoneNo,String captchaCode) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(true);
        String suffix = this.getCookieValue(request, LogicConstant.MSG_LOGIN_OP_CODE);
        String messageRandom =  (String) redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_MSG_RANDOM_KEY + suffix);
        //获取图形验证码
        String codeInRedis = (String) redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_PREFIX+messageRandom+suffix);
        String result="";

        if (StringUtils.isEmpty(captchaCode)){
            response.setResult(false);
            response.setErrorMsg("请输入验证码！");
            return response;
        }
        if (StringUtils.isBlank(codeInRedis) || !captchaCode.equals(codeInRedis)) {
            response.setResult(false);
            response.setErrorMsg("验证码错误！");
            return response;
        }
        List<String> userNames = getPhoneUser(phoneNo);
        if(userNames==null || userNames.size()<1){
            //每个错误手机号的图形验证码只能使用一次
            if(StringUtils.isNotBlank(codeInRedis)){
                redisDao.delObject(RedisKeyConstant.LOGIN_CAPTCHA_PREFIX+messageRandom+suffix);
            }
            throw new ApplicationException("error.100026");
        }
        Object redis = redisDao.getObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + phoneNo);
        String redisCode = "";
        if(null != redis){
            redisCode = redis.toString();
        }
        //验证码信息是否存在
        if (StringUtils.isEmpty(redisCode)){
            // 不存在生成验证码，保存至redis，失效1分钟，登录时校验
            Profiles profiles = Profiles.of("dev","prepub-sz","test", "uat");
            if(env.acceptsProfiles(profiles)) {
                // 开发和公司测试环境不生成验证码
                result = CompanyConstant.SYSTEM_COMPANY_CODE;
            } else {
                Random random = new Random();
                for(int i = 0; i < 6; i++) {
                    result += random.nextInt(10);
                }
            }
            //result = "123456";
            redisDao.setObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + phoneNo, result , 120 * 1000);
        }else {
            //存在有效验证码，给一个错误提示。
            response.setResult(false);
            response.setErrorMsg("已存在有效验证码，请勿重复发送！");
            return response;
        }
        //调用短信登录接口 参数手机号码、验证码
        try{
            String content = this.getMsgContent(result, userNames.get(0));
            LOGGER.info("发送短信接口返回结果为:" + JSON.toJSONString(messageService.sendMessage(phoneNo, content)));
        }catch (Exception e){
            response.setResult(false);
            response.setErrorMsg("验证码发送失败");
            e.printStackTrace();
            LOGGER.info("client.smsLogin error. caused by {}" + e.getMessage());
        }finally {
            LOGGER.info("短信登录生成验证码 ： " + result);
            try {
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return response;
    }

    /**
     * 将电话号码的中间四位替换为星号
     *
     * @param phoneNumber 原始电话号码
     * @return 替换后的电话号码
     */
    private String maskMiddleFourDigits(String phoneNumber) {
        // 确保电话号码长度至少为8位
        if (phoneNumber == null || phoneNumber.length() < 8) {
            return phoneNumber;
        }

        // 计算开始替换的索引位置
        int start = (phoneNumber.length() - 4) / 2;

        // 使用StringBuilder来构建新的字符串
        StringBuilder maskedNumber = new StringBuilder(phoneNumber);

        // 替换中间四位为星号
        for (int i = 0; i < 4; i++) {
            maskedNumber.setCharAt(start + i, '*');
        }

        return maskedNumber.toString();
    }

    /**
     * 获取验证码 根据用户名
     *
     * @param username 用户名
     * @return 用户电话（加密）
     */
    public JsonResponse<String> getDynamicCodeByUsername(String username, String password) {
        JsonResponse<String> response = new JsonResponse<>();
        response.setSuccess(true);
        String result = null;

        // 根据 username 获取用户信息
        UserDtoExample userDtoExample = new UserDtoExample();
        userDtoExample.createCriteria().andUserNameEqualTo(username);
        List<UserDto> userDtos = userDtoMapper.selectByExample(userDtoExample);
        UserDto userDto = new UserDto();
        if (CollectionUtils.isEmpty(userDtos)) {
            //response.setSuccess(false);
            //response.setErrorMsg("不存在的用户");
            throw new ApplicationException("error.000202");
        } else {
            userDto = userDtos.get(0);
            // 判断账号密码是否一致
            if (!passwordEncoder.matches(password, userDto.getPassword())) {
                throw new ApplicationException("error.000202");
            }
        }

        String redisCode = "";
        Profiles profiles = Profiles.of("dev", "prepub-sz", "test", "uat");
        //验证码信息是否存在
        if (StringUtils.isEmpty(redisCode)) {
            // 不存在生成验证码，保存至redis，失效2分钟，登录时校验
            if (env.acceptsProfiles(profiles) || "admin".equals(username)) {
                // 开发和公司测试环境   或者管理员  不生成验证码
                result = "123456";
            } else {
                Random random = new Random();
                int num = random.nextInt(900000) + 100000; // 获取6位随机数
                result = String.valueOf(num);
            }
            redisDao.setObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + username, result, 120 * 1000);
        }
        Profiles profiles1 = Profiles.of("dev", "prepub-sz");
        if (!env.acceptsProfiles(profiles1)) {
            userDto.setTelephone(questionnaireService.deCodeAes(userDto.getTelephone()));
        } else {
            userDto.setTelephone(userDto.getTelephone());
        }
        response.setResult(maskMiddleFourDigits(userDto.getTelephone()));
        if (!env.acceptsProfiles(profiles) && !"admin".equals(username)) {// 开发和公司测试环境不发送验证码
            try {
                String content = this.getMsgContent(result, username);
                MessageDto messageDto = messageManager.sendSms(Collections.singletonList(userDto.getTelephone()), content);
                LOGGER.info("发送短信接口返回结果为:" + JSON.toJSONString(messageDto));
            } catch (Exception e) {
                response.setSuccess(false);
                response.setErrorMsg("验证码发送失败");
                e.printStackTrace();
                LOGGER.info("client.smsLogin error. caused by {}" + e.getMessage());
            } finally {
                LOGGER.info("短信登录生成验证码 ： " + result);
            }
        }
        return response;
    }

    /**
     * 发邮件
     * @param request
     * @param userName
     * @return
     */
    public JsonResponse<Boolean> getMailCaptchaByUser(HttpServletRequest request, String userName) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(true);
        StringBuilder result = new StringBuilder();
        // 根据用户名查找用户
        List<String> userNameList = commonBizMapper.getUserByUserName(userName);
        if (CollectionUtils.isEmpty(userNameList)) {
            response.setResult(false);
            response.setErrorMsg("用户不存在,请仔细检查用户名!");
            return response;
        }
        
        // 内部用户不能使用忘记密码功能
        String userType = commonBizMapper.getUserTypeByUserName(userName);
        if ("0".equals(userType)) {
            response.setResult(false);
            response.setErrorMsg("当前账号为内部用户,不能修改密码");
            return response;
        }

        List<String> mailList = commonBizMapper.getMailByUserName(userName);
        if (StringUtils.isEmpty(mailList.get(0))) {
            response.setResult(false);
            response.setErrorMsg("该用户无可用邮箱");
            return response;
        }

        Object redis = redisDao.getObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userName);
        String redisCode = "";
        if (ObjectUtils.isNotEmpty(redis)) redisCode = redis.toString();

        if (StringUtils.isNotEmpty(redisCode)) {
            // 存在有效验证码，给一个错误提示。
            response.setResult(false);
            response.setErrorMsg("已存在有效验证码，请勿重复发送！");
            return response;
        } else {
            // 不存在生成验证码，保存至redis，失效2分钟
            Random random = new Random();
            for(int i = 0; i < 6; i++) {
                result.append(random.nextInt(10));
            }
            redisDao.setObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userName, result.toString(), 120 * 1000);
        }

        try {
            // 调用短信登录接口 参数手机号码、验证码
            String content = this.getMsgContentMail(result.toString(), userNameList.get(0));
            String messageContent = "上市公司及股东一体化服务平台";
            cscSignaturePersonManager.pushSendMessage(mailList, messageContent, content, 101);
            LOGGER.info("发送邮箱验证码接口返回结果为:" + JSON.toJSONString(messageService.sendMailMessage(mailList.get(0), content)));
        } catch (Exception e) {
            response.setResult(false);
            response.setErrorMsg("验证码发送失败");
            e.printStackTrace();
            LOGGER.info(e.getMessage());
            redisDao.delObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userName);
        } finally {
            LOGGER.info("忘记密码生成验证码(邮箱)：" + result);
        }
        return response;
    }

    /**
     * 发短信
     * @param request
     * @param userName
     * @return
     */
    public JsonResponse<Boolean> getPhoneCaptchaByUser(HttpServletRequest request, String userName) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(true);
        StringBuilder result = new StringBuilder();
        // 根据用户名查找用户
        List<String> userNameList = commonBizMapper.getUserByUserName(userName);
        if (CollectionUtils.isEmpty(userNameList)) {
            response.setResult(false);
            response.setErrorMsg("用户不存在,请仔细检查用户名!");
            return response;
        }

        Object redis = redisDao.getObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + userName);
        String redisCode = "";
        if (ObjectUtils.isNotEmpty(redis)) redisCode = redis.toString();

        if (StringUtils.isNotEmpty(redisCode)) {
            // 存在有效验证码，给一个错误提示。
            response.setResult(false);
            response.setErrorMsg("已存在有效验证码，请勿重复发送！");
            return response;
        } else {
            // 不存在生成验证码，保存至redis，失效2分钟
            Random random = new Random();
            for(int i = 0; i < 6; i++) {
                result.append(random.nextInt(10));
            }
            redisDao.setObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + userName, result.toString(), 120 * 1000);
        }

        List<String> phoneList = commonBizMapper.getPhoneByUserName(userName);
        if (StringUtils.isEmpty(phoneList.get(0))) {
            response.setResult(false);
            response.setErrorMsg("该用户无可用手机号");
            return response;
        }

        try {
            // 调用短信登录接口 参数手机号码、验证码
            String content = this.getMsgContentMail(result.toString(), userNameList.get(0));
            Callable<MessageDto> call = () -> messageManager.sendSms(phoneList, content);
            ExecutorService executorService = Executors.newFixedThreadPool(1);
            try {
                Future<MessageDto> future = executorService.submit(call);
                // 10s超时
                MessageDto messageMain = future.get(10, TimeUnit.SECONDS);
                if (messageMain == null) {
                    throw new BusinessException("调用短信接口异常");
                }
                // 判断是否有错误代码
                if (StringUtils.isEmpty(messageMain.getCode()) || !NumberEnum.ONE_THOUSAND.getString()
                        .equals(messageMain.getCode())) {
                    throw new BusinessException(messageMain.getMsg());
                }
            } catch (Exception e) {
                response.setResult(false);
                response.setErrorMsg(e.getMessage());
            } finally {
                // 关闭线程池
                try {
                    executorService.shutdown();
                    // (所有的任务都结束的时候，返回TRUE)
                    if (!executorService.awaitTermination(NumberEnum.THREE.getInteger(), TimeUnit.SECONDS)) {
                        // 超时的时候向线程池中所有的线程发出中断(interrupted)。
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    // awaitTermination方法被中断的时候也中止线程池中全部的线程的执行。
                    executorService.shutdownNow();
                }
            }
        } catch (Exception e) {
            response.setResult(false);
            response.setErrorMsg("验证码发送失败");
            e.printStackTrace();
            LOGGER.info(e.getMessage());
            redisDao.delObject(RedisKeyConstant.MAIL_VERIFY_CODE_PREFIX + userName);
        } finally {
            LOGGER.info("忘记密码生成验证码(邮箱)：" + result);
        }
        return response;
    }

    public String getMsgContentMail(String code, String userName){
        String personName = getPersonNameByUserName(userName);
        return "尊敬的"+personName+"用户，您好，您本次重置密码的验证码为："+code+"，2分钟内有效，请注意保密。【上市公司及股东一体化服务平台】";
    }

    public JsonResponse<Boolean> getUserNameDynamicCode(String phoneNo) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(true);
        String result = "";
        // 用户表找不到手机号对应用户
        List<String> userNames = getPhoneUser(phoneNo);
        if(userNames == null || userNames.size() < 1) { throw new ApplicationException("error.100026"); }
        Object redis = redisDao.getObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + phoneNo);
        String redisCode = "";
        if(null != redis){ redisCode = redis.toString(); }
        // 验证码信息是否存在
        if (StringUtils.isEmpty(redisCode)){
            // 不存在生成验证码，保存至redis，失效1分钟，登录时校验
            Random random = new Random();
            for(int i = 0; i < 6; i++) {
                result += random.nextInt(10);
            }
            redisDao.setObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX + phoneNo, result , 120 * 1000);
        }else {
            // 存在有效验证码，给一个错误提示。
            response.setResult(false);
            response.setErrorMsg("已存在有效验证码，请勿重复发送！");
            return response;
        }
        // 调用短信登录接口 参数手机号码、验证码
        try {
            String content = this.getMsgContent(result, userNames.get(0));
            LOGGER.info("发送短信接口返回结果为:" + JSON.toJSONString(messageService.sendMessage(phoneNo, content)));
        } catch (Exception e) {
            response.setResult(false);
            response.setErrorMsg("验证码发送失败");
            e.printStackTrace();
            LOGGER.info("client.smsLogin error. caused by {}" + e.getMessage());
        } finally {
            LOGGER.info("短信登录生成验证码：" + result);
        }
        return response;
    }

    public String getMsgContent(String code, String userName){
//        String personName = getPersonNameByUserName(userName);
        System.out.println("上市公司及股东一体化服务平台提醒您，您本次平台登录验证码为" + code + "，2分钟内有效，请勿转发或泄漏。");
        return "上市公司及股东一体化服务平台提醒您，您本次平台登录验证码为" + code + "，2分钟内有效，请勿转发或泄漏。";
    }

    public String getPersonNameByUserName(String userName){
        return commonBizMapper.getPersonNameByUserName(userName);
    }

    /**
     * 发送短信验证码之前需要先校验图形验证码是否正确。
     * @return response
     */
    public JsonResponse<Boolean> captchaCodeOnChange(HttpServletRequest request,String captchaCode) {
        JsonResponse<Boolean> response = new JsonResponse<Boolean>();
        //输入验证码不能为空
        if (StringUtils.isEmpty(captchaCode)){
            response.setResult(false);
            return response;
        }
        response.setResult(true);
        String suffix = this.getCookieValue(request, LogicConstant.MSG_LOGIN_OP_CODE);
        String messageRandom = (String) redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_MSG_RANDOM_KEY + suffix);
        //获取验证码
        String codeInRedis = (String) redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_PREFIX+messageRandom+suffix);
        if(StringUtils.isBlank(codeInRedis) || !captchaCode.equals(codeInRedis)){
            response.setResult(false);
            return response;
        }
        return response;
    }

    public List<String> getPhoneUser(String phoneNo) {
        return  commonBizMapper.getPhoneUser(phoneNo);
    }

    public String getCookieValue(HttpServletRequest request, String name) {
        String suffix;
        if(request.getCookies() == null) { return ""; }
        Cookie cookie = Arrays.stream(request.getCookies())
                .filter(o -> name.equals(o.getName()))
                .findFirst()
                .orElse(null);
        suffix = cookie == null ?"" :cookie.getValue();
        return suffix;
    }

    public Map<String, Object> getSelectData() {

        Map<String, Object> resultMap = Maps.newHashMap();

        //发行人行业（证监会）
        List<RegTreeDto> industryCrscList = commonBizMapper.getLabelByCode("INDUSTRY_CSRC_2012");
        resultMap.put("industryCrscList", sortSelectList(industryCrscList));

        //申万行业
        List<RegTreeDto> industrySWList = commonBizMapper.getLabelByCode("INDUSTRY_SW_2021");
        resultMap.put("industrySWList", sortSelectList(industrySWList));

        //公司性质
        List<RegTreeDto> companyNatureList = commonBizMapper.getSaCodeByCode("REP_COMPANY_NATURE");
        resultMap.put("companyNatureList", sortSelectList(companyNatureList));

        //拟上市板块
        List<RegTreeDto> plateTreeTag = commonBizMapper.getSaCodeByNo("CASE_BELONGS_PLATE");
        resultMap.put("plateTreeList", sortSelectList(plateTreeTag));

        //所在地区
        List<OptionResultAreaAndIndustryDto> areaSelDataList =  getAreaList();
        resultMap.put("areaSelDataList",areaSelDataList);

        return resultMap;
    }

    public List<RegTreeDto> sortSelectList(List<RegTreeDto> selectList) {
        List<RegTreeDto> resultList = new ArrayList<>();
        if (selectList.size() > 0) {
            //获取pId为0的，为父节点
            for (int i = 0; i < selectList.size(); i++) {
                if ("0".equals(selectList.get(i).getpId())) {
                    RegTreeDto tempDto = selectList.get(i);
                    resultList.add(getChildList(tempDto, selectList));
                }
            }
        }
        return resultList;
    }

    public RegTreeDto getChildList(RegTreeDto repTreeTagDto, List<RegTreeDto> selectList) {
        List<RegTreeDto> childList = new ArrayList<>();
        for (int i = 0; i < selectList.size(); i++) {
            if (repTreeTagDto.getId().equals(selectList.get(i).getpId())) {
                RegTreeDto tempDto = selectList.get(i);
                getChildList(tempDto, selectList);
                childList.add(selectList.get(i));
            }
        }
        repTreeTagDto.setChildren(childList);
        return repTreeTagDto;
    }


    //地区下拉
    public List<OptionResultAreaAndIndustryDto> getAreaList(){
        //新公告获取地区和就公告区别
        List<OptionResultAreaAndIndustryDto> resultList = Lists.newArrayList();

        //获取地区分组
        List<String> groupList = commonBizMapper.getAreaGroupList();
        if(CollectionUtils.isNotEmpty(groupList)){
            String groupFirst = groupList.get(0);
            if(StringUtils.isEmpty(groupFirst)){//调整顺序
                groupList.remove(0);
                groupList.add(groupFirst);
            }
            //获取地区数据
            List<OptionResultAreaAndIndustryDto> areaList = commonBizMapper.getAreaTreeList();
            if(CollectionUtils.isNotEmpty(areaList)){
//                if("1".equals(searchFromPlate) || "6".equals(searchFromPlate) || "10".equals(searchFromPlate)){
                OptionResultAreaAndIndustryDto industryDto = new OptionResultAreaAndIndustryDto();
                industryDto.setLabel("境外");
                industryDto.setId("999999");
                industryDto.setpId("0");
                industryDto.setValue("999999");
                industryDto.setAreaGroup("");
                areaList.add(industryDto);
//                }
                //处理地区数据分组-找出省级地区
                OptionResultAreaAndIndustryDto areaAndIndustryDto;
                for(String groupCode : groupList){
                    areaAndIndustryDto = new OptionResultAreaAndIndustryDto();
                    areaAndIndustryDto.setLabel(groupCode);
                    List<OptionResultAreaAndIndustryDto> provinceList = areaList.stream().filter(item -> groupCode.equals(item.getAreaGroup())).collect(Collectors.toList());
                    for(OptionResultAreaAndIndustryDto areaDto : provinceList){//获取市
                        List<OptionResultAreaAndIndustryDto> city = areaList.stream().filter(item -> areaDto.getId().equals(item.getpId())).collect(Collectors.toList());
                        areaDto.setChildren(city);
                    }
                    areaAndIndustryDto.setItems(provinceList);
                    resultList.add(areaAndIndustryDto);
                }
            }
        }
        return resultList;
    }

    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    public List<RouterDto> getOpenedSystems(HttpServletRequest request, RouterDto param) {
        List<RouterDto> result = new ArrayList<>();
        String orgType = String.valueOf(getUserInfo().getInfo().get("orgType"));
        if(StringUtils.isNotEmpty(orgType) && !"null".equals(orgType)) {
            int type = Integer.parseInt(orgType);
            // 获取系统列表
            SystemConfigExample sysExample = new SystemConfigExample();
            sysExample.createCriteria().andSysCodeIsNotNull();
            sysExample.setOrderByClause("sys_order");
            List<SystemConfig> systemList = systemConfigMapper.selectByExampleWithBLOBs(sysExample);
            // 判断用户类型
            if (type > 3) {
                // 上市公司，仅增加自己公司开通的系统
                Company company = companyMapper.selectByPrimaryKey(getUserInfo().getCompanyId());
                String openedSystemCodeStr = company.getSystemState();
                if (StringUtils.isNotEmpty(openedSystemCodeStr)) {
                    String[] openedSystemCodes = openedSystemCodeStr.split(",");
                    for(String code: openedSystemCodes) {
                        for(SystemConfig config: systemList) {
                            if(config.getSysName().equals(code)) {
                                RouterDto router = systemConfigService.getOpenedSystemRouter(request, config, type);
                                if (router != null) { result.add(router); }
                                break;
                            }
                        }
                    }
                }
            }else {
                // 内部用户，显示所有系统
                for(SystemConfig config: systemList) {
                    RouterDto router = systemConfigService.getOpenedSystemRouter(request, config, type);
                    if (router != null) { result.add(router); }
                }
            }
        }
        // 过滤menuName
        if (StringUtils.isNotEmpty(param.getMenuName())) {
            result = result.stream().filter(item -> item.getMenuName().equals(param.getMenuName())).collect(Collectors.toList());
        }
        return result;
    }

    public SystemConfig getSystemStatus(Map<String,String> map) {
        SystemConfigExample systemConfigExample = new SystemConfigExample();
        systemConfigExample.createCriteria().andSysCodeEqualTo(map.get("sysCode"));
        return systemConfigMapper.selectByExample(systemConfigExample).get(0);
    }

    //public void logoutMq() {
    //    //向其他微服发送消息
    //    String uid = UUID.randomUUID().toString();
    //    MQMsgDto<String> send = new MQMsgDto<>();
    //    send.setUid(uid);
    //    send.setSystemType(saSystemUtil.getSysCode());
    //    send.setBusinessType(TopicConstant.BUS_TYPE_LOGOUT);
    //    send.setOperationType(TopicConstant.OP_TYPE_LOGOUT);
    //    send.setInfo("logout");
    //    try {
    //        saSystemUtil.insertMqMessage(send);
    //        topicProducer.sendTopic(new HashMap<String,String>(){{put("logout","logout");}},saSystemUtil.getSysCode(), TopicConstant.BUS_TYPE_LOGOUT, TopicConstant.OP_TYPE_LOGOUT,uid);
    //    } catch (Exception e) {
    //        throw new RuntimeException(e);
    //    }
    //}

    /**
     * 判断 day 是不是批日
     * @param day 日期字符串，格式为 xxxx-xx-xx
     * @return boolean
     */
    public boolean dayIsP(String day) throws ParseException {
        String tradeDate = commonBizMapper.getTradeDate(day);
        if(StringUtils.isNotEmpty(tradeDate)) {
            String previousDay = getPreviousDay(day);
            String previousDayIsTradeDate = commonBizMapper.getTradeDate(previousDay);
            return StringUtils.isNotEmpty(previousDayIsTradeDate);
        } else {
            String nextDay = getNextDay(day);
            String nextDayIsTradeDate = commonBizMapper.getTradeDate(nextDay);
            return StringUtils.isNotEmpty(nextDayIsTradeDate);
        }
    }

    /**
     * 获取给定日期的前一天
     * @param dateString 日期字符串，格式为xxxx-xx-xx
     * @return 前一天的日期字符串
     */
    public static String getPreviousDay(String dateString) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(dateString);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取给定日期的后一天
     * @param dateString 日期字符串，格式为xxxx-xx-xx
     * @return 后一天的日期字符串
     */
    public static String getNextDay(String dateString) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(dateString);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return sdf.format(calendar.getTime());
    }
}
