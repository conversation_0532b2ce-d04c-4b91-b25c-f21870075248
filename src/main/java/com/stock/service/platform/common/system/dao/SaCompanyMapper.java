package com.stock.service.platform.common.system.dao;

import com.stock.core.dto.TreeDto;
import com.stock.service.platform.common.system.dto.CompanyCodeAndName;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.dto.SaUserDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface SaCompanyMapper {

    List<SaCompanyDto> queryPagingCompanyBySelective(SaCompanyDto dto);

    void updateByPrimaryKeys(@Param("id") String id, @Param("deleteFlag") String deleteFlag);

    void deleteByPrimaryKeys(@Param("id") String id, @Param("param") SaCompanyDto param);

    void deleteSaOrgByPrimaryKeys(@Param("id") String id, @Param("param") SaCompanyDto param);

    SaCompanyDto getCompanyById(@Param("id") String id);

    void insertSelective(SaCompanyDto insertCompany);

    void updateByPrimaryKeySelective(SaCompanyDto updateCompany);

    List<String> selectAllCompany();

    List<String> queryAllChildCompany();

    List<String> queryCompanyByDepartmentId(String orgId);

    List<String> queryDepartmentIdByBranchCode(String orgId);

    List<SaUserDto> queryCompanyUsers(String id);

    SaCompanyDto getCompanyByCode(String companyCode);

    List<Map<String, String>> selectCompanyDictByOrgId(@Param("params") Map params);

    /**
     * 查sa_company
     * @param companyCode
     * @return
     */
    SaCompanyDto getCompanyByCode1(String companyCode);

    int selectByIdSeriakey(Map<String, String> userDto);

    List<String> selectCompanyByUserId(String companyCode);

    List<SaCompanyDto> getCompanyList(List<String> codeList);

    List<SaCompanyDto> getCompanyChildList(List<String> codeList);

    List<Map<String, String>> getCompany();

    List<SaCompanyDto> queryCompanyByParentId(String id);

    int getChildrenCompanyCode(String companyCode);

    void updateSaOrgByPrimaryKeys(String companyId, String status);

    List<SaCompanyDto> selectOrgSelectionTree();

    List<TreeDto> getTree();

    String selectOldCompanyCode(String companyCode);

    List<CompanyCodeAndName> selectAllCompanyCodeAndName();
}