package com.stock.service.platform.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;

public class IPUtils {
    /**
     * 获取当前服务器的IP地址(内网)
     *
     */
    public static String getHostAddress() {
        try {
            InetAddress address = InetAddress.getLocalHost();
            return address.getHostAddress();
        } catch (Exception e) {
            return "127.0.0.1";
        }
    }

    /**
     * 获取本机外网ip
     *
     */
    public static String getInternetHostAddress() {
        try {
            String address = "http://pv.sohu.com/cityjson?ie=utf-8";
            URL url = new URL(address);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(3000);
            urlConnection.setReadTimeout(3000);
            //first set User-Agent to solve Server returned HTTP response code: 403 for URL
            urlConnection.setRequestProperty("User-Agent", "Mozilla/4.76");
            BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
            StringBuilder stringBuilder = new StringBuilder();
            String read;
            while ((read = in.readLine()) != null) {
                stringBuilder.append(read);
            }
            // 解析
            String string = StringUtils.split(stringBuilder.toString(), "=")[1];
            string = StringUtils.split(string, ";")[0];
            JSONObject jsonObject = JSON.parseObject(string);
            return jsonObject.getString("cip");
        } catch (Exception e) {
            e.printStackTrace();
            return "127.0.0.1";
        }
    }
}
