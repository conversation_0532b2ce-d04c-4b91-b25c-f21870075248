package com.stock.service.platform.common.dto;

import java.io.Serializable;

/**
 * @Description: (这里用一句话描述这个类的作用)
 * @author: wang<PERSON><PERSON>
 * @date:
 */
public class RelatedCategoryParamDto implements Serializable {
    private static final long serialVersionUID = -3061517995179914847L;
    String treeType;
    String itemType;
    String categoryName;

    public String getTreeType() {
        return treeType;
    }

    public void setTreeType(String treeType) {
        this.treeType = treeType;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

}
