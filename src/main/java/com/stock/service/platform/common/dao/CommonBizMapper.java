package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.dto.OptionResultAreaAndIndustryDto;
import com.stock.service.platform.common.dto.RegTreeDto;

import java.util.List;
import java.util.Map;

public interface CommonBizMapper {

    List<String> getPhoneUser(String phoneNo);

    Integer getCurrentAudith(Map<String,String> map);

    List<RegTreeDto> getLabelByCode(String labelCode);

    List<RegTreeDto> getTreeTagByCode(String labelCode);

    List<RegTreeDto> getSaCodeByNo(String labelCode);

    List<String> getAreaGroupList();

    List<OptionResultAreaAndIndustryDto> getAreaTreeList();

    List<String> getUserByUserName(String userName);

    List<RegTreeDto> getSaCodeByCode(String labelCode);

    List<String> getMailByUserName(String userName);

    List<String> getPhoneByUserName(String userName);

    String getUserTypeByUserName(String userName);

    String getPersonNameByUserName(String userName);

    String getTradeDate(String day);
}