<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OrgMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Org">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="p_org_id" jdbcType="VARCHAR" property="pOrgId" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="threshold_flag" jdbcType="VARCHAR" property="thresholdFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, org_code, org_name, p_org_id, order_id, level, org_type, create_user, create_time,
    update_user, update_time, status, threshold_flag
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.OrgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_org
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_org
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.OrgExample">
    delete from sa_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <delete id="deleteByPrimaryKeys">
    delete from sa_org where id = #{id}
  </delete>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Org">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into sa_org
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="pOrgId != null">
        p_org_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="thresholdFlag != null">
        threshold_flag,
      </if>
      <if test="childrenCompanyFlag != null">
        children_company_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="pOrgId != null">
        #{pOrgId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        #{level},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="thresholdFlag != null">
        #{thresholdFlag,jdbcType=VARCHAR},
      </if>
      <if test="childrenCompanyFlag != null">
        #{childrenCompanyFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.OrgExample" resultType="java.lang.Long">
    select count(*) from sa_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_org
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCode != null">
        org_code = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.pOrgId != null">
        p_org_id = #{record.pOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.level != null">
        org_type = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.orgType != null">
        org_type = #{record.orgType,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdFlag != null">
        threshold_flag = #{record.thresholdFlag,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_org
    set id = #{record.id,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      org_code = #{record.orgCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      p_org_id = #{record.pOrgId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=INTEGER},
      level = #{record.level,jdbcType=VARCHAR},
      org_type = #{record.orgType,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR},
      threshold_flag = #{record.thresholdFlag,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Org">
    update sa_org
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="pOrgId != null">
        p_org_id = #{pOrgId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="thresholdFlag != null">
        threshold_flag = #{thresholdFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Org">
    update sa_org
    set company_id = #{companyId,jdbcType=VARCHAR},
      org_code = #{orgCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      p_org_id = #{pOrgId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      level = #{level,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      threshold_flag = #{thresholdFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeys">
    update sa_org set status = #{status}, order_id = #{order} where id = #{id}
  </update>
  <update id="updatePersonOrg">
    update sa_person set org_id = #{orgId} where company_code = #{companyCode} and person_type = '1'
  </update>
    <update id="updateUserOrg">
      update sa_user set org_no = #{orgId} where company_id = #{companyId} and user_type = '1'
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.OrgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <select id="selectOrgDictByIsMgr" resultType="java.util.Map" parameterType="java.util.Map">
    select
        so.id as `key`,
        so.id as `value`,
        so.company_id as `companyId`,
        so.org_name as text
    from sa_org so
    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
    where so.status = '1'
    <if test="params.pId != null and params.pId != ''">
        and (so.p_org_id = #{params.pId} or so.id = #{params.pId}
                <if test="params.limitCodeStr != null and params.limitCodeStr != ''">
                    or pso.id = #{params.limitCodeStr} or pso.p_org_id = #{params.limitCodeStr}
                </if>
            )
    </if>
    <if test="params.orgType != null and params.orgType != ''">
      and so.org_type = #{params.orgType}
    </if>
  </select>

  <select id="selectOrgIdByBranch" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
    where so.status = '1'
    and so.org_type in ('2','3','4')
    and (so.id = #{orgId} or pso.id = #{orgId} or pso.p_org_id = #{orgId})
  </select>
  <select id="selectOrgIdByBranchBesb" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
    where so.status = '1'
    and so.org_type in ('3','4')
    and (pso.id = #{orgId} or pso.p_org_id = #{orgId})
  </select>
  <select id="selectOrgIdByDept" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    where so.status = '1'
    and so.org_type in ('3','4')
    and (so.id = #{orgId} or so.p_org_id = #{orgId})
  </select>
  <select id="checkBranchIfShare" resultType="java.lang.String" parameterType="java.lang.String">
      select pso.id from sa_org so left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
      where so.id = #{orgId} and pso.id is not null and pso.threshold_flag = '1'
  </select>
  <select id="selectByCompanyId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    org.*
    from sa_org org,sa_company company
    where org.company_id = company.id
    and company.id= #{companyId}
  </select>
  <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT org.*
    FROM sa_org org
    WHERE org.id = (SELECT org_id FROM sa_person WHERE user_id = #{userId})
  </select>

  <select id="selParentById" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT org.*
    FROM sa_org org
    WHERE org.id = #{id}
  </select>
  <select id="selectBySonIds" resultType="com.stock.service.platform.common.entity.Org">
    select DISTINCT
      org.id, org.company_id, org.org_code, org.org_name, org.p_org_id, org.order_id, org.level, org.org_type, org.create_user, org.create_time,
      org.update_user, org.update_time, org.status, org.threshold_flag
    from sa_org org
    left join sa_org org1 on org.id = org1.p_org_id
    where org.status = "1"
    <if test="orgIds != null and orgIds.size !=0">
      AND org1.id IN
      <foreach collection="orgIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>