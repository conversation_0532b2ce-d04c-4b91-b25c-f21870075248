package com.stock.service.platform.common.system.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(value = {"handler"})
public class MenuDto implements Serializable {
    private String id; //主键ID
    private String name; //菜单名称
    private String pMenuId; //上一级菜单
    private List<MenuDto> menuField; //子菜单列表
    private String menuName;
    private String path;//资源路劲

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpMenuId() {
        return pMenuId;
    }

    public void setpMenuId(String pMenuId) {
        this.pMenuId = pMenuId;
    }

    public List<MenuDto> getMenuField() {
        return menuField;
    }

    public void setMenuField(List<MenuDto> menuField) {
        this.menuField = menuField;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
