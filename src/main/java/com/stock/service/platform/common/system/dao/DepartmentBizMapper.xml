<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.system.dao.DepartmentBizMapper">
    <sql id="Base_Column_List" >
        id,company_id,org_code, org_name,p_org_id,order_id, org_type,`level`,status,threshold_flag
    </sql>
    <select id="listDepartmentByCompanyId" resultType="com.stock.service.platform.common.system.dto.DepartmentDto">
        select
            <include refid="Base_Column_List"></include>
        from
            sa_org
        where
            company_id = #{companyId}
            and status = '1'
    </select>
    <select id="getNodeByOrderLevelAndTreeId" resultType="com.stock.service.platform.common.system.dto.DepartmentDto">
        select
        <include refid="Base_Column_List"></include>
        from sa_org where  (company_id is null or company_id = '') and order_id = #{treeOrder} and p_org_id = #{pid}  and status = '1'
    </select>
<!--        from sa_org where  company_id is null and order_id = #{treeOrder} and `level` = #{treeLevel} and p_org_id = #{pid}  and status = '1'-->
    <select id="countByTreeIdAndPid" resultType="int">
        select count(1) from sa_org where (company_id is null or company_id = '') and p_org_id=#{pid}  and status = '1'
    </select>
    <select id="countByPid" resultType="int">
        select count(1) from sa_org where (company_id is null or company_id = '') and p_org_id=#{pid} and status = '1'
    </select>
    <select id="listNodeByPidAndTreeId"  resultType="com.stock.service.platform.common.system.dto.DepartmentDto" >
        select
        <include refid="Base_Column_List"></include>
        from sa_org where company_id=#{treeId} and p_org_id = #{pid} and status = '1'
        order by order_id asc
    </select>
    <select id="listGtNodeByPidAndTreeIdAndLevel"  resultType="com.stock.service.platform.common.system.dto.DepartmentDto" >
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
        sa_org
        WHERE
        status = '1'
        and company_id is null and p_org_id =  #{pid} AND order_id > #{order}
        order by order_id asc
    </select>
</mapper>