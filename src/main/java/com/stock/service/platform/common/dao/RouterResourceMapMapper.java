package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.RouterResourceMap;
import com.stock.service.platform.common.entity.RouterResourceMapExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface RouterResourceMapMapper {
    long countByExample(RouterResourceMapExample example);

    int deleteByExample(RouterResourceMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(RouterResourceMap record);

    int insertSelective(RouterResourceMap record);

    List<RouterResourceMap> selectByExampleWithRowbounds(RouterResourceMapExample example, RowBounds rowBounds);

    List<RouterResourceMap> selectByExample(RouterResourceMapExample example);

    RouterResourceMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") RouterResourceMap record, @Param("example") RouterResourceMapExample example);

    int updateByExample(@Param("record") RouterResourceMap record, @Param("example") RouterResourceMapExample example);

    int updateByPrimaryKeySelective(RouterResourceMap record);

    int updateByPrimaryKey(RouterResourceMap record);
}