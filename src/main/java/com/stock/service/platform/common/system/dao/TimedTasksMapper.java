package com.stock.service.platform.common.system.dao;


import com.stock.service.platform.common.system.dto.TasksDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface TimedTasksMapper {

    List<Map<String,Object>> getTasksList(Map<String, Object> param);

    @Select("select count(*) from activemq_topic where message_type = '1' and sys_code = #{sysCode}")
    int getTasksCount(Map<String, Object> param);

    int getTasksPCount(Map<String, Object> param);

    void insertActivemqTopic01(Map<String,Object> param);

    void updateIsSuccess(Map<String, Object> param);

    void insertTasksP(TasksDto tasksDto);

    List<Map<String,Object>> getTasksPList(Map<String, Object> param);

    void updateIsSuccessP(Map<String, Object> param);

    int isTradingDay(String date);

    int getSuccessCount(String date);

    List<String> getOneYearTradingDay(String oneYearAgo, String currentDate);

    String getMaxTradeDate(String date);

    TasksDto getTimedTasksByTradeDate(String tradeDate);

    List<Map<String, Object>> getTasksPGroupList(Map<String, Object> param);

    int getTasksPGroupCount();
}
