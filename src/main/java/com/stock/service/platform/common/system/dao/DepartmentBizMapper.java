package com.stock.service.platform.common.system.dao;

import com.stock.service.platform.common.system.dto.DepartmentDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DepartmentBizMapper {
   List<DepartmentDto> listDepartmentByCompanyId(String companyId);
   DepartmentDto getNodeByOrderLevelAndTreeId(@Param("treeId") String treeId, @Param("treeOrder") Integer treeOrder, @Param("treeLevel") String treeLevel, @Param("pid") String pid);
   int countByTreeIdAndPid(@Param("treeId") String treeId, @Param("pid") String pid);
   int countByPid(@Param("pid") String pid);
   List<DepartmentDto> listNodeByPidAndTreeId(@Param("treeId") String treeId,@Param("pid") String pid);
   List<DepartmentDto> listGtNodeByPidAndTreeIdAndLevel(@Param("treeId") String treeId, @Param("pid") String pid, @Param("treeLevel") String treeLevel,@Param("order") Integer order);
}
