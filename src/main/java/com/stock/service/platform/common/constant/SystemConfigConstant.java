package com.stock.service.platform.common.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统配置表相关常量
 * sa_system_config.sys_config JSON内容
 * {
 *     "dev": { // 本地环境
 *         "open_state_api": "http://localhost:8080/xxxxx", // 确认系统是否开通接口
 *         "login_api": "http://localhost:8080/xxxxx?companyCode=${companyCode}", // 其他系统登录跳转接口,${companyCode}为动态参数
 *     },
 *     "prepub-sz": { // 内部测试
 *        结构同"dev"
 *     },
 *     "test": { // 客户测试
 *         结构同"dev"
 *     },
 *     "prod": { // 客户生产
 *         结构同"dev"
 *     }
 * }
 */
public class SystemConfigConstant {

    /**
     * 目前支持的接入系统接口 变量名前缀：SYS_CONFIG_API_
     */
    public static final String SYS_CONFIG_API_LOGIN_NAME = "login_api"; // 接入系统登录接口
    public static final String SYS_CONFIG_API_LOGIN_NAME_OUT = "login_api_out"; // 接入系统登录接口
    public static final String SYS_CONFIG_API_OPEN_STATE_NAME = "open_state_api"; // 确认接入系统是否开通

    /**
     * 允许调用平台接口的IP地址
     */
    public static final String SYS_CONFIG_CALL_API_IP_ADDR = "call_api_ip_address";

    public static final Map<String, String>[] SYS_CONFIG_API_MAP = new HashMap[] {
            new HashMap<String, String>() {{
                put("label", "内网登录跳转路径");
                put("desc", "跳转对应系统内网时浏览器地址栏的路径");
                put("value", SystemConfigConstant.SYS_CONFIG_API_LOGIN_NAME);
            }},
            new HashMap<String, String>() {{
                put("label", "外网登录跳转路径");
                put("desc", "跳转对应系统外网时浏览器地址栏的路径");
                put("value", SystemConfigConstant.SYS_CONFIG_API_LOGIN_NAME_OUT);
            }},
            //new HashMap<String, String>() {{
            //    put("label", "确认接口");
            //    put("desc", "每晚定时查询对应公司是否开通本系统");
            //    put("value", SystemConfigConstant.SYS_CONFIG_API_OPEN_STATE_NAME);
            //}},
            //new HashMap<String, String>() {{
            //    put("label", "允许调用平台接口的IP地址");
            //    put("desc", "只有配置的接口才能调用平台的接口，使用英文逗号,分隔");
            //    put("value", SystemConfigConstant.SYS_CONFIG_CALL_API_IP_ADDR);
            //}}
    };


    /**
     * 系统接口参数中动态参数只能包含如下参数，静态参数请自行在页面定义
     * companyCode 当前用户所在公司
     * userId 当前用户编号
     * username 当前用户账号
     * token 当前用户凭证
     * access_token 当前用户凭证
     */
    public static final List<String> SYS_CONFIG_API_DYNAMIC_PARAM_CONTENT =
            Arrays.asList("companyCode", "userId", "username", "token", "access_token");

    /**
     * 不期待的值
     */
    public static final String UNEXPECTED = "unexpected";

    /**
     * URL中参数等于这个会被解析为动态参数
     */
    public static final String DYNAMIC_PARAM_SIGN = "DYNAMIC";

}
