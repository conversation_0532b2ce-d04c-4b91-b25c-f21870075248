package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.CompanyDto;
import com.stock.service.platform.common.entity.CompanyDtoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CompanyDtoMapper {
    long countByExample(CompanyDtoExample example);

    int deleteByExample(CompanyDtoExample example);

    int deleteByPrimaryKey(String id);

    int insert(CompanyDto record);

    int insertSelective(CompanyDto record);

    List<CompanyDto> selectByExample(CompanyDtoExample example);

    CompanyDto selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CompanyDto record, @Param("example") CompanyDtoExample example);

    int updateByExample(@Param("record") CompanyDto record, @Param("example") CompanyDtoExample example);

    int updateByPrimaryKeySelective(CompanyDto record);

    int updateByPrimaryKey(CompanyDto record);

    void resettingSystemState();

    String getSystemState(String companyId);

    void updateSystemState(String companyId, String systemState);

    String getMarketStockTypeByCompanyCode(String companyCode);

    String getSocialUnifiedCreditCodeByCompanyCode(String companyCode);
}