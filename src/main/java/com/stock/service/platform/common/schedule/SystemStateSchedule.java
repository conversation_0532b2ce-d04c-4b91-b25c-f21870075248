package com.stock.service.platform.common.schedule;

import com.stock.core.dao.RedisDao;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.SystemConfigConstant;
import com.stock.service.platform.common.dao.CompanyMapper;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.entity.Company;
import com.stock.service.platform.common.entity.CompanyExample;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import com.stock.service.platform.common.system.dto.TasksDto;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@EnableScheduling
@Component
public class SystemStateSchedule {

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private TimedTasksMapper timedTasksMapper;

    /**
     * 定时刷新公司各系统的开通情况
     */
    //@Scheduled(cron = "0 0 22 * * ? ")
    @Transactional(rollbackFor = Exception.class)
    public void systemOpenState(){
        String lock = redisDao.acquireDistributedLockWithTimeout("system_open_state_lock", 10, 1000 * 1800);
        if (StringUtils.isNotEmpty(lock)) {
            TasksDto tasksDto = new TasksDto();
            tasksDto.setName("systemOpenState");
            tasksDto.setZhName("获取系统状态");
            tasksDto.setIsSuccess("2");
            timedTasksMapper.insertTasksP(tasksDto);
            try {
                // 获取各系统列表
                SystemConfigExample configExample = new SystemConfigExample();
                configExample.createCriteria().andSysCodeIsNotNull();
                List<SystemConfig> systemList = systemConfigMapper.selectByExampleWithBLOBs(configExample);
                // 获取公司列表
                CompanyExample example = new CompanyExample();
                example.createCriteria().andCompanyCodeIsNotNull();
                List<Company> companies = companyMapper.selectByExample(example);
                for (Company company: companies) {
                    String systemState = "";
                    for (SystemConfig system: systemList) {
                        if (StringUtils.isNotEmpty(system.getSysConfig())) {
                            Map<String, Object> config = JsonUtil.fromJson(system.getSysConfig(), HashMap.class);
                            String url = MapUtils.getString(config, SystemConfigConstant.SYS_CONFIG_API_OPEN_STATE_NAME);
                            if(StringUtils.isNotEmpty(url)) {
                                Map<String, Object> response = restTemplate.postForObject(url, company.getCompanyCode() ,HashMap.class);
                                if (response != null) {
                                    if(CodeTableConstant.DATA_STATUS_1.equals(String.valueOf(response.get("status")))) {
                                        if(StringUtils.EMPTY.equals(systemState)) {
                                            systemState = system.getSysName();
                                        }else {
                                            systemState += "," + system.getSysName();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Company record = new Company();
                    record.setId(company.getId());
                    record.setSystemState(systemState);
                    companyMapper.updateByPrimaryKeySelective(record);
                }

                Map<String, Object> param = new HashMap<>();
                param.put("id", tasksDto.getId());
                param.put("isSuccess", "1");
                timedTasksMapper.updateIsSuccessP(param);
            } catch (Exception e) {
                Map<String, Object> param = new HashMap<>();
                param.put("id", tasksDto.getId());
                param.put("isSuccess", "0");
                param.put("errorMessage", e.getMessage());
                timedTasksMapper.updateIsSuccessP(param);
                e.printStackTrace();
            }
        }}
}
