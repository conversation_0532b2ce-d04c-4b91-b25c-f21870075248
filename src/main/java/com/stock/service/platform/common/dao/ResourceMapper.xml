<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ResourceMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Resource">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="p_id" jdbcType="VARCHAR" property="pId" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="resource_des" jdbcType="VARCHAR" property="resourceDes" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_url" jdbcType="VARCHAR" property="resourceUrl" />
    <result column="curr_status" jdbcType="VARCHAR" property="currStatus" />
    <result column="relation" jdbcType="VARCHAR" property="relation" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, p_id, resource_name, resource_des, resource_type, resource_url, curr_status, 
    relation, create_user, create_time, update_user, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.ResourceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_resource
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_resource
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.ResourceExample">
    delete from sa_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Resource">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_resource (id, p_id, resource_name, 
      resource_des, resource_type, resource_url, 
      curr_status, relation, create_user, 
      create_time, update_user, update_time, 
      status)
    values (#{id,jdbcType=VARCHAR}, #{pId,jdbcType=VARCHAR}, #{resourceName,jdbcType=VARCHAR}, 
      #{resourceDes,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, #{resourceUrl,jdbcType=VARCHAR}, 
      #{currStatus,jdbcType=VARCHAR}, #{relation,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Resource">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="pId != null">
        p_id,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="resourceDes != null">
        resource_des,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="resourceUrl != null">
        resource_url,
      </if>
      <if test="currStatus != null">
        curr_status,
      </if>
      <if test="relation != null">
        relation,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="pId != null">
        #{pId,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="resourceDes != null">
        #{resourceDes,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="currStatus != null">
        #{currStatus,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.ResourceExample" resultType="java.lang.Long">
    select count(*) from sa_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_resource
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.pId != null">
        p_id = #{record.pId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceName != null">
        resource_name = #{record.resourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceDes != null">
        resource_des = #{record.resourceDes,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceUrl != null">
        resource_url = #{record.resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.currStatus != null">
        curr_status = #{record.currStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.relation != null">
        relation = #{record.relation,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_resource
    set id = #{record.id,jdbcType=VARCHAR},
      p_id = #{record.pId,jdbcType=VARCHAR},
      resource_name = #{record.resourceName,jdbcType=VARCHAR},
      resource_des = #{record.resourceDes,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_url = #{record.resourceUrl,jdbcType=VARCHAR},
      curr_status = #{record.currStatus,jdbcType=VARCHAR},
      relation = #{record.relation,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Resource">
    update sa_resource
    <set>
      <if test="pId != null">
        p_id = #{pId,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="resourceDes != null">
        resource_des = #{resourceDes,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceUrl != null">
        resource_url = #{resourceUrl,jdbcType=VARCHAR},
      </if>
      <if test="currStatus != null">
        curr_status = #{currStatus,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        relation = #{relation,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Resource">
    update sa_resource
    set p_id = #{pId,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      resource_des = #{resourceDes,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_url = #{resourceUrl,jdbcType=VARCHAR},
      curr_status = #{currStatus,jdbcType=VARCHAR},
      relation = #{relation,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.ResourceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>