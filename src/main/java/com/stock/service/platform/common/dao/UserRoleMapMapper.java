package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.UserRoleMap;
import com.stock.service.platform.common.entity.UserRoleMapExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface UserRoleMapMapper {
    long countByExample(UserRoleMapExample example);

    int deleteByExample(UserRoleMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(UserRoleMap record);

    int insertSelective(UserRoleMap record);

    List<UserRoleMap> selectByExampleWithRowbounds(UserRoleMapExample example, RowBounds rowBounds);

    List<UserRoleMap> selectByExample(UserRoleMapExample example);

    UserRoleMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") UserRoleMap record, @Param("example") UserRoleMapExample example);

    int updateByExample(@Param("record") UserRoleMap record, @Param("example") UserRoleMapExample example);

    int updateByPrimaryKeySelective(UserRoleMap record);

    int updateByPrimaryKey(UserRoleMap record);

    List<String> getRoleNameByRoleIds(List<String> roleList);

    String getUserNameByUserId(String userId);

    List<String> getPlatformRoleNameByRoleIds(List<String> roles);
}