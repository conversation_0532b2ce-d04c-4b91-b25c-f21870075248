package com.stock.service.platform.common.system.service;

import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.service.BaseService;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.user.dto.UserDto;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.dto.RegTreeDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.service.StockService;
import com.stock.service.platform.common.sysCodeConvert.CodeConvert;
import com.stock.service.platform.common.system.dao.InfoSyncMapper;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.CompanyOrgDto;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.util.PasswordUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.person.dto.PersonDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 信息同步功能，  同步 组织架构、角色、上市公司、用户 等信息到平台
 * 上线当天使用，然后废弃
 */
@Service
public class InfoSyncService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(InfoSyncService.class);

    @Resource
    private InfoSyncMapper infoSyncMapper;

    @Resource
    private CommonBizMapper commonBizMapper;

    @Resource
    private SaUserCompanyMapMapper saUserCompanyMapMapper;

    @Resource
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    @Resource
    private UserRoleMapMapper userRoleMapMapper;

    @Resource
    private SaCompanyMapper saCompanyMapper;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private StockService stockService;

    @Resource
    private SaSystemUtil saSystemUtil;

    @Resource
    private TopicProducer topicProducer;

    @Resource
    private CompanyContactMapper companyContactMapper;

    @Resource
    private QuestionnaireService questionnaireService;

    @Resource
    private Environment env;

    @Resource
    private OuterRoleMapper outerRoleMapper;

    @Resource
    private CompanyDtoMapper companyDtoMapper;

    @Resource
    private UserDtoMapper userDtoMapper;

    @Resource
    private PersonMapper personMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    /**
     * 同步 组织架构
     *
     * @return 新增条数
     */
    public int importDeptInfo(List<MultipartFile> files) throws Exception {
        long startTime = System.nanoTime();
        // 文件校验
        this.validateFile(files);
        // 清表
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andOrgTypeNotEqualTo("1").andOrgTypeNotEqualTo("4"); // 1总公司,4上市公司,不删
        orgMapper.deleteByExample(orgExample);
        int insertNum = 0;
        MultipartFile multipartFile = files.get(0);
        try (InputStream is = multipartFile.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            // 获取sheet中的行数
            int rowCount = getExcelRealRow(sheet);
            // 需要发送的mq集合
            List<Map<String, Object>> infoList = new ArrayList<>();
            // 逐行读取数据
            for (int i = 0; i <= rowCount; i++) {
                if (i == 0) continue; // 第一行是表头，跳过第一行
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell0 = row.getCell(0); // 主键(id)
                    Cell cell1 = row.getCell(1); // 机构代码(org_code)
                    Cell cell2 = row.getCell(2); // 组织机构名称(org_name)
                    Cell cell3 = row.getCell(3); // 上级组织机构ID(p_org_id)
                    Cell cell4 = row.getCell(4); // 排序编号(order_id)
                    Cell cell5 = row.getCell(5); // 机构层级(level,总部1,分公司2,营业部3)
                    Cell cell6 = row.getCell(6); // 机构类型(org_type,总部1,分公司2,营业部3)
                    Cell cell7 = row.getCell(7); // 删除标志(status,删除0,有效1)
                    Cell cell8 = row.getCell(8); // 是否允许所属营业部相互查看(threshold_flag,不允许0,允许1)
                    Cell cell9 = row.getCell(9); // 来源(source,回购管理HGGL,股权激励GQJL,合规交易TDGF)
                    Org record = new Org();
                    record.setId(cellToString(cell0));
                    record.setOrgCode(cellToString(cell1));
                    record.setOrgName(cellToString(cell2));
                    record.setpOrgId(cellToString(cell3));
                    record.setOrderId(cellToInt(cell4));
                    record.setLevel(cellToString(cell5));
                    record.setOrgType(cellToString(cell6));
                    record.setStatus(cellToString(cell7));
                    record.setThresholdFlag(cellToString(cell8));
                    record.setSource(cellToString(cell9));
                    record.setCreateTime(new Date());
                    record.setCreateUser(getUserInfo().getPersonName());
                    record.setUpdateTime(new Date());
                    record.setUpdateUser(getUserInfo().getPersonName());
                    infoSyncMapper.insertOrgSelective(record);
                    insertNum++;
                    // 向其他微服发送消息==============================================================================
                    Map<String, Object> info = new HashMap<>();
                    if ("2".equals(record.getOrgType())) {
                        record.setBranchName(record.getOrgName());
                        record.setBranchCode(record.getOrgCode());
                    }
                    if ("3".equals(record.getOrgType())) {
                        record.setBusinessDepartment(record.getOrgName());
                        record.setDepCode(record.getOrgCode());
                        record.setTradeOrgId(record.getpOrgId());
                    }
                    List<Org> orgList = new ArrayList<>();
                    String id = record.getpOrgId();
                    int maxLevel = orgMapper.selOrgLevel();
                    for (int j = 0; j <= maxLevel; j++) {
                        Org org1 = orgMapper.selParentById(id);
                        if (org1 == null) break;
                        orgList.add(org1);
                        if ("1".equals(org1.getLevel()) || "1".equals(org1.getOrgType())) break;
                        id = org1.getpOrgId();
                    }
                    info.put("org", record);
                    info.put("orgP", orgList);
                    info.put("userId", getUserInfo().getInfo().get("id"));
                    info.put("isBranchCompany", "2".equals(record.getOrgType()) ? "1" : "0"); // 1 分公司, 0 营业部
                    infoList.add(info);
                }
            }
            // 平台逻辑处理完了,开始发送mq
            for (Map<String, Object> info : infoList) {
                String uid = UUID.randomUUID().toString();
                MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType("HGGL,GQJL");
                send.setBusinessType(TopicConstant.BUS_TYPE_ORG);
                send.setOperationType(TopicConstant.OP_TYPE_INSERT);
                send.setInfo(info);
                saSystemUtil.insertMqMessage(send);
                topicProducer.sendTopic(info, "HGGL,GQJL", TopicConstant.BUS_TYPE_ORG, TopicConstant.OP_TYPE_INSERT, uid);
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
        long endTime = System.nanoTime();
        logger.info("导入组织架构总耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
        return insertNum;
    }

    /**
     * 同步 角色
     */
    public int importRoleInfo(List<MultipartFile> files) throws IOException {
        long startTime = System.nanoTime();
        // 文件校验
        this.validateFile(files);
        // 清表
        OuterRoleExample outerRoleExample = new OuterRoleExample();
        outerRoleExample.createCriteria();
        outerRoleMapper.deleteByExample(outerRoleExample);
        int insertNum = 0;
        MultipartFile multipartFile = files.get(0);
        try (InputStream is = multipartFile.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            // 获取sheet中的行数
            int rowCount = getExcelRealRow(sheet);
            // 逐行读取数据
            for (int i = 0; i <= rowCount; i++) {
                if (i == 0) continue; // 第一行是表头，跳过第一行
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell0 = row.getCell(0); // 主键(id)
                    Cell cell1 = row.getCell(1); // 角色名(role_name)
                    Cell cell2 = row.getCell(2); // 角色描述(role_desc)
                    Cell cell3 = row.getCell(3); // 角色类型(role_type)
                    OuterRole record = new OuterRole();
                    String id = cellToString(cell0);
                    record.setId(id);
                    if (id.startsWith("TDGF")) record.setSysCode("TDGF");
                    else if (id.startsWith("GQJL")) record.setSysCode("GQJL");
                    else throw new IllegalArgumentException("数据错误,id并没有以系统代码开头");
                    record.setSource("1");
                    record.setRoleName(cellToString(cell1));
                    record.setRoleDesc(cellToString(cell2));
                    record.setRoleType(cellToString(cell3));
                    infoSyncMapper.insertRoleSelective(record);
                    insertNum++;
                }
            }
        } catch (IOException e) {
            throw new IOException(e);
        }
        long endTime = System.nanoTime();
        logger.info("导入角色总耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
        return insertNum;
    }

    /**
     * 同步 上市公司
     */
    public int importListedCompanyInfo(List<MultipartFile> files) throws Exception {
        long startTimeTotal = System.nanoTime();
        // 文件校验
        this.validateFile(files);
        // --------------------------------------------清表--------------------------------------------
        // 组织架构
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andOrgTypeEqualTo("4"); // 4是上市公司
        orgMapper.deleteByExample(orgExample);
        // 上市公司
        CompanyDtoExample companyDtoExample = new CompanyDtoExample();
        companyDtoMapper.deleteByExample(companyDtoExample);
        // 上市公司联系人
        CompanyContactExample companyContactExample = new CompanyContactExample();
        companyContactMapper.deleteByExample(companyContactExample);
        // -----------------------------------选择器的数据,用于数据映射-----------------------------------
        // 公司性质
        List<RegTreeDto> companyNatureList = commonBizMapper.getSaCodeByCode("REP_COMPANY_NATURE");
        // 市场
        List<RegTreeDto> stockJtList = commonBizMapper.getSaCodeByCode("STOCK_JT");
        //-------------------------------------------------------------------------------------------
        int insertNum = 0;
        List<Map<String, Object>> infoList = new ArrayList<>();
        MultipartFile multipartFile = files.get(0);
        try (InputStream is = multipartFile.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            // 获取sheet中的行数
            int rowCount = getExcelRealRow(sheet);
            // 逐行读取数据
            for (int i = 0; i <= rowCount; i++) {
                if (i == 0) continue; // 第一行是表头，跳过第一行
                long startTime = System.nanoTime();
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell0 = row.getCell(0); // 主键(id)
                    Cell cell1 = row.getCell(1); // 证券代码(companyCode)
                    Cell cell2 = row.getCell(2); // 证券名称(companyStockName)
                    Cell cell3 = row.getCell(3); // 公司名称(zhName)
                    Cell cell4 = row.getCell(4); // 公司简称(zhSortName)
                    //Cell cell5 = row.getCell(5); // 板块(marketStockType)
                    Cell cell6 = row.getCell(6); // 公司登记日期(registerDate)
                    Cell cell7 = row.getCell(7); // 公司性质(orgform)
                    Cell cell8 = row.getCell(8); // 注册地址(registerAddress)
                    Cell cell9 = row.getCell(9); // 公司注册资金(元)(registerCapital)
                    Cell cell10 = row.getCell(10); // Email(companyMail)
                    Cell cell11 = row.getCell(11); // 公司上市日期(listingDate)
                    Cell cell12 = row.getCell(12); // 统一社会信用代码(socialUnifiedCreditCode)
                    Cell cell13 = row.getCell(13); // 发行价格(issuePrice)
                    Cell cell14 = row.getCell(14); // A股股本(capitalStockA)
                    Cell cell15 = row.getCell(15); // B股股本(capitalStockB)
                    Cell cell16 = row.getCell(16); // H股股本(capitalStockH)
                    Cell cell17 = row.getCell(17); // 总股本(capitalStockTotal)
                    Cell cell18 = row.getCell(18); // 法定代表人(corporate)
                    Cell cell19 = row.getCell(19); // 法人证件号码(corporateIdNumber)
                    Cell cell20 = row.getCell(20); // 服务营业部(organisationNo)
                    Cell cell21 = row.getCell(21); // 公司电话(phone)
                    Cell cell22 = row.getCell(22); // 备注信息(remark)
                    Cell cell23 = row.getCell(23); // 删除标志(deleteflag,1删除,0未删除,2删除中)
                    Cell cell24 = row.getCell(24); // 最上级公司CompanyCode(parentCompanyCode)
                    Cell cell25 = row.getCell(25); // 父公司id(parentId)
                    Cell cell26 = row.getCell(26); // 联系人(contact)
                    Cell cell27 = row.getCell(27); // 来源
                    Cell cell28 = row.getCell(28); // 市场(market)
                    SaCompanyDto companyDto = new SaCompanyDto();
                    companyDto.setId(cellToString(cell0));
                    // 证券代码
                    companyDto.setCompanyCode(cellToString(cell1));
                    // 证券名称
                    companyDto.setCompanyStockName(cellToString(cell2));
                    // 公司名称
                    companyDto.setZhName(cellToString(cell3));
                    // 公司简称
                    companyDto.setZhSortName(cellToString(cell4));
                    // 板块-码值
                    String marketStockType = companyDtoMapper.getMarketStockTypeByCompanyCode(companyDto.getCompanyCode());
                    if (StringUtils.isEmpty(marketStockType)) {
                        marketStockType = "99";
                    }
                    companyDto.setMarketStockType(marketStockType);
                    // 公司登记日期
                    companyDto.setRegisterDate(cellToDate(cell6));
                    // 公司性质-码值
                    if (StringUtils.isNotEmpty(cellToString(cell7))) {
                        // 通过名字映射码值
                        Optional<RegTreeDto> xingZhiOptional = companyNatureList.stream().filter(regTreeDto -> cellToString(cell7).equals(regTreeDto.getLabelName())).findAny();
                        if (xingZhiOptional.isPresent()) {
                            RegTreeDto regTreeDto = xingZhiOptional.get();
                            companyDto.setOrgform(regTreeDto.getLabelValue());
                        } else {
                            throw new RuntimeException("未匹配的公司性质选项文字");
                        }
                    } else {
                        companyDto.setOrgform(null);
                    }
                    // 注册地址
                    companyDto.setRegisterAddress(cellToString(cell8));
                    // 注册资本(元)
                    companyDto.setRegisterCapital(cellToBigDecimal(cell9));
                    // 公司邮箱
                    companyDto.setCompanyMail(cellToString(cell10));
                    // 上市日期
                    companyDto.setListingDate(cellToDate(cell11));
                    // 社会统一信用代码
                    companyDto.setSocialUnifiedCreditCode(cellToString(cell12));
                    if (StringUtils.isEmpty(companyDto.getSocialUnifiedCreditCode())) {
                        String socialUnifiedCreditCode = companyDtoMapper.getSocialUnifiedCreditCodeByCompanyCode(companyDto.getCompanyCode());
                        if (StringUtils.isEmpty(marketStockType)) {
                            socialUnifiedCreditCode = null;
                        }
                        companyDto.setSocialUnifiedCreditCode(socialUnifiedCreditCode);
                    }
                    // 发行价格
                    companyDto.setIssuePrice(cellToBigDecimal(cell13));
                    // A股总股本
                    companyDto.setCapitalStockA(cellToBigDecimal(cell14));
                    // B股总股本
                    companyDto.setCapitalStockB(cellToBigDecimal(cell15));
                    // H股总股本
                    companyDto.setCapitalStockH(cellToBigDecimal(cell16));
                    // 总股本
                    companyDto.setCapitalStockTotal(cellToBigDecimal(cell17));
                    // 法人
                    companyDto.setCorporate(cellToString(cell18));
                    // 法人身份证号
                    companyDto.setCorporateIdNumber(cellToString(cell19));
                    // 服务营业部 -- 逗号分隔
                    companyDto.setOrganisationNo(cellToString(cell20));
                    // 公司电话
                    companyDto.setPhone(cellToString(cell21));
                    // 备注
                    companyDto.setRemark(cellToString(cell22));
                    // （1 删除 0 未删除 2删除中）
                    companyDto.setDeleteflag(cellToString(cell23));
                    // 最上级公司CompanyCode
                    companyDto.setParentCompanyCode(cellToString(cell24));
                    // 父公司id
                    companyDto.setParentId(cellToString(cell25));
                    // ++++联系人处理++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                    String contactsString = cellToString(cell26);
                    if (StringUtils.isNotBlank(contactsString)) {
                        String[] contactsArray = contactsString.split(";");
                        List<CompanyContact> contacts = new ArrayList<>();
                        for (String contact : contactsArray) {
                            String[] contactArray = contact.split(",");
                            CompanyContact companyContact = new CompanyContact();
                            companyContact.setContactName(contactArray[0]);
                            if ("股权激励".equals(cellToString(cell27))) {
                                companyContact.setContactPhone(contactArray[1]);
                            } else {
                                Profiles profiles = Profiles.of("dev","prepub-sz");
                                if (!env.acceptsProfiles(profiles)) {
                                    companyContact.setContactPhone(questionnaireService.enCodeAec(contactArray[1]));
                                } else {
                                    companyContact.setContactPhone(contactArray[1]);
                                }
                            }
                            companyContact.setContactEmail(contactArray[2]);
                            companyContact.setContactSystem(contactArray[3]);
                            contacts.add(companyContact);
                        }
                        companyDto.setContacts(contacts);
                    }
                    // +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
                    // 数据来源
                    companyDto.setSource(cellToString(cell27));
                    if (StringUtils.isNotEmpty(cellToString(cell28))) {
                        // 通过名字映射码值
                        RegTreeDto stockJt = stockJtList.stream().filter(regTreeDto -> cellToString(cell28).equals(regTreeDto.getLabelName())).findAny().orElseThrow(() -> new Exception("未匹配的市场选项文字"));
                        // 市场-码值
                        companyDto.setMarket(stockJt.getLabelValue());
                    } else {
                        companyDto.setMarket(null);
                    }
                    long endTime = System.nanoTime();
                    logger.info(companyDto.getZhName() + "数据整理耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
                    StripTrailingZerosUtil.convertBigDecimalsToPlainString(companyDto);
                    infoList.add(this.createCompany(companyDto, getUserInfo()));
                    insertNum++;
                }
            }
            //insertNum = infoList.size();
            long startTime = System.nanoTime();
            for (Map<String, Object> info : infoList) {
                // 向其他微服发送消息
                String uid = UUID.randomUUID().toString();
                String systemType = saSystemUtil.getSysCode();
                ModelMapper modelMapper = new ModelMapper();
                SaCompanyDto saCompany = modelMapper.map(info.get("saCompany"), SaCompanyDto.class);
                StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompany);
                if (StringUtils.isNotEmpty(saCompany.getParentId())) {
                    systemType = "GQJL";
                }
                MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType(systemType);
                send.setBusinessType(TopicConstant.BUS_TYPE_COMPANY);
                send.setOperationType(TopicConstant.OP_TYPE_INSERT);
                send.setInfo(info);
                saSystemUtil.insertMqMessage(send);
                topicProducer.sendTopic(info, systemType, TopicConstant.BUS_TYPE_COMPANY, TopicConstant.OP_TYPE_INSERT, uid);
            }
            long endTime = System.nanoTime();
            logger.info("上市公司发送MQ消息耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
        } catch (Exception e) {
            throw new Exception(e);
        }
        long endTimeTotal = System.nanoTime();
        logger.info("导入上市公司总耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTimeTotal - startTimeTotal) / 1_000_000_000);
        return insertNum;
    }

    /**
     * 同步 内部用户
     */
    public int importInnerUserInfo(List<MultipartFile> files) throws IOException {
        long startTimeTotal = System.nanoTime();
        // 校验
        this.validateFile(files);
        // 清表***********************************************************************
        UserDtoExample userDtoExample = new UserDtoExample();
        userDtoExample.createCriteria().andUserTypeEqualTo("0").andIdNotEqualTo("1");
        List<com.stock.service.platform.common.entity.UserDto> userDtos = userDtoMapper.selectByExample(userDtoExample);
        List<String> deleteIdList = userDtos.stream().map(com.stock.service.platform.common.entity.UserDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            // 清理user表
            UserDtoExample userDtoExample1 = new UserDtoExample();
            userDtoExample1.createCriteria().andIdIn(deleteIdList);
            userDtoMapper.deleteByExample(userDtoExample1);
            // 清理person表
            PersonExample personExample = new PersonExample();
            personExample.createCriteria().andIdIn(deleteIdList);
            personMapper.deleteByExample(personExample);
            // 清理outerRoleUserMap表
            OuterRoleUserMapExample roleUserMapExample = new OuterRoleUserMapExample();
            roleUserMapExample.createCriteria().andUserIdIn(deleteIdList);
            outerRoleUserMapMapper.deleteByExample(roleUserMapExample);
            // 清理userRoleMap表
            UserRoleMapExample userRoleMapExample = new UserRoleMapExample();
            userRoleMapExample.createCriteria().andUserIdIn(deleteIdList);
            userRoleMapMapper.deleteByExample(userRoleMapExample);
            // 清理saUserCompanyMap表
            SaUserCompanyMapExample saUserCompanyMapExample = new SaUserCompanyMapExample();
            saUserCompanyMapExample.createCriteria().andUserIdIn(deleteIdList);
            saUserCompanyMapMapper.deleteByExample(saUserCompanyMapExample);
        }
        // *****************************************************************
        List<PersonDto> mqList = new ArrayList<>();
        int insertNum = 0;
        MultipartFile multipartFile = files.get(0);
        try (InputStream is = multipartFile.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            // 获取sheet中的行数
            int rowCount = getExcelRealRow(sheet);
            // 逐行读取数据
            for (int i = 0; i <= rowCount; i++) {
                if (i == 0) continue; // 第一行是表头，跳过第一行
                long startTime = System.nanoTime();
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell0 = row.getCell(0); // 主键(id)
                    Cell cell1 = row.getCell(1); // 姓名(personName)
                    Cell cell2 = row.getCell(2); // 用户名(userName)
                    Cell cell3 = row.getCell(3); // 所属部门码值(isMgr,总部1,分公司2,营业部3)
                    Cell cell4 = row.getCell(4); // 部门id(orgIdList)
                    Cell cell5 = row.getCell(5); // 手机(telephone)
                    Cell cell6 = row.getCell(6); // mail(mail)
                    Cell cell7 = row.getCell(7); // 备注(remark)
                    Cell cell8 = row.getCell(8); // 接入系统角色(checkedRoleIds)
                    Cell cell9 = row.getCell(9); // 锁定状态(lock_state)
                    Cell cell10 = row.getCell(10); // 平台角色(roleId)
                    SaUser user = new SaUser();
                    user.setId(cellToString(cell0));
                    user.setCompanyId(""); // 所属上市公司 内部用户没有
                    user.setOrgNo(cellToString(cell4)); // 所属部门
                    user.setUserName(cellToString(cell2));
                    user.setPassword(passwordEncoder.encode(DigestUtils.md5DigestAsHex(LogicConstant.DEFAULT_PASSWORD.getBytes(StandardCharsets.UTF_8))));
                    user.setRealName(cellToString(cell1));
                    user.setTelephone(cellToString(cell5));
                    user.setMail(cellToString(cell6));
                    user.setRemark(cellToString(cell7));
                    user.setPasswordUpdateTime(new Date());
                    user.setErrorNum(0);
                    user.setLockType("0");
                    user.setLockState(cellToString(cell9)); // 锁定状态
                    user.setCreateUser("admin");
                    user.setCreateTime(new Date());
                    user.setUpdateUser("admin");
                    user.setUpdateTime(new Date());
                    user.setStatus("1");
                    user.setUserType("0"); // 内部用户固定0
                    user.setIsFirst("1");
                    user.setSort(1);
                    infoSyncMapper.insertUserSelective(user);
                    insertNum++;
                    PersonWithBLOBs personWithBLOBs = new PersonWithBLOBs();
                    personWithBLOBs.setId(cellToString(cell0));
                    personWithBLOBs.setPersonType("0"); // 内部用户固定0
                    personWithBLOBs.setPersonName(cellToString(cell1));
                    personWithBLOBs.setOrgId(cellToString(cell4)); // 所属部门
                    personWithBLOBs.setTelephone(cellToString(cell5));
                    personWithBLOBs.setMail(cellToString(cell6));
                    personWithBLOBs.setRemark(cellToString(cell7));
                    personWithBLOBs.setUserId(cellToString(cell0));
                    personWithBLOBs.setCompanyCode("601066"); // 定死中信建投的股票码
                    personWithBLOBs.setSource("GQJL");  // 内部只导股权激励
                    personWithBLOBs.setCreateUser(getUserInfo().getPersonName());
                    personWithBLOBs.setCreateTime(new Date());
                    personWithBLOBs.setUpdateUser(getUserInfo().getPersonName());
                    personWithBLOBs.setUpdateTime(new Date());
                    personWithBLOBs.setStatus("1");
                    personWithBLOBs.setHiddenKey(UUID.randomUUID().toString());
                    infoSyncMapper.insertPersonSelective(personWithBLOBs);
                    //向其他微服发送消息
                    PersonDto personDto = new PersonDto();
                    personDto.setId(cellToString(cell0));
                    personDto.setOrgId(cellToString(cell4)); // 所属部门
                    personDto.setOrgIdList(Arrays.asList(cellToString(cell4).split(","))); // 所属部门List
                    personDto.setPersonName(cellToString(cell1));
                    personDto.setUserName(cellToString(cell2));
                    Profiles profiles1 = Profiles.of("dev", "prepub-sz");
                    if (!env.acceptsProfiles(profiles1)) {
                        personDto.setTelephone(questionnaireService.deCodeAes(cellToString(cell5)));
                    } else {
                        personDto.setTelephone(cellToString(cell5));
                    }
                    personDto.setSource("GQJL");  // 内部只导股权激励
                    personDto.setRemark(cellToString(cell7));
                    personDto.setMail(cellToString(cell6));
                    personDto.setUserType("0"); // 内部用户固定0
                    personDto.setUserTypeCode("0");
                    personDto.setIsMgr(cellToString(cell3));
                    personDto.setIsAdmin("0");
                    personDto.setIsLock("0");
                    personDto.setLockCnt("0");
                    personDto.setBusinessDepartmentStr(cellToString(cell4));
                    personDto.setStatus("1");
                    personDto.setCompanyCode("601066"); // 定死中信建投的股票码
                    personDto.setPersonType("0"); // 内部用户固定0
                    personDto.setRealName(cellToString(cell1));
                    personDto.setPhone(cellToString(cell5));
                    personDto.setUserId(cellToString(cell0));
                    personDto.setCreateUser(getUserInfo().getPersonName());
                    personDto.setCreateTime(new Date());
                    personDto.setUpdateUser(getUserInfo().getPersonName());
                    personDto.setUpdateTime(new Date());
                    personDto.setHiddenKey(UUID.randomUUID().toString());
                    mqList.add(personDto);
                    // 接入系统角色  新增
                    String[] checkedRoleIds = cellToString(cell8).split(",");
                    for (String roleId : checkedRoleIds) {
                        if (StringUtils.isNotEmpty(roleId)) {
                            OuterRoleUserMap outerRoleUserMap = new OuterRoleUserMap();
                            outerRoleUserMap.setUserId(cellToString(cell0));
                            outerRoleUserMap.setRoleId(roleId);
                            outerRoleUserMap.setSource("1");
                            outerRoleUserMap.setSysCode(roleId.substring(0, 4));
                            outerRoleUserMapMapper.insertSelective(outerRoleUserMap);
                        }
                    }
                    // 平台角色
                    UserRoleMap userRoleMap = new UserRoleMap();
                    userRoleMap.setUpdateUser("admin");
                    userRoleMap.setUserId(cellToString(cell0));
                    userRoleMap.setRoleId(cellToString(cell10));
                    userRoleMap.setUpdateTime(new Date());
                    userRoleMapMapper.insertSelective(userRoleMap);
                }
                long endTime = System.nanoTime();
                logger.info("导入内部用户数据整理入库耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
            }
            long startTime = System.nanoTime();
            for (PersonDto personDto : mqList) {
                sendSyncPersonInfoMsg(personDto, personDto.getUserName());
            }
            long endTime = System.nanoTime();
            logger.info("导入内部用户发送MQ消息耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
        } catch (IOException e) {
            throw new IOException(e);
        }
        long endTimeTotal = System.nanoTime();
        logger.info("导入内部人员总耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTimeTotal - startTimeTotal) / 1_000_000_000);
        return insertNum;
    }

    /**
     * 同步 外部用户
     */
    public int importOuterUserInfo(List<MultipartFile> files) throws Exception {
        long startTimeTotal = System.nanoTime();
        // 校验
        this.validateFile(files);
        // 清表***********************************************************************
        UserDtoExample userDtoExample = new UserDtoExample();
        userDtoExample.createCriteria().andUserTypeEqualTo("1").andIdNotEqualTo("1");
        List<com.stock.service.platform.common.entity.UserDto> userDtos = userDtoMapper.selectByExample(userDtoExample);
        List<String> deleteIdList = userDtos.stream().map(com.stock.service.platform.common.entity.UserDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIdList)) {
            UserDtoExample userDtoExample1 = new UserDtoExample();
            userDtoExample1.createCriteria().andIdIn(deleteIdList);
            userDtoMapper.deleteByExample(userDtoExample1);
            PersonExample personExample = new PersonExample();
            personExample.createCriteria().andIdIn(deleteIdList);
            personMapper.deleteByExample(personExample);
            OuterRoleUserMapExample roleUserMapExample = new OuterRoleUserMapExample();
            roleUserMapExample.createCriteria().andUserIdIn(deleteIdList);
            outerRoleUserMapMapper.deleteByExample(roleUserMapExample);
            UserRoleMapExample userRoleMapExample = new UserRoleMapExample();
            userRoleMapExample.createCriteria().andUserIdIn(deleteIdList);
            userRoleMapMapper.deleteByExample(userRoleMapExample);
            SaUserCompanyMapExample saUserCompanyMapExample = new SaUserCompanyMapExample();
            saUserCompanyMapExample.createCriteria().andUserIdIn(deleteIdList);
            saUserCompanyMapMapper.deleteByExample(saUserCompanyMapExample);
            // 清空sa_company表的system_state字段
            companyDtoMapper.resettingSystemState();
        }
        // *****************************************************************
        int insertNum = 0;
        MultipartFile multipartFile = files.get(0);
        // 查上市公司code和org、company表的关联---目的是通过companyCode找orgId
        List<CompanyOrgDto> companyOrgDtoList = infoSyncMapper.selectCompanyList();
        try (InputStream is = multipartFile.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            // 获取sheet中的行数
            int rowCount = getExcelRealRow(sheet);
            // 要给合规发送的mq集合
            List<Map<String,Object>> infoList = new ArrayList<>();
            // 逐行读取数据
            for (int i = 0; i <= rowCount; i++) {
                if (i == 0) continue; // 第一行是表头，跳过第一行
                long startTime = System.nanoTime();
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell cell0 = row.getCell(0); // 主键(id)
                    Cell cell1 = row.getCell(1); // 姓名(personName)
                    Cell cell2 = row.getCell(2); // 用户名(userName)
                    Cell cell3 = row.getCell(3); // 所属上市公司id(orgId)
                    Cell cell4 = row.getCell(4); // 所属上市公司code(companyCode)
                    Cell cell5 = row.getCell(5); // 职务(jobs)
                    Cell cell6 = row.getCell(6); // 手机(telephone)
                    Cell cell7 = row.getCell(7); // 邮箱(mail)
                    Cell cell8 = row.getCell(8); // 备注(remark)
                    Cell cell9 = row.getCell(9); // 证件号码(certificate)
                    Cell cell10 = row.getCell(10); // 子系统角色(checkedRoleIds)
                    Cell cell11 = row.getCell(11); // 用户锁定状态(lock_state)
                    Cell cell12 = row.getCell(12); // 平台角色(roleId)
                    Cell cell13 = row.getCell(13); // 来源(source)
                    Cell cell14 = row.getCell(14); // 外部公司证券代码(CompanyCode)
                    Cell cell15 = row.getCell(15); // 地址(address)
                    Cell cell16 = row.getCell(16); // 传真(fax)
                    Cell cell17 = row.getCell(17); // 电话(phone)
                    SaUser user = new SaUser();
                    user.setId(cellToString(cell0));
                    // 合规交易使用code导入上市公司 股权激励使用id
                    String orgNo;
                    if ("TDGF".equals(cellToString(cell13))) {
                        // 通过companyCode找companyId
                        CompanyOrgDto companyOrgDto1 = companyOrgDtoList.stream().filter(companyOrgDto -> cellToString(cell4).equals(companyOrgDto.getCompanyCode())).findAny().orElseThrow(() -> new RuntimeException("未找到对应的上市公司id")); // 因为是必填，如果没找到报错
                        user.setCompanyId(companyOrgDto1.getCompanyId()); // 所属上市公司
                        user.setOrgNo(companyOrgDto1.getOrgId()); //  这个字段是 关联上市公司在org表的orgId
                        orgNo = companyOrgDto1.getOrgId();
                    } else {
                        // 通过companyId找orgId
                        CompanyOrgDto companyOrgDto2 = companyOrgDtoList.stream().filter(companyOrgDto -> cellToString(cell3).equals(companyOrgDto.getCompanyId())).findAny().orElseThrow(() -> new RuntimeException("未找到对应的上市公司id")); // 因为是必填，如果没找到报错
                        user.setCompanyId(cellToString(cell3)); // 所属上市公司
                        user.setOrgNo(companyOrgDto2.getOrgId()); //  这个字段是 关联上市公司在org表的orgId
                        orgNo = companyOrgDto2.getOrgId();
                    }
                    user.setUserName(cellToString(cell2));
                    user.setPassword(passwordEncoder.encode(DigestUtils.md5DigestAsHex(PasswordUtil.generatePassword().getBytes(StandardCharsets.UTF_8))));
                    user.setRealName(cellToString(cell1));
                    if ("GQJL".equals(cellToString(cell13))) {
                        user.setTelephone(cellToString(cell6));
                    } else {
                        Profiles profiles = Profiles.of("dev","prepub-sz");
                        if (!env.acceptsProfiles(profiles)) {
                            user.setTelephone(questionnaireService.enCodeAec(cellToString(cell6)));
                        } else {
                            user.setTelephone(cellToString(cell6));
                        }
                    }
                    user.setMail(cellToString(cell7));
                    user.setRemark(cellToString(cell8));
                    user.setPasswordUpdateTime(new Date());
                    user.setErrorNum(0);
                    user.setLockType("0");
                    user.setLockState(String.valueOf(Double.valueOf(cellToString(cell11)).intValue())); // 锁定状态
                    user.setCreateUser(getUserInfo().getPersonName());
                    user.setCreateTime(new Date());
                    user.setUpdateUser(getUserInfo().getPersonName());
                    user.setUpdateTime(new Date());
                    user.setStatus("1");
                    user.setUserType("1"); // 外部1
                    user.setIsFirst("1");
                    user.setSort(getSortNumber(cellToString(cell2)));
                    infoSyncMapper.insertUserSelective(user);
                    insertNum++;
                    PersonWithBLOBs personWithBLOBs = new PersonWithBLOBs();
                    personWithBLOBs.setId(cellToString(cell0));
                    personWithBLOBs.setPersonType("1"); // 外部1
                    personWithBLOBs.setPersonName(cellToString(cell1));
                    personWithBLOBs.setOrgId(orgNo); // 所属部门
                    if ("GQJL".equals(cellToString(cell13))) {
                        personWithBLOBs.setTelephone(cellToString(cell6));
                    } else {
                        Profiles profiles = Profiles.of("dev","prepub-sz");
                        if (!env.acceptsProfiles(profiles)) {
                            personWithBLOBs.setTelephone(questionnaireService.enCodeAec(cellToString(cell6)));
                        } else {
                            personWithBLOBs.setTelephone(cellToString(cell6));
                        }
                    }
                    personWithBLOBs.setMail(cellToString(cell7));
                    personWithBLOBs.setCertificate(cellToString(cell9));  // 内部没有身份证号
                    personWithBLOBs.setCertificateType("SF");
                    personWithBLOBs.setRemark(cellToString(cell8));
                    personWithBLOBs.setUserId(cellToString(cell0));
                    personWithBLOBs.setCompanyCode(cellToString(cell14));
                    personWithBLOBs.setSource(cellToString(cell13));  // 内部只导股权激励
                    personWithBLOBs.setCreateUser(getUserInfo().getPersonName());
                    personWithBLOBs.setCreateTime(new Date());
                    personWithBLOBs.setUpdateUser(getUserInfo().getPersonName());
                    personWithBLOBs.setUpdateTime(new Date());
                    personWithBLOBs.setStatus("1");
                    personWithBLOBs.setJobs(cellToString(cell5));
                    personWithBLOBs.setHiddenKey(UUID.randomUUID().toString());
                    personWithBLOBs.setAddress(cellToString(cell15));
                    personWithBLOBs.setFax(cellToString(cell16));
                    personWithBLOBs.setPhone(cellToString(cell17));
                    infoSyncMapper.insertPersonSelective(personWithBLOBs);
                    // 上市公司管理  新增  两种情况，合规交易 需要根据code找orgid，股权激励需要根据companyId找orgId
                    String orgId;
                    if ("TDGF".equals(cellToString(cell13))) {
                        // 通过companyCode找orgId
                        CompanyOrgDto companyOrgDto1 = companyOrgDtoList.stream().filter(companyOrgDto -> cellToString(cell4).equals(companyOrgDto.getCompanyCode())).findAny().orElseThrow(() -> new RuntimeException("未找到对应的上市公司id")); // 因为是必填，如果没找到报错
                        orgId = companyOrgDto1.getOrgId();
                    } else {
                        // 通过companyId找orgId
                        CompanyOrgDto companyOrgDto2 = companyOrgDtoList.stream().filter(companyOrgDto -> cellToString(cell3).equals(companyOrgDto.getCompanyId())).findAny().orElseThrow(() -> new RuntimeException("未找到对应的上市公司id")); // 因为是必填，如果没找到报错
                        orgId = companyOrgDto2.getOrgId();
                    }
                    SaUserCompanyMap saUserCompanyMap = new SaUserCompanyMap();
                    saUserCompanyMap.setUserId(cellToString(cell0));
                    saUserCompanyMap.setCompanyId(orgId); // 不知道为什么这个companId给orgId； 外部用户给的是orgId，内部给的是companyid；
                    saUserCompanyMap.setUpdateUser(getUserInfo().getPersonName());
                    saUserCompanyMap.setUpdateTime(new Date());
                    saUserCompanyMapMapper.insertSelective(saUserCompanyMap);
                    // 接入系统角色  新增
                    String[] checkedRoleIds = cellToString(cell10).split(",");
                    // 查询该用户所属公司的系统开通状态
                    List<String> systemStateList = new ArrayList<>();
                    String systemState = companyDtoMapper.getSystemState(user.getCompanyId());
                    if (StringUtils.isNotEmpty(systemState)) {
                        String[] systemStateArray = systemState.split(",");
                        // 将数组转换为列表
                        systemStateList = new ArrayList<>(Arrays.asList(systemStateArray));
                    }
                    for (String roleId : checkedRoleIds) {
                        if (StringUtils.isNotEmpty(roleId)) {
                            OuterRoleUserMap outerRoleUserMap = new OuterRoleUserMap();
                            outerRoleUserMap.setUserId(cellToString(cell0));
                            outerRoleUserMap.setRoleId(roleId);
                            outerRoleUserMap.setSource("1");
                            outerRoleUserMap.setSysCode(roleId.substring(0, 4));
                            outerRoleUserMapMapper.insertSelective(outerRoleUserMap);
                            if (roleId.contains("HGGL")) {
                                systemStateList.add("回购管理");
                            } else if (roleId.contains("GQJL")) {
                                systemStateList.add("股权激励");
                            } else if (roleId.contains("TDGF")) {
                                systemStateList.add("合规交易");
                            }
                        }
                    }
                    // 刷新sa_company表的system_state字段
                    systemStateList = systemStateList.stream().distinct().collect(Collectors.toList());
                    companyDtoMapper.updateSystemState(user.getCompanyId(), String.join(",", systemStateList));
                    // 平台角色
                    UserRoleMap userRoleMap = new UserRoleMap();
                    userRoleMap.setUpdateUser(getUserInfo().getPersonName());
                    userRoleMap.setUserId(cellToString(cell0));
                    userRoleMap.setRoleId(cellToString(cell12));
                    userRoleMap.setUpdateTime(new Date());
                    userRoleMapMapper.insertSelective(userRoleMap);

                    // 给合规发送mq
                    if ("TDGF".equals(cellToString(cell13))) {
                        Map<String, Object> info = new HashMap<>();
                        info.put("userId", cellToString(cell0));
                        info.put("userName", cellToString(cell2));
                        info.put("companyCode", cellToString(cell4));
                        infoList.add(info);
                    }
                }
                long endTime = System.nanoTime();
                logger.info("导入外部用户数据整理入库耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
            }
            long startTime = System.nanoTime();
            for (Map<String,Object> info : infoList) {
                String uid = UUID.randomUUID().toString();
                MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType("TDGF");
                send.setBusinessType(TopicConstant.BUS_TYPE_USER);
                send.setOperationType(TopicConstant.OP_TYPE_OUT_USER);
                send.setInfo(info);
                saSystemUtil.insertMqMessage(send);
                topicProducer.sendTopic(info, "TDGF", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_OUT_USER, uid);
            }
            long endTime = System.nanoTime();
            logger.info("导入外部用户给合规发送MQ耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        long endTimeTotal = System.nanoTime();
        logger.info("导入外部人员总耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTimeTotal - startTimeTotal) / 1_000_000_000);
        return insertNum;
    }

    int getExcelRealRow(Sheet sheet) {
        boolean flag = false;
        for (int i = 1; i <= sheet.getLastRowNum(); ) {
            Row r = sheet.getRow(i);
            if (r == null) {
                // 如果是空行（即没有任何数据、格式），直接把它以下的数据往上移动
                sheet.shiftRows(i + 1, sheet.getLastRowNum(), -1);
                continue;
            }
            flag = false;
            for (Cell c : r) {
                if (c.getCellType() != Cell.CELL_TYPE_BLANK) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                i++;
                continue;
            } else {
                // 如果是空白行（即可能没有数据，但是有一定格式）
                if (i == sheet.getLastRowNum())// 如果到了最后一行，直接将那一行remove掉
                    sheet.removeRow(r);
                else//如果还没到最后一行，则数据往上移一行
                    sheet.shiftRows(i + 1, sheet.getLastRowNum(), -1);
            }
        }
        return sheet.getLastRowNum();
    }

    // 通用excel文件验证，
    private void validateFile(List<MultipartFile> files) throws IOException {
        if (CollectionUtils.isEmpty(files)) {
            throw new IllegalArgumentException("文件数为0");
        }
        MultipartFile multipartFile = files.get(0);
        // 判断是否是xls或xlsx类型
        if (!"application/vnd.ms-excel".equals(multipartFile.getContentType()) && !"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(multipartFile.getContentType())) {
            throw new IllegalArgumentException("文件不是.xls或.xlsx");
        }
        // ------------ 非空验证  如果表头带有必填则这列都要必填--------------------------------------
        InputStream is = multipartFile.getInputStream();
        Workbook workbook = new XSSFWorkbook(is);
        Sheet sheet = workbook.getSheetAt(0);
        int rowCount = getExcelRealRow(sheet);
        Row headerRow = sheet.getRow(0);
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell.getStringCellValue().contains("必填")) {
                for (int j = 1; j <= rowCount; j++) {
                    Row dataRow = sheet.getRow(j);
                    Cell dataCell = dataRow.getCell(i);
                    if (dataCell == null || dataCell.getCellTypeEnum() == CellType.BLANK) {
                        throw new RuntimeException(cell.getStringCellValue() + "是必填的, " + (j + 1) + " 行为空");
                    }
                }
            }
            if (cell.getStringCellValue().contains("不可重复")) {
                // 检查不可重复项
                Set<String> uniqueValues = new HashSet<>();
                Set<String> duplicateValues = new HashSet<>();
                for (int j = 1; j <= rowCount; j++) {
                    Row dataRow = sheet.getRow(j);
                    Cell dataCell = dataRow.getCell(i);
                    if (dataCell != null) {
                        String dataCellValue = dataCell.toString();
                        if (StringUtils.isNotEmpty(dataCellValue)) {
                            if (uniqueValues.contains(dataCellValue)) {
                                duplicateValues.add(dataCellValue);
                            } else {
                                uniqueValues.add(dataCellValue);
                            }
                        }
                    }
                }
                if (!duplicateValues.isEmpty()) {
                    throw new RuntimeException(cell.getStringCellValue() + "是不可重复的, 重复值: " + duplicateValues);
                }
            }
        }
        // --------------------------------------------------

    }

    // 输入userName例如A99900006 返回userName后两位
    private Integer getSortNumber(String userName) {
        if (userName.length() == 9 && userName.startsWith("A")) {
            try {
                String substring = userName.substring(7);
                return Integer.parseInt(substring);
            } catch (Exception e) {
                return null;
            }
        } else {
            return null;
        }
    }


    /**
     * 创建上市公司
     * 复制于： com.stock.service.platform.common.system.service.SaCompanyService#createOrUpdate
     * 区别只是新增的时候指定了主键id
     * 上线当天使用，然后废弃
     */
    private Map<String, Object> createCompany(SaCompanyDto dto, UserInfo userInfo) throws Exception {
        JsonResponse<String> response = new JsonResponse<>();
        Profiles profiles = Profiles.of("dev", "prepub-sz");
        String companyId;
        String companyName;
        Map<String, Object> info = new HashMap<>();
        if (StringUtils.isNotBlank(dto.getId())) { // 新增
            long startTime = System.nanoTime();
            SaCompanyDto insertCompany = new SaCompanyDto();
            BeanUtils.copyProperties(dto, insertCompany);
            insertCompany.setCompanyName(insertCompany.getZhName());
            insertCompany.setBusinessLicenseNumber(insertCompany.getSocialUnifiedCreditCode());
            insertCompany.setCompanyProperty(insertCompany.getOrgform());
            insertCompany.setCompanyRegisterDate(insertCompany.getRegisterDate());
            insertCompany.setCompanyListingDate(insertCompany.getListingDate());
            insertCompany.setRegisteredCapitalMoney(insertCompany.getRegisterCapital());
            insertCompany.setTotalCapitalStock(insertCompany.getCapitalStockTotal());
            insertCompany.setLegalPersonName(insertCompany.getCorporate());
            insertCompany.setLegalPersonIdNumber(insertCompany.getCorporateIdNumber());
            insertCompany.setBusinessDepartmentStr(insertCompany.getOrganisationNo());
            insertCompany.setCreateDate(new Date());
            insertCompany.setCreateBy(userInfo.getUserId());
            insertCompany.setModifiedDate(new Date());
            insertCompany.setModifiedBy(userInfo.getUserId());
            insertCompany.setCreateTime(new Date());
            insertCompany.setCreateUser(userInfo.getUserId());
            insertCompany.setUpdateTime(new Date());
            insertCompany.setUpdateUser(userInfo.getUserId());
            Map<String, String> subsystemCodes = CodeConvert.convertCode(insertCompany.getMarketStockType(), "marketStockType");
            insertCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));
            if (StringUtils.isEmpty(insertCompany.getCompanyCode())) {
                int codeSort = saCompanyMapper.getChildrenCompanyCode(insertCompany.getParentCompanyCode());
                insertCompany.setCompanyCode(insertCompany.getParentCompanyCode() + String.format("%04d", codeSort));
            }
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(insertCompany);
            infoSyncMapper.insertCompanySelective(insertCompany);
            // 修改联系人信息，先删后插
            CompanyContactExample example = new CompanyContactExample();
            example.createCriteria().andCompanyCodeEqualTo(dto.getCompanyCode());
            companyContactMapper.deleteByExample(example);
            if (CollectionUtils.isNotEmpty(dto.getContacts())) {
                dto.getContacts().forEach(item -> {
                    if (item.getContactSystem().equals("TDGF")) {
                        if (!env.acceptsProfiles(profiles)) {
                            item.setContactPhone(questionnaireService.enCodeAec(item.getContactPhone()));
                        } else {
                            item.setContactPhone(item.getContactPhone());
                        }
                    }
                    item.setCompanyCode(dto.getCompanyCode());
                    item.setUpdateTime(new Date());
                    item.setUpdateUser(userInfo.getUserId());
                    companyContactMapper.insertSelective(item);
                    if (!env.acceptsProfiles(profiles)) {
                        item.setContactPhone(questionnaireService.deCodeAes(item.getContactPhone()));
                    } else {
                        item.setContactPhone(item.getContactPhone());
                    }
                });
            }
            companyId = insertCompany.getId();
            companyName = insertCompany.getZhName();
            response.setResult("新增上市公司成功");
            int maxLevel = 0;
            String id = "";
            List<Org> orgList = new ArrayList<>();
            List<Org> recordList = new ArrayList<>();
            for (String pOrgId : dto.getOrganisationNo().split(",")) {
                // org表
                Org record = new Org();
                record.setCompanyId(companyId);
                record.setOrgName(companyName);
                record.setOrgType("4");
                record.setpOrgId(pOrgId);
                recordList.add(record);
                if (StringUtils.isNotEmpty(insertCompany.getParentId())) {
                    record.setChildrenCompanyFlag("1");
                } else {
                    record.setChildrenCompanyFlag("0");
                }
                orgMapper.insertSelective(record);
                id = record.getpOrgId();
                maxLevel = orgMapper.selOrgLevel();
                for (int i = 0; i <= maxLevel; i++) {
                    Org org = orgMapper.selParentById(id);
                    if (org == null) {
                        break;
                    }
                    orgList.add(org);
                    if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                        break;
                    }
                    id = org.getpOrgId();
                }
            }

            info.put("saCompany", insertCompany);
            info.put("org", recordList);
            info.put("orgP", orgList);
            info.put("userId", getUserInfo().getInfo().get("id"));
            long endTime = System.nanoTime();
            logger.info(insertCompany.getZhName() + "数据入库耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime - startTime) / 1_000_000_000);
            // 调用交易数据数据整合
            //if (StringUtils.isNotEmpty(insertCompany.getCompanyCode())) {
            //    long startTime1 = System.nanoTime();
            //    stockService.stockInfoMergeByCompanyCode(insertCompany.getCompanyCode());
            //    long endTime1 = System.nanoTime();
            //    logger.info(insertCompany.getZhName() + "交易数据整合耗时>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + (endTime1 - startTime1) / 1_000_000_000);
            //}
        }
        return info;
    }

    private void sendSyncPersonInfoMsg(PersonDto personDto, String oldUserName) {
        //向其他微服发送消息
        Map<String, Object> personTopic = new HashMap<>();
        OrgExample orgExample = new OrgExample();
        orgExample.createCriteria().andIdIn(Arrays.asList(personDto.getOrgId().split(",")));
        List<Org> orgListTopic = orgMapper.selectByExample(orgExample);
        int maxLevel;
        String id;
        List<Org> orgList = new ArrayList<>();
        for (Org orgTopic : orgListTopic) {
            id = orgTopic.getpOrgId();
            maxLevel = orgMapper.selOrgLevel();
            for (int i = 0; i <= maxLevel; i++) {
                Org org = orgMapper.selParentById(id);
                if (org == null) {
                    break;
                }
                orgList.add(org);
                if ("1".equals(org.getLevel()) || "1".equals(org.getOrgType())) {
                    break;
                }
                id = org.getpOrgId();
            }
        }

        UserDto userDto = new UserDto();
        // 把person数据给到user
        userDto.setId(personDto.getId());
        userDto.setOrgId(personDto.getOrgId());
        userDto.setUserName(personDto.getUserName());
        oldUserName = userDto.getUserName();
        userDto.setRealName(personDto.getPersonName());
        userDto.setTelephone(personDto.getTelephone());
        userDto.setMail(personDto.getMail());
        userDto.setRemark(personDto.getRemark());
        userDto.setUserType(personDto.getUserType());
        userDto.setHiddenKey(personDto.getHiddenKey());
        userDto.setMergeStatus("3");

        personTopic.put("org", orgListTopic);
        personTopic.put("orgP", orgList);
        personTopic.put("person", personDto);
        personTopic.put("user", userDto);
        personTopic.put("userId", getUserInfo().getInfo().get("id"));
        personTopic.put("oldUserName", oldUserName);
        personTopic.put("oldChildrenUserName", null);
        String uid = UUID.randomUUID().toString();
        MQMsgDto<Map<String, Object>> send = new MQMsgDto<>();
        send.setUid(uid);
        send.setSystemType(saSystemUtil.getSysCode());
        send.setBusinessType(TopicConstant.BUS_TYPE_USER);
        send.setOperationType(TopicConstant.OP_TYPE_INSERT);
        send.setInfo(personTopic);
        try {
            saSystemUtil.insertMqMessage(send);
            topicProducer.sendTopic(personTopic, saSystemUtil.getSysCode(), TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_INSERT, uid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 单元格转String
     * @param cell
     * @return cell To String
     */
    public static String cellToString(Cell cell) {
        if (cell == null) {
            return "";
        }
        String returnValue;
        switch (cell.getCellTypeEnum()) {
            case STRING:
                returnValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 格式化日期
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    returnValue = sdf.format(cell.getDateCellValue());
                } else {
                    // 将数字转换为字符串，去掉末尾的.0
                    returnValue = Double.toString(cell.getNumericCellValue());
                    if (returnValue.endsWith(".0")) {
                        returnValue = returnValue.substring(0, returnValue.length() - 2);
                    }
                }
                break;
            default:
                returnValue = "";
                break;
        }
        return returnValue;
    }

    /**
     * 单元格转int
     * @param cell
     * @return int
     */
    public static int cellToInt(Cell cell) {
        if (cell == null) {
            return 0;
        }
        try {
            switch (cell.getCellTypeEnum()) {
                case STRING:
                    // 尝试将字符串转换为整数
                    return Integer.parseInt(cell.getStringCellValue().trim());
                case NUMERIC:
                    // 直接转换为整数，注意这里可能会丢失小数部分
                    return (int) cell.getNumericCellValue();
                default:
                    // 对于其他类型，返回默认值
                    return 0;
            }
        } catch (NumberFormatException e) {
            // 如果转换过程中出现异常，返回默认值
            return 0;
        }
    }

    /**
     * 单元格转Date
     * @param cell
     * @return Date
     */
    public static Date cellToDate(Cell cell) {
        if (cell == null) {
            return null;
        }
        try {
            switch (cell.getCellTypeEnum()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        // 如果是日期格式，直接转换为Date
                        return cell.getDateCellValue();
                    } else {
                        // 如果是数字但不是日期格式，不处理
                        return null;
                    }
                case STRING:
                    // 尝试解析字符串格式的日期
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
                    String dateString = cell.getStringCellValue().trim();
                    return sdf.parse(dateString);
                default:
                    return null;
            }
        } catch (Exception e) {
            // 如果转换过程中出现异常，返回null
            return null;
        }
    }

    /**
     * 单元格转BigDecimal
     * @param cell
     * @return BigDecimal
     */
    public static BigDecimal cellToBigDecimal(Cell cell) {
        if (cell == null) {
            return null;
        }
        try {
            switch (cell.getCellTypeEnum()) {
                case NUMERIC:
                    // 直接转换为BigDecimal
                    //return BigDecimal.valueOf(cell.getNumericCellValue());
                    return new BigDecimal(
                            new BigDecimal(String.valueOf(cell.getNumericCellValue())).stripTrailingZeros().toPlainString());
                case STRING:
                    // 尝试解析字符串格式的数字
                    String numericString = cell.getStringCellValue().trim();
                    return new BigDecimal(numericString);
                default:
                    // 对于其他类型，返回null
                    return null;
            }
        } catch (NumberFormatException e) {
            // 如果转换过程中出现异常，返回null
            return null;
        }
    }
}
