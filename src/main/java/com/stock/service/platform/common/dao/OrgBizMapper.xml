<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.OrgBizMapper">

<!--  <select id="selectOrgDictByIsMgr" resultType="java.util.Map" parameterType="java.util.Map">-->
<!--    select-->
<!--        so.id as `key`,-->
<!--        so.id as `value`,-->
<!--        so.org_name as text-->
<!--    from sa_org so-->
<!--    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'-->
<!--    where so.status = '1'-->
<!--    <if test="params.pId != null and params.pId != ''">-->
<!--        and (so.p_org_id = #{params.pId} or so.id = #{params.pId}-->
<!--                <if test="params.limitCodeStr != null and params.limitCodeStr != ''">-->
<!--                    or pso.id = #{params.limitCodeStr} or pso.p_org_id = #{params.limitCodeStr}-->
<!--                </if>-->
<!--            )-->
<!--    </if>-->
<!--    <if test="params.orgType != null and params.orgType != ''">-->
<!--      and so.org_type = #{params.orgType}-->
<!--    </if>-->
<!--  </select>-->

  <select id="selectOrgIdByBranch" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
    where so.status = '1'
    and so.org_type in ('2','3','4')
    and (so.id = #{orgId} or pso.id = #{orgId} or pso.p_org_id = #{orgId})
  </select>
  <select id="selectOrgIdByBranchBesb" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
    where so.status = '1'
    and so.org_type in ('3','4')
    and (FIND_IN_SET(pso.id,#{orgId}) or FIND_IN_SET(pso.p_org_id,#{orgId}))
  </select>
  <select id="selectOrgIdByDept" resultType="java.lang.String" parameterType="java.lang.String">
    select group_concat(so.id)
    from sa_org so
    where so.status = '1'
    and so.org_type in ('3','4')
    and (FIND_IN_SET(so.id,#{orgId}) or FIND_IN_SET(so.p_org_id,#{orgId}))
  </select>
  <select id="checkBranchIfShare" resultType="java.lang.String" parameterType="java.lang.String">
      select group_concat(pso.id) from sa_org so left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
      where FIND_IN_SET(so.id, #{orgId}) and pso.id is not null and pso.threshold_flag = '1'
  </select>
<!--  <select id="selectByCompanyId" parameterType="java.lang.String" resultType="com.stock.service.platform.common.entity.Org">-->
<!--    select-->
<!--    org.*-->
<!--    from sa_org org,sa_company company-->
<!--    where org.company_id = company.id-->
<!--    and company.id= #{companyId}-->
<!--  </select>-->
<!--  <select id="selectByUserId" parameterType="java.lang.String" resultType="com.stock.service.platform.common.entity.Org">-->
<!--    SELECT org.*-->
<!--    FROM sa_org org-->
<!--    WHERE org.id = (SELECT org_id FROM sa_person WHERE user_id = #{userId})-->
<!--  </select>-->
</mapper>