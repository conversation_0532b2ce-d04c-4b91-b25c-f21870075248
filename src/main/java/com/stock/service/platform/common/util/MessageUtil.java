package com.stock.service.platform.common.util;

import com.alibaba.fastjson.JSON;
import com.stock.service.platform.common.entity.Company;
import com.stock.service.platform.common.entity.MessageContext;
import com.stock.service.platform.common.entity.MessageInfo;
import com.stock.service.platform.common.exception.BusinessException;
import microsoft.exchange.webservices.data.core.exception.misc.ArgumentException;
import org.apache.commons.collections.CollectionUtils;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
public class MessageUtil {

  /**
   * 校验注解
   */
  public static void required(Object object) {
    if (object == null) {
      throw new ArgumentException("入参为空");
    }
    getFields(object);
  }

  public static List<Field> getFields(Object object) {
    if (object == null) {
      return null;
    }
    Class<?> clazz = object.getClass();
    MessageModule messageModule = clazz.getAnnotation(MessageModule.class);
    if (messageModule == null) {
      return null;
    }
    List<Field> returnList = new ArrayList<>();
    List<Field> fieldList = new ArrayList<Field>();
    //获得父类属性
    for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
      try {
        Field[] fs = clazz.getDeclaredFields();
        Collections.addAll(fieldList, fs);
      } catch (Exception e) {
      }
    }
    returnList.addAll(fieldList);
    for (Field field : fieldList) {
      String fieldName = field.getName();
      //类的方法
      String methodName = "get" + fieldName.substring(0, 1).toUpperCase()
          + fieldName.substring(1);
      Message message = field.getAnnotation(Message.class);
      Required required = field.getAnnotation(Required.class);
      Method method;
      try {
        try {
          method = object.getClass().getMethod(methodName);
        } catch (Exception e) {
          methodName = methodName.replace("get", "is");
          method = object.getClass().getMethod(methodName);
        }
        Object v = method.invoke(object);//取得对象
        if (message != null) {
          boolean flag = false;
          if (v instanceof String) {
            if (v == null || StringUtil.isEmpty(v.toString())) {
              flag = true;
            }
          } else {
            if (v == null) {
              flag = true;
            }
          }
          if (StringUtil.equals("true", message.required()) && !"list".equals(message.type())
              && flag) {
            throw new ArgumentException(message.title() + "不能为空！");
          }
          List<Field> subList = getFields(v);
          if (subList != null && !subList.isEmpty()) {
            returnList.addAll(subList);
          }
        }
        if (required != null) {
          boolean flag = false;
          if (v instanceof String) {
            if (v == null || StringUtil.isEmpty(v.toString())) {
              flag = true;
            }
          } else {
            if (v == null) {
              flag = true;
            }
          }
          if (flag && (StringUtil.isNotEmpty(required.value()) || required.required())) {
            throw new ArgumentException(required.value() + "不能为空！");
          }
        }
      } catch (NoSuchMethodException | SecurityException e) {
        e.printStackTrace();
      } catch (IllegalAccessException e) {
        e.printStackTrace();
      } catch (IllegalArgumentException e) {
        e.printStackTrace();
      } catch (InvocationTargetException e) {
        e.printStackTrace();
      }
    }
    return returnList;
  }

  //要插入的日志list

  private List<MessageContext> messageContextList;

  /**
   * 记录日志（对比）
   *
   * @param newObject 修改后的object
   * @param oldObject 修改前的object
   */
  public MessageInfo getMessageContext(Object newObject, Object oldObject) throws IntrospectionException {
    this.writeMessge(newObject, oldObject, null);
    MessageInfo messageInfo = new MessageInfo();
    if (CollectionUtils.isNotEmpty(this.messageContextList)) {
      String remarks = "";
      for (MessageContext mc : this.messageContextList) {
        if (StringUtil.isEmpty(remarks)) {
          remarks = mc.getRemark();
        } else {
          remarks = StringUtil.join(remarks, ";", mc.getRemark());
        }
      }
      messageInfo.setOperationRemark(remarks);
      messageInfo.setOperationContext(JSON.toJSONString(this.messageContextList));
      this.messageContextList.clear();
    }
    return messageInfo;
  }

  /**
   * 实体类对比 生成日志内容list
   */
  public void writeMessge(Object newObject, Object oldObject, Set<String> privateWordSet) throws IntrospectionException {
    if (newObject == null && oldObject == null) {
      return;
    }
    Class<?> clazz = (newObject == null ? oldObject : newObject).getClass();
    String classPath = clazz.getCanonicalName();
    MessageModule module = (MessageModule) clazz.getAnnotation(MessageModule.class);
    String moduleStr = "";
    if (module != null) {
      moduleStr = module.title();
    }
    String addOrdel = null;
    //新增--针对list
    if (newObject != null && oldObject == null) {
      addOrdel = "add";
      //删除--针对list
    } else if (newObject == null && oldObject != null) {
      addOrdel = "del";
    } else {
      addOrdel = "upd";
    }
    List<Field> fieldList = new ArrayList<Field>();
    //获得父类属性
    if (clazz != Object.class) {
      try {
        Field[] fs = clazz.getDeclaredFields();
        Collections.addAll(fieldList, fs);
      } catch (Exception e) {
      }
    }
    try {
      //遍历实体类的每个属性
      for (Field field : fieldList) {
        String fieldName = field.getName();
        Message message = field.getAnnotation(Message.class);
        if (message != null) {
          if (CollectionUtils.isNotEmpty(privateWordSet)) {
            if (!privateWordSet.contains(fieldName)) {
              continue;
            }
          }
          PropertyDescriptor pd = new PropertyDescriptor(fieldName, clazz);
          Method readMethod = pd.getReadMethod();//获得写方法
          String tipMsg = message.title();
          String type = message.type();
          String primary = message.primary();
          Object nValue = newObject == null ? null : readMethod.invoke(newObject);
          Object oValue = oldObject == null ? null : readMethod.invoke(oldObject);
          //如果属性为list
          if (StringUtil.equals(type, "list")) {
            List nList = (ArrayList) nValue;
            List oList = (ArrayList) oValue;
            //定义新增数据的list和删除数据的list

            List newList = (nList == null ? null : new ArrayList(nList));
            List delList = (oList == null ? null : new ArrayList(oList));

            Method methodCetID = null;
            Object nObject;
            Object oObject;
            if (newList != null && delList != null) {
              for (Object nObj : nList) {
                Boolean earchAll = false;
                try {
                  methodCetID = nObj.getClass().getMethod("getId");
                } catch (NoSuchMethodException e) {
                  try {
                    methodCetID = nObj.getClass().getMethod("getKey");
                  } catch (NoSuchMethodException e1) {
                    try {
                      methodCetID = nObj.getClass().getMethod("getValue");
                    } catch (NoSuchMethodException e2) {
                      earchAll = true;
                    }
                  }
                }

                if (!earchAll) {
                  for (Object oObj : oList) {
                    nObject = methodCetID.invoke(nObj);
                    oObject = methodCetID.invoke(oObj);
                    if (nObject != null && oObject != null) {
                      if (StringUtil.equals(nObject.toString(),
                          oObject.toString())) {
                        this.writeMessge(nObj, oObj, null);
                        newList.remove(nObj);
                        delList.remove(oObj);
                        break;
                      }
                    }
                  }
                } else {
                  for (Object oObj : oList) {
                    this.writeMessge(nObj, oObj, null);
                    newList.remove(nObj);
                    delList.remove(oObj);
                    break;
                  }
                }
              }
            }
            if (newList != null && !newList.isEmpty()) {
              for (Object newObj : newList) {
                this.writeMessge(newObj, null, null);
              }
            }
            if (delList != null && !delList.isEmpty()) {
              for (Object delObj : delList) {
                this.writeMessge(null, delObj, null);
              }
            }
            //如果属性为object
          } else if (StringUtil.equals(type, "object")) {
            this.writeMessge(nValue, oValue, null);
            //如果属性为map
          } else if (StringUtil.equals(type, "map")) {
            continue;
            //基本属性
          } else {
            String nStr;
            String oStr;

            if (StringUtil.equals(addOrdel, "add")) {
              if (null != nValue) {
                if (!StringUtil.equals(primary, "true")) {
                  continue;
                }
                if (nValue instanceof Date) {
                  nStr = DateUtil.formatStrByDate((Date) nValue, "yyyy-MM-dd HH:mm:ss");
                } else if (nValue instanceof Boolean) {
                  nStr = (Boolean) nValue == true ? "是" : "否";
                } else {
                  nStr = nValue.toString();
                }
                if (StringUtil.isNotEmpty(nStr)) {
                  MessageContext messageContext = new MessageContext();
                  messageContext.setTitle(moduleStr + "-" + tipMsg);
                  messageContext.setPrivateWord(tipMsg);
                  messageContext.setClassPath(classPath);
                  messageContext.setOperationType("新增");
                  messageContext.setNewText(nStr);
                  messageContext.setOldText("");
                  messageContext.setRemark(StringUtil.join(tipMsg, "[", nStr, "]"));
                  this.createMessageContext(messageContext);
                }
              }
            } else if (StringUtil.equals(addOrdel, "del")) {
              if (null != oValue) {
                if (!StringUtil.equals(primary, "true")) {
                  continue;
                }
                if (oValue instanceof Date) {
                  oStr = DateUtil.formatStrByDate((Date) nValue, "yyyy-MM-dd HH:mm:ss");
                } else if (oValue instanceof Boolean) {
                  oStr = (Boolean) oValue == true ? "是" : "否";
                } else {
                  oStr = oValue.toString();
                }
                if (StringUtil.isNotEmpty(oStr)) {
                  MessageContext messageContext = new MessageContext();
                  messageContext.setTitle(moduleStr + "-" + tipMsg);
                  messageContext.setPrivateWord(tipMsg);
                  messageContext.setClassPath(classPath);
                  messageContext.setOperationType("删除");
                  messageContext.setNewText("");
                  messageContext.setOldText(oStr);
                  messageContext.setRemark(StringUtil.join(tipMsg, "[", oStr, "]"));
                  this.createMessageContext(messageContext);
                }
              }
            } else if (StringUtil.equals(addOrdel, "upd")) {
              if (nValue != null && oValue != null) {
                if (nValue instanceof Date) {
                  nStr = DateUtil.formatStrByDate((Date) nValue, "yyyy-MM-dd HH:mm:ss");
                  oStr = DateUtil.formatStrByDate((Date) oValue, "yyyy-MM-dd HH:mm:ss");

                } else if (nValue instanceof Boolean) {
                  nStr = (Boolean) nValue == true ? "是" : "否";
                  oStr = (Boolean) oValue == true ? "是" : "否";

                } else if (nValue instanceof BigDecimal) {
                  BigDecimal newBd = (BigDecimal) nValue;
                  BigDecimal oldBd = (BigDecimal) oValue;
                  if (newBd.compareTo(oldBd) == 0) {
                    continue;
                  }
                  nStr = String.valueOf(newBd);
                  oStr = String.valueOf(oldBd);
                } else {
                  nStr = nValue.toString();
                  oStr = oValue.toString();
                }

                if (StringUtil.isNotEmpty(nStr)  && !StringUtil.equals(nStr, oStr)) {
                  MessageContext messageContext = new MessageContext();
                  messageContext.setTitle(moduleStr + "-" + tipMsg);
                  messageContext.setPrivateWord(tipMsg);
                  messageContext.setClassPath(classPath);
                  messageContext.setOperationType("更新");
                  messageContext.setNewText(nStr);
                  messageContext.setOldText(oStr);
                  messageContext.setRemark(StringUtil.join(tipMsg, " [", oStr, "]-->[", nStr, "]"));
                  this.createMessageContext(messageContext);
                }
              } else if (nValue != null && oValue == null) {
                if (nValue instanceof Date) {
                  nStr = DateUtil.formatStrByDate((Date) nValue, "yyyy-MM-dd HH:mm:ss");
                } else if (nValue instanceof Boolean) {
                  nStr = (Boolean) nValue == true ? "是" : "否";
                } else {
                  nStr = nValue.toString();
                }
                if (StringUtil.isNotEmpty(nStr)) {
                  MessageContext messageContext = new MessageContext();
                  messageContext.setTitle(moduleStr + "-" + tipMsg);
                  messageContext.setPrivateWord(tipMsg);
                  messageContext.setClassPath(classPath);
                  messageContext.setOperationType("更新");
                  messageContext.setNewText(nStr);
                  messageContext.setOldText("");
                  messageContext.setRemark(StringUtil.join(tipMsg, " []-->[", nStr, "]"));
                  this.createMessageContext(messageContext);
                }
              } else if (oValue != null && nValue == null) {
                if (oValue instanceof Date) {
                  oStr = DateUtil.formatStrByDate((Date) nValue, "yyyy-MM-dd HH:mm:ss");
                } else if (oValue instanceof Boolean) {
                  oStr = (Boolean) oValue == true ? "是" : "否";
                } else {
                  oStr = oValue.toString();
                }
                if (StringUtil.isNotEmpty(oStr)) {
                  MessageContext messageContext = new MessageContext();
                  messageContext.setTitle(moduleStr + "-" + tipMsg);
                  messageContext.setPrivateWord(tipMsg);
                  messageContext.setClassPath(classPath);
                  messageContext.setOperationType("更新");
                  messageContext.setNewText("");
                  messageContext.setOldText(oStr);
                  messageContext.setRemark(StringUtil.join(tipMsg, " [", oStr, "]-->[]"));
                  this.createMessageContext(messageContext);
                }
              }
            } else {
              throw new BusinessException("异常");
            }
          }
        }
      }

    } catch (SecurityException e) {
      e.printStackTrace();
    } catch (IllegalAccessException e) {
      e.printStackTrace();
    } catch (IllegalArgumentException e) {
      e.printStackTrace();
    } catch (InvocationTargetException e) {
      e.printStackTrace();
    }
  }

  //生成日志说明list
  public void createMessageContext(MessageContext messageCoutext) {
    if (this.messageContextList == null) {
      this.messageContextList = new ArrayList<MessageContext>();
    }
    this.messageContextList.add(messageCoutext);
  }


  public static void main(String[] args) throws IntrospectionException {
    Company company1 = new Company();
    company1.setId("1");
    company1.setDeleteflag("1");
    company1.setZhName("vanke");

    Company company2 = new Company();
    company2.setId("1");
    company2.setCompanyCode("000002");
    company2.setZhName("vanke");

    Set<String> privateWordSet = new HashSet<>();
    privateWordSet.add("companyStatus");
    MessageUtil util = new MessageUtil();
    MessageInfo messageInfo = util.getMessageContext(company1, company2);
    System.out.println(messageInfo);
    // 获取ip
    //ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    //HttpServletRequest request = attributes.getRequest();
    //CommonService.getIp(request);
  }
}
