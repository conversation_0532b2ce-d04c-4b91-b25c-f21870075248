package com.stock.service.platform.common.security;

import com.stock.core.dao.RedisDao;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.util.EntityUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathFactory;
import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;
import org.w3c.dom.Node;
import javax.xml.namespace.NamespaceContext;

@Slf4j
@Component
public class GetUserIdByCASVerify {


    @Autowired
    private RestTemplate restTemplate;


    @Value("${cas.server.url}")
    private String casUrl;

    @Resource
    BaseService baseService;

    @Autowired
    private RedisDao redisDao;

    @Resource
    private UserMapper userMapper;

    public Map<String,String> verify(String ticket, String service) {

        Map<String,String> map = null;
        try {
            String encodedUrl = URLEncoder.encode(service, "UTF-8")
                    .replaceAll("\\+", "%20") // 将 + 替换为 %20
                    .replaceAll("%21", "!")
                    .replaceAll("%27", "'")
                    .replaceAll("%28", "(")
                    .replaceAll("%29", ")")
                    .replaceAll("%7E", "~");
            String url = casUrl + "?ticket=" + ticket + "&service=" + encodedUrl;
            log.info("url:--------------------------- " + url);
            HttpPost httpPost = new HttpPost(url);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            String res = EntityUtils.toString(entity, "UTF-8");

            log.info("responseString:--------------------------- " + res);
            //String res = restTemplate.getForObject(url, String.class);
            //log.info("res:--------------------------- " + res);
            //            String res = "<cas:serviceResponse xmlns:cas='http://www.yale.edu/tp/cas'>\n" +
//                    "\t<cas:authenticationSuccess>\n" +
//                    "\t\t<cas:user>haozizhao</cas:user>\n" +
//                    "\t\t\n" +
//                    "\t\t\t \n" +
//                    "     \n" +
//                    "\t   \n" +
//                    "            <cas:attributes>\n" +
//                    "                \n" +
//                    "                    <cas:ding>ldap</cas:ding>\n" +
//                    "                \n" +
//                    "                    <cas:employeeNo>CSC13083</cas:employeeNo>\n" +
//                    "                \n" +
//                    "                    <cas:mobilePhone>135****9152</cas:mobilePhone>\n" +
//                    "                \n" +
//                    "                    <cas:email>hao**@**.cn</cas:email>\n" +
//                    "                \n" +
//                    "                    <cas:ouName>CN=郝子昭,OU=DNBJ,OU=ZBBJ,DC=csctest,DC=local</cas:ouName>\n" +
//                    "                \n" +
//                    "                    <cas:unitName>总公司-总公司信息技术部-信息技术卖方业务系统开发组</cas:unitName>\n" +
//                    "                \n" +
//                    "                    <cas:cscPhone>010-85159223</cas:cscPhone>\n" +
//                    "                \n" +
//                    "                    <cas:realName>郝子昭</cas:realName>\n" +
//                    "                \n" +
//                    "            </cas:attributes>\n" +
//                    "        \n" +
//                    "\t\t\n" +
//                    "\t</cas:authenticationSuccess>\n" +
//                    "</cas:serviceResponse>\n";
            InputSource source = new InputSource(new StringReader(res));

            // 解析XML响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true); // 开启命名空间支持
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(source);

            // 使用XPath获取userId的值
            XPath xpath = XPathFactory.newInstance().newXPath();
            xpath.setNamespaceContext(new NamespaceContext() {
                @Override
                public String getNamespaceURI(String prefix) {
                    if ("cas".equals(prefix)) {
                        return "http://www.yale.edu/tp/cas";
                    }
                    return null;
                }

                @Override
                public String getPrefix(String namespaceURI) {
                    if ("http://www.yale.edu/tp/cas".equals(namespaceURI)) {
                        return "cas";
                    }
                    return null;
                }

                @Override
                public Iterator<String> getPrefixes(String namespaceURI) {
                    return null;
                }
            });
            Node userIdNode1casFailure = (Node) xpath.evaluate("/cas:serviceResponse/cas:authenticationFailure", doc, XPathConstants.NODE);
            String userId = null;
            if (userIdNode1casFailure != null) {
                log.info("casFailure:--------------------------- " + userIdNode1casFailure.getFirstChild().getTextContent());
            } else {
                Node userIdNode1casSuccess = (Node) xpath.evaluate("/cas:serviceResponse/cas:authenticationSuccess", doc, XPathConstants.NODE);
                if (userIdNode1casSuccess != null) {
                    Node employeeNoNode = (Node) xpath.evaluate("/cas:serviceResponse/cas:authenticationSuccess/cas:attributes/cas:employeeNo", doc, XPathConstants.NODE);
                    Node userIdNode = (Node) xpath.evaluate("/cas:serviceResponse/cas:authenticationSuccess/cas:attributes/cas:userId", doc, XPathConstants.NODE);

                    if (employeeNoNode != null) {
                        userId = employeeNoNode.getTextContent();
                    }else if (userIdNode != null) {
                        userId = userIdNode.getTextContent();
                    } else {
                        log.info("casSuccess-notUserId-notEmp:--------------------------- ");
                    }

                }
            }

            // 输出userId的值
            log.info("casR-userId: ---------" + userId);
            String platformUserId = userMapper.getUserIdByUserName(userId);
            if (platformUserId != null) {
                map = this.baseService.getUserNameAndPasswordByPersonId(platformUserId);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return map;
    }
}
