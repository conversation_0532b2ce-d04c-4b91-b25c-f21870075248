package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.Router;
import com.stock.service.platform.common.entity.RouterExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface RouterMapper {
    long countByExample(RouterExample example);

    int deleteByExample(RouterExample example);

    int deleteByPrimaryKey(String id);

    int insert(Router record);

    int insertSelective(Router record);

    List<Router> selectByExampleWithRowbounds(RouterExample example, RowBounds rowBounds);

    List<Router> selectByExample(RouterExample example);

    Router selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Router record, @Param("example") RouterExample example);

    int updateByExample(@Param("record") Router record, @Param("example") RouterExample example);

    int updateByPrimaryKeySelective(Router record);

    int updateByPrimaryKey(Router record);
}