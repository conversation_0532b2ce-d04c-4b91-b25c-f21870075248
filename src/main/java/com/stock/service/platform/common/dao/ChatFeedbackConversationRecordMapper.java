package com.stock.service.platform.common.dao;

import com.stock.service.platform.compliance.dto.ChatFeedbackConversationRecordDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ChatFeedbackConversationRecordMapper {
    int insertSelective(ChatFeedbackConversationRecordDto record);

    List<ChatFeedbackConversationRecordDto> getConversationRecordList(String feedbackId);
}
