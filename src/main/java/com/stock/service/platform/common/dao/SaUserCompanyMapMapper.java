package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.SaUserCompanyMap;
import com.stock.service.platform.common.entity.SaUserCompanyMapExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface SaUserCompanyMapMapper {
    long countByExample(SaUserCompanyMapExample example);

    int deleteByExample(SaUserCompanyMapExample example);

    int deleteByPrimaryKey(String id);

    int insert(SaUserCompanyMap record);

    int insertSelective(SaUserCompanyMap record);

    List<SaUserCompanyMap> selectByExampleWithRowbounds(SaUserCompanyMapExample example, RowBounds rowBounds);

    List<SaUserCompanyMap> selectByExample(SaUserCompanyMapExample example);

    SaUserCompanyMap selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") SaUserCompanyMap record, @Param("example") SaUserCompanyMapExample example);

    int updateByExample(@Param("record") SaUserCompanyMap record, @Param("example") SaUserCompanyMapExample example);

    int updateByPrimaryKeySelective(SaUserCompanyMap record);

    int updateByPrimaryKey(SaUserCompanyMap record);
}