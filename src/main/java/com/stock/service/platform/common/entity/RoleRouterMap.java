package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class RoleRouterMap implements Serializable {
    private String id;

    private String roleId;

    private String routerId;

    private String authorityFlag;

    private String updateUser;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId == null ? null : roleId.trim();
    }

    public String getRouterId() {
        return routerId;
    }

    public void setRouterId(String routerId) {
        this.routerId = routerId == null ? null : routerId.trim();
    }

    public String getAuthorityFlag() {
        return authorityFlag;
    }

    public void setAuthorityFlag(String authorityFlag) {
        this.authorityFlag = authorityFlag == null ? null : authorityFlag.trim();
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}