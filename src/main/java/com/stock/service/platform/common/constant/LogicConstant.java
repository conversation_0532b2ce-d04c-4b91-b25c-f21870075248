package com.stock.service.platform.common.constant;

/**
 * <AUTHOR>
 * 业务逻辑常量接口类
 */
public class LogicConstant {

    /**
     * 分隔符：逗号
     */
    public static final String SEPARATOR_COMMA = ",";

    /**
     * 分隔符：句号
     */
    public static final String SEPARATOR_POINT = ".";

    /**
     * 账号密码登录类型
     */
    public static final String USERNAME_PASSWORD_LOGIN_TYPE = "0";

    /**
     * 手机验证码登录类型
     */
    public static final Object TELEPHONE_VERIFY_CODE_LOGIN_TYPE = "1";

    /**
     * 登录类型
     */
    public static final String LOGIN_TYPE_NAME = "loginType";

    public static final String XLSX_SUFFIX = ".xlsx";

    /**
     * 默认密码
     */
    public static final String DEFAULT_PASSWORD = "Aa1234!@#$";


    public static final String LOGIN_OP_CODE = "platform_login_op_code";


    public static final String MSG_LOGIN_OP_CODE = "platform_msg_login_op_code";

    // 状态-等待回答
    public static final String STATUS_WAIT = "0";

    // 状态-回答成功
    public static final String STATUS_SUCCESS = "1";

    // 状态-回答超时
    public static final String STATUS_TIMEOUT = "2";

    // 状态-模型接口报错
    public static final String STATUS_ERROR = "3";

    // 状态-停止生成
    public static final String STATUS_STOP = "6";

    // 状态-回答内容为空
    public static final String STATUS_EMPTY = "4";

}

