<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.CommonBizMapper">

    <select id="getPhoneUser" resultType="java.lang.String">
        select person_name
        from sa_person t1
                 inner join sa_user t2
                            on t1.id=t2.id
        where t1.telephone=#{phoneNo}
    </select>

    <select id="getCurrentAudith" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1) from sa_user_role_map t1
                                 LEFT JOIN sa_role_router_map t2 on t1.role_id = t2.role_id
                                 left join sa_router t3 on t2.router_id  = t3.id
        where t1.user_id = #{id}
          and t3.path = #{routerUrl}
    </select>

    <select id="getLabelByCode" parameterType="string" resultType="com.stock.service.platform.common.dto.RegTreeDto">
        SELECT
            id,
            p_id pId,
            label_name labelName,
            label_value labelValue
        FROM
            `capital-repmanage-jt`.maa_conf_label
        WHERE
            STATUS = '1'
          AND label_code = #{labelCode}
        ORDER BY
            label_sort
    </select>
    <select id="getSaCodeByCode" parameterType="java.lang.String" resultType="com.stock.service.platform.common.dto.RegTreeDto">
        select t.code_value labelValue,
               t.code_name labelName,
               t.id id,
               0 pId
        from sa_code t
        where t.code_no = #{labelCode}
          and t.valid_flag = '1'
        order by t.sort_no
    </select>

    <select id="getTreeTagByCode" parameterType="string" resultType="com.stock.service.platform.common.dto.RegTreeDto">
        SELECT
            id,
            p_id pId,
            label_name labelName,
            label_value labelValue,
            0 num
        FROM
            `capital-repmanage-jt`.maa_conf_label
        WHERE
            STATUS = '1'
          AND p_id = '0'
          AND label_code = #{labelCode}
        ORDER BY
            label_sort
    </select>

    <select id="getSaCodeByNo" parameterType="java.lang.String" resultType="com.stock.service.platform.common.dto.RegTreeDto">
        select t.code_value labelValue,
               t.code_name labelName,
               t.id id,
               0 pId
        from `capital-repmanage-jt`.sa_code_cloud t
        where t.code_no = #{labelCode}
          and t.valid_flag = '1'
          and t.code_value in ('00','02','04','07','09', '05')
        order by t.sort_no
    </select>

    <select id="getAreaGroupList" resultType="String">
        select p.area_group from `capital-repmanage-jt`.provinces p where p.area_group != 'null' group by p.area_group
    </select>

    <select id="getAreaTreeList" resultType="com.stock.service.platform.common.dto.OptionResultAreaAndIndustryDto">
        SELECT id AS id,area_name AS label, CASE WHEN parent_id IS NULL OR parent_id = '' THEN '0' ELSE parent_id END AS pId, area_no AS value,
        area_group AS areaGroup, area_initial AS initial
        FROM `capital-repmanage-jt`.provinces
        WHERE
            RIGHT(area_no,'2') = '00' AND area_name <![CDATA[ <> ]]> '县'
        ORDER BY area_no+0
    </select>
    <select id="getUserByUserName" resultType="java.lang.String">
        select user_name from sa_user where status = '1' and user_name = #{userName}
    </select>
    <select id="getMailByUserName" resultType="java.lang.String">
        select mail from sa_user where status = '1' and user_name = #{userName}
    </select>
    <select id="getPhoneByUserName" resultType="java.lang.String">
        select telephone from sa_user where status = '1' and user_name = #{userName}
    </select>
    <select id="getUserTypeByUserName" resultType="java.lang.String">
        select user_type from sa_user where user_name = #{userName}
    </select>
    <select id="getPersonNameByUserName" resultType="java.lang.String">
        SELECT
            sp.person_name
        FROM
            sa_person sp
                LEFT JOIN sa_user su ON su.id = sp.id
        WHERE
            su.user_name = #{userName}
        limit 1
    </select>
    <select id="getTradeDate" resultType="java.lang.String">
        select trade_date from `capital-repmanage-jt`.`trade_date` where band_type = 'A' and valid_flag = '0' and sisdel = '0' and trade_date = #{tradeDate}
    </select>
</mapper>