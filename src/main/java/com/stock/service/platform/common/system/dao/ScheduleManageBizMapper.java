package com.stock.service.platform.common.system.dao;

import com.stock.service.platform.common.system.dto.KettleTaskDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ScheduleManageBizMapper {
    List<KettleTaskDto> queryTaskList(KettleTaskDto kettleTaskDto);

    void insertTask(KettleTaskDto dto);

    void updateTask(KettleTaskDto dto);

    List<KettleTaskDto> selectFalseList();

    void updateTaxationCal(KettleTaskDto dto);

    void updateUserInfoInTask(KettleTaskDto taskDto);

    int queryTaskCount(KettleTaskDto kettleTaskDto);

    void updateTaskInfoById(KettleTaskDto dto);

    int getCountOfTaxactionCal(String calDate);

    void updateTaxationStatusSuccessUntilDay(String selDate);
}
