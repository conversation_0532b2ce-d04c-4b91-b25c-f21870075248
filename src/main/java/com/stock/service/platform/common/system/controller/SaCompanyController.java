package com.stock.service.platform.common.system.controller;

import com.google.common.base.Throwables;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.util.BeanUtil;
import com.stock.service.platform.common.dao.CommonBizMapper;
import com.stock.service.platform.common.dao.CompanyContactMapper;
import com.stock.service.platform.common.dao.CompanyDtoMapper;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.dto.RegTreeDto;
import com.stock.service.platform.common.entity.CompanyContactExample;
import com.stock.service.platform.common.entity.CompanyDtoExample;
import com.stock.service.platform.common.entity.OrgExample;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.system.dto.ComBoxDto;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.service.InfoSyncService;
import com.stock.service.platform.common.system.service.SaCompanyService;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.mqManage.service.MQManagerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.http.MediaType.APPLICATION_JSON_UTF8_VALUE;

@RestController
@RequestMapping("system/company")
public class SaCompanyController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SaCompanyController.class);

    @Resource
    private SaCompanyService companyService;

    @Resource
    CommonBizMapper commonBizMapper;

    @Resource
    CommonService commonService;

    @Autowired
    private InfoSyncService infoSyncService;

    @Resource
    private MQManagerService mqManagerService;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private CompanyDtoMapper companyDtoMapper;

    @Resource
    private CompanyContactMapper companyContactMapper;

    /**
     * 查询上市公司初始化
     */
    @RequestMapping(value = "queryPagingCompanyBySelective")
    public Map<String, Object> queryPagingCompanyBySelective(@RequestBody SaCompanyDto dto) throws IllegalAccessException {
        Map<String, Object> resultMap = new HashMap<>();
        if(Integer.parseInt(getUserInfo().getInfo().get("orgType").toString()) <= 3){
            resultMap = companyService.queryPagingCompanyBySelective(dto);
        }
        return resultMap;
    }

    /**
     * 根据ID获取上市公司数据 For
     */
    @PostMapping(value = "/getBusDepById", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<Object> getBusDepById(@RequestParam(required = false) String id) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(companyService.getCompanyById(id));
        }catch (Exception e) {
            response.setErrorMsg("获取公司信息失败");
        }
        return response;
    }

    /**
     * 查询营业部选项列表
     */
    @PostMapping(value = "queryBusinessDepartmentOptions", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<List<ComBoxDto>> queryBusinessDepartmentOptions() {
        String orgId = (String) getUserInfo().getInfo().get("orgId");
        String orgType = (String) getUserInfo().getInfo().get("orgType");
        JsonResponse<List<ComBoxDto>> response = companyService.queryBusinessDepartmentOptions(orgId,orgType);
        return response;
    }


    /***
     * 新增或编辑上市公司
     * For
     * @param
     * @return JsonResponse<String>
     */
    @PostMapping(value = "/createOrUpdate", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<String> createOrUpdate(@RequestBody(required = false) SaCompanyDto dto) throws Exception {
        UserInfo userInfo = super.getUserInfo();
        JsonResponse<String> response = new JsonResponse<>();
        if(null == userInfo){
            response.setResult("登录用户参数异常!");
        }else {
            response = companyService.createOrUpdate(dto, userInfo);
        }
        return response;
    }

    /***
     * 根据ID删除
     * For
     * @param
     * @return
     */
    @PostMapping(value = "/deleteCompanyById", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<String> deleteCompanyById(
            @RequestParam(required = false) String id,@RequestParam(required = false) String seriakey,@RequestParam(required = false) String companyCode) {
        UserInfo userInfo = super.getUserInfo();
        JsonResponse<String> response = new JsonResponse<>();
        if(null == userInfo){
            response.setResult("登录用户参数异常!");
        }else {
            response = companyService.deleteCompanyById(id,seriakey, userInfo,companyCode);
        }
        return response;
    }

    /**
     * 查询是否是刷新过的代码
     */
    @PostMapping(value = "checkCompanyCodeConvert", produces = APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public JsonResponse<String> checkCompanyCodeConvert(@RequestParam(required = false) String id,
                                                @RequestParam(required = false) String companyCode) {
        JsonResponse<String> response = new JsonResponse<>();
        response.setResult(companyService.checkCompanyCodeConvert(id, companyCode));
        return response;
    }

    /**
     * 查询上市公司代码是否重复
     */
    @PostMapping(value = "checkBusDepCode", produces = APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public JsonResponse<Object> checkBusDepCode(@RequestParam(required = false) String id,
                                                @RequestParam(required = false) String companyCode) {
        JsonResponse<Object> response = new JsonResponse<>();
        response.setResult(companyService.checkBusDepCode(id, companyCode));
        return response;
    }

    /**
     * 根据公司代码获取同步数据
     */
    @PostMapping(value = "getCompanyByCode", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<SaCompanyDto> getCompanyByCode(String companyCode) throws IllegalAccessException {
        JsonResponse<SaCompanyDto> response = new JsonResponse<>();
        SaCompanyDto result = new SaCompanyDto();
        SaCompanyDto dto =  companyService.getCompanyByCode(companyCode);
        if(null != dto){
            BeanUtil.copy(dto, result);
        }
        StripTrailingZerosUtil.convertBigDecimalsToPlainString(result);
        response.setResult(result);
        return response;
    }
    /**
     * 根据公司代码获取同步数据
     */
    @PostMapping(value = "getCompanyInfoByCode", produces = APPLICATION_JSON_UTF8_VALUE)
    public JsonResponse<SaCompanyDto> getCompanyInfoByCode(String companyCode) throws IllegalAccessException {
        JsonResponse<SaCompanyDto> response = new JsonResponse<>();
        SaCompanyDto result = new SaCompanyDto();
        SaCompanyDto dto =  companyService.getCompanyInfoByCode(companyCode);
        if(null != dto){
            BeanUtil.copy(dto, result);
        }
        StripTrailingZerosUtil.convertBigDecimalsToPlainString(result);
        response.setResult(result);
        return response;
    }

    /**
     * 查询上市公司代码是否重复
     */
    @PostMapping(value = "checkSocialUnifiedCreditCode", produces = APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public JsonResponse<Object> checkSocialUnifiedCreditCode(@RequestParam(required = false) String id,
                                                @RequestParam(required = false) String socialUnifiedCreditCode) {
        JsonResponse<Object> response = new JsonResponse<>();
        response.setResult(companyService.checkSocialUnifiedCreditCode(id, socialUnifiedCreditCode));
        return response;
    }

    @PostMapping("/queryTableData")
    @ResponseBody
    public JsonResponse<Object> queryTableData(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(companyService.queryTableData(param));
        }catch (Exception e) {
            response.setErrorMsg("获取接入系统列表数据报错");
        }
        return response;
    }

    /**
     * 获取员工管理下拉
     * @return
     */
    @PostMapping(value = "getStaffSelectData")
    @ResponseBody
    public JsonResponse<Map<String, Object>> getStaffSelectData(){
        Map<String, Object> selectDataMap = new HashMap<>();
        //拟上市板块
        List<RegTreeDto> plateTreeTag = commonBizMapper.getSaCodeByNo("CASE_BELONGS_PLATE");
        RegTreeDto regTreeDto = new RegTreeDto();
        regTreeDto.setLabelValue("99");
        regTreeDto.setLabelName("非上市公司");
        regTreeDto.setId("99");
        regTreeDto.setpId("0");
        plateTreeTag.add(regTreeDto);
        selectDataMap.put("stockMarketDataList", commonService.sortSelectList(plateTreeTag));
        //公司性质
        List<RegTreeDto> companyNatureList = commonBizMapper.getSaCodeByCode("REP_COMPANY_NATURE");
        selectDataMap.put("companyNatureList", commonService.sortSelectList(companyNatureList));
        //市场
        List<RegTreeDto> stockJtList = commonBizMapper.getSaCodeByCode("STOCK_JT");
        selectDataMap.put("stockJtList", commonService.sortSelectList(stockJtList));

        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(selectDataMap);
        return response;
    }

    /**
     * 同步上市公司，上线后废弃
     *
     * @param files 文件
     */
    @PostMapping("/importListedCompanyInfo")
    public JsonResponse<Integer> importListedCompanyInfo(@RequestParam("files") List<MultipartFile> files) {
        JsonResponse<Integer> jsonResponse = new JsonResponse<>();
        int result;
        UserInfo userInfo = getUserInfo();
        // 先将导入上市公司按钮隐藏掉
        mqManagerService.setButtonStatus("saCompany","导入上市公司","0",userInfo.getPersonName());
        try {
            result = infoSyncService.importListedCompanyInfo(files);
            jsonResponse.setResult(result);
        } catch (Exception e) {
            // 如果失败，清表
            // 组织架构
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andOrgTypeEqualTo("4"); // 4是上市公司
            orgMapper.deleteByExample(orgExample);
            // 上市公司
            CompanyDtoExample companyDtoExample = new CompanyDtoExample();
            companyDtoMapper.deleteByExample(companyDtoExample);
            // 上市公司联系人
            CompanyContactExample companyContactExample = new CompanyContactExample();
            companyContactMapper.deleteByExample(companyContactExample);
            // 如果失败，要将按钮状态恢复回去
            mqManagerService.setButtonStatus("saCompany","导入上市公司","1",userInfo.getPersonName());
            jsonResponse.setSuccess(false);
            jsonResponse.setErrorMsg(e.getMessage());
            logger.info(Throwables.getStackTraceAsString(e));
        }
        return jsonResponse;
    }

}
