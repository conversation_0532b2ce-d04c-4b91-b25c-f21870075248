package com.stock.service.platform.common.system.controller;

import com.google.common.collect.Maps;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.OrgExample;
import com.stock.service.platform.common.system.dto.DepartmentDto;
import com.stock.service.platform.common.system.service.DepartmentService;
import com.stock.service.platform.common.system.service.InfoSyncService;
import com.stock.service.platform.common.tree.inter.TreeNode;
import com.stock.service.platform.mqManage.service.MQManagerService;
import com.stock.service.platform.organization.dto.OrgDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/department")
public class DepartmentController extends BaseController {
    @Autowired
    private DepartmentService service;

    @Autowired
    private InfoSyncService infoSyncService;

    @Autowired
    private MQManagerService mqManagerService;

    @Resource
    private OrgMapper orgMapper;

    /**
     * 获取组织机构树111111111111
     */
    @ApiOperation(value = "获取组织机构树", notes = "获取组织机构树")
    @RequestMapping(value = "/queryOrgTree")
    public JsonResponse<Map<String,Object>> queryOrgTree(OrgDto orgDto) {
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();
        response.setResult(service.listOrgTree(orgDto));
        return response;
    }
    @RequestMapping("/nodeMoveUp")
    @ResponseBody
    public JsonResponse<List<DepartmentDto>> nodeMoveUp(@RequestBody DepartmentDto node) {
        JsonResponse<List<DepartmentDto> > response = new JsonResponse<>();
        try {
            List<DepartmentDto> nodeList = service.moveUpNode(node);
            response.setResult(nodeList);
        }catch (Exception e) {
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }
    @RequestMapping("/nodeMoveDown")
    @ResponseBody
    public JsonResponse<List<DepartmentDto>> nodeMoveDown(@RequestBody DepartmentDto node) {
        JsonResponse<List<DepartmentDto> > response = new JsonResponse<>();
        try {
            List<DepartmentDto> nodeList = service.moveDownNode(node);
            response.setResult(nodeList);
        }catch (Exception e) {
            response.setErrorMsg(e.getMessage());
        }

        return response;
    }
    @RequestMapping("/editNode")
    @ResponseBody
    public JsonResponse<String> editNode(@RequestBody DepartmentDto nodeDto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        if(service.checkRepeatOrgCode(nodeDto.getId(),nodeDto.getOrgCode(),0)) {
            jsonResponse.setErrorMsg("当前机构代码重复");
            return jsonResponse;
        }
        if(service.checkRepeatOrgName(nodeDto.getId(),nodeDto.getOrgName(),0)) {
            jsonResponse.setErrorMsg("当前机构名称重复");
            return jsonResponse;
        }
        service.updateNodeById(nodeDto);

        jsonResponse.setResult("success");
        return jsonResponse;
    }
    @RequestMapping("/addBrotherNode")
    @ResponseBody
    public JsonResponse<DepartmentDto> addBrotherNode(@RequestBody DepartmentDto nodeDto) {
        JsonResponse<DepartmentDto > response = new JsonResponse<>();
        if(service.checkRepeatOrgCode(nodeDto.getId(),nodeDto.getOrgCode(),1)) {
            response.setErrorMsg("当前机构代码重复");
            return response;
        }
        if(service.checkRepeatOrgName(nodeDto.getId(),nodeDto.getOrgName(),1)) {
            response.setErrorMsg("当前机构名称重复");
            return response;
        }

        DepartmentDto treeNode = service.addNode(nodeDto);
        response.setResult(treeNode);
        return response;
    }
    @RequestMapping("/addChildNode")
    @ResponseBody
    public JsonResponse<DepartmentDto> addChildNode(@RequestBody DepartmentDto nodeDto) {
        JsonResponse<DepartmentDto > response = new JsonResponse<>();
        if(service.checkRepeatOrgCode(nodeDto.getId(),nodeDto.getOrgCode(),1)) {
            response.setErrorMsg("当前机构代码重复");
            return response;
        }
        if(service.checkRepeatOrgName(nodeDto.getId(),nodeDto.getOrgName(),1)) {
            response.setErrorMsg("当前机构名称重复");
            return response;
        }

        DepartmentDto treeNode = service.addChildNode(nodeDto);
        response.setResult(treeNode);
        return response;
    }
    @RequestMapping("/adjustNode")
    @ResponseBody
    public  JsonResponse<List<TreeNode>> adjustNode(String fromNodeId, String toNodeId, String type) {
        JsonResponse<List<TreeNode>> jsonResponse = new JsonResponse<>();
        List<TreeNode> list = service.adjustNode(fromNodeId, toNodeId, type);
        jsonResponse.setResult(list);
        return jsonResponse ;
    }
    @RequestMapping("/getNodeList")
    @ResponseBody
    public JsonResponse getNodeList(String treeId) {
        JsonResponse<Object> jsonResponse = new JsonResponse<>();
        List<DepartmentDto> nodeList = service.listNode(treeId);
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("nodeList",nodeList);
        jsonResponse.setResult(map);
        return jsonResponse;
    }

    @RequestMapping("/deleteNode")
    @ResponseBody
    public JsonResponse<List<TreeNode>> deleteNode(String id) {
        JsonResponse<List<TreeNode>> jsonResponse = new JsonResponse<>();
        List<TreeNode> delete = service.delete(id);
        jsonResponse.setResult(delete);
        return jsonResponse;

    }
    @RequestMapping("/checkPerson")
    @ResponseBody
    public JsonResponse<Boolean> checkPerson(String ids) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        boolean b = service.checkOrgHasPerson(ids);
        jsonResponse.setResult(b);
        return jsonResponse;

    }
    @RequestMapping("/addFirstNode")
    @ResponseBody
    public JsonResponse<List<OrgDto>> addFirstNode() {
        JsonResponse<List<OrgDto>> jsonResponse = new JsonResponse<>();
        List<OrgDto> orgDtos = service.addFirstNode();
        jsonResponse.setResult(orgDtos);
        return jsonResponse;

    }

    @RequestMapping("/getOrgList")
    @ResponseBody
    public JsonResponse<List<Org>> getOrgList(@RequestBody Org org) {
        JsonResponse<List<Org>> jsonResponse = new JsonResponse<>();
        List<Org> OrgList = service.getOrgList(org);
        jsonResponse.setResult(OrgList);
        return jsonResponse;

    }

    /**
     * 同步股权激励组织架构，上线后废弃
     *
     * @param files 文件
     */
    @PostMapping("/importDeptInfo")
    public JsonResponse<Integer> importDeptInfo(@RequestParam("files") List<MultipartFile> files) throws IOException {
        JsonResponse<Integer> jsonResponse = new JsonResponse<>();
        int result;
        UserInfo userInfo = getUserInfo();
        // 先将导入上市公司按钮隐藏掉
        mqManagerService.setButtonStatus("organization","导入机构部门","0",userInfo.getPersonName());
        try {
            result = infoSyncService.importDeptInfo(files);
            jsonResponse.setSuccess(true);
            jsonResponse.setResult(result);
        } catch (Exception e) {
            // 如果失败，清表
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andOrgTypeNotEqualTo("1").andOrgTypeNotEqualTo("4"); // 1总公司,4上市公司,不删
            orgMapper.deleteByExample(orgExample);
            // 如果失败，要将按钮状态恢复回去
            mqManagerService.setButtonStatus("organization","导入机构部门","1",userInfo.getPersonName());
            jsonResponse.setSuccess(false);
            jsonResponse.setErrorMsg(e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    @PostMapping("/getDepartmentTreeList")
    public JsonResponse<List<OrgDto>> getDepartmentTreeList(@RequestBody OrgDto orgDto) {
        JsonResponse<List<OrgDto>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(service.getOrgListByPId(orgDto));
        return jsonResponse;
    }

}
