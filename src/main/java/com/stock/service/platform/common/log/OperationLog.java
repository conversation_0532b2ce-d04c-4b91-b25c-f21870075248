package com.stock.service.platform.common.log;

import com.stock.core.dto.UserInfo;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class OperationLog implements Serializable {

  private String parameter;
  private String result;
  private String operationTypeDetail;
  private String router;
  private String method;
  private String remark;
  private String ip;
  private UserInfo userInfo;

  public OperationLog() {
  }

  public OperationLog(String parameter, String result, String operationTypeDetail, String router,
                      String method, String remark, String ip, UserInfo userInfo) {
    this.parameter = parameter;
    this.result = result;
    this.operationTypeDetail = operationTypeDetail;
    this.router = router;
    this.method = method;
    this.remark = remark;
    this.ip = ip;
    this.userInfo = userInfo;
  }

  public String getOperationTypeDetail() {
    return operationTypeDetail;
  }

  public void setOperationTypeDetail(String operationTypeDetail) {
    this.operationTypeDetail = operationTypeDetail;
  }

  public String getIp() {
    return ip;
  }

  public void setIp(String ip) {
    this.ip = ip;
  }

  public String getParameter() {
    return parameter;
  }

  public void setParameter(String parameter) {
    this.parameter = parameter;
  }

  public String getResult() {
    return result;
  }

  public void setResult(String result) {
    this.result = result;
  }

  public String getRouter() {
    return router;
  }

  public void setRouter(String router) {
    this.router = router;
  }

  public String getMethod() {
    return method;
  }

  public void setMethod(String method) {
    this.method = method;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public UserInfo getUserInfo() {
    return userInfo;
  }

  public void setUserInfo(UserInfo userInfo) {
    this.userInfo = userInfo;
  }
}
