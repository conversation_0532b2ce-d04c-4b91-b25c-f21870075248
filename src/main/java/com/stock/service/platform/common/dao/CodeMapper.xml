<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.CodeMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Code">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="code_no" jdbcType="VARCHAR" property="codeNo" />
    <result column="code_value" jdbcType="VARCHAR" property="codeValue" />
    <result column="code_name" jdbcType="VARCHAR" property="codeName" />
    <result column="code_desc" jdbcType="VARCHAR" property="codeDesc" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
    <result column="code_type" jdbcType="VARCHAR" property="codeType" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="valid_flag" jdbcType="VARCHAR" property="validFlag" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code_no, code_value, code_name, code_desc, sort_no, code_type, type, valid_flag, 
    version, company_id, create_user, create_time, update_user, update_time, status
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.CodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_code
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_code
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.CodeExample">
    delete from sa_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Code">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_code (id, code_no, code_value, 
      code_name, code_desc, sort_no, 
      code_type, type, valid_flag, 
      version, company_id, create_user, 
      create_time, update_user, update_time, 
      status)
    values (#{id,jdbcType=VARCHAR}, #{codeNo,jdbcType=VARCHAR}, #{codeValue,jdbcType=VARCHAR}, 
      #{codeName,jdbcType=VARCHAR}, #{codeDesc,jdbcType=VARCHAR}, #{sortNo,jdbcType=INTEGER}, 
      #{codeType,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{validFlag,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Code">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="codeNo != null">
        code_no,
      </if>
      <if test="codeValue != null">
        code_value,
      </if>
      <if test="codeName != null">
        code_name,
      </if>
      <if test="codeDesc != null">
        code_desc,
      </if>
      <if test="sortNo != null">
        sort_no,
      </if>
      <if test="codeType != null">
        code_type,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="validFlag != null">
        valid_flag,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="codeNo != null">
        #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="codeValue != null">
        #{codeValue,jdbcType=VARCHAR},
      </if>
      <if test="codeName != null">
        #{codeName,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        #{sortNo,jdbcType=INTEGER},
      </if>
      <if test="codeType != null">
        #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.CodeExample" resultType="java.lang.Long">
    select count(*) from sa_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.codeNo != null">
        code_no = #{record.codeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.codeValue != null">
        code_value = #{record.codeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.codeName != null">
        code_name = #{record.codeName,jdbcType=VARCHAR},
      </if>
      <if test="record.codeDesc != null">
        code_desc = #{record.codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.sortNo != null">
        sort_no = #{record.sortNo,jdbcType=INTEGER},
      </if>
      <if test="record.codeType != null">
        code_type = #{record.codeType,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.validFlag != null">
        valid_flag = #{record.validFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_code
    set id = #{record.id,jdbcType=VARCHAR},
      code_no = #{record.codeNo,jdbcType=VARCHAR},
      code_value = #{record.codeValue,jdbcType=VARCHAR},
      code_name = #{record.codeName,jdbcType=VARCHAR},
      code_desc = #{record.codeDesc,jdbcType=VARCHAR},
      sort_no = #{record.sortNo,jdbcType=INTEGER},
      code_type = #{record.codeType,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      valid_flag = #{record.validFlag,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Code">
    update sa_code
    <set>
      <if test="codeNo != null">
        code_no = #{codeNo,jdbcType=VARCHAR},
      </if>
      <if test="codeValue != null">
        code_value = #{codeValue,jdbcType=VARCHAR},
      </if>
      <if test="codeName != null">
        code_name = #{codeName,jdbcType=VARCHAR},
      </if>
      <if test="codeDesc != null">
        code_desc = #{codeDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortNo != null">
        sort_no = #{sortNo,jdbcType=INTEGER},
      </if>
      <if test="codeType != null">
        code_type = #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        valid_flag = #{validFlag,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Code">
    update sa_code
    set code_no = #{codeNo,jdbcType=VARCHAR},
      code_value = #{codeValue,jdbcType=VARCHAR},
      code_name = #{codeName,jdbcType=VARCHAR},
      code_desc = #{codeDesc,jdbcType=VARCHAR},
      sort_no = #{sortNo,jdbcType=INTEGER},
      code_type = #{codeType,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      valid_flag = #{validFlag,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.CodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>