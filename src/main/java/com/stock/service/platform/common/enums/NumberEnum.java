package com.stock.service.platform.common.enums;


import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public enum NumberEnum {
  ZERO(0, "0", 0L, 0D, BigDecimal.ZERO),
  ONE(1, "1", 1L, 1D, BigDecimal.ONE),
  TWO(2, "2", 2L, 2D, new BigDecimal("2")),
  THREE(3, "3", 3L, 3D, new BigDecimal("3")),
  FIVE(5, "5", 5L, 5D, new BigDecimal("5")),
  NINE(9, "9", 9L, 9D, new BigDecimal(9)),
  TEN(10, "10", 10L, 10D, BigDecimal.TEN),
  TWELVE(12, "12", 12L, 12D, new BigDecimal(12)),
  TWENTY_TWO(22, "22", 22L, 22D, new BigDecimal(22)),
  TWENTY_THREE(23, "23", 23L, 23D, new BigDecimal(23)),
  THIRTY(30, "30", 30L, 30D, new BigDecimal("30")),
  ONE_THOUSAND(1000, "1000", 1000L, 1000D, new BigDecimal(1000)),

  ;

  private Integer typeInteger;
  private String typeString;
  private Long typeLong;
  private Double typeDouble;
  private BigDecimal typeBigDecimal;

  NumberEnum(Integer typeInteger, String typeString, Long typeLong,
             Double typeDouble, BigDecimal typeBigDecimal) {
    this.typeInteger = typeInteger;
    this.typeString = typeString;
    this.typeLong = typeLong;
    this.typeDouble = typeDouble;
    this.typeBigDecimal = typeBigDecimal;
  }

  public Integer getInteger() {
    return typeInteger;
  }

  public String getString() {
    return typeString;
  }

  public Long getLong() {
    return typeLong;
  }

  public Double getDouble() {
    return typeDouble;
  }

  public BigDecimal getBigDecimal() {
    return typeBigDecimal;
  }
}
