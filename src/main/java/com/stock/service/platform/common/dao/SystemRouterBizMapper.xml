<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.SystemRouterBizMapper">

  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.dto.RouterDto">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="p_router_id" jdbcType="VARCHAR" property="pMenuId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="router_name" jdbcType="VARCHAR" property="menuName" />
    <result column="router_des" jdbcType="VARCHAR" property="menuDes" />
    <result column="router_type" jdbcType="VARCHAR" property="menuType" />
    <result column="router_folder_flag" jdbcType="INTEGER" property="menuFolderFlag" />
    <result column="sort_no" jdbcType="INTEGER" property="sortNo" />
    <result column="router_icon" jdbcType="VARCHAR" property="menuIcon" />
    <result column="meta" jdbcType="VARCHAR" property="meta" />
    <result column="para" jdbcType="VARCHAR" property="para" />
    <result column="component" jdbcType="VARCHAR" property="component" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="redirect" jdbcType="VARCHAR" property="redirect" />
    <result column="userId" jdbcType="VARCHAR" property="userId" />
    <result column="authority_flag" jdbcType="VARCHAR" property="authorityFlag"/>
  </resultMap>

  <!-- 查询用户角色对应的权限菜单 -->
  <select id="selectByCondition" parameterType="String" resultMap="BaseResultMap">
    SELECT
      DISTINCT
      IFNULL(m.id, '') id,
      IFNULL(m.p_router_id, '') p_router_id,
      m.para,
      m.router_name,
      m.router_type,
      m.router_des,
      m.router_folder_flag,
      m.router_icon,
      m.path,
      m.component,
      m.redirect,
      m.meta,
      m.sort_no,
      IFNULL(#{userId},'') AS userId,
      mm.authority_flag
    FROM sa_router m
           LEFT JOIN sa_role_router_map mm ON m.id = mm.router_id
    WHERE FIND_IN_SET (mm.role_id, (
      SELECT group_concat(r.id) FROM sa_role r
                                       LEFT JOIN sa_user_role_map rm ON r.id = rm.role_id
      WHERE rm.user_id = #{userId}))
    ORDER BY m.sort_no
  </select>

  <!-- 查询用户角色对应的资源ID -->
  <select id="selectResourcesByUserId" parameterType="String" resultType="String">
    SELECT DISTINCT r.resource_name
    FROM sa_resource r
           LEFT JOIN sa_router_resource_map rm ON r.id = rm.resource_id
    WHERE FIND_IN_SET(rm.router_id, (
      SELECT group_concat(m.id) FROM sa_router m
                                       LEFT JOIN sa_role_router_map mm ON m.id = mm.router_id
      WHERE FIND_IN_SET (mm.role_id, (
        SELECT group_concat(r.id) FROM sa_role r
                                         LEFT JOIN sa_user_role_map rm ON r.id = rm.role_id
        WHERE rm.user_id = #{userId}))
    ))
  </select>

</mapper>