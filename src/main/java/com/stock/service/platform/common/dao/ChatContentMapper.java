package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.ChatContent;
import com.stock.service.platform.common.entity.ChatContentExample;
import com.stock.service.platform.common.entity.ChatContentWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface ChatContentMapper {
    long countByExample(ChatContentExample example);

    int deleteByExample(ChatContentExample example);

    int deleteByPrimaryKey(String id);

    int insert(ChatContentWithBLOBs record);

    int insertSelective(ChatContentWithBLOBs record);

    List<ChatContentWithBLOBs> selectByExampleWithBLOBsWithRowbounds(ChatContentExample example, RowBounds rowBounds);

    List<ChatContentWithBLOBs> selectByExampleWithBLOBs(ChatContentExample example);

    List<ChatContent> selectByExampleWithRowbounds(Chat<PERSON>ontentExample example, RowBounds rowBounds);

    List<ChatContent> selectByExample(ChatContentExample example);

    ChatContentWithBLOBs selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") ChatContentWithBLOBs record, @Param("example") ChatContentExample example);

    int updateByExampleWithBLOBs(@Param("record") ChatContentWithBLOBs record, @Param("example") ChatContentExample example);

    int updateByExample(@Param("record") ChatContent record, @Param("example") ChatContentExample example);

    int updateByPrimaryKeySelective(ChatContentWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(ChatContentWithBLOBs record);

    int updateByPrimaryKey(ChatContent record);
}