<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.CompanyMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.Company">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleteflag" jdbcType="VARCHAR" property="deleteflag" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="zh_name" jdbcType="VARCHAR" property="zhName" />
    <result column="zh_sort_name" jdbcType="VARCHAR" property="zhSortName" />
    <result column="corporate" jdbcType="VARCHAR" property="corporate" />
    <result column="organisation_no" jdbcType="VARCHAR" property="organisationNo" />
    <result column="register_address" jdbcType="VARCHAR" property="registerAddress" />
    <result column="register_date" jdbcType="TIMESTAMP" property="registerDate" />
    <result column="company_mail" jdbcType="VARCHAR" property="companyMail" />
    <result column="register_capital" jdbcType="DECIMAL" property="registerCapital" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="orgform" jdbcType="VARCHAR" property="orgform" />
    <result column="company_stock_name" jdbcType="VARCHAR" property="companyStockName" />
    <result column="corporate_id_number" jdbcType="VARCHAR" property="corporateIdNumber" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="market_stock_type" jdbcType="VARCHAR" property="marketStockType" />
    <result column="hidden_key" jdbcType="VARCHAR" property="hiddenKey" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="system_state" jdbcType="VARCHAR" property="systemState" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_user, create_time, update_user, update_time, deleteflag, company_code, 
    zh_name, zh_sort_name, corporate, organisation_no, register_address, register_date, 
    company_mail, register_capital, phone, orgform, company_stock_name, corporate_id_number, 
    remark, market_stock_type, hidden_key,
    source, system_state
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.CompanyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_company
    where id = #{id,jdbcType=VARCHAR}
  </select>
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">-->
<!--    delete from sa_company-->
<!--    where id = #{id,jdbcType=VARCHAR}-->
<!--  </delete>-->
<!--  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.CompanyExample">-->
<!--    delete from sa_company-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.stock.service.platform.common.entity.Company">-->
<!--    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">-->
<!--      SELECT CONCAT(uuid_short(),'')-->
<!--    </selectKey>-->
<!--    insert into sa_company (id, create_user, create_time, -->
<!--      update_user, update_time, deleteflag, -->
<!--      company_code, zh_name, zh_sort_name, -->
<!--      corporate, organisation_no, register_address, -->
<!--      register_date, company_mail, register_capital, -->
<!--      phone, orgform, company_stock_name, -->
<!--      corporate_id_number,-->
<!--      remark, market_stock_type,-->
<!--      hidden_key, source, system_state-->
<!--      )-->
<!--    values (#{id,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, -->
<!--      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{deleteflag,jdbcType=VARCHAR}, -->
<!--      #{companyCode,jdbcType=VARCHAR}, #{zhName,jdbcType=VARCHAR}, #{zhSortName,jdbcType=VARCHAR}, -->
<!--      #{corporate,jdbcType=VARCHAR}, #{organisationNo,jdbcType=VARCHAR}, #{registerAddress,jdbcType=VARCHAR}, -->
<!--      #{registerDate,jdbcType=TIMESTAMP}, #{companyMail,jdbcType=VARCHAR}, #{registerCapital,jdbcType=DECIMAL}, -->
<!--      #{phone,jdbcType=VARCHAR}, #{orgform,jdbcType=VARCHAR}, #{companyStockName,jdbcType=VARCHAR}, -->
<!--      #{corporateIdNumber,jdbcType=VARCHAR},-->
<!--      #{remark,jdbcType=VARCHAR}, #{marketStockType,jdbcType=VARCHAR},-->
<!--      #{hiddenKey,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR}, #{systemState,jdbcType=VARCHAR}-->
<!--      )-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.Company">-->
<!--    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">-->
<!--      SELECT CONCAT(uuid_short(),'')-->
<!--    </selectKey>-->
<!--    insert into sa_company-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      id,-->
<!--      <if test="createUser != null">-->
<!--        create_user,-->
<!--      </if>-->
<!--      <if test="createTime != null">-->
<!--        create_time,-->
<!--      </if>-->
<!--      <if test="updateUser != null">-->
<!--        update_user,-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        update_time,-->
<!--      </if>-->
<!--      <if test="deleteflag != null">-->
<!--        deleteflag,-->
<!--      </if>-->
<!--      <if test="companyCode != null">-->
<!--        company_code,-->
<!--      </if>-->
<!--      <if test="zhName != null">-->
<!--        zh_name,-->
<!--      </if>-->
<!--      <if test="zhSortName != null">-->
<!--        zh_sort_name,-->
<!--      </if>-->
<!--      <if test="corporate != null">-->
<!--        corporate,-->
<!--      </if>-->
<!--      <if test="organisationNo != null">-->
<!--        organisation_no,-->
<!--      </if>-->
<!--      <if test="registerAddress != null">-->
<!--        register_address,-->
<!--      </if>-->
<!--      <if test="registerDate != null">-->
<!--        register_date,-->
<!--      </if>-->
<!--      <if test="companyMail != null">-->
<!--        company_mail,-->
<!--      </if>-->
<!--      <if test="registerCapital != null">-->
<!--        register_capital,-->
<!--      </if>-->
<!--      <if test="phone != null">-->
<!--        phone,-->
<!--      </if>-->
<!--      <if test="orgform != null">-->
<!--        orgform,-->
<!--      </if>-->
<!--      <if test="companyStockName != null">-->
<!--        company_stock_name,-->
<!--      </if>-->
<!--      <if test="corporateIdNumber != null">-->
<!--        corporate_id_number,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="marketStockType != null">-->
<!--        market_stock_type,-->
<!--      </if>-->
<!--      <if test="hiddenKey != null">-->
<!--        hidden_key,-->
<!--      </if>-->
<!--      <if test="source != null">-->
<!--        source,-->
<!--      </if>-->
<!--      <if test="systemState != null">-->
<!--        system_state,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      #{id,jdbcType=VARCHAR},-->
<!--      <if test="createUser != null">-->
<!--        #{createUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createTime != null">-->
<!--        #{createTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="updateUser != null">-->
<!--        #{updateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        #{updateTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="deleteflag != null">-->
<!--        #{deleteflag,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="companyCode != null">-->
<!--        #{companyCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="zhName != null">-->
<!--        #{zhName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="zhSortName != null">-->
<!--        #{zhSortName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="corporate != null">-->
<!--        #{corporate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="organisationNo != null">-->
<!--        #{organisationNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="registerAddress != null">-->
<!--        #{registerAddress,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="registerDate != null">-->
<!--        #{registerDate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="companyMail != null">-->
<!--        #{companyMail,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="registerCapital != null">-->
<!--        #{registerCapital,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="phone != null">-->
<!--        #{phone,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orgform != null">-->
<!--        #{orgform,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="companyStockName != null">-->
<!--        #{companyStockName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="corporateIdNumber != null">-->
<!--        #{corporateIdNumber,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="marketStockType != null">-->
<!--        #{marketStockType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="hiddenKey != null">-->
<!--        #{hiddenKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="source != null">-->
<!--        #{source,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="systemState != null">-->
<!--        #{systemState,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.CompanyExample" resultType="java.lang.Long">
    select count(*) from sa_company
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_company
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteflag != null">
        deleteflag = #{record.deleteflag,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.zhName != null">
        zh_name = #{record.zhName,jdbcType=VARCHAR},
      </if>
      <if test="record.zhSortName != null">
        zh_sort_name = #{record.zhSortName,jdbcType=VARCHAR},
      </if>
      <if test="record.corporate != null">
        corporate = #{record.corporate,jdbcType=VARCHAR},
      </if>
      <if test="record.organisationNo != null">
        organisation_no = #{record.organisationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.registerAddress != null">
        register_address = #{record.registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.registerDate != null">
        register_date = #{record.registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyMail != null">
        company_mail = #{record.companyMail,jdbcType=VARCHAR},
      </if>
      <if test="record.registerCapital != null">
        register_capital = #{record.registerCapital,jdbcType=DECIMAL},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.orgform != null">
        orgform = #{record.orgform,jdbcType=VARCHAR},
      </if>
      <if test="record.companyStockName != null">
        company_stock_name = #{record.companyStockName,jdbcType=VARCHAR},
      </if>
      <if test="record.corporateIdNumber != null">
        corporate_id_number = #{record.corporateIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.marketStockType != null">
        market_stock_type = #{record.marketStockType,jdbcType=VARCHAR},
      </if>
      <if test="record.hiddenKey != null">
        hidden_key = #{record.hiddenKey,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.systemState != null">
        system_state = #{record.systemState,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
<!--  <update id="updateByExample" parameterType="map">-->
<!--    update sa_company-->
<!--    set id = #{record.id,jdbcType=VARCHAR},-->
<!--      create_user = #{record.createUser,jdbcType=VARCHAR},-->
<!--      create_time = #{record.createTime,jdbcType=TIMESTAMP},-->
<!--      update_user = #{record.updateUser,jdbcType=VARCHAR},-->
<!--      update_time = #{record.updateTime,jdbcType=TIMESTAMP},-->
<!--      deleteflag = #{record.deleteflag,jdbcType=VARCHAR},-->
<!--      company_code = #{record.companyCode,jdbcType=VARCHAR},-->
<!--      zh_name = #{record.zhName,jdbcType=VARCHAR},-->
<!--      zh_sort_name = #{record.zhSortName,jdbcType=VARCHAR},-->
<!--      corporate = #{record.corporate,jdbcType=VARCHAR},-->
<!--      organisation_no = #{record.organisationNo,jdbcType=VARCHAR},-->
<!--      register_address = #{record.registerAddress,jdbcType=VARCHAR},-->
<!--      register_date = #{record.registerDate,jdbcType=TIMESTAMP},-->
<!--      company_mail = #{record.companyMail,jdbcType=VARCHAR},-->
<!--      register_capital = #{record.registerCapital,jdbcType=DECIMAL},-->
<!--      phone = #{record.phone,jdbcType=VARCHAR},-->
<!--      orgform = #{record.orgform,jdbcType=VARCHAR},-->
<!--      company_stock_name = #{record.companyStockName,jdbcType=VARCHAR},-->
<!--      corporate_id_number = #{record.corporateIdNumber,jdbcType=VARCHAR},-->
<!--      remark = #{record.remark,jdbcType=VARCHAR},-->
<!--      market_stock_type = #{record.marketStockType,jdbcType=VARCHAR},-->
<!--      hidden_key = #{record.hiddenKey,jdbcType=VARCHAR},-->
<!--      source = #{record.source,jdbcType=VARCHAR},-->
<!--      system_state = #{record.systemState,jdbcType=VARCHAR}-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.Company">
    update sa_company
    <set>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteflag != null">
        deleteflag = #{deleteflag,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="zhName != null">
        zh_name = #{zhName,jdbcType=VARCHAR},
      </if>
      <if test="zhSortName != null">
        zh_sort_name = #{zhSortName,jdbcType=VARCHAR},
      </if>
      <if test="corporate != null">
        corporate = #{corporate,jdbcType=VARCHAR},
      </if>
      <if test="organisationNo != null">
        organisation_no = #{organisationNo,jdbcType=VARCHAR},
      </if>
      <if test="registerAddress != null">
        register_address = #{registerAddress,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyMail != null">
        company_mail = #{companyMail,jdbcType=VARCHAR},
      </if>
      <if test="registerCapital != null">
        register_capital = #{registerCapital,jdbcType=DECIMAL},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="orgform != null">
        orgform = #{orgform,jdbcType=VARCHAR},
      </if>
      <if test="companyStockName != null">
        company_stock_name = #{companyStockName,jdbcType=VARCHAR},
      </if>
      <if test="corporateIdNumber != null">
        corporate_id_number = #{corporateIdNumber,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="marketStockType != null">
        market_stock_type = #{marketStockType,jdbcType=VARCHAR},
      </if>
      <if test="hiddenKey != null">
        hidden_key = #{hiddenKey,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="systemState != null">
        system_state = #{systemState,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
<!--  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Company">-->
<!--    update sa_company-->
<!--    set create_user = #{createUser,jdbcType=VARCHAR},-->
<!--      create_time = #{createTime,jdbcType=TIMESTAMP},-->
<!--      update_user = #{updateUser,jdbcType=VARCHAR},-->
<!--      update_time = #{updateTime,jdbcType=TIMESTAMP},-->
<!--      deleteflag = #{deleteflag,jdbcType=VARCHAR},-->
<!--      company_code = #{companyCode,jdbcType=VARCHAR},-->
<!--      zh_name = #{zhName,jdbcType=VARCHAR},-->
<!--      zh_sort_name = #{zhSortName,jdbcType=VARCHAR},-->
<!--      corporate = #{corporate,jdbcType=VARCHAR},-->
<!--      organisation_no = #{organisationNo,jdbcType=VARCHAR},-->
<!--      register_address = #{registerAddress,jdbcType=VARCHAR},-->
<!--      register_date = #{registerDate,jdbcType=TIMESTAMP},-->
<!--      company_mail = #{companyMail,jdbcType=VARCHAR},-->
<!--      register_capital = #{registerCapital,jdbcType=DECIMAL},-->
<!--      phone = #{phone,jdbcType=VARCHAR},-->
<!--      orgform = #{orgform,jdbcType=VARCHAR},-->
<!--      company_stock_name = #{companyStockName,jdbcType=VARCHAR},-->
<!--      corporate_id_number = #{corporateIdNumber,jdbcType=VARCHAR},-->
<!--      remark = #{remark,jdbcType=VARCHAR},-->
<!--      market_stock_type = #{marketStockType,jdbcType=VARCHAR},-->
<!--      hidden_key = #{hiddenKey,jdbcType=VARCHAR},-->
<!--      source = #{source,jdbcType=VARCHAR},-->
<!--      system_state = #{systemState,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=VARCHAR}-->
<!--  </update>-->
<!--  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.CompanyExample" resultMap="BaseResultMap">-->
<!--    select-->
<!--    <if test="distinct">-->
<!--      distinct-->
<!--    </if>-->
<!--    <include refid="Base_Column_List" />-->
<!--    from sa_company-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--    <if test="orderByClause != null">-->
<!--      order by ${orderByClause}-->
<!--    </if>-->
<!--  </select>-->
</mapper>