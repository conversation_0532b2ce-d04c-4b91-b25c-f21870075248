package com.stock.service.platform.common.tree.inter;

import java.util.List;

public interface TreeNode {

     String getId();

     void setId(String id);

     String getTreeId();

     void setTreeId(String treeId);

     String getPid();

     void setPid(String pid);

     String getName();

     void setName(String name);

     Integer getTreeOrder();

     void setTreeOrder(Integer treeOrder) ;
     String getTreeLevel();

     void setTreeLevel(String treeLevel);

     String getStatus();

     void setStatus(String status);

     List<? extends TreeNode> getChildren();

     Object getAttribute();

}
