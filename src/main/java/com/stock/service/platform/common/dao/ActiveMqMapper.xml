<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ActiveMqMapper">
  <insert id="insertActivemqTopic" parameterType="java.util.Map">
    INSERT INTO activemq_topic (id,message_id,sys_code, info, is_success, business_type, operation_type, create_time, update_time)
    VALUES (CONCAT(uuid_short(), ''), #{messageId},#{sysCode}, #{info}, #{isSuccess}, #{businessType}, #{operationType}, now(), now())
  </insert>

  <update id="updateIsSuccess" parameterType="java.util.Map">
    update activemq_topic set is_success=#{status},error_message=#{errorMessage},update_time=now() where message_id = #{uid} and sys_code = #{sysCode}
  </update>


  <insert id="insertReceiveInfo" parameterType="java.util.Map">
    INSERT INTO activemq_topic (id,message_id,sys_code, info, is_success, business_type, operation_type, create_time, update_time)
    VALUES (CONCAT(uuid_short(), ''), #{messageId},#{sysCode}, #{info}, #{isSuccess}, #{businessType}, #{operationType}, now(), now())
  </insert>


  <select id="selectActivemqTopicByUid" parameterType="string"
          resultType="com.stock.service.platform.common.dto.ActivemqTopicDto">
    select id,
           message_id as messageId,
           sys_code as sysCode,
           info,
           is_success as isSuccess,
           message_type as messageType,
           error_message as errorMessage,
           business_type as businessType,
           operation_type as operationType
    from activemq_topic
    where message_id = #{messageId}
  </select>
    <select id="getActiveMq" resultType="com.stock.service.platform.common.dto.ActivemqTopicDto">
        select id,
               message_id as messageId,
               sys_code as sysCode,
               info,
               is_success as isSuccess,
               message_type as messageType,
               error_message as errorMessage,
               business_type as businessType,
               operation_type as operationType,
               create_time as createTime
        from activemq_topic
        where message_id = #{uid} and sys_code = #{sysCode}
    </select>
</mapper>