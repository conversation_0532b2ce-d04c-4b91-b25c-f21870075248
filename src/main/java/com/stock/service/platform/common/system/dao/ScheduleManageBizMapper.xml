<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.service.platform.common.system.dao.ScheduleManageBizMapper">
    <insert id="insertTask"  parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        <selectKey keyProperty="id" order="BEFORE"  resultType="java.lang.String">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into kettle_task
        (id,TASK_NAME,START_TIME,END_TIME,TASK_TYPE,STATUS,DATA_TIME,ERROR_MESSAGE)
        values
        (#{id},#{taskName},#{startTime},#{endTime},#{taskType},#{status},#{dataTime},#{errorMessage})
    </insert>
    <update id="updateTask" parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        update kettle_task
        set STATUS = '0'
        where id = #{id}
    </update>
    <update id="updateTaxationCal" parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        update kettle_task kt
        <set>
            <if test="status != null">
                kt.STATUS = #{status},
            </if>
            <if test="errorMessage != null">
                kt.ERROR_MESSAGE = #{errorMessage},
            </if>
        </set>
        where kt.id = #{id}
    </update>

    <select id="queryTaskList"  parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto"
            resultType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        select
        kt.id id,
        kt.TASK_NAME taskName,
        date_format(kt.START_TIME,'%Y-%m-%d %H:%i:%s') beginTime,
        date_format(kt.END_TIME,'%Y-%m-%d %H:%i:%s') overTime,
        kt.TASK_TYPE as taskType,
        kt.START_TIME startTime,
        kt.END_TIME endTime,
        (select sc.code_name from sa_code sc where sc.code_no = 'KETTLE_TASK_TYPE' and sc.code_value = kt.TASK_TYPE) as taskTypeText,
        (case when kt.STATUS = '0' then '成功' else '失败' end) status
        from kettle_task kt
        <where>
            <if test="taskName != null and taskName != ''">
                and kt.TASK_NAME like concat('%',#{taskName},'%')
            </if>
            <if test="beginTime != null and beginTime != ''">
                and kt.START_TIME  <![CDATA[ >= ]]> concat(#{beginTime}, ' 00:00:00')
            </if>
            <if test="overTime != null and overTime != ''">
                and kt.END_TIME  <![CDATA[ <= ]]> concat(#{overTime}, ' 23:59:59')
            </if>
            <if test="taskType != null and taskType != ''">
                and kt.TASK_TYPE = #{taskType}
            </if>
            <if test="status != null and status != ''">
                and kt.STATUS = #{status}
            </if>
            <choose>
                <when test="queryType != null and queryType == 'java'.toString()">
                    and kt.TASK_TYPE not in ('SYC_PRODUCT_NET_VALUE','SYC_EXERCISE_RESULT')
                </when>
                <when test="queryType != null and queryType == 'kettle'.toString()">
                    and kt.TASK_TYPE in ('SYC_PRODUCT_NET_VALUE','SYC_EXERCISE_RESULT')
                </when>
            </choose>
        </where>
        ORDER BY kt.START_TIME DESC
    </select>
    <select id="selectFalseList" parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto"
            resultType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        SELECT
            kt.id id,
            kt.TASK_NAME taskName,
            kt.START_TIME startTime,
            kt.END_TIME endTime,
            kt.TASK_TYPE taskType,
            kt.STATUS status,
            kt.DATA_TIME dataTime
        FROM kettle_task kt
        WHERE kt.STATUS = '1'
          and kt.TASK_TYPE = 'TAXATION_CAL_DF'
    </select>
    <select id="queryTaskCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM kettle_task kt
        WHERE kt.STATUS = #{status}
          and kt.TASK_TYPE = #{taskType}
          and DATE(kt.START_TIME) = #{startTime}
    </select>
    <update id="updateUserInfoInTask" parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        update kettle_task kt
        <set>
            <if test="status != null">
                kt.STATUS = #{status},
            </if>
            <if test="endTime != null">
                kt.END_TIME = #{endTime},
            </if>
        </set>
        where kt.TASK_NAME = #{taskName}
        and kt.TASK_TYPE = #{taskType}
        and kt.DATA_TIME = #{dataTime}
    </update>

    <update id="updateTaskInfoById" parameterType="com.stock.service.platform.common.system.dto.KettleTaskDto">
        update kettle_task
        set
        <if test="errorMessage!= null and errorMessage != ''">
            ERROR_MESSAGE = #{errorMessage},
        </if>
        STATUS = #{status}
        where id = #{id}
    </update>

    <select id="getCountOfTaxactionCal" parameterType="java.lang.String" resultType="int">
        select count(1)
        from kettle_task kt
        where kt.TASK_TYPE = 'TAXATION_CAL_DF' and date_format(kt.DATA_TIME,'%Y-%m-%d') = #{calDate}
    </select>

    <update id="updateTaxationStatusSuccessUntilDay" parameterType="java.lang.String">
        update kettle_task kt
        set
            kt.STATUS = '0'
        where kt.TASK_TYPE = 'TAXATION_CAL_DF' and date_format(kt.start_time,'%Y-%m-%d')  &lt;= #{selDate}
    </update>
</mapper>