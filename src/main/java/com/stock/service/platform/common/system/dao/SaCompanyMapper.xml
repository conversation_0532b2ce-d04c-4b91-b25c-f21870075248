<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.service.platform.common.system.dao.SaCompanyMapper">
    <select id="queryPagingCompanyBySelective" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.id id,
        sc.company_code companyCode,
        sc.zh_name zhName,
        sc.corporate corporate,
        sc.organisation_no organisationNo,
        GROUP_CONCAT(org.org_name SEPARATOR ',') businessDepartmentName,
        sc.company_stock_name companyStockName,
        sc.corporate_id_number corporateIdNumber,
        sc.social_unified_credit_code socialUnifiedCreditCode,
        (case sc.deleteflag when '0' then '正常' when '2' then '删除中' ELSE '注销' END) statusName,
        sc.deleteflag deleteflag,
        sc.hidden_key seriakey,
        sc.parent_id parentId,
        sc.parent_company_code parentCompanyCode
        from sa_company sc
        LEFT JOIN sa_org org ON find_in_set(org.id, sc.organisation_no)
        <where>
            sc.deleteflag = '0'
            AND (sc.parent_id IS NULL OR sc.parent_id = '')
            AND (sc.parent_company_code IS NULL OR sc.parent_company_code = '')
            <if test="orgIds != null and orgIds.size !=0">
                and org.id in
                <foreach collection="orgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null and userId !='' ">
                AND sucm.user_id = #{userId}
            </if>
            <if test="companyCode != null and companyCode !='' ">
                AND sc.company_code like concat('%',#{companyCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="zhName != null and zhName !='' ">
                and sc.zh_name like concat('%',#{zhName,jdbcType=VARCHAR},'%')
            </if>
            <if test="companyCodeList != null and companyCodeList.size !=0">
                and sc.company_code in
                <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ids != null and ids.size !=0">
                and sc.id in
                <foreach collection="ids" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgId != null and orgId !='' ">
                and sc.organisation_no like concat('%',#{orgId,jdbcType=VARCHAR},'%')
            </if>
        </where>
        GROUP BY sc.id
        order by sc.update_time desc
    </select>
<!--    LEFT JOIN sa_user_company_map sucm ON sucm.company_id = sc.id-->

    <select id="getCompanyList" parameterType="java.util.List" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.id id,
        sc.company_code companyCode,
        sc.parent_company_code parentCompanyCode,
        sc.zh_name zhName,
        sc.corporate corporate,
        sc.organisation_no organisationNo,
        org.org_name businessDepartmentName,
        sc.company_stock_name companyStockName,
        sc.corporate_id_number corporateIdNumber,
        (case sc.deleteflag when '0' then '正常' when '2' then '删除中' ELSE '注销' END) statusName,
        sc.deleteflag deleteflag,
        sc.hidden_key seriakey,
        sc.parent_id parentId,
        sc.parent_company_code parentCompanyCode,
        sc.social_unified_credit_code socialUnifiedCreditCode
        from sa_company sc
        LEFT JOIN sa_org org ON find_in_set(org.id, sc.organisation_no)
        <where>
            sc.deleteflag = '0'
            <if test="codeList != null and codeList.size !=0">
                and sc.parent_company_code in
                <foreach collection="codeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        order by sc.update_time desc
    </select>

    <update id="updateByPrimaryKeys">
        update sa_company set deleteflag = #{deleteFlag} where id = #{id}
    </update>

    <update id="deleteByPrimaryKeys">
        delete from sa_company
        where id = #{id}
    </update>

    <update id="deleteSaOrgByPrimaryKeys">
        delete from sa_org
        where company_id = #{id}
    </update>

    <select id="getCompanyById" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.id id,
        sc.company_code companyCode,
        sc.zh_name zhName,
        sc.zh_sort_name zhSortName,
        sc.corporate corporate,
        sc.organisation_no organisationNo,
        org.org_name businessDepartmentName,
        sc.company_stock_name companyStockName,
        sc.corporate_id_number corporateIdNumber,
        sc.market_stock_type marketStockType,
        sc.orgform orgform,
        sc.register_date registerDate,
        sc.register_address registerAddress,
        sc.remark remark,
        sc.company_mail companyMail,
        sc.phone phone,
        sc.register_capital registerCapital,
        sc.listing_date listingDate,
        sc.issue_price issuePrice,
        sc.capital_stock_a capitalStockA,
        sc.capital_stock_b capitalStockB,
        sc.capital_stock_h capitalStockH,
        sc.capital_stock_total capitalStockTotal,
        sc.social_unified_credit_code socialUnifiedCreditCode,
        sc.parent_id parentId,
        sc.deleteflag deleteflag,
        sc.system_state systemState,
        sc.market market
        from sa_company sc
        LEFT JOIN sa_org org ON find_in_set(org.id, sc.organisation_no)
        LEFT JOIN `capital-repmanage-jt`.sa_company_cloud scc on scc.company_code=sc.company_code
        where sc.id = #{id}
        GROUP BY sc.id
    </select>

    <select id="selectAllCompany" resultType="java.lang.String">
        SELECT sc.company_code from sa_company sc WHERE deleteflag='0' and (parent_company_code is null or parent_company_code = '')
    </select>

    <select id="queryAllChildCompany" resultType="java.lang.String">
        SELECT sc.company_code
        FROM sa_company sc
        WHERE deleteflag='0' AND parent_company_code IS NOT NULL AND parent_company_code <![CDATA[ <> ]]> ''
    </select>

    <select id="queryCompanyByDepartmentId" resultType="java.lang.String">
		SELECT sc.company_code FROM sa_company sc
        WHERE sc.organisation_no = #{orgId}
        and sc.deleteflag = '0'
    </select>
    <select id="queryDepartmentIdByBranchCode" resultType="java.lang.String">
        SELECT DISTINCT org.id	FROM
        sa_org org
        WHERE
         org.p_org_id = #{orgId}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        update sa_company
        <set>
            <if test="companyCode != null">
                company_code = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="zhName != null">
                zh_name = #{zhName,jdbcType=VARCHAR},
            </if>
            <if test="zhSortName != null">
                zh_sort_name = #{zhSortName,jdbcType=VARCHAR},
            </if>
            <if test="corporate != null">
                corporate = #{corporate,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                organisation_no = #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                register_address = #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                register_date = #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyMail != null">
                company_mail = #{companyMail,jdbcType=VARCHAR},
            </if>

            register_capital = #{registerCapital,jdbcType=DECIMAL},

            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteflag != null">
                deleteflag = #{deleteflag,jdbcType=VARCHAR},
            </if>
            <if test="orgform != null">
                orgform = #{orgform,jdbcType=VARCHAR},
            </if>
            <if test="companyStockName != null">
                company_stock_name = #{companyStockName,jdbcType=VARCHAR},
            </if>
            <if test="corporateIdNumber != null">
                corporate_id_number = #{corporateIdNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="marketStockType != null">
                market_stock_type = #{marketStockType,jdbcType=VARCHAR},
            </if>
            <if test="listingDate != null">
                listing_date = #{listingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="issuePrice != null">
                issue_price = #{issuePrice,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockA != null">
                capital_stock_a = #{capitalStockA,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockB != null">
                capital_stock_b = #{capitalStockB,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockH != null">
                capital_stock_h =  #{capitalStockH,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockTotal != null">
                capital_stock_total = #{capitalStockTotal,jdbcType=DECIMAL},
            </if>
            <if test="socialUnifiedCreditCode != null">
                social_unified_credit_code = #{socialUnifiedCreditCode,jdbcType=VARCHAR},
            </if>
            <if test="market != null">
                market = #{market,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateSaOrgByPrimaryKeys">
        update sa_org set status = #{status} where company_id = #{companyId}
    </update>

    <insert id="insertSelective" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into sa_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            hidden_key,
            legal_person_id_type,
            company_status,
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="zhName != null">
                zh_name,
            </if>
            <if test="zhSortName != null">
                zh_sort_name,
            </if>
            <if test="corporate != null">
                corporate,
            </if>
            <if test="organisationNo != null">
                organisation_no,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerDate != null">
                register_date,
            </if>
            <if test="companyMail != null">
                company_mail,
            </if>
            <if test="registerCapital != null">
                register_capital,
            </if>

            <if test="phone != null">
                phone,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteflag != null">
                deleteflag,
            </if>
            <if test="orgform != null">
                orgform,
            </if>
            <if test="companyStockName != null">
                company_stock_name,
            </if>
            <if test="corporateIdNumber != null">
                corporate_id_number,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="marketStockType != null">
                market_stock_type,
            </if>
            <if test="listingDate != null">
                listing_date,
            </if>
            <if test="issuePrice != null">
                issue_price,
            </if>
            <if test="capitalStockA != null">
                capital_stock_a,
            </if>
            <if test="capitalStockB != null">
                capital_stock_b,
            </if>
            <if test="capitalStockH != null">
                capital_stock_h,
            </if>
            <if test="capitalStockTotal != null">
                capital_stock_total,
            </if>
            <if test="socialUnifiedCreditCode != null">
                social_unified_credit_code,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="parentCompanyCode != null">
                parent_company_code,
            </if>
            <if test="market != null">
                market,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            uuid(),
            0,
            1,
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="zhName != null">
                #{zhName,jdbcType=VARCHAR},
            </if>
            <if test="zhSortName != null">
                #{zhSortName,jdbcType=VARCHAR},
            </if>
            <if test="corporate != null">
                #{corporate,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyMail != null">
                #{companyMail,jdbcType=VARCHAR},
            </if>
            <if test="registerCapital != null">
                #{registerCapital,jdbcType=DECIMAL},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteflag != null">
                #{deleteflag,jdbcType=VARCHAR},
            </if>
            <if test="orgform != null">
                #{orgform,jdbcType=VARCHAR},
            </if>
            <if test="companyStockName != null">
                #{companyStockName,jdbcType=VARCHAR},
            </if>
            <if test="corporateIdNumber != null">
                #{corporateIdNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="marketStockType != null">
                #{marketStockType,jdbcType=VARCHAR},
            </if>
            <if test="listingDate != null">
                #{listingDate,jdbcType=TIMESTAMP},
            </if>
            <if test="issuePrice != null">
                #{issuePrice,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockA != null">
               #{capitalStockA,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockB != null">
                #{capitalStockB,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockH != null">
                #{capitalStockH,jdbcType=DECIMAL},
            </if>
            <if test="capitalStockTotal != null">
                #{capitalStockTotal,jdbcType=DECIMAL},
            </if>
            <if test="socialUnifiedCreditCode != null">
                #{socialUnifiedCreditCode,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=VARCHAR},
            </if>
            <if test="parentCompanyCode != null">
                #{parentCompanyCode,jdbcType=VARCHAR},
            </if>
            <if test="market != null">
                #{market,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="queryCompanyUsers" resultType="com.stock.service.platform.common.system.dto.SaUserDto">
        SELECT su.id FROM sa_user su
        WHERE su.id IN (SELECT sp.id FROM sa_person sp
                        WHERE sp.org_id IN (SELECT so.id FROM sa_org so WHERE so.company_id = #{id}))
    </select>

    <select id="getCompanyByCode" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.company_code companyCode,
        sc.company_stock_name companyStockName,
        sc.zh_name zhName,
        sc.zh_sort_name zhSortName,
        sc.market_stock_type marketStockType
        from kettle_company sc
        where
        sc.company_code = #{companyCode}
        limit 1
    </select>

    <select id="selectCompanyDictByOrgId" resultType="java.util.Map" parameterType="java.util.Map">
        select
        so.id as `key`,
        so.id as `value`,
        sc.zh_name as text
        from sa_company sc
        left join sa_org so on so.company_id = sc.id and so.org_type = '4' and so.status = '1'
        left join sa_org so2 on so2.id = so.p_org_id and so.status = '1'
        where sc.deleteflag = '0'
        <if test="params.orgType != null and params.orgType == '2'.toString()">
          and ( so.p_org_id = #{params.pId} or so2.p_org_id = #{params.pId} )
        </if>
        <if test="params.orgType != null and params.orgType == '3'.toString()">
            and (sc.organisation_no = #{params.pId}
                    <if test="params.limitCodeStr != null and params.limitCodeStr != ''">
                        or so2.id = #{params.limitCodeStr} or so2.p_org_id = #{params.limitCodeStr}
                    </if>
                )
        </if>
        <if test="params.orgType != null and params.orgType == '4'.toString()">
            and so.id = #{params.pId}
        </if>
    </select>
    <select id="getCompanyByCode1" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.company_code companyCode,
        sc.company_stock_name companyStockName,
        sc.zh_name zhName,
        sc.zh_sort_name zhSortName,
        sc.market_stock_type marketStockType
        from sa_company sc
        where
        sc.company_code = #{companyCode}
        limit 1
    </select>

    <select id="selectByIdSeriakey" parameterType="java.util.Map" resultType="java.lang.Integer">
        select
            count(1)
        from sa_company
        where id = #{id,jdbcType=VARCHAR}
          and hidden_key = #{seriakey,jdbcType=VARCHAR}
    </select>

    <select id="selectCompanyByUserId" parameterType="java.lang.String" resultType="java.lang.String">
        select company_id from sa_user_company_map where user_id = #{userId}
    </select>
    <select id="getCompanyChildList" parameterType="java.util.List" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
        sc.id id,
        sc.company_code companyCode,
        sc.zh_name zhName,
        sc.corporate corporate,
        sc.organisation_no organisationNo,
        sc.company_stock_name companyStockName,
        sc.corporate_id_number corporateIdNumber,
        (case sc.deleteflag when '0' then '正常' when '2' then '删除中' ELSE '注销' END) statusName,
        sc.deleteflag deleteflag,
        sc.parent_id parentId,
        sc.hidden_key seriakey
        from sa_company sc
        <where>
            <if test="codeList != null and codeList.size !=0">
                and sc.parent_company_code in
                <foreach collection="codeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        order by sc.update_time desc
    </select>

    <select id="queryCompanyByParentId" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" >
        select id FROM sa_company where parent_id = #{id}
    </select>

    <select id="getCompany" resultType="java.util.Map">
        select sc.parent_id as parentId,
               so.id         as `key`,
               so.id         as `value`,
               so.company_id as `companyId`,
               so.org_name   as text
        from sa_company sc
                 left join sa_org so on so.company_id = sc.id

    </select>
    <select id="getChildrenCompanyCode" resultType="int">
        select
            count(1) + 1
        from
            sa_company so
        where  parent_company_code = #{companyCode}
    </select>
    <select id="selectOrgSelectionTree" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select
            so.id as `key`,
            so.id as `value`,
            so.org_name as text,
            so.p_org_id as `parentId`
        from sa_org so
        where so.status = '1'
          and so.org_type = '4'
    </select>
    <select id="getTree" resultType="com.stock.core.dto.TreeDto">
        select DISTINCT sc.parent_id as parentId,
                        so.id         as `value`,
                        so.company_id as `id`,
                        concat(so.org_name, '(', sc.company_code, ')') as name
        from sa_company sc
                 inner join sa_org so on so.company_id = sc.id
        GROUP BY so.company_id,so.org_name
    </select>
    <select id="selectOldCompanyCode" parameterType="java.lang.String" resultType="java.lang.String">
        select old_company_code from company_code_convert where batch > 0 AND old_company_code = #{companyCode}
    </select>

    <select id="selectAllCompanyCodeAndName"
            resultType="com.stock.service.platform.common.system.dto.CompanyCodeAndName">
        select scc.company_code companyCode, scc.zh_name zhName, scc.zh_sort_name zhSortName
        from `capital-repmanage-jt`.sa_company_cloud scc
        where scc.liststate IN ('0','1')
          and scc.belongs_plate in ('00', '01', '02', '04', '05', '07', '09')
        group by scc.company_code, scc.zh_name, scc.zh_sort_name
    </select>
</mapper>
