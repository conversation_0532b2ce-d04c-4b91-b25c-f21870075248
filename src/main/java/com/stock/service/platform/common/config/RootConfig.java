package com.stock.service.platform.common.config;

import com.stock.core.file.DefaultFileServer;
import com.stock.core.file.FileServer;
import com.stock.core.misc.CodeListBean;
import com.stock.core.misc.ExcelHandler;
import com.stock.core.misc.ExcelMappingFactoryBean;
import com.stock.core.security.token.AccessTokenRedisStoreStrategy;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

@Configuration
@EnableAsync
@EnableScheduling
public class RootConfig {

    @Bean
    public CodeListBean codeListBean() {
        return new CodeListBean();
    }

    @Bean
    public AccessTokenStoreStrategy accessTokenStoreStrategy() {
        AccessTokenStoreStrategy accessTokenStoreStrategy = new AccessTokenRedisStoreStrategy();
        return accessTokenStoreStrategy;
    }

    @Bean
    public ExcelMappingFactoryBean excelExportMappingFactoryBean() {
        ExcelMappingFactoryBean excelExportMappingFactoryBean = new ExcelMappingFactoryBean();
        excelExportMappingFactoryBean.setMappingFilePath(new String[] { "classpath*:/templates/*.ExcelRule.xml" });
        return excelExportMappingFactoryBean;
    }

    @Bean
    public ExcelHandler excelHandler() {
        return new ExcelHandler();
    }

    @Bean(name="fileServer")
    public FileServer cloudFileServer() {
        DefaultFileServer fileServer = new DefaultFileServer();
        fileServer.setIsMultiTenant(false);
        return fileServer;
    }

    @Bean
    public WebClient webClient() {
        HttpClient httpClient = HttpClient.create().responseTimeout(Duration.ofMinutes(5));
        ReactorClientHttpConnector connector = new ReactorClientHttpConnector(httpClient);
        return WebClient.builder().clientConnector(connector).build();
    }
}
