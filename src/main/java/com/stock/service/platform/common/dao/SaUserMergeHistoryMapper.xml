<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.SaUserMergeHistoryMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.SaUserMergeHistory">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="subsidiary_user_id" jdbcType="VARCHAR" property="subsidiaryUserId" />
    <result column="main_user_marge_role_id" jdbcType="VARCHAR" property="mainUserMargeRoleId" />
    <result column="merged_role_id" jdbcType="VARCHAR" property="mergedRoleId" />
    <result column="merge_flag" jdbcType="VARCHAR" property="mergeFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, subsidiary_user_id, main_user_marge_role_id, merged_role_id, create_time, 
    create_user
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_user_merge_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_user_merge_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_user_merge_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistoryExample">
    delete from sa_user_merge_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistory">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_user_merge_history (id, user_id, subsidiary_user_id, 
      main_user_marge_role_id, merged_role_id, merge_flag, create_time,
      create_user)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{subsidiaryUserId,jdbcType=VARCHAR}, 
      #{mainUserMargeRoleId,jdbcType=VARCHAR}, #{mergedRoleId,jdbcType=VARCHAR}, #{mergeFlag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{createUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistory">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_user_merge_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="userId != null">
        user_id,
      </if>
      <if test="subsidiaryUserId != null">
        subsidiary_user_id,
      </if>
      <if test="mainUserMargeRoleId != null">
        main_user_marge_role_id,
      </if>
      <if test="mergedRoleId != null">
        merged_role_id,
      </if>
      <if test="mergeFlag != null">
        merge_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryUserId != null">
        #{subsidiaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="mainUserMargeRoleId != null">
        #{mainUserMargeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="mergedRoleId != null">
        #{mergedRoleId,jdbcType=VARCHAR},
      </if>
      <if test="mergeFlag != null">
        #{mergeFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistoryExample" resultType="java.lang.Long">
    select count(*) from sa_user_merge_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_user_merge_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.subsidiaryUserId != null">
        subsidiary_user_id = #{record.subsidiaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.mainUserMargeRoleId != null">
        main_user_marge_role_id = #{record.mainUserMargeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.mergedRoleId != null">
        merged_role_id = #{record.mergedRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.mergeFlag != null">
        merge_flag = #{record.mergeFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_user_merge_history
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      subsidiary_user_id = #{record.subsidiaryUserId,jdbcType=VARCHAR},
      main_user_marge_role_id = #{record.mainUserMargeRoleId,jdbcType=VARCHAR},
      merged_role_id = #{record.mergedRoleId,jdbcType=VARCHAR},
      merge_flag = #{record.mergeFlag,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistory">
    update sa_user_merge_history
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryUserId != null">
        subsidiary_user_id = #{subsidiaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="mainUserMargeRoleId != null">
        main_user_marge_role_id = #{mainUserMargeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="mergedRoleId != null">
        merged_role_id = #{mergedRoleId,jdbcType=VARCHAR},
      </if>
      <if test="mergeFlag != null">
        merge_flag = #{mergeFlag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.SaUserMergeHistory">
    update sa_user_merge_history
    set user_id = #{userId,jdbcType=VARCHAR},
      subsidiary_user_id = #{subsidiaryUserId,jdbcType=VARCHAR},
      main_user_marge_role_id = #{mainUserMargeRoleId,jdbcType=VARCHAR},
      merged_role_id = #{mergedRoleId,jdbcType=VARCHAR},
      merge_flag = #{mergeFlag,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>