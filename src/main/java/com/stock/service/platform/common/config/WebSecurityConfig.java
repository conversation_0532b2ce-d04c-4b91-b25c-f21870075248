package com.stock.service.platform.common.config;

import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;
import com.alibaba.druid.wall.spi.MySqlWallProvider;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper.DefaultTyping;
import com.google.common.collect.Lists;
import com.stock.core.security.CustomExceptionTranslationFilter;
import com.stock.core.security.SystemSecurityMetadataSource;
import com.stock.core.security.handler.CustomAccessDeniedHandler;
import com.stock.core.security.handler.CustomAuthenticationEntryPoint;
import com.stock.core.security.token.AccessTokenRedisStoreStrategy;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import com.stock.core.security.token.TokenAuthenticationFilter;
import com.stock.service.platform.common.security.*;
import com.stock.service.platform.user.service.UserManagerService;
import org.springframework.beans.factory.BeanClassLoaderAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.vote.AffirmativeBased;
import org.springframework.security.access.vote.AuthenticatedVoter;
import org.springframework.security.access.vote.RoleVoter;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.jackson2.SecurityJackson2Modules;
import org.springframework.security.web.access.ExceptionTranslationFilter;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * websecurity相关配置
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter implements BeanClassLoaderAware {

    @Autowired
    RedisTemplate<String, ?> redisTemplate;

    private static final String[] IGNORE_URI = new String[]{
            "/health",
            "/user/getMailCaptcha", // 登录页忘记密码获取邮箱验证码
            "/user/saveNewPassword", // 登录页忘记密码保存
            "/user/getDynamicCode", // 登录页发送手机验证码
            "/user/getDynamicCodeAndCheck", // 整合 验证验证码和用户名登录获取验证码
            "/user/getUserNameDynamicCode", // 手机号登录(废弃)
            "/user/captchaCodeOnChange", // 登录页图形验证码(废弃改为点字验证)
            "/user/checkUser", // 登录时判断当前登录用户是内部用户还是外部用户
            "/common/getSystemConfigConstant",
            "/common/dayIsP", // 判断日期是不是批日
            "/captcha/**", // 点字验证码
            "/openapi/**", // 登录
            "/repManage/**",
            "/error",
            "/agreement/**"
    };

    private ClassLoader loader;

    @Autowired
    private UserManagerService userManagerService;

    @Autowired
    private CustomAuthenticationSuccessHandler authenticationSuccessHandler;

    @Autowired
    private CustomAuthenticationFailureHandler authenticationFailureHandler;

    @Autowired
    private CustomLogoutHandler logoutHandler;

    @Autowired
    private CustomLogoutSuccessHandler logoutSuccessHandler;

    @Override
    public void setBeanClassLoader(ClassLoader classLoader) {
        this.loader = classLoader;
    }

    /**
     * redis序列化
     *
     * @return
     */
    @Bean
    public RedisSerializer<Object> defaultRedisSerializer() {
        return new GenericJackson2JsonRedisSerializer(objectMapper());
    }

    /**
     * 序列化工具
     *
     * @return
     */
    private ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.activateDefaultTyping(mapper.getPolymorphicTypeValidator(), DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY);
        mapper.registerModules(SecurityJackson2Modules.getModules(this.loader));
        return mapper;
    }

    /**
     * 配置不需要校验的地址
     *
     * @param web
     * @throws Exception
     */
    @Override
    public void configure(WebSecurity web) throws Exception {
        web.ignoring().antMatchers(IGNORE_URI);
    }

    /**
     * security配置
     *
     * @param httpSecurity
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity.csrf()
                .disable()
                .authorizeRequests()
                .anyRequest()
                .authenticated()
                .withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {
                    @Override
                    public <O extends FilterSecurityInterceptor> O postProcess(O fsi) {
                        fsi.setRejectPublicInvocations(false);
                        fsi.setAccessDecisionManager(accessDecisionManager());
                        fsi.setSecurityMetadataSource(systemSecurityMetadataSource());
                        return fsi;
                    }
                })

                .and()
                .formLogin()  //开启登录
                .successHandler(authenticationSuccessHandler) // 登录成功
                .failureHandler(authenticationFailureHandler) // 登录失败
                .permitAll()

                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)

                .and()
                .logout()
                .addLogoutHandler(logoutHandler)
                .logoutSuccessHandler(logoutSuccessHandler) // 退出成功
                .permitAll()
        ;

        // 设置响应头
        httpSecurity
                .headers().cacheControl()
                .and()
                .xssProtection()
                .and()
                .contentTypeOptions()
                .and()
                .frameOptions()
                .sameOrigin().httpStrictTransportSecurity().includeSubDomains(true)
                .maxAgeInSeconds(Duration.ofDays(365).getSeconds());

        // 添加 filter
        httpSecurity.addFilterAt(usernamePasswordCodeAuthenticationFilter(),
                UsernamePasswordAuthenticationFilter.class);

        // 添加 filter
        httpSecurity.addFilterAt(tokenAuthenticationFilter(),
                UsernamePasswordAuthenticationFilter.class);
        httpSecurity.addFilterBefore(customExceptionTranslationFilter(), ExceptionTranslationFilter.class);

        // 添加自定义未授权和未登录结果返回
        httpSecurity
                .exceptionHandling()
                .accessDeniedHandler(customAccessDeniedHandler())
                .authenticationEntryPoint(customAuthenticationEntryPoint());

        RedisSerializer<Object> defaultRedisSerializer = defaultRedisSerializer();
        redisTemplate.setValueSerializer(defaultRedisSerializer);
        redisTemplate.setHashValueSerializer(defaultRedisSerializer);

    }

    /**
     * 设置userDetail和密码编码
     *
     * @param auth
     * @throws Exception
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService((UserDetailsService) userManagerService).passwordEncoder(passwordEncoder());
    }

    /**
     * token过滤器
     *
     * @return
     */
    @Bean
    public TokenAuthenticationFilter tokenAuthenticationFilter() {
        return new TokenAuthenticationFilter();
    }

    /**
     * 认证成功处理器
     *
     * @return
     */
    @Bean
    public CustomAuthenticationSuccessHandler customAuthenticationSuccessHandler() {
        return new CustomAuthenticationSuccessHandler();
    }

    /**
     * 权限禁止处理器
     *
     * @return
     */
    @Bean
    public CustomAccessDeniedHandler customAccessDeniedHandler() {
        return new CustomAccessDeniedHandler();
    }

    /**
     * 认证入口
     *
     * @return
     */
    @Bean
    public CustomAuthenticationEntryPoint customAuthenticationEntryPoint() {
        return new CustomAuthenticationEntryPoint();
    }

    /**
     * 密码编码器
     *
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 权限
     *
     * @return
     */
    @Bean
    public SystemSecurityMetadataSource systemSecurityMetadataSource() {
        return new SystemSecurityMetadataSource();
    }

    /**
     * 权限处理器
     *
     * @return
     */
    @Bean
    public AffirmativeBased accessDecisionManager() {
        AuthenticatedVoter authenticatedVoter = new AuthenticatedVoter();
        RoleVoter roleVoter = new RoleVoter();
        roleVoter.setRolePrefix("RES_");
        List<AccessDecisionVoter<? extends Object>> decisionVoters = Lists.newArrayList();
        decisionVoters.add(authenticatedVoter);
        decisionVoters.add(roleVoter);
        AffirmativeBased affirmativeBased = new AffirmativeBased(decisionVoters);
        return affirmativeBased;
    }

    @Bean
    public CustomExceptionTranslationFilter customExceptionTranslationFilter() {
        CustomExceptionTranslationFilter customExceptionTranslationFilter = new CustomExceptionTranslationFilter(
                customAuthenticationEntryPoint());
        customExceptionTranslationFilter.setAccessDeniedHandler(customAccessDeniedHandler());
        return customExceptionTranslationFilter;
    }

    /**
     * 用户名密码 / 手机号验证码 登录过滤器
     *
     * @return
     * @throws Exception
     */
    @Bean
    public UsernamePasswordCodeAuthenticationFilter usernamePasswordCodeAuthenticationFilter() throws Exception {
        UsernamePasswordCodeAuthenticationFilter filter = new UsernamePasswordCodeAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManagerBean());
        filter.setAuthenticationSuccessHandler(authenticationSuccessHandler);
        filter.setAuthenticationFailureHandler(authenticationFailureHandler);
        return filter;
    }

    /**
     * token处理器
     *
     * @return
     */
    @Bean
    public AccessTokenStoreStrategy accessTokenStoreStrategy() {
        AccessTokenStoreStrategy accessTokenStoreStrategy = new AccessTokenRedisStoreStrategy();
        return accessTokenStoreStrategy;
    }

    @Bean
    public WallConfig wallConfig() {
        WallConfig config = new WallConfig(MySqlWallProvider.DEFAULT_CONFIG_DIR);
        config.setMultiStatementAllow(true);// 允许一次执行多条语句
        config.setNoneBaseStatementAllow(true);// 允许非基本语句的其他语句
        return config;
    }

    @Bean
    public WallFilter wallFilter() {
        WallFilter wallFilter = new WallFilter();
        wallFilter.setConfig(wallConfig());
        return wallFilter;
    }

}
