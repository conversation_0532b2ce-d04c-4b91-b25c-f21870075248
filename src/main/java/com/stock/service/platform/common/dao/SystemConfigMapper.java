package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import java.util.List;
import java.util.Map;

public interface SystemConfigMapper {

    List<SystemConfig> selectByExampleWithBLOBs(SystemConfigExample example);

    List<SystemConfig> selectByExample(SystemConfigExample example);

    SystemConfig selectByPrimaryKey(String id);

    int updateByPrimaryKeyWithBLOBs(SystemConfig record);

    void lockStatusClick(Map<String, Object> param);
}