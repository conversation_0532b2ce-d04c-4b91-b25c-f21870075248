package com.stock.service.platform.common.util;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2024-05-18 17:13
 */
@Component
public class StripTrailingZerosUtil {
    public static void convertBigDecimalsToPlainString(Object object) throws IllegalAccessException {
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getType().equals(BigDecimal.class)) {
                field.setAccessible(true); // 设置访问权限
                BigDecimal bigDecimalValue = (BigDecimal) field.get(object);
                if (bigDecimalValue != null) {
                    BigDecimal strippedValue = new BigDecimal(bigDecimalValue.stripTrailingZeros().toPlainString());
                    field.set(object, strippedValue); // 将不使用科学计数法的值设置回字段
                }
                field.setAccessible(false); // 恢复访问权限
            }
        }
    }
}
