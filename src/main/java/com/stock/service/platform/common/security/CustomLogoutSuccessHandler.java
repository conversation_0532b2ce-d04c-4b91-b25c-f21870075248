package com.stock.service.platform.common.security;

import com.stock.core.dto.JsonResponse;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import com.stock.core.util.JsonUtil;
import com.stock.core.util.WebUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Description: 退出成功处理器
 * @author: xiazhengwei
 * @date:
 */
@Component
public class CustomLogoutSuccessHandler implements LogoutSuccessHandler {

    @Autowired
    private AccessTokenStoreStrategy accessTokenStoreStrategy;

    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        JsonResponse<Boolean> result = new JsonResponse<>();
        String accessToken = WebUtil.getCurrentRequest().getHeader(HttpHeaders.AUTHORIZATION);
        accessTokenStoreStrategy.invalidAccessToken(accessToken);
        result.setResult(Boolean.TRUE);
        PrintWriter out = response.getWriter();
        out.print(JsonUtil.toJson(result));
        out.flush();
        out.close();
        out.close();
    }
}
