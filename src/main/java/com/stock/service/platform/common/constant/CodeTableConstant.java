package com.stock.service.platform.common.constant;

/**
 * 码表常量接口类
 *
 * <AUTHOR>
 */
public class CodeTableConstant {

    /**
     * 用户可用状态
     */
    public static final String USER_AVAILABLE = "1";

    /**
     * 用户不可用状态
     */
    public static final String USER_UNAVAILABLE = "0";

    /**
     * 用户锁定状态
     */
    public static final String USER_LOCK = "1";

    /**
     * 用户解锁状态
     */
    public static final String USER_UNLOCK = "0";

    /**
     * 人员新增来源：新增
     */
    public static final String PERSON_INSERT_SOURCE_INSERT = "0";

    /**
     * 身份类型code_no
     */
    public static final String IDENTITY_TYPR_CODE_NO = "IDENTITY_TYPE";

    /**
     * 数据状态：0删除、1有效
     */
    public static final String DATA_STATUS_0 = "0";

    /**
     * 数据状态：0删除、1有效
     */
    public static final String DATA_STATUS_1 = "1";


    /**
     * 是否是超级管理员
     */
    public static final String IS_ADMIN = "1";

    /**
     * 性别 男
     */
    public static final String SEX_CODE_MALE = "1";

    /**
     * 性别 女
     */
    public static final String SEX_CODE_FEMALE = "0";

    /**
     * 固定的首页路由
     */
    public static final String ROUTER_CODE_INDEX = "20";

    /**
     * 是否区分 1:是 0:否
     */
    public static final String YES = "1";
    public static final String NO = "0";

    /**
     * userName前缀
     */
    public static final String A = "A";


}