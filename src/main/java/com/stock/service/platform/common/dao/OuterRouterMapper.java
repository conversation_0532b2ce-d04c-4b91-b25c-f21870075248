package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.OuterRouter;
import com.stock.service.platform.common.entity.OuterRouterExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface OuterRouterMapper {
    long countByExample(OuterRouterExample example);

    int deleteByExample(OuterRouterExample example);

    int deleteByPrimaryKey(String id);

    int insert(OuterRouter record);

    int insertSelective(OuterRouter record);

    List<OuterRouter> selectByExampleWithRowbounds(OuterRouterExample example, RowBounds rowBounds);

    List<OuterRouter> selectByExample(OuterRouterExample example);

    OuterRouter selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") OuterRouter record, @Param("example") OuterRouterExample example);

    int updateByExample(@Param("record") OuterRouter record, @Param("example") OuterRouterExample example);

    int updateByPrimaryKeySelective(OuterRouter record);

    int updateByPrimaryKey(OuterRouter record);
}