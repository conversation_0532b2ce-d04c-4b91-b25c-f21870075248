<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.system.dao.SaLogManageMapper">

    <select id="queryBySelectiveChange"
            parameterType="com.stock.service.platform.common.system.dto.SaLogManageDto"
            resultType="com.stock.service.platform.common.system.dto.SaLogManageDto">
        SELECT *
        FROM (
        SELECT
        CASE WHEN so.org_type = '4' THEN sc.company_code ELSE '601066' END AS companyCode,
        CASE WHEN so.org_type = '4' THEN sc.zh_name ELSE '中信建投证券股份有限公司' END AS companyName,
        su.user_name userName,
        log.ip_address logIP,
        log.request_url logURL,
        log.request_type logType,
        log.method_name methodName,
        log.method_param param,
        log.create_time createTime,
        log.system_name systemName
        FROM system_web_request_log log,sa_user su,sa_person sp,sa_org so
        left join sa_company sc ON so.company_id = sc.id
        WHERE log.user_id = su.id
        AND su.id = sp.user_id
        AND sp.org_id = so.id
        ) t
        <where>
            <if test="companyCode != null and companyCode != ''">
                AND t.companyCode = #{companyCode}
            </if>
            <if test="userName != null and userName != ''">
                AND t.userName = #{userName}
            </if>
            <if test="startTime != null and endTime != null">
                AND t.createTime BETWEEN #{startTime} AND date_add(#{endTime},interval 1 day)
            </if>
            <if test="logURL != null and logURL != ''">
                AND t.logURL = #{logURL}
            </if>
            <if test="logType != null and logType != ''">
                AND t.logType = #{logType}
            </if>
            <if test="systemName != null and systemName != ''">
                AND t.systemName = #{systemName}
            </if>
        </where>
        ORDER BY t.createTime DESC
    </select>


    <select id="queryLoginLogManageBySelective" resultType="com.stock.service.platform.common.system.dto.SaLoginLogManageDto">
        SELECT
        id id,
        log_type logType,
        log_name logName,
        REPLACE ( substring_index( log_content, ',', 1 ), '登录用户ID=', '' ) userId,
        REPLACE ( SUBSTRING_INDEX( substring_index( log_content, ',', 2 ), ',',- 1 ), '登录用户名=', '' ) userName,
        log_content,
        log_ip logIp,
        log_time logTime
        FROM
        sa_log
        <where>
            <if test="userName != null and userName != ''">
                REPLACE ( SUBSTRING_INDEX( substring_index( log_content, ',', 2 ), ',',- 1 ), '登录用户名=', '' ) = #{userName}
            </if>
            <if test="startTime != null and startTime != ''">
                AND log_time >= concat(#{startTime}, ' 00:00:00')
            </if>
            <if test="endTime != null and endTime != ''">
                AND log_time <![CDATA[<=]]> concat(#{endTime}, ' 23:59:59')
            </if>
            <if test="logName != null and logName != ''">
                AND log_name = #{logName}
            </if>
        </where>
        order by log_time DESC



    </select>

    <select id="queryOperationLogManageBySelective"
            parameterType="com.stock.service.platform.common.system.dto.SaOperationLogManageDto"
            resultType="com.stock.service.platform.common.system.dto.SaOperationLogManageDto">
        SELECT
        su.user_name userName,
        sol.ip,
        case ifnull(su.user_type,'0') when '0' then '内部用户' else '外部用户' end as userType,
        case ifnull(su.user_type,'0') when '0' then GROUP_CONCAT(DISTINCT so.org_name ORDER BY so.org_name ASC SEPARATOR ', ') else '' end as orgName,
        case ifnull(su.user_type,'0') when '0' then '' else GROUP_CONCAT(DISTINCT so.org_name ORDER BY so.org_name ASC SEPARATOR ', ') end as companyName,
        sol.router,
        sol.operation_type_detail operationTypeDetail,
        sol.remark,
        sol.create_time createTime
        FROM sa_operation_log sol
        left join sa_user su on sol.create_user = su.id
        left join sa_org so on FIND_IN_SET(so.id, su.org_no)
        <where>
            <if test="userName != null and userName != ''">
                su.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="orgId != null and orgId != ''">
                FIND_IN_SET(#{orgId}, su.org_no)
            </if>
            <if test="companyId != null and companyId != ''">
                FIND_IN_SET(#{companyId}, su.org_no)
            </if>
            <if test="startTime != null and startTime != ''">
                AND sol.create_time >= concat(#{startTime}, ' 00:00:00')
            </if>
            <if test="endTime != null and endTime != ''">
                AND sol.create_time <![CDATA[<=]]> concat(#{endTime}, ' 23:59:59')
            </if>
        </where>
        GROUP BY sol.id
        order by sol.create_time DESC
    </select>
</mapper>