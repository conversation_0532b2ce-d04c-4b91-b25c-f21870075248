package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.OuterRole;
import com.stock.service.platform.common.entity.OuterRoleExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface OuterRoleMapper {
    long countByExample(OuterRoleExample example);

    int deleteByExample(OuterRoleExample example);

    int deleteByPrimaryKey(String id);

    int insert(OuterRole record);

    int insertSelective(OuterRole record);

    List<OuterRole> selectByExampleWithRowbounds(OuterRoleExample example, RowBounds rowBounds);

    List<OuterRole> selectByExample(OuterRoleExample example);

    OuterRole selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") OuterRole record, @Param("example") OuterRoleExample example);

    int updateByExample(@Param("record") OuterRole record, @Param("example") OuterRoleExample example);

    int updateByPrimaryKeySelective(OuterRole record);

    int updateByPrimaryKey(OuterRole record);

    List<OuterRole> selectRole(String userType);

    String getParentCompanyId(String userId);
}