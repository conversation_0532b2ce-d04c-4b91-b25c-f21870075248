package com.stock.service.platform.common.system.dao;



import com.stock.service.platform.common.system.dto.SaLogManageDto;
import com.stock.service.platform.common.system.dto.SaLoginLogManageDto;
import com.stock.service.platform.common.system.dto.SaOperationLogManageDto;

import java.util.List;

public interface SaLogManageMapper {
  /**
   * 根据条件查询日志
   *
   * @param saLogManageParam
   * @return
   */
  List<SaLogManageDto> queryBySelectiveChange(SaLogManageDto saLogManageParam);

  List<SaLoginLogManageDto> queryLoginLogManageBySelective(SaLoginLogManageDto saLogManageParam);

  List<SaOperationLogManageDto> queryOperationLogManageBySelective(SaOperationLogManageDto saOperationLogManageParam);
}
