<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.UserRoleMapMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.UserRoleMap">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, role_id, update_user, update_time
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.UserRoleMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_user_role_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_user_role_map
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_user_role_map
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.UserRoleMapExample">
    delete from sa_user_role_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.UserRoleMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_user_role_map (id, user_id, role_id, 
      update_user, update_time)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.UserRoleMap">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_user_role_map
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="userId != null">
        user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.UserRoleMapExample" resultType="java.lang.Long">
    select count(*) from sa_user_role_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_user_role_map
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_user_role_map
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.UserRoleMap">
    update sa_user_role_map
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.UserRoleMap">
    update sa_user_role_map
    set user_id = #{userId,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.UserRoleMapExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_user_role_map
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
    <select id="getRoleNameByRoleIds" resultType="java.lang.String">
      SELECT sor.role_name
      FROM sa_outer_role sor
      <where>
        <if test="roleList != null">
          sor.id IN
          <foreach item="item" index="index" collection="roleList" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
      </where>
      order by sor.role_name
    </select>
  <select id="getPlatformRoleNameByRoleIds" resultType="java.lang.String">
    SELECT sr.role_name
    FROM sa_role sr
    <where>
      <if test="roles != null">
        sr.id IN
        <foreach item="item" index="index" collection="roles" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    order by sr.role_name
  </select>
  <select id="getUserNameByUserId" resultType="java.lang.String">
    select user_name from sa_user where id = #{userId}
  </select>
</mapper>