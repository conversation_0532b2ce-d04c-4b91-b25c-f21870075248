package com.stock.service.platform.common.controller;

import com.google.code.kaptcha.Producer;
import com.google.common.collect.Maps;
import com.stock.core.Constant;
import com.stock.core.controller.BaseController;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.exception.ApplicationException;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.constant.RedisKeyConstant;
import com.stock.service.platform.common.constant.SystemConfigConstant;
import com.stock.service.platform.common.dto.RouterDto;
import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.StockService;
import com.stock.service.platform.common.util.CommonUtil;
import com.stock.service.platform.outer.service.RoleOuterService;
import com.stock.service.platform.user.service.UserManagerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * 常规controller
 *
 * <AUTHOR>
 */
@Api(tags = "常规共通")
@Controller
public class CommonController extends BaseController {

    @Autowired
    private UserManagerService userManagerService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private Producer captchaProducer;

    @Resource
    private StockService stockService;

    @Resource
    private RoleOuterService service;
    /**
     * 用刷新TOKEN交换新的访问TOKEN
     */
    @ApiOperation(value = "刷新token", notes = "刷新token", httpMethod = "POST")
    @RequestMapping(value = "/refreshToken", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<Map<String, String>> refreshToken(String refreshToken) {
        if (StringUtils.isEmpty(refreshToken)) {
            throw new ApplicationException("error.10002");
        }
        JsonResponse<Map<String, String>> response = new JsonResponse<Map<String, String>>();
        Map<String, String> result = Maps.newHashMap();
        result.put(Constant.ACCESS_TOKEN, userManagerService.getAccessTokenByRefreshToken(refreshToken));
        result.put(Constant.REFRESH_TOKEN, refreshToken);
        response.setResult(result);
        return response;
    }

    /**
     * 账号密码登陆-获取图形验证码
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/captcha/getCaptchaCode")
    public void getCaptchaCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        this.getCaptchaCodeImage(request, response, RedisKeyConstant.LOGIN_CAPTCHA_RANDOM_KEY);
    }

    /**
     * 手机号验证码登陆-获取图形验证码
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/captcha/getMessageCaptchaCode")
    public void getMessageCaptchaCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        this.getCaptchaCodeImage(request, response, RedisKeyConstant.LOGIN_CAPTCHA_MSG_RANDOM_KEY);
    }

    /**
     * 生成图形验证码
     *
     * @throws Exception
     */
    private void getCaptchaCodeImage(HttpServletRequest request, HttpServletResponse response, String keyName)throws Exception {
        // 获取当前操作编码
        String opKeyName = LogicConstant.LOGIN_OP_CODE;
        if(RedisKeyConstant.LOGIN_CAPTCHA_MSG_RANDOM_KEY.equals(keyName)) {
            opKeyName = LogicConstant.MSG_LOGIN_OP_CODE;
        }
        String suffix = commonService.getCookieValue(request, opKeyName);
        // 获取不到操作码就生成
        if(StringUtils.isEmpty(suffix)) {
            suffix = String.valueOf(CommonUtil.getNum(8));
            Cookie cookie = new Cookie(opKeyName, suffix);
            cookie.setPath("/");
            response.addCookie(cookie);
        }
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");
        //生成验证码
        String capText = captchaProducer.createText();
        //8位随机数，防止多人同时登录，验证码取同一个问题。
        Long random = CommonUtil.getNum(8);
        //保存到redis
        redisDao.setObject(keyName + suffix, random.toString(),120 * 1000);
        redisDao.setObject(RedisKeyConstant.LOGIN_CAPTCHA_PREFIX+ random + suffix, capText,120 * 1000);
        //向客户端写出
        BufferedImage bi = captchaProducer.createImage(capText);
        ServletOutputStream out = response.getOutputStream();
        ImageIO.write(bi, "jpg", out);
        try {
            out.flush();
        } finally {
            out.close();
        }
    }

    @PostMapping("/common/getOpenedSystems")
    @ResponseBody
    public JsonResponse<Object> getOpenedSystems(HttpServletRequest request, @RequestBody RouterDto param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(commonService.getOpenedSystems(request, param));
        }catch (Exception e) {
            response.setErrorMsg("获取开通系统情况失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("/common/getRoleListGroupBySystem")
    @ResponseBody
    public JsonResponse<Object> getRoleListGroupBySystem(@RequestBody User param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.getRoleListGroupBySystem(param));
        }catch (Exception e) {
            response.setErrorMsg("获取角色列表失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("/common/getSystemConfigConstant")
    @ResponseBody
    public JsonResponse<Object> getSystemConfigConstant() {
        JsonResponse<Object> response = new JsonResponse<>();
        Map<String, Object> result = new HashMap<>();
        result.put("API_LIST", SystemConfigConstant.SYS_CONFIG_API_MAP);
        result.put("DYNAMIC_PARAM_SIGN", SystemConfigConstant.DYNAMIC_PARAM_SIGN);
        result.put("DYNAMIC_PARAM_CONTENT", SystemConfigConstant.SYS_CONFIG_API_DYNAMIC_PARAM_CONTENT);
        result.put("ALLOW_CALL_API_IP_LIST", SystemConfigConstant.SYS_CONFIG_CALL_API_IP_ADDR);
        response.setResult(result);
        return response;
    }



    @PostMapping("/common/subsystemLogin")
    @ResponseBody
    public JsonResponse<Map<String, Object>> subsystemLogin() {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        try {
            UserInfo userInfo = getUserInfo();
            Map<String, Object> idMap = new HashMap<>();
            idMap.put("userId", userInfo.getUserId());
            idMap.put("userName", userInfo.getUsername());
            response.setResult(idMap);
        }catch (Exception e) {
            response.setErrorMsg("subsystem login fail --- 子系统登录失败");
            e.printStackTrace();
        }
        return response;
    }


    @PostMapping("/common/stockInfoMergeFirst")
    @ResponseBody
    public JsonResponse<String> stockInfoMergeFirst() throws Exception {
        JsonResponse<String> response = new JsonResponse<>();
        stockService.stockInfoMergeFirst();
        response.setResult("true");
        return response;
    }

    @PostMapping("/common/getSystemStatus")
    @ResponseBody
    public JsonResponse<Object> stockInfoMergeFirst(@RequestBody Map<String, String> map) throws Exception {
        JsonResponse<Object> response = new JsonResponse<>();
        response.setResult(commonService.getSystemStatus(map));
        return response;
    }

    @PostMapping("/common/dayIsP")
    @ResponseBody
    public JsonResponse<Object> dayIsP(@RequestBody Map<String, String> map) throws Exception {
        JsonResponse<Object> response = new JsonResponse<>();
        response.setResult(commonService.dayIsP(map.get("day")));
        return response;
    }

}
