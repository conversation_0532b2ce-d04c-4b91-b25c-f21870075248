package com.stock.service.platform.common.security;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.stock.core.dao.RedisDao;
import com.stock.core.exception.ApplicationException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.constant.RedisKeyConstant;
import com.stock.service.platform.common.service.CommonService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * 用户名密码，手机号验证码登录
 */
public class UsernamePasswordCodeAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    @Autowired
    BaseService baseService;

    @Autowired
    RedisDao redisDao;

    @Autowired
    CommonService commonService;

    @Autowired
    private GetUserIdByCASVerify getUserIdByCASVerify;

    @Autowired
    private CaptchaService captchaService;

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        String loginType = obtainLoginType(request);
        String username;
        String password;
        if (Objects.equals(loginType, LogicConstant.TELEPHONE_VERIFY_CODE_LOGIN_TYPE)) {
            String telephone = obtainTelephone(request);
            String verifyCode = obtainVerifyCode(request);

            String ticket = request.getParameter("ticket");
            String service = request.getParameter("service");
            // CAS登录逻辑
            if (telephone == null && verifyCode == null && ticket != null && service != null) {
                // cas 登录
                Map<String, String> map = getUserIdByCASVerify.verify(ticket, service);
                if (map != null) {
                    username = map.get("USERNAME");
                    password = map.get("PASSWORD");
                } else {
                    // 未查询到用户密码信息
                    throw new ApplicationException("error.000122");
                }
            } else {
                // 手机验证码登录
                if(StringUtils.isBlank(telephone)){
                    throw new ApplicationException("error.000104");
                }
                checkVerifyCode(telephone,verifyCode);
                Map<String,String> map = baseService.getUserNameAndPassword(telephone);
                if (map != null) {
                    username = map.get("USERNAME");
                    password = map.get("PASSWORD");
                } else {
                    // 未查询到用户密码信息
                    throw new ApplicationException("error.000102");
                }
            }
        }else {
            String suffix = commonService.getCookieValue(request, LogicConstant.LOGIN_OP_CODE);
            String random = "";
            Object o = redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_RANDOM_KEY + suffix);
            if (o != null) {
                random = o.toString();
            }
            // 账号密码登录
            //String captchaCode = obtainCaptchaCode(request);
            //checkCaptchaCode(captchaCode,random + suffix);

            // 验证手机验证码
            checkVerifyCode(request.getParameter("username"),request.getParameter("verifyCode"));

            // 文字点击验证码二次校验
            String captchaTextCode = obtainCaptchaTextCode(request);
            checkCaptchaTextCode(captchaTextCode);

            username = obtainUsername(request);
            password = obtainPassword(request);
        }

        if (username == null) {
            username = "";
        }

        if (password == null) {
            password = "";
        }

        username = username.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
        this.setDetails(request, authRequest);
        Authentication authenticate = this.getAuthenticationManager().authenticate(authRequest);
        return authenticate;
    }

    private void checkVerifyCode(String username, String verifyCode) {
        String codeInRedis = (String) redisDao.getObject(RedisKeyConstant.TELEPHONE_VERIFY_CODE_PREFIX+username);
        // 手机验证码失效
        if(StringUtils.isBlank(codeInRedis)){
            throw new ApplicationException("error.990013");
        }
        // 验证码不相符
        if(!Objects.equals(verifyCode,codeInRedis)){
            throw new ApplicationException("error.990014");
        }
    }

    private void checkCaptchaCode(String verifyCode,String random) {
        String codeInRedis = (String) redisDao.getObject(RedisKeyConstant.LOGIN_CAPTCHA_PREFIX+random);
        // 验证码失效(有效期60秒)
        if(StringUtils.isBlank(random)){
            throw new ApplicationException("error.990007");
        }
        // 未存在图形验证码
        if(StringUtils.isBlank(codeInRedis)){
            throw new ApplicationException("error.990007");
        }
        // 验证码不相符
        if(!Objects.equals(verifyCode,codeInRedis)){
            throw new ApplicationException("error.000105");
        }
    }

    private void checkCaptchaTextCode(String captchaTextCode) {

        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(captchaTextCode);
        ResponseModel response = captchaService.verification(captchaVO);
        if(response.isSuccess() == false){
            /** 验证码校验失败，返回信息告诉前端
                repCode  0000  无异常，代表成功
                repCode  9999  服务器内部异常
                repCode  0011  参数不能为空
                repCode  6110  验证码已失效，请重新获取
                repCode  6111  验证失败
                repCode  6112  获取验证码失败,请联系管理员
                repCode  6113  底图未初始化成功，请检查路径
                repCode  6201  get接口请求次数超限，请稍后再试!
                repCode  6206  无效请求，请重新获取验证码
                repCode  6202  接口验证失败数过多，请稍后再试
                repCode  6204  check接口请求次数超限，请稍后再试!
             **/
            if("9999".equals(response.getRepCode())){
                throw new ApplicationException("error.9999");
            }
            if("0011".equals(response.getRepCode())){
                throw new ApplicationException("error.0011");
            }
            if("6110".equals(response.getRepCode())){
                throw new ApplicationException("error.6110");
            }
            if("6111".equals(response.getRepCode())){
                throw new ApplicationException("error.6111");
            }
            if("6112".equals(response.getRepCode())){
                throw new ApplicationException("error.6112");
            }
            if("6113".equals(response.getRepCode())){
                throw new ApplicationException("error.6113");
            }
            if("6201".equals(response.getRepCode())){
                throw new ApplicationException("error.6201");
            }
            if("6206".equals(response.getRepCode())){
                throw new ApplicationException("error.6206");
            }
            if("6202".equals(response.getRepCode())){
                throw new ApplicationException("error.6202");
            }
            if("6204".equals(response.getRepCode())){
                throw new ApplicationException("error.6204");
            }
        }
    }


    private String obtainLoginType(HttpServletRequest request) {
        return request.getParameter(LogicConstant.LOGIN_TYPE_NAME);
    }

    private String obtainTelephone(HttpServletRequest request) {
        return request.getParameter("telephone");
    }

    private String obtainVerifyCode(HttpServletRequest request) {
        return request.getParameter("verifyCode");
    }
    private String obtainCaptchaCode(HttpServletRequest request) {
        return request.getParameter("captchaCode");
    }

    private String obtainCaptchaTextCode(HttpServletRequest request) {
        return request.getParameter("captchaTextCode");
    }

}
