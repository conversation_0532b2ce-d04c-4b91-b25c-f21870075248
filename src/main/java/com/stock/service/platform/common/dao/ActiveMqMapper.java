package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.dto.ActivemqTopicDto;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface ActiveMqMapper {

    void insertActivemqTopic(Map<String,String> param);

    void updateIsSuccess(Map<String,Object> param);

    void insertReceiveInfo(Map<String,Object> param);


    List<ActivemqTopicDto> selectActivemqTopicByUid(String messageId);

    ActivemqTopicDto getActiveMq(String sysCode, Object uid);
}