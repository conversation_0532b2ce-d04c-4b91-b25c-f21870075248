package com.stock.service.platform.common.security;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.stock.core.dao.RedisDao;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.log.LogEvent;
import com.stock.core.security.token.AccessToken;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import com.stock.core.service.BaseService;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.constant.CompanyConstant;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.user.dao.UserBizMapper;
import com.stock.service.platform.user.dto.UserManageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

@Component
public class CustomAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    @Autowired
    private AccessTokenStoreStrategy accessTokenStoreStrategy;

    @Autowired
    private UserBizMapper userBizMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RedisDao redisDao;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        JsonResponse<AccessToken> result = new JsonResponse<>();

        String loginType = request.getParameter(LogicConstant.LOGIN_TYPE_NAME);
        UserInfo userInfo = (UserInfo) authentication.getPrincipal();
        request.getSession().setAttribute("userInfo",userInfo);
        request.getSession().setAttribute(LogicConstant.LOGIN_TYPE_NAME,loginType);

        UserManageResponse user = userBizMapper.selectUserInfo(userInfo.getUserId());

        Map<String, String> map = Maps.newHashMap();
        map.put("id", userInfo.getUserId());
        map.put("errorNum", "0");
        map.put("lockType", "0");
        map.put("unlockTime", "");
        userBizMapper.updateUserLockInfo(map);

        Map<String, Object> info = userInfo.getInfo();
        // 登录成功删除信息中的密码
        info.remove("password");
        // 判断内外部用户
        if(Integer.parseInt(user.getOrgType()) > 3){
            info.put("companyId",user.getCompanyId());
            info.put("companyCode",user.getCompanyCode());
            info.put("companyName",user.getCompanyName());
            info.put("companyZhName",user.getCompanyName());
        } else {
            info.put("companyId", CompanyConstant.SYSTEM_COMPANY_ID);
            info.put("companyCode", CompanyConstant.SYSTEM_COMPANY_CODE);
            info.put("companyName", CompanyConstant.SYSTEM_COMPANY_SHORT_NAME);
            info.put("companyZhName", CompanyConstant.SYSTEM_COMPANY_SHORT_NAME);
        }
        info.put("orgId",user.getOrgId());
        info.put("orgName",user.getOrgName());
        info.put("orgType",user.getOrgType());
        info.put("currentEnv", commonService.getCurrentEnvironment());
        AccessToken accessToken = accessTokenStoreStrategy.issueAccessToken(userInfo, 72000 * 1000L, 72000 * 1000L);
        result.setResult(accessToken);

        // 将登录类型放入redis，登出过滤器用
        this.redisDao.setObject("access_token_login_type_" + accessToken.getAccessToken(), loginType, 7200 * 1000L);

        PrintWriter out = response.getWriter();
        out.print(JsonUtil.toJson(result));
        out.flush();
        out.close();

        LogEvent loginEvent = new LogEvent(this);
        loginEvent.setLogType(loginType);
        loginEvent.setLogWay("0");
        loginEvent.setLogName("用户登录");
        userInfo = (UserInfo)authentication.getPrincipal();
        Map<String, String> param = Maps.newLinkedHashMap();
        param.put("登录用户ID", userInfo.getUserId());
        param.put("登录用户名", userInfo.getUsername());
        param.put("会话ID", request.getSession(false).getId());
        loginEvent.setLogContent(Joiner.on(",").withKeyValueSeparator("=").join(param));
        this.commonService.postLog(loginEvent);
    }
}
