package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.SystemWebRequestLog;
import com.stock.service.platform.common.entity.SystemWebRequestLogExample;
import com.stock.service.platform.common.entity.SystemWebRequestLogWithBLOBs;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface SystemWebRequestLogMapper {
    long countByExample(SystemWebRequestLogExample example);

    int deleteByExample(SystemWebRequestLogExample example);

    int deleteByPrimaryKey(String id);

    int insert(SystemWebRequestLogWithBLOBs record);

    int insertSelective(SystemWebRequestLogWithBLOBs record);

    List<SystemWebRequestLogWithBLOBs> selectByExampleWithBLOBsWithRowbounds(SystemWebRequestLogExample example, RowBounds rowBounds);

    List<SystemWebRequestLogWithBLOBs> selectByExampleWithBLOBs(SystemWebRequestLogExample example);

    List<SystemWebRequestLog> selectByExampleWithRowbounds(SystemWebRequestLogExample example, RowBounds rowBounds);

    List<SystemWebRequestLog> selectByExample(SystemWebRequestLogExample example);

    SystemWebRequestLogWithBLOBs selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") SystemWebRequestLogWithBLOBs record, @Param("example") SystemWebRequestLogExample example);

    int updateByExampleWithBLOBs(@Param("record") SystemWebRequestLogWithBLOBs record, @Param("example") SystemWebRequestLogExample example);

    int updateByExample(@Param("record") SystemWebRequestLog record, @Param("example") SystemWebRequestLogExample example);

    int updateByPrimaryKeySelective(SystemWebRequestLogWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(SystemWebRequestLogWithBLOBs record);

    int updateByPrimaryKey(SystemWebRequestLog record);
}