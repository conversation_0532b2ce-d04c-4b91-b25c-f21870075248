package com.stock.service.platform.common.schedule;

import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.RedisDaoService;
import com.stock.service.platform.common.service.StockService;
import com.stock.service.platform.common.system.dao.TimedTasksMapper;
import com.stock.service.platform.common.system.dto.TasksDto;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-03-14 15:48
 */
@EnableScheduling
@Component
public class VcTradSkDailyMergeSchedule {

    private static final Logger logger = LoggerFactory.getLogger(VcTradSkDailyMergeSchedule.class);

    @Resource
    private RedisDaoService redisDaoService;

    @Resource
    private StockService stockService;

    @Resource
    private TimedTasksMapper timedTasksMapper;

    @Resource
    private CommonService commonService;


    /**
     * 定时合并四张表的交易数据
     */
    /*@Scheduled(cron = "0 0 2 * * ? ")
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 30 * 60 * 1000))
    public void vcTradSkDailyMerge() throws Exception {
        boolean lock = redisDaoService.lock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", 29 * 60 * 1000);
        if (lock) {
            // 查出小于今天的最大交易日
            String tradeDate = timedTasksMapper.getMaxTradeDate();
            // 判断定时任务表是否有trade_date等于tradeDate的数据
            TasksDto tasksDto = timedTasksMapper.getTimedTasksByTradeDate(tradeDate);
            if (ObjectUtils.isEmpty(tasksDto)) {
                tasksDto = new TasksDto();
                tasksDto.setName("vcTradSkDailyMerge");
                tasksDto.setZhName("交易数据合并");
                tasksDto.setIsSuccess("2");
                tasksDto.setTradeDate(tradeDate);
                timedTasksMapper.insertTasksP(tasksDto);
            }
            // 查t_dxs_task_status表判断biz_date等于tradeDate的数据有几条
            int successNum = timedTasksMapper.getSuccessCount(tradeDate.replaceAll("-",""));
            Map<String, Object> param = new HashMap<>();
            if (successNum == 4) {
                try {
                    stockService.stockInfoMerge(tradeDate);
                    param.put("id", tasksDto.getId());
                    param.put("isSuccess", "1");
                } catch (Exception e) {
                    param.put("isSuccess", "0");
                    param.put("errorMessage", e.getMessage());
                    throw new Exception(e);
                } finally {
                    timedTasksMapper.updateIsSuccessP(param);
                    redisDaoService.unlock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge");
                }
            } else {
                param.put("id", tasksDto.getId());
                param.put("isSuccess", "0");
                param.put("errorMessage", "状态表中有未同步成功的");
                timedTasksMapper.updateIsSuccessP(param);
                redisDaoService.unlock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge");
                throw new Exception("状态表中有未同步成功的");
            }
        }
    }*/

    /**
     * 定时合并四张表的交易数据
     */
    @Scheduled(cron = "0 0 2 * * ? ")
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 30 * 60 * 1000))
    public void vcTradSkDailyMerge() throws ParseException {
        boolean lock = redisDaoService.lock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", 29 * 60 * 1000);
        if (lock) {
            // 获取日期
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String date = formatter.format(calendar.getTime());
            // 判断今天是否符合批日
            if (commonService.dayIsP(date)) {
                // 查出小于今天的最大交易日,后面定时任务需要用这个日期
                String tradeDate = timedTasksMapper.getMaxTradeDate(date);
                // 插入日志
                TasksDto tasksDto = timedTasksMapper.getTimedTasksByTradeDate(tradeDate);
                if (ObjectUtils.isEmpty(tasksDto)) {
                    tasksDto = new TasksDto();
                    tasksDto.setName("vcTradSkDailyMerge");
                    tasksDto.setZhName("交易数据合并");
                    tasksDto.setIsSuccess("2");
                    tasksDto.setTradeDate(tradeDate);
                    timedTasksMapper.insertTasksP(tasksDto);
                }
                // 判断数交推送是否成功
                if (timedTasksMapper.getSuccessCount(tradeDate.replaceAll("-","")) == 4) {
                    Map<String, Object> param = new HashMap<>();
                    try {
                        stockService.stockInfoMerge(tradeDate);
                        param.put("id", tasksDto.getId());
                        param.put("isSuccess", "1");
                    } catch (Exception e) {
                        param.put("isSuccess", "0");
                        param.put("errorMessage", e.getMessage());
                    } finally {
                        timedTasksMapper.updateIsSuccessP(param);
                        redisDaoService.unlock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge");
                    }
                } else {
                    Map<String, Object> param = new HashMap<>();
                    param.put("id", tasksDto.getId());
                    param.put("isSuccess", "0");
                    param.put("errorMessage", "数据交换系统任务推送状态表有失败的");
                    timedTasksMapper.updateIsSuccessP(param);
                    redisDaoService.unlock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge");
                }
            } else {
                redisDaoService.unlock("VcTradSkDailyMergeSchedule_vcTradSkDailyMerge", "VcTradSkDailyMergeSchedule_vcTradSkDailyMerge");
            }
        }
    }
}
