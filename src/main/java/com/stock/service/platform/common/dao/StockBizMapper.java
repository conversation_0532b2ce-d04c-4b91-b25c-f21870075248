package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.dto.VcTradSkDailyDto;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-14 18:05
 */
public interface StockBizMapper {
    List<String> getAllTradeDate();

    void deleteVcTradSkDailyData();

    List<String> getAllCompanyCodeByDate(String date);

    List<String> getAllCompanyCode();

    List<VcTradSkDailyDto> getStockPerformance(String date);

    List<VcTradSkDailyDto> getStockEvaluation(String date);

    List<VcTradSkDailyDto> getStockHolders(String date);

    List<VcTradSkDailyDto> getStockShares(String date);

    void insertVcTradSkDaily(List<VcTradSkDailyDto> vcTradSkDailyList);

    //List<String> getAllTradeDateByCompanyCode(String companyCode);

    List<VcTradSkDailyDto> getStockPerformanceByCompanyCode(String companyCode, String startDate, String endDate);

    List<VcTradSkDailyDto> getStockEvaluationByCompanyCode(String companyCode, String startDate, String endDate);

    List<VcTradSkDailyDto> getStockHoldersByCompanyCode(String companyCode, String startDate, String endDate);

    List<VcTradSkDailyDto> getStockSharesByCompanyCode(String companyCode, String startDate, String endDate);

    void deleteVcTradSkDailyDataByCompanyCode(String companyCode);

    void deleteVcTradSkDailyDataByDate(String date);

    VcTradSkDailyDto getStockHoldersByCodeAndDate(String companyCode, String date);

    VcTradSkDailyDto getStockSharesByCodeAndDate(String companyCode, String date);
}
