package com.stock.service.platform.common.system.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.tree.inter.TreeNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class DepartmentDto extends Org implements TreeNode {
    private List<DepartmentDto> children = new ArrayList<>();
    private boolean checked;
    public String getLabel() {
        return this.getOrgName();
    }
    @Override
    @JsonIgnore
    public String getTreeId() {

        return this.getCompanyId();
    }
    @Override
    @JsonIgnore
    public String getCreateUser() {
        return super.getCreateUser();
    }
    @Override
    @JsonIgnore
    public Date getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    @JsonIgnore
    public String getUpdateUser() {
        return super.getUpdateUser();
    }

    @Override
    @JsonIgnore
    public Date getUpdateTime() {
        return super.getUpdateTime();
    }
    @Override
    @JsonIgnore
    public void setTreeId(String treeId) {
        super.setCompanyId(treeId);
    }

    @Override
    @JsonIgnore
    public String getPid() {
        return getpOrgId();
    }

    @Override
    @JsonIgnore
    public void setPid(String pid) {
        super.setpOrgId(pid);
    }

    @Override
    @JsonIgnore
    public String getName() {
        return getOrgName();
    }

    @Override
    @JsonIgnore
    public void setName(String name) {
        super.setOrgName(name);
    }

    @Override
    @JsonIgnore
    public Integer getTreeOrder() {

        return getOrderId();
    }

    @Override
    @JsonIgnore
    public void setTreeOrder(Integer treeOrder) {
        super.setOrderId(treeOrder);
    }

    @Override
    @JsonIgnore
    public String getTreeLevel() {
        return super.getLevel();
    }

    @Override
    @JsonIgnore
    public void setTreeLevel(String treeLevel) {
        setLevel(treeLevel);
    }

    @Override
    @JsonIgnore
    public Object getAttribute() {
        return null;
    }


}
