<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.CompanyContactMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.common.entity.CompanyContact">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, update_user, update_time, company_code, contact_name, contact_phone, contact_email,contact_system
  </sql>
  <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.CompanyContactExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_company_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sa_company_contact
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sa_company_contact
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.CompanyContactExample">
    delete from sa_company_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.stock.service.platform.common.entity.CompanyContact">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_company_contact (id, update_user, update_time, 
      company_code, contact_name, contact_phone, 
      contact_email)
    values (#{id,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{companyCode,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{contactEmail,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.stock.service.platform.common.entity.CompanyContact">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into sa_company_contact
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactEmail != null">
        contact_email,
      </if>
      <if test="contactSystem != null">
        contact_system,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        #{contactEmail,jdbcType=VARCHAR},
      </if>
      <if test="contactSystem != null">
        #{contactSystem,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.stock.service.platform.common.entity.CompanyContactExample" resultType="java.lang.Long">
    select count(*) from sa_company_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update sa_company_contact
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.contactEmail != null">
        contact_email = #{record.contactEmail,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update sa_company_contact
    set id = #{record.id,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      contact_email = #{record.contactEmail,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.common.entity.CompanyContact">
    update sa_company_contact
    <set>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactEmail != null">
        contact_email = #{contactEmail,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.CompanyContact">
    update sa_company_contact
    set update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_code = #{companyCode,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      contact_email = #{contactEmail,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.CompanyContactExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sa_company_contact
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>