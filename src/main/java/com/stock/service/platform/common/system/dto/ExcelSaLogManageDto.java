package com.stock.service.platform.common.system.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExcelSaLogManageDto implements Serializable {

    //公司编码
    @ExcelProperty("公司编码")
    @ColumnWidth(13)
    private String companyCode;

    //公司名称
    @ExcelProperty("公司名称")
    @ColumnWidth(13)
    private String companyName;

    //账号
    @ExcelProperty("账号")
    @ColumnWidth(10)
    private String userName;

    //ip
    @ExcelProperty("操作ip")
    @ColumnWidth(20)
    private String logIP;

    //url
    @ExcelProperty("方法路径")
    @ColumnWidth(20)
    private String logURL;

    //方法类型
    @ExcelProperty("方法类型")
    @ColumnWidth(13)
    private String logType;

    //方法名
    @ExcelProperty("方法名")
    @ColumnWidth(15)
    private String methodName;

    //参数
    @ExcelProperty("参数")
    @ColumnWidth(30)
    private String param;

    //修改时间
    @ExcelProperty("操作时间")
    @ColumnWidth(20)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createTime;
}
