<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.StockBizMapper">
    <select id="getAllTradeDate" resultType="java.lang.String">
        SELECT DISTINCT TradingDay tradingDay FROM stock_performance
        UNION
        SELECT DISTINCT TradingDay tradingDay FROM stock_evaluation
        UNION
        SELECT DISTINCT chg_date tradingDay FROM stock_holders
        UNION
        SELECT DISTINCT chg_date tradingDay FROM stock_shares
    </select>

    <delete id="deleteVcTradSkDailyData">
        TRUNCATE TABLE vc_trad_sk_daily
    </delete>

    <delete id="deleteVcTradSkDailyDataByCompanyCode">
        delete from vc_trad_sk_daily where SECURITY_CD = #{companyCode}
    </delete>

    <delete id="deleteVcTradSkDailyDataByDate">
        delete from vc_trad_sk_daily where TRADE_DATE = #{date}
    </delete>

    <select id="getAllCompanyCodeByDate" resultType="java.lang.String">
        SELECT DISTINCT SecuCode FROM stock_performance WHERE TradingDay = #{date}
        UNION
        SELECT DISTINCT SecuCode FROM stock_evaluation WHERE TradingDay = #{date}
        UNION
        SELECT DISTINCT sec_code FROM stock_holders WHERE chg_date = #{date}
        UNION
        SELECT DISTINCT sec_code FROM stock_shares WHERE chg_date = #{date}
    </select>

    <select id="getAllCompanyCode" resultType="java.lang.String">
        SELECT DISTINCT SecuCode FROM stock_performance
        UNION
        SELECT DISTINCT SecuCode FROM stock_evaluation
        UNION
        SELECT DISTINCT sec_code FROM stock_holders
        UNION
        SELECT DISTINCT sec_code FROM stock_shares
    </select>

    <select id="getStockPerformance" resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            SecuCode securityCd,
            TradingDay tradeDate,
            ClosePrice NEW,
            ChangePCT chg,
            TurnoverVolume cjl,
            TurnoverValue tvalcny
        FROM stock_performance
        WHERE TradingDay = #{date}
    </select>

    <select id="getStockEvaluation" resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            SecuCode securityCd,
            TradingDay tradeDate,
            TotalMV zsz,
            PE pe9,
            PB pb8,
            NegotiableMV agszbhxs
        FROM stock_evaluation
        WHERE TradingDay = #{date}
    </select>

    <select id="getStockHolders" resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            holder_num shareNum
        FROM stock_holders
        WHERE chg_date = #{date}
    </select>

    <select id="getStockShares" resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            total_shares totalshares,
            float_shares ffshares,
            restricted_share rshare,
            float_shares cshare,
            float_shares_float_ashare ashare,
            limit_shares_limit_ashare asharer,
            float_shares_float_bshare bshare,
            limit_shares_limit_bshare bsharer,
            float_shares_float_hshare hshare,
            limit_shares_limit_hshare hsharer
        FROM stock_shares
        WHERE chg_date = #{date}
    </select>

    <insert id="insertVcTradSkDaily">
        insert into vc_trad_sk_daily
        (ID,
        ID_SOURCE,
        SECURITY_CD,
        TRADE_DATE,
        NEW,
        CHG,
        CJL,
        TVALCNY,
        ZSZ,
        PE9,
        PB8,
        AGSZBHXS,
        BGSZBHXSZRM,
        SHARE_NUM,
        TOTALSHARES,
        FFSHARES,
        RSHARE,
        CSHARE,
        ASHARE,
        ASHARER,
        BSHARE,
        BSHARER,
        HSHARE,
        HSHARER,
        INSERTTIME)
        VALUES
        <foreach collection="vcTradSkDailyList" item="item" separator=",">
            (uuid_short(),
            uuid_short(),
            #{item.securityCd},
            #{item.tradeDate},
            #{item.NEW},
            #{item.chg},
            #{item.cjl},
            #{item.tvalcny},
            #{item.zsz},
            #{item.pe9},
            #{item.pb8},
            #{item.agszbhxs},
            #{item.bgszbhxszrm},
            #{item.shareNum},
            #{item.totalshares},
            #{item.ffshares},
            #{item.rshare},
            #{item.cshare},
            #{item.ashare},
            #{item.asharer},
            #{item.bshare},
            #{item.bsharer},
            #{item.hshare},
            #{item.hsharer},
            now())
        </foreach>
    </insert>

<!--    <select id="getAllTradeDateByCompanyCode" resultType="java.lang.String">-->
<!--        SELECT DISTINCT DATE(TradingDay) tradingDay FROM stock_performance WHERE SecuCode = #{companyCode}-->
<!--        UNION-->
<!--        SELECT DISTINCT DATE(TradingDay) tradingDay FROM stock_evaluation WHERE SecuCode = #{companyCode}-->
<!--        UNION-->
<!--        SELECT DISTINCT DATE(chg_date) tradingDay FROM stock_holders WHERE sec_code = #{companyCode}-->
<!--        UNION-->
<!--        SELECT DISTINCT DATE(chg_date) tradingDay FROM stock_shares WHERE sec_code = #{companyCode}-->
<!--    </select>-->

    <select id="getStockPerformanceByCompanyCode" resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            SecuCode securityCd,
            TradingDay tradeDate,
            ClosePrice NEW,
            ChangePCT chg,
            TurnoverVolume cjl,
            TurnoverValue tvalcny
        FROM stock_performance
        WHERE SecuCode = #{companyCode}
          AND TradingDay BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="getStockEvaluationByCompanyCode"
            resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            SecuCode securityCd,
            TradingDay tradeDate,
            TotalMV zsz,
            PE pe9,
            PB pb8,
            NegotiableMV agszbhxs
        FROM stock_evaluation
        WHERE SecuCode = #{companyCode}
          AND TradingDay BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="getStockHoldersByCompanyCode"
            resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            holder_num shareNum
        FROM stock_holders
        WHERE sec_code = #{companyCode}
    </select>
<!--          AND chg_date BETWEEN #{startDate} AND #{endDate}-->

    <select id="getStockSharesByCompanyCode"
            resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            total_shares totalshares,
            float_shares ffshares,
            restricted_share rshare,
            float_shares cshare,
            float_shares_float_ashare ashare,
            limit_shares_limit_ashare asharer,
            float_shares_float_bshare bshare,
            limit_shares_limit_bshare bsharer,
            float_shares_float_hshare hshare,
            limit_shares_limit_hshare hsharer
        FROM stock_shares
        WHERE sec_code = #{companyCode}
    </select>
<!--          AND chg_date BETWEEN #{startDate} AND #{endDate}-->
    <select id="getStockHoldersByCodeAndDate"
            resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            holder_num shareNum
        FROM stock_holders
        WHERE chg_date &lt;= #{date}
          AND sec_code = #{companyCode}
        order by chg_date desc
            limit 1
    </select>
    <select id="getStockSharesByCodeAndDate"
            resultType="com.stock.service.platform.common.dto.VcTradSkDailyDto">
        SELECT
            sec_code securityCd,
            chg_date tradeDate,
            total_shares totalshares,
            float_shares ffshares,
            restricted_share rshare,
            float_shares cshare,
            float_shares_float_ashare ashare,
            limit_shares_limit_ashare asharer,
            float_shares_float_bshare bshare,
            limit_shares_limit_bshare bsharer,
            float_shares_float_hshare hshare,
            limit_shares_limit_hshare hsharer
        FROM stock_shares
        WHERE chg_date &lt;= #{date}
          AND sec_code = #{companyCode}
          order by chg_date desc
            limit 1
    </select>
</mapper>