package com.stock.service.platform.common.dao;

import com.stock.service.platform.common.entity.CompanyContact;
import com.stock.service.platform.common.entity.CompanyContactExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface CompanyContactMapper {
    long countByExample(CompanyContactExample example);

    int deleteByExample(CompanyContactExample example);

    int deleteByPrimaryKey(String id);

    int insert(CompanyContact record);

    int insertSelective(CompanyContact record);

    List<CompanyContact> selectByExampleWithRowbounds(CompanyContactExample example, RowBounds rowBounds);

    List<CompanyContact> selectByExample(CompanyContactExample example);

    CompanyContact selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") CompanyContact record, @Param("example") CompanyContactExample example);

    int updateByExample(@Param("record") CompanyContact record, @Param("example") CompanyContactExample example);

    int updateByPrimaryKeySelective(CompanyContact record);

    int updateByPrimaryKey(CompanyContact record);
}