package com.stock.service.platform.common.service;

import com.google.common.base.Throwables;
import com.google.common.eventbus.AsyncEventBus;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.core.log.LogEvent;
import com.stock.core.misc.MultiTenantContextHolder;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.LogMapper;
import com.stock.service.platform.common.entity.Log;
import com.stock.service.platform.common.entity.SaOperationLogWithBLOBs;
import com.stock.service.platform.common.exception.BusinessException;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.log.OperationLogManager;
import microsoft.exchange.webservices.data.core.exception.misc.ArgumentException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LogService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(LogService.class);

    @Autowired
    private LogMapper logMapper;

    @Resource
    private AsyncEventBus asyncEventBus;

    @Resource
    private OperationLogManager operationLogManager;

    @Async
    @EventListener
    public void processLog(LogEvent logEvent) {
        if (logEvent.getTenantId() != null) {
            MultiTenantContextHolder.set(logEvent.getTenantId());
        }
        Log entity = new Log();
        entity.setLogType(logEvent.getLogType());
        entity.setLogName(logEvent.getLogName());
        entity.setLogContent(logEvent.getLogContent());
        entity.setLogIp(logEvent.getLogRemoteAddress());
        entity.setLogTime(getDBTime());       
        logger.info("log user is [{}]", logEvent.getLogUser());
        logMapper.insertSelective(entity);
    }

    public void asyncSaveOperationLog(OperationLogEvent operationLogEvent) {
        asyncEventBus.post(operationLogEvent);
    }

    public JsonResponse<String> createOperationLog(SaOperationLogWithBLOBs operationLogParam, UserInfo userInfo) {
        JsonResponse<String> response = new JsonResponse<>();
        try {
            if (null == operationLogParam) {
                throw new ArgumentException("公司员工参数异常");
            }
            if (null == userInfo) {
                throw new ArgumentException("登陆用户参数异常");
            }
            if (operationLogManager.createOperationLog(operationLogParam, userInfo)) {
                response.setResult("创建系统日志成功");
            } else {
                response.setErrorMsg("创建系统日志失败");
            }
        } catch (ArgumentException e) {
            response.setErrorMsg(e.getMessage());
        } catch (BusinessException e) {
            response.setErrorMsg(e.getMessage());
            logger.info("创建系统日志异常！原因：{}", Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            response.setErrorMsg("创建系统日志出错！原因：" + e.getMessage());
            logger.info("创建系统日志出错！原因：{}", Throwables.getStackTraceAsString(e));
        }
        return response;
    }
}
