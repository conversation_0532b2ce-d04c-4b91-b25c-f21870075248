package com.stock.service.platform.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MessageDto implements Serializable {

  private String code;
  private String msg;
  private String msgId;
  private String transactionId;
  private String data;
  private String userId;
  private String sendTime;

  private List<MessageDetailDto> failList;
  private List<MessageDetailDto> successList;
  private List<MessageDetailDto> unknownList;

}