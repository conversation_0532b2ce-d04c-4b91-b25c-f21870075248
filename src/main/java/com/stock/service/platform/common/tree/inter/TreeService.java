package com.stock.service.platform.common.tree.inter;

import java.util.List;

public interface TreeService {
    /**
     * 功能描述: <br>添加节点
     * @Param: [node]
     * @Return: com.stock.capital.cloud.tree.inter.TreeNode
     * @Author: DarkSql
     * @Date: 2021/1/20 14:00
     */
     <T extends TreeNode> T addNode(T node);
    /**
     * 功能描述: <br>向下移动节点
     * @Param: [node]
     * @Return: java.util.List<com.stock.capital.cloud.tree.inter.TreeNode>
     * @Author: DarkSql
     * @Date: 2021/1/20 14:00
     */
    <T extends TreeNode> List<T> moveDownNode(T node);
    /**
     * 功能描述: <br>向上移动节点
     * @Param: [node]
     * @Return: java.util.List<com.stock.capital.cloud.tree.inter.TreeNode>
     * @Author: DarkSql
     * @Date: 2021/1/20 14:00
     */
    <T extends  TreeNode> List<T> moveUpNode(T node);
    /**
     * 功能描述: <br>添加子节点
     * @Param: [node]
     * @Return: com.stock.capital.cloud.tree.inter.TreeNode
     * @Author: DarkSql
     * @Date: 2021/1/20 14:00
     */
    <T extends  TreeNode> T addChildNode(T node);
    /**
     * 功能描述: <br>调整节点位置,从from节点移动到to节点,type=0表示移动到子节点,否则移动到顶级节点
     * @Param: [from, to, type]
     * @Return: void
     * @Author: DarkSql
     * @Date: 2021/1/20 14:01
     */
    <T extends  TreeNode>List<T> adjustNode(String from,String to,String type);
    /**
     * 功能描述: <br>删除节点,并且返回删除后的同级别所有节点
     * @Param: [nodeId]
     * @Return: java.util.List<? extends com.stock.capital.cloud.tree.inter.TreeNode>
     * @Author: DarkSql
     * @Date: 2021/1/20 14:03
     */
    <T extends TreeNode> List<T> delete(String nodeId);
    /**
     * 功能描述: <br>获取当前树节点的树形结构集合
     * @Param: [treeId]
     * @Return: java.util.List<? extends com.stock.capital.cloud.tree.inter.TreeNode>
     * @Author: DarkSql
     * @Date: 2021/1/21 10:17
     */
    <T extends TreeNode> List<T>  listNode(String treeId);



}
