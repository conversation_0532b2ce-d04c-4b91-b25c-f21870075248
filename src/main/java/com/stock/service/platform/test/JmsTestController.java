package com.stock.service.platform.test;

import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.besMQ.component.TopicProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/captcha")
public class JmsTestController {

    @Autowired
    private TopicProducer topicProducer;

    /**
     * 发送消息
     */
    @RequestMapping("/sendMessages")
    public void sendMessages(String msg) throws Exception {
        topicProducer.sendTopic(msg, "HGGL", TopicConstant.BUS_TYPE_COMPANY, TopicConstant.OP_TYPE_INSERT,"1");
    }
}

