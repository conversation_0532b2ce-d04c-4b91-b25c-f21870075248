package com.stock.service.platform.regulation.controller;

import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.common.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("regulation")
public class RegulationController {

    @Autowired
    private CommonService commonService;

    @Value("${service.gui.baseUrl}")
    private String baseUrl;

    @GetMapping(value = "getTokenAndTenantInfo")
    @ResponseBody
    public JsonResponse<Map<String,Object>> getTokenAndTenantInfo() {
        JsonResponse<Map<String,Object>> result = new JsonResponse<>();
        Map<String,Object> map  = new HashMap<>();
        String guiAccessToken = commonService.getGuiAccessToken();
        String tenantInfo = commonService.getTenantInfoChangeCompanyCode();
        map.put("token",guiAccessToken);
        map.put("tenantInfo",tenantInfo);
        map.put("ydBaseUrl",baseUrl);
        result.setResult(map);
        return result;
    }
}
