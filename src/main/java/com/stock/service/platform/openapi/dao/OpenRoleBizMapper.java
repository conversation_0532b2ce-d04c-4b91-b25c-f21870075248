package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.openapi.request.OpenRoleRequest;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OpenRoleBizMapper {
    /**
     * 查询role
     * @param dto
     * @return RoleManageResponse
     */
    List<OpenRoleResponse> queryRoleList(OpenRoleRequest dto);


    /**
     * 插入role
     * @param role
     * @return
     */
    int insert(OpenRoleRequest role);

    int updateByPrimaryKeySelective(OpenRoleRequest role);

    int deleteByPrimaryKey(List<String> splist,String source);

    int selectByName(OpenRoleRequest role);

    int deleteByRoleId(List<String> splist, String source);

    int deleteUserRoleByRoleId(List<String> splist, String source);

    List<OpenRoleResponse> deleteCheck(List<String> splist);

    OpenRoleResponse updateCheck(OpenRoleRequest role);

    int deleteBySource(String source);

    int setRoleList(List<OpenRoleRequest> selfList,String source);

    int deleteRoleRouter();

    int deleteUserRole();


}
