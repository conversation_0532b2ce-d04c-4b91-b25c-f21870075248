package com.stock.service.platform.openapi.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 角色权限实体类
 */
public class OpenPermissionRequest implements Serializable {
    private String id; //主键id

    private String roleId;//角色id
    private String routerIds;//权限id列表 多id用逗号分割
    private String source;//系统来源 对应库内update_user
    private Date updateTime;//更新时间
    private String createOrDelete;//0:创建.1:删除
    private List<OpenPermissionRequest> selfList;

    public List<OpenPermissionRequest> getSelfList() {
        return selfList;
    }

    public void setSelfList(List<OpenPermissionRequest> selfList) {
        this.selfList = selfList;
    }

    public String getCreateOrDelete() {
        return createOrDelete;
    }

    public void setCreateOrDelete(String createOrDelete) {
        this.createOrDelete = createOrDelete;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRouterIds() {
        return routerIds;
    }

    public void setRouterIds(String routerIds) {
        this.routerIds = routerIds;
    }
}
