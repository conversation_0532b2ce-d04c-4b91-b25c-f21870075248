package com.stock.service.platform.openapi.request;

import java.io.Serializable;
import java.util.List;

public class OpenUserRoleRequest implements Serializable {
    private String id;
    private String userId;
    private String roles;
    private String source;
    private String createOrDelete;
    private List<OpenUserRoleRequest> selfList;

    public List<OpenUserRoleRequest> getSelfList() {
        return selfList;
    }

    public void setSelfList(List<OpenUserRoleRequest> selfList) {
        this.selfList = selfList;
    }

    public String getCreateOrDelete() {
        return createOrDelete;
    }

    public void setCreateOrDelete(String createOrDelete) {
        this.createOrDelete = createOrDelete;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
