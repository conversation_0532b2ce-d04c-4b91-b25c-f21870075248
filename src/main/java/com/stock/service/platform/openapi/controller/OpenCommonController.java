package com.stock.service.platform.openapi.controller;

import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.openapi.request.OpenLoginRequest;
import com.stock.service.platform.openapi.service.OpenCommonService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("openapi")
public class OpenCommonController {

    @Autowired
    private OpenCommonService service;

    /**
     * 登录
     */
    @PostMapping(value = "login")
    @ResponseBody
    public JsonResponse<Object>login(@RequestBody OpenLoginRequest dto) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            if (StringUtils.isNotEmpty(dto.getCode())) {
                response.setResult(service.validateCode(dto.getCode()));
            }else {
                response.setResult(service.validateToken(dto.getToken()));
            }

        } catch (Exception e) {
            String errorMsg = e.getMessage();
            if(StringUtils.isEmpty(errorMsg)) {
                errorMsg = "系统接口报错";
            }
            response.setErrorMsg(errorMsg);
            e.printStackTrace();
        }
        return response;
    }
}
