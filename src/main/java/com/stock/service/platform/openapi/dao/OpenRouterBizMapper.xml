<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.openapi.dao.OpenRouterBizMapper">
    <update id="updateByPrimaryKeySelective">
        update sa_router
        <set>
            <if test="pRouterId != null">
                p_router_id = #{pRouterId,jdbcType=VARCHAR},
            </if>
            <if test="routerName != null">
                router_name = #{routerName,jdbcType=VARCHAR},
            </if>
            <if test="routerDes != null">
                router_des = #{routerDes,jdbcType=VARCHAR},
            </if>
            <if test="routerType != null">
                router_type = #{routerType,jdbcType=VARCHAR},
            </if>
            <if test="routerFolderFlag != null">
                router_folder_flag = #{routerFolderFlag,jdbcType=INTEGER},
            </if>
            <if test="sortNo != null">
                sort_no = #{sortNo,jdbcType=INTEGER},
            </if>
            <if test="meta != null">
                meta = #{meta,jdbcType=VARCHAR},
            </if>
            <if test="path != null">
                path = #{path,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByPrimaryKey">
        delete from sa_router
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByRouterId">
        delete from sa_role_router_map
        where router_id = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="queryRouterListByRoleId" resultType="com.stock.service.platform.openapi.request.OpenRouterRequest">
        select router.id,router.p_router_id,router.router_des,temp.id,
        case ifnull(temp.id,'0') when '0' then '0' else '1' end as hasPermission
        from sa_router router left join (
        select
        inner_router.id
        from
        sa_router inner_router
        join sa_role_router_map rrm on rrm.router_id = inner_router.id
        where rrm.role_id = #{roleId}
        ) temp on router.id = temp.id
        where
        <if test="pRouterId == null or pRouterId == ''">
            p_router_id is null or p_router_id = ''
        </if>
        <if test="pRouterId != null and pRouterId != ''">
            p_router_id = #{pRouterId}
        </if>
        ORDER BY router.sort_no
    </select>
    <select id="queryRouterList" resultType="com.stock.service.platform.openapi.response.OpenRouterResponse">
        SELECT
        router.p_router_id,
        router.router_des,
        router.router_name,
        router.router_type,
        router.path
        FROM
        sa_router router
        <where>
            <if test="routerName != null and routerName != ''">
                router_name = #{routerName}
            </if>
            <if test="pRouterId != null and pRouterId != ''">
                p_router_id = #{pRouterId}
            </if>
            <if test="path != null and path != ''">
                path = #{path}
            </if>
            <if test="routerType != null and routerType != ''">
                router_type = #{routerType}
            </if>
        </where>
    </select>
    <select id="selectByName" resultType="java.lang.Integer">
        select count(*) from sa_router where router_name=#{routerName}
    </select>

    <insert id="insertRoleRouterList">
        insert into sa_role_router_map (id,role_id,router_id) values
        <foreach collection="routerIdSet" item="routerId" open="" close="" separator="">
            (SELECT uuid_short(),#{roleId},#{routerId})
        </foreach>
    </insert>

    <insert id="insertRoleRouter">
        insert into sa_role_router_map (id, role_id, router_id)
        select (SELECT uuid_short()), #{roleId}, #{routerId}
        from dual
    </insert>
    <insert id="insert">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_router
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,router_name,path,
            <if test="pRouterId != null and pRouterId != '' ">
                p_router_id,
            </if>

            <if test="routerDes != null and routerDes != '' ">
                router_des,
            </if>
            <if test="routerType != null and routerType != '' ">
                router_type,
            </if>
            <if test="routerFolderFlag != null and routerFolderFlag != '' ">
                router_folder_flag,
            </if>
            <if test="sortNo != null and sortNo != '' ">
                sort_no,
            </if>
            <if test="meta != null and meta != ''">
                meta,
            </if>
            <if test="createUser != null and createUser != ''">
                create_user,
            </if>
            <if test="createTime != null and createTime != ''">
                create_time,
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user,
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR}, #{routerName,jdbcType=VARCHAR}, #{path,jdbcType=VARCHAR},
            <if test="pRouterId != null and pRouterId != ''">
                #{pRouterId,jdbcType=VARCHAR},
            </if>
            <if test="routerDes != null and routerDes != ''">
                #{routerDes,jdbcType=VARCHAR},
            </if>
            <if test="routerType != null and routerType != ''">
                #{routerType,jdbcType=VARCHAR},
            </if>
            <if test="routerFolderFlag != null and routerFolderFlag != ''">
                #{routerFolderFlag,jdbcType=INTEGER},
            </if>
            <if test="sortNo != null and sortNo != ''">
                #{sortNo,jdbcType=INTEGER},
            </if>
            <if test="meta != null and meta != ''">
                #{meta,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null and createTime != ''">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null and updateUser != ''">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null and updateTime != ''">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != ''">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>