package com.stock.service.platform.openapi.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class OpenRoleRequest implements Serializable {

    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String id;
    private String ids;

    @ApiModelProperty("角色名")
    private String roleName;

    @ApiModelProperty("具体描述")
    private String roleDes;

    private String currStatus;

    private String companyId;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private String status;
    private String source; //来源于哪个系统
    private List<OpenRoleRequest> selfList;

    public List<OpenRoleRequest> getSelfList() {
        return selfList;
    }

    public void setSelfList(List<OpenRoleRequest> selfList) {
        this.selfList = selfList;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {

        this.source = source;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleDes() {
        return roleDes;
    }

    public void setRoleDes(String roleDes) {
        this.roleDes = roleDes;
    }

    public String getCurrStatus() {
        return currStatus;
    }

    public void setCurrStatus(String currStatus) {
        this.currStatus = currStatus;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
