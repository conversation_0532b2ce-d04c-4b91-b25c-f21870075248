package com.stock.service.platform.openapi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 人员信息
 */
@ApiModel(value = "人员dto", description = "人员dto")
public class OpenPersonResponse implements Serializable {
    private String userId;//对应系统用户id
    private String id;

    @Override
    public String toString() {
        return "{" +
                "id='" + id + '\'' +
                ", personName='" + personName + '\'' +
                ", source='" + source + '\'' +
                '}';
    }


    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id", example = "01")
    private String orgId;

    /**
     * 任职记录ID
     */
    @ApiModelProperty(value = "任职记录ID", example = "01")
    private String resumeId;


    /**
     * 用户锁状态
     */
    @ApiModelProperty(value = "用户锁定状态", example = "1")
    private String userLockStatus;

    /**
     * 用户可用状态
     */
    @ApiModelProperty(value = "用户可用状态", example = "1")
    private String userAvailableStatus;

    /**
     * 人员名
     */
    @ApiModelProperty(value = "人员名字", example = "张三")
    private String personName;

    /**
     * 用户名字
     */
    @ApiModelProperty(value = "用户名字", example = "zhang.san")
    private String userName;

    /**
     * 人员手机
     */
    @ApiModelProperty(value = "人员手机号", example = "13000000000")
    private String telephone;

    /**
     * 机构名字
     */
    @ApiModelProperty(value = "机构名字", example = "董事会")
    private String orgNames;

    /**
     * 职位名称
     */
    @ApiModelProperty(value = "身份名字", example = "董事长")
    private String identityNames;

    /**
     * 来源
     */
    private String source;

    @ApiModelProperty(value = "机构类别")
    private String orgType;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }


    public String getUserLockStatus() {
        return userLockStatus;
    }

    public void setUserLockStatus(String userLockStatus) {
        this.userLockStatus = userLockStatus;
    }

    public String getUserAvailableStatus() {
        return userAvailableStatus;
    }

    public void setUserAvailableStatus(String userAvailableStatus) {
        this.userAvailableStatus = userAvailableStatus;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        if (StringUtils.isBlank(telephone)) {
            this.telephone = null;
            return;
        }
        this.telephone = telephone;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getIdentityNames() {
        return identityNames;
    }

    public void setIdentityNames(String identityNames) {
        this.identityNames = identityNames;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = "0".equals(source)?"plf":source;
    }

    public String getResumeId() {
        return resumeId;
    }

    public void setResumeId(String resumeId) {
        this.resumeId = resumeId;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }
}