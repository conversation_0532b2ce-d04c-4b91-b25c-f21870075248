package com.stock.service.platform.openapi.response;

import java.io.Serializable;

public class OpenOrgResponse implements Serializable {
    private String id;

    private String companyId;

    private String orgCode;

    private String orgName;

    private String pOrgId;

    private String orgType;

    private String status;

    private String source;

    @Override
    public String toString() {
        return "{" +
                "id='" + id + '\'' +
                ", companyId='" + companyId + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", pOrgId='" + pOrgId + '\'' +
                ", orgType='" + orgType + '\'' +
                ", source='" + source + '\'' +
                '}';
    }

    private static final long serialVersionUID = 1L;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId == null ? null : companyId.trim();
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getpOrgId() {
        return pOrgId;
    }

    public void setpOrgId(String pOrgId) {
        this.pOrgId = pOrgId == null ? null : pOrgId.trim();
    }


    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType == null ? null : orgType.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

}