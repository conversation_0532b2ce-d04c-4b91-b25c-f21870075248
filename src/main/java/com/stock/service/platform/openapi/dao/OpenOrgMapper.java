package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.OrgExample;
import com.stock.service.platform.openapi.request.OpenOrgRequest;
import com.stock.service.platform.openapi.response.OpenOrgResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface OpenOrgMapper {
    long countByExample(OrgExample example);

    int deleteByExample(OrgExample example);

    int deleteByPrimaryKey(OpenOrgRequest record);

    int insert(Org record);

    int insertSelective(OpenOrgRequest record);

    List<Org> selectByExampleWithRowbounds(OrgExample example, RowBounds rowBounds);

    List<Org> selectByExample(OrgExample example);

    Org selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") Org record, @Param("example") OrgExample example);

    int updateByExample(@Param("record") Org record, @Param("example") OrgExample example);

    int updateByPrimaryKeySelective(OpenOrgRequest record);

    int updateByPrimaryKey(OpenOrgRequest record);

    List<Map<String,String>> selectOrgDictByIsMgr(@Param("params") Map params);

    String selectOrgIdByBranch(@Param("orgId") String orgId);

    String selectOrgIdByDept(@Param("orgId") String orgId);

    String selectOrgIdByBranchBesb(@Param("orgId") String orgId);

    String checkBranchIfShare(@Param("orgId") String orgId);

    // 仅可用于外部公司
    Org selectByCompanyId(@Param("companyId") String companyId);

    Org selectByUserId(@Param("userId") String userId);

    List<OpenOrgResponse> queryOrg(OpenOrgRequest dto);

    int checkOrgCode(String orgCode,String id);

    String checkOrgType(String pOrgId);
}