package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.openapi.request.OpenRouterRequest;
import com.stock.service.platform.openapi.response.OpenRouterResponse;
import com.stock.service.platform.openapi.service.OpenRouterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("openapi/router")
public class OpenRouterController extends BaseController {

    @Autowired
    OpenRouterService openRouterService;


    /**
     * 展示路由信息列表
     */
    @PostMapping("queryRouterList")
    public JsonResponse<List<OpenRouterResponse>> listRouterInfo(@RequestBody OpenRouterRequest dto) {
        JsonResponse<List<OpenRouterResponse>> response = new JsonResponse<>();
        try {
            response = openRouterService.queryRoleListService(dto);
        } catch (Exception e) {
            response.setErrorMsg("查询路由信息列表失败:" + e.getMessage());
            e.printStackTrace();
        }
        return response;
    }


    /**
     * 创建路由信息
     */
    @PostMapping("createRouter")
    public JsonResponse<Boolean> createRouter(@RequestBody OpenRouterRequest router) {
        JsonResponse<Boolean> response =new JsonResponse<>();
        // 创建设置参数为null
        router.setId(null);
        try {
             response=openRouterService.editRouter(router);
        } catch (Exception e) {
            response.setErrorMsg("创建路由失败:" + e.getMessage());
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 编辑路由信息
     */
    @PostMapping("modifyRouter")
    public JsonResponse<Boolean> modifyRouter(@RequestBody OpenRouterRequest router) {
        JsonResponse<Boolean> response =new JsonResponse<>();
        try {
            response=openRouterService.editRouter(router);
        } catch (Exception e) {
            response.setErrorMsg("编辑路由失败:" + e.getMessage());
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 删除路由
     */
    @PostMapping("deleteRouter")
    public JsonResponse<Boolean> deleteRouter(@RequestBody Map<String,Object> map) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        try {
            response=openRouterService.deleteRouter(String.valueOf(map.get("idList")));
        } catch (Exception e) {
            response.setErrorMsg("删除路由失败:" + e.getMessage());
            e.printStackTrace();
        }
        return response;
    }



}
