package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.request.OpenPersonRequest;
import com.stock.service.platform.openapi.response.OpenPersonResponse;
import com.stock.service.platform.openapi.service.OpenPersonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 人员controller
 */
@Api(value = "人员管理", description = "人员信息相关api")
@RestController
@RequestMapping("openapi/person")
public class OpenPersonController extends BaseController {

    @Resource
    OpenPersonService openPersonService;


    @ApiOperation(value = "查询人员列表", notes = "查询人员列表")
    @PostMapping("/queryPersonList")
    public JsonResponse<Map<String, List<OpenPersonResponse>>> listPersonInfo(@RequestBody OpenPersonRequest request) {
        JsonResponse<Map<String, List<OpenPersonResponse>>> jsonResponse =new JsonResponse<>();
        try {
            Map<String, List<OpenPersonResponse>> openPersonResponseList = openPersonService.listPersonInfoService(request);
            jsonResponse.setResult(openPersonResponseList);
        }catch (BaseException e) {
            jsonResponse.setErrorMsg("查询人员列表错误:"+e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 全量插入人员数据 先删后插
     *
     * @param request
     * @return
     */
    @PostMapping("setPersonInfoList")
    public JsonResponse<String> setPersonInfoList(@RequestBody OpenPersonRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPersonService.setPersonInfoList(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("全量插入人员信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 创建人员信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "创建人员信息", notes = "创建人员信息")
    @PostMapping("createPersonInfo")
    public JsonResponse<String> createPersonInfo(@RequestBody OpenPersonRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        request.setId(null);
        try {
            jsonResponse.setResult(openPersonService.editPersonInfo(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("创建人员信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 修改用户信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "修改人员信息", notes = "修改人员信息")
    @PostMapping("updatePersonInfo")
    public JsonResponse<String> updatePersonInfo(@RequestBody OpenPersonRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPersonService.editPersonInfo(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("修改人员信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }


    /**
     * 删除人员
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "根据人员id删除人员", notes = "根据人员id删除人员")
    @PostMapping("deletePersonByIds")
    public JsonResponse<String> deletePersonByIds(@RequestBody OpenPersonRequest request) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPersonService.deletePersonByIds(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("删除人员错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

}
