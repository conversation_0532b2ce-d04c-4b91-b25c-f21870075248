package com.stock.service.platform.openapi.response;

import java.io.Serializable;

public class OpenCompanyResponse implements Serializable {
    private String id;

    private String companyCode;

    private String zhName;

    private String zhSortName;

    private String corporate;

    private String businessDepartmentName;

    private String companyStockName;

    private String marketStockType;

    private String statusName;

    private String source;//系统来源

    @Override
    public String toString() {
        return "{" +
                "id='" + id + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", zhName='" + zhName + '\'' +
                ", source='" + source + '\'' +
                '}';
    }

    private static final long serialVersionUID = 1L;

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName == null ? null : zhName.trim();
    }


    public String getZhSortName() {
        return zhSortName;
    }

    public void setZhSortName(String zhSortName) {
        this.zhSortName = zhSortName == null ? null : zhSortName.trim();
    }

    public String getCorporate() {
        return corporate;
    }

    public void setCorporate(String corporate) {
        this.corporate = corporate == null ? null : corporate.trim();
    }

    public String getBusinessDepartmentName() {
        return businessDepartmentName;
    }

    public void setBusinessDepartmentName(String businessDepartmentName) {
        this.businessDepartmentName = businessDepartmentName;
    }

    public String getCompanyStockName() {
        return companyStockName;
    }

    public void setCompanyStockName(String companyStockName) {
        this.companyStockName = companyStockName == null ? null : companyStockName.trim();
    }


    public String getMarketStockType() {
        return marketStockType;
    }

    public void setMarketStockType(String marketStockType) {
        this.marketStockType = marketStockType == null ? null : marketStockType.trim();
    }

}