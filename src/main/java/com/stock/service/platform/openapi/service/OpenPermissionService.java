package com.stock.service.platform.openapi.service;

import com.alibaba.fastjson.JSON;
import com.stock.core.exception.BaseException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.openapi.dao.OpenPermissionBizMapper;
import com.stock.service.platform.openapi.request.OpenPermissionRequest;
import com.stock.service.platform.openapi.request.OpenRoleRequest;
import com.stock.service.platform.openapi.request.OpenUserRequest;
import com.stock.service.platform.openapi.request.OpenUserRoleRequest;
import com.stock.service.platform.openapi.response.OpenPermissionResponse;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import com.stock.service.platform.openapi.response.OpenUserRoleResponse;
import com.stock.service.platform.common.service.MessageService;
import com.stock.service.platform.common.util.PasswordUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 权限相关的service
 */
@Service
public class OpenPermissionService extends BaseService {
    private static final Logger logger = LoggerFactory.getLogger(OpenPermissionService.class);

    @Autowired
    private OpenPermissionBizMapper openPermissionBizMapper;

    @Autowired
    private Environment env;

    @Autowired
    private MessageService messageService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    RoleMapper roleMapper;

    @Autowired
    RouterMapper routerMapper;

    @Autowired
    RoleRouterMapMapper roleRouterMapMapper;

    @Autowired
    UserMapper userMapper;

    @Autowired
    UserRoleMapMapper userRoleMapMapper;

    public Map<String, List<OpenPermissionResponse>> listRouterByRoleId(String roleId) throws BaseException {
        if (StringUtils.isEmpty(roleId.trim())) {
            throw new BaseException("id为空");
        }
        List<OpenPermissionResponse> list = openPermissionBizMapper.queryRouterListByRoleId(roleId);
        Map<String, List<OpenPermissionResponse>> map = list.stream().collect(Collectors.groupingBy(OpenPermissionResponse::getUpdateUser));
        return map;
    }

    public String createOrDeleteRoleRouters(OpenPermissionRequest dto) throws BaseException {
        if (StringUtils.isEmpty(dto.getRoleId().trim())) {
            throw new BaseException("角色id为空");
        }
        if (StringUtils.isEmpty(dto.getRouterIds().trim())) {
            throw new BaseException("权限id为空");
        }
        List<String> splist = Arrays.stream(dto.getRouterIds().split(",")).collect(Collectors.toList());
        if ("0".equals(dto.getCreateOrDelete())) {//0是创建
            for (String routerId : splist) {
                int i = openPermissionBizMapper.checkRoleRouter(dto.getRoleId(), routerId);
                if (i>0) { //检查角色是否有对应权限
                    return "创建失败该角色已有权限,权限id为:"+routerId;
                }
            }
            openPermissionBizMapper.createRoleRouter(dto.getRoleId(), splist, dto.getSource());
            return "创建成功";
        } else {//1 是删除
            int i = openPermissionBizMapper.deleteRoleRouter(dto.getRoleId(), splist, dto.getSource());
            if (i < splist.size()) {
                List<OpenPermissionResponse> response = openPermissionBizMapper.selectRoleRouter(dto.getRoleId(), splist);
                return "删除错误,未成功删除信息为:" + response.toString();
            }
            return "删除成功";
        }

    }

    /**
     * 设置用户
     *
     * @param userDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String setUser(OpenUserRequest userDto) throws BaseException {
        // 检查用户是否存在
        if (openPermissionBizMapper.selectByPrimaryKey(userDto.getId()) > 0) {
            throw new BaseException("用户已存在");
        }
        if (openPermissionBizMapper.selectByUserName(userDto.getUserName()) > 0) {
            throw new BaseException("用户名:"+userDto.getUserName()+"已存在");
        }
        // 设置
        String passWord;
        Profiles profiles = Profiles.of("dev","prepub-sz","test", "uat");
        if(env.acceptsProfiles(profiles)) {
            passWord = LogicConstant.DEFAULT_PASSWORD;
        } else {
            passWord = PasswordUtil.randomPassword(8);
        }
        String MD5Password = DigestUtils.md5DigestAsHex(passWord.getBytes(StandardCharsets.UTF_8));
        userDto.setNewPassword(MD5Password);
        // 发送短信
        String content = this.getMsgContent(passWord, userDto.getUserName());
        logger.info("发送短信接口返回结果为:" + JSON.toJSONString(messageService.sendMessage(userDto.getTelephone(), content)));
        // 设置
        userDto.setNewPassword(passwordEncoder.encode(userDto.getNewPassword()));
        openPermissionBizMapper.insertUser(userDto);
        return "设置成功";
    }

    public String getMsgContent(String password, String userName) {
        logger.info("message content :" + "您好，您股权激励管理系统登录账号为：" + userName + "，登录初始密码为：" + password + "请您及时登录并修改初始密码");
        return "您好，您股权激励管理系统登录账号为：" + userName + "，登录初始密码为：" + password + "请您及时登录并修改初始密码";
    }

    public String createOrDeleteUserRoles(OpenUserRoleRequest dto) throws BaseException{
        if (StringUtils.isEmpty(dto.getUserId().trim())) {
            throw new BaseException("用户id为空");
        }
        if (StringUtils.isEmpty(dto.getRoles().trim())) {
            throw new BaseException("角色id为空");
        }
        if ("0".equals(dto.getCreateOrDelete())) {//0是创建
            List<String> splist = Arrays.stream(dto.getRoles().split(",")).collect(Collectors.toList());
            if (openPermissionBizMapper.checkUserRole(dto.getUserId(), splist) > 0) {//查询用户是否有该权限
                return "该用户已有该角色";
            } else {
                openPermissionBizMapper.insertUserRole(dto.getUserId(), splist, dto.getSource());
            }
            return "创建成功";
        } else {// 1 是删除
            String roles = dto.getRoles();
            List<String> splist = Arrays.stream(roles.split(",")).collect(Collectors.toList());
            int i = openPermissionBizMapper.deleteUserRole(dto.getUserId(), splist, dto.getSource());
            if (i < splist.size()) {
                List<OpenUserRoleResponse> response = openPermissionBizMapper.selectUserRole(dto.getUserId(), splist);
                return "删除错误,未成功删除信息为:" + response.toString();
            }
            return "删除成功";
        }

    }

    public Map<String, List<OpenUserRoleResponse>> getUserOfRolesByUserId(OpenUserRequest dto) throws BaseException{
        if (StringUtils.isEmpty(dto.getId().trim())) {
            throw new BaseException("id为空");
        }
        List<OpenUserRoleResponse> list = openPermissionBizMapper.selectUserRolesByUserId(dto);
        Map<String, List<OpenUserRoleResponse>> map = list.stream().collect(Collectors.groupingBy(OpenUserRoleResponse::getUpdateUser));
        return map;
    }

    public Map<String, List<OpenUserRequest>> getRoleOfUsersByRoleId(OpenRoleRequest dto) throws BaseException{
        if (StringUtils.isEmpty(dto.getId().trim())) {
            throw new BaseException("id为空");
        }
        List<OpenUserRequest> list =openPermissionBizMapper.getRoleOfUsersByRoleId(dto);
        Map<String, List<OpenUserRequest>> map = list.stream().collect(Collectors.groupingBy(OpenUserRequest::getUpdateUser));
        return map;
    }
    @Transactional(rollbackFor = Exception.class)
    public String setRoleRoutersList(OpenPermissionRequest request) throws BaseException{
        //判断角色或权限是否存在
        List<OpenPermissionRequest> selfList = request.getSelfList();
        List<String> collect;
        List<String> roleIdCollect = selfList.stream().map(OpenPermissionRequest::getRoleId).collect(Collectors.toList());
        collect=roleIdCollect.stream().distinct().collect(Collectors.toList());
        if (collect.size()<roleIdCollect.size()){
            throw new BaseException("角色id参数有重复,请重新填写");
        }
        for (OpenPermissionRequest openPermissionRequest : selfList) {
            if (StringUtils.isEmpty(openPermissionRequest.getRoleId().trim())) {
                throw new BaseException("角色id为空");
            }
            if (StringUtils.isEmpty(openPermissionRequest.getRouterIds().trim())) {
                throw new BaseException("权限id为空");
            }
            RoleExample example = new RoleExample();
            example.createCriteria().andIdEqualTo(openPermissionRequest.getRoleId());
            List<Role> roles = roleMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(roles)){
                throw new BaseException("角色不存在,角色id为:"+openPermissionRequest.getRoleId());
            }
            List<String> routerIdList = Arrays.stream(openPermissionRequest.getRouterIds().split(",")).collect(Collectors.toList());
            for (String routerId : routerIdList) {
                RouterExample routerExample = new RouterExample();
                routerExample.createCriteria().andIdEqualTo(routerId);
                List<Router> routers = routerMapper.selectByExample(routerExample);
                if (CollectionUtils.isEmpty(routers)){
                    throw new BaseException("权限不存在,权限id为:"+routerId);
                }
            }
        }
        //判断完,先删除
        RoleRouterMapExample roleRouterMapExample = new RoleRouterMapExample();
        roleRouterMapExample.createCriteria().andUpdateUserEqualTo(request.getSource());
        roleRouterMapMapper.deleteByExample(roleRouterMapExample);
        //后插入
        for (OpenPermissionRequest openPermissionRequest : selfList) {
            List<String> routerIdList = Arrays.stream(openPermissionRequest.getRouterIds().split(",")).collect(Collectors.toList());
            openPermissionBizMapper.createRoleRouter(openPermissionRequest.getRoleId(), routerIdList, request.getSource());
        }
        return "创建成功";
    }

    @Transactional(rollbackFor = Exception.class)
    public String setUserRolesList(OpenUserRoleRequest request) throws BaseException{
        //判断角色或用户是否存在
        List<OpenUserRoleRequest> selfList = request.getSelfList();
        List<String> collect;
        List<String> userIdCollect = selfList.stream().map(OpenUserRoleRequest::getUserId).collect(Collectors.toList());
        collect=userIdCollect.stream().distinct().collect(Collectors.toList());
        if (collect.size()<userIdCollect.size()){
            throw new BaseException("角色id参数有重复,请重新填写");
        }
        for (OpenUserRoleRequest openUserRoleRequest : selfList) {
            if (StringUtils.isEmpty(openUserRoleRequest.getUserId().trim())) {
                throw new BaseException("用户id为空");
            }
            if (StringUtils.isEmpty(openUserRoleRequest.getRoles().trim())) {
                throw new BaseException("角色id为空");
            }
            UserExample example = new UserExample();
            example.createCriteria().andIdEqualTo(openUserRoleRequest.getUserId());
            List<User> users = userMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(users)){
                throw new BaseException("用户不存在,用户id为:"+openUserRoleRequest.getUserId());
            }
            List<String> roleIdList = Arrays.stream(openUserRoleRequest.getRoles().split(",")).collect(Collectors.toList());
            for (String roleId : roleIdList) {
                RoleExample roleExample = new RoleExample();
                roleExample.createCriteria().andIdEqualTo(roleId);
                List<Role> roles = roleMapper.selectByExample(roleExample);
                if (CollectionUtils.isEmpty(roles)){
                    throw new BaseException("角色不存在,角色id为:"+roleId);
                }
            }
        }
        //判断完,先删除
        UserRoleMapExample userRoleMapExample = new UserRoleMapExample();
        userRoleMapExample.createCriteria().andUpdateUserEqualTo(request.getSource());
        userRoleMapMapper.deleteByExample(userRoleMapExample);
        //后插入
        for (OpenUserRoleRequest openUserRoleRequest : selfList) {
            List<String> roleIdList = Arrays.stream(openUserRoleRequest.getRoles().split(",")).collect(Collectors.toList());
            openPermissionBizMapper.insertUserRole(openUserRoleRequest.getUserId(), roleIdList, request.getSource());
        }
        return "创建成功";
    }

    public Map<String, List<OpenRoleResponse>> listRoleByRouterId(String id) {
        if (StringUtils.isEmpty(id.trim())) {
            throw new BaseException("id为空");
        }
        List<OpenRoleResponse> list = openPermissionBizMapper.queryRoleListByRouterId(id);
        Map<String, List<OpenRoleResponse>> map = list.stream().collect(Collectors.groupingBy(OpenRoleResponse::getUpdateUser));
        return map;
    }
}
