<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.openapi.dao.OpenOrgMapper">
    <resultMap id="BaseResultMap" type="com.stock.service.platform.openapi.request.OpenOrgRequest">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="p_org_id" jdbcType="VARCHAR" property="pOrgId"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="org_type" jdbcType="VARCHAR" property="orgType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="threshold_flag" jdbcType="VARCHAR" property="thresholdFlag"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , company_id, org_code, org_name, p_org_id, order_id, level, org_type, create_user, create_time,
    update_user, update_time, status, threshold_flag
    </sql>
    <select id="selectByExample" parameterType="com.stock.service.platform.common.entity.OrgExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sa_org
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sa_org
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.stock.service.platform.openapi.request.OpenOrgRequest">
        delete
        from sa_org
        where id = #{id,jdbcType=VARCHAR}
          and source = #{source,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByExample" parameterType="com.stock.service.platform.common.entity.OrgExample">
        delete from sa_org
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.stock.service.platform.common.entity.Org">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_org (id, company_id, org_code,
        org_name, p_org_id, order_id, level
        org_type, create_user, create_time,
        update_user, update_time, status, threshold_flag
        )
        values (#{id,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{orgCode,jdbcType=VARCHAR},
        #{orgName,jdbcType=VARCHAR}, #{pOrgId,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, #{level,jdbcType=VARCHAR},
        #{orgType,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR},
        #{thresholdFlag,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.stock.service.platform.openapi.request.OpenOrgRequest">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="companyId != null and companyId != '' ">
                company_id,
            </if>
            <if test="orgCode != null and orgCode != '' ">
                org_code,
            </if>
            <if test="orgName != null and orgName != '' ">
                org_name,
            </if>
            <if test="pOrgId != null and pOrgId != '' ">
                p_org_id,
            </if>
            <if test="orgType != null and orgType != '' ">
                org_type,
            </if>
            status,
            <if test="source != null and source != '' ">
                source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="companyId != null and companyId != '' ">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null and orgCode != '' ">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != '' ">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="pOrgId != null and pOrgId != '' ">
                #{pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null and orgType != '' ">
                #{orgType,jdbcType=VARCHAR},
            </if>
            '1',
            <if test="source != null and source != '' ">
                #{source,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.stock.service.platform.common.entity.OrgExample"
            resultType="java.lang.Long">
        select count(*) from sa_org
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update sa_org
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.companyId != null">
                company_id = #{record.companyId,jdbcType=VARCHAR},
            </if>
            <if test="record.orgCode != null">
                org_code = #{record.orgCode,jdbcType=VARCHAR},
            </if>
            <if test="record.orgName != null">
                org_name = #{record.orgName,jdbcType=VARCHAR},
            </if>
            <if test="record.pOrgId != null">
                p_org_id = #{record.pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="record.orderId != null">
                order_id = #{record.orderId,jdbcType=INTEGER},
            </if>
            <if test="record.level != null">
                org_type = #{record.level,jdbcType=VARCHAR},
            </if>
            <if test="record.orgType != null">
                org_type = #{record.orgType,jdbcType=VARCHAR},
            </if>
            <if test="record.createUser != null">
                create_user = #{record.createUser,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateUser != null">
                update_user = #{record.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.thresholdFlag != null">
                threshold_flag = #{record.thresholdFlag,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update sa_org
        set id = #{record.id,jdbcType=VARCHAR},
        company_id = #{record.companyId,jdbcType=VARCHAR},
        org_code = #{record.orgCode,jdbcType=VARCHAR},
        org_name = #{record.orgName,jdbcType=VARCHAR},
        p_org_id = #{record.pOrgId,jdbcType=VARCHAR},
        order_id = #{record.orderId,jdbcType=INTEGER},
        level = #{record.level,jdbcType=VARCHAR},
        org_type = #{record.orgType,jdbcType=VARCHAR},
        create_user = #{record.createUser,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_user = #{record.updateUser,jdbcType=VARCHAR},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        status = #{record.status,jdbcType=VARCHAR},
        threshold_flag = #{record.thresholdFlag,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stock.service.platform.openapi.request.OpenOrgRequest">
        update sa_org
        <set>
            <if test="companyId != null and companyId != '' ">
                company_id = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null and orgCode != '' ">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != '' ">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="pOrgId != null and pOrgId != '' ">
                p_org_id = #{pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null and orgType != '' ">
                org_type = #{orgType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR} and source = #{source,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.common.entity.Org">
        update sa_org
        set company_id     = #{companyId,jdbcType=VARCHAR},
            org_code       = #{orgCode,jdbcType=VARCHAR},
            org_name       = #{orgName,jdbcType=VARCHAR},
            p_org_id       = #{pOrgId,jdbcType=VARCHAR},
            order_id       = #{orderId,jdbcType=INTEGER},
            level          = #{level,jdbcType=VARCHAR},
            org_type       = #{orgType,jdbcType=VARCHAR},
            create_user    = #{createUser,jdbcType=VARCHAR},
            create_time    = #{createTime,jdbcType=TIMESTAMP},
            update_user    = #{updateUser,jdbcType=VARCHAR},
            update_time    = #{updateTime,jdbcType=TIMESTAMP},
            status         = #{status,jdbcType=VARCHAR},
            threshold_flag = #{thresholdFlag,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <select id="selectByExampleWithRowbounds" parameterType="com.stock.service.platform.common.entity.OrgExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sa_org
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectOrgDictByIsMgr" resultType="java.util.Map" parameterType="java.util.Map">
        select
        so.id as `key`,
        so.id as `value`,
        so.org_name as text
        from sa_org so
        left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
        where so.status = '1'
        <if test="params.pId != null and params.pId != ''">
            and (so.p_org_id = #{params.pId} or so.id = #{params.pId}
            <if test="params.limitCodeStr != null and params.limitCodeStr != ''">
                or pso.id = #{params.limitCodeStr} or pso.p_org_id = #{params.limitCodeStr}
            </if>
            )
        </if>
        <if test="params.orgType != null and params.orgType != ''">
            and so.org_type = #{params.orgType}
        </if>
    </select>

    <select id="selectOrgIdByBranch" resultType="java.lang.String" parameterType="java.lang.String">
        select group_concat(so.id)
        from sa_org so
                 left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
        where so.status = '1'
          and so.org_type in ('2', '3', '4')
          and (so.id = #{orgId} or pso.id = #{orgId} or pso.p_org_id = #{orgId})
    </select>
    <select id="selectOrgIdByBranchBesb" resultType="java.lang.String" parameterType="java.lang.String">
        select group_concat(so.id)
        from sa_org so
                 left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
        where so.status = '1'
          and so.org_type in ('3', '4')
          and (pso.id = #{orgId} or pso.p_org_id = #{orgId})
    </select>
    <select id="selectOrgIdByDept" resultType="java.lang.String" parameterType="java.lang.String">
        select group_concat(so.id)
        from sa_org so
        where so.status = '1'
          and so.org_type in ('3', '4')
          and (so.id = #{orgId} or so.p_org_id = #{orgId})
    </select>
    <select id="checkBranchIfShare" resultType="java.lang.String" parameterType="java.lang.String">
        select pso.id
        from sa_org so
                 left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
        where so.id = #{orgId}
          and pso.id is not null
          and pso.threshold_flag = '1'
    </select>
    <select id="selectByCompanyId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select org.*
        from sa_org org,
             sa_company company
        where org.company_id = company.id
          and company.id = #{companyId}
    </select>
    <select id="selectByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT org.*
        FROM sa_org org
        WHERE org.id = (SELECT org_id FROM sa_person WHERE user_id = #{userId})
    </select>
    <select id="queryOrg" resultType="com.stock.service.platform.openapi.response.OpenOrgResponse">
        SELECT id,company_id,org_code,org_name,p_org_id,org_type,status,IFNULL(source,'plf') source
        FROM sa_org
        <where>
            <if test="id !=null and id !=''">
                id=#{id}
            </if>
            <if test="companyId !=null and companyId !=''">
                and company_id=#{companyId}
            </if>
            <if test="orgCode !=null and orgCode !=''">
                and org_code=#{orgCode}
            </if>
            <if test="orgName !=null and orgName !=''">
                and org_name = #{orgName}
            </if>
            <if test="pOrgId !=null and pOrgId !=''">
                and p_org_id=#{pOrgId}
            </if>
            <if test="orgType !=null and orgType !=''">
                and org_type=#{orgType}
            </if>
        </where>
    </select>
    <select id="checkOrgCode" resultType="java.lang.Integer">
        select count(*) from sa_org
        where org_code = #{orgCode}
        <if test="id != null">
            and id=#{id}
        </if>
    </select>
    <select id="checkOrgType" resultType="java.lang.String">
        select org_type
        from sa_org
        where id = #{pOrgId}
    </select>
</mapper>