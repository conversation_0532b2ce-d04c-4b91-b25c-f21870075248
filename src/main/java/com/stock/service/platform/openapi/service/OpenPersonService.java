package com.stock.service.platform.openapi.service;

import com.stock.core.exception.BaseException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.openapi.dao.OpenPersonBizMapper;
import com.stock.service.platform.openapi.request.OpenPersonRequest;
import com.stock.service.platform.openapi.request.OpenUserRequest;
import com.stock.service.platform.openapi.response.OpenPersonResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员业务类
 */
@Service
public class OpenPersonService extends BaseService {

    @Resource
    OpenPersonBizMapper openPersonBizMapper;

    @Autowired
    OpenPermissionService openPermissionService;


    public Map<String, List<OpenPersonResponse>> listPersonInfoService(OpenPersonRequest request) throws BaseException {
        List<OpenPersonResponse> list = openPersonBizMapper.listPersonInfo(request);
        list.removeIf(openPersonResponse -> "1".equals(openPersonResponse.getId()));
        Map<String, List<OpenPersonResponse>> map = list.stream().collect(Collectors.groupingBy(OpenPersonResponse::getSource));
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public String setPersonInfoList(OpenPersonRequest request) throws BaseException {
        //清空sa_person  sa_user 表
        openPersonBizMapper.deleteBySource(request.getSource());
        for (OpenPersonRequest dto : request.getSelfList()) {
            // 检查参数
            if (StringUtils.isEmpty(dto.getOrgId().trim())) {
                throw new BaseException("机构id为空");
            }
            if (dto.getMail() != null && checkMail(dto.getMail(), null)) {
                throw new BaseException("邮箱:"+dto.getMail()+"已存在");
            }
            if (dto.getTelephone() != null && checkTelephone(dto.getTelephone(), null)) {
                throw new BaseException("手机号:"+dto.getTelephone()+"已存在");
            }
            if (dto.getUserName() != null && checkUserName(dto.getUserName(), null)) {
                throw new BaseException("用户名:"+dto.getUserName()+"已存在");
            }
        }
        //再插入新数据
        openPersonBizMapper.setPersonInfoList(request.getSelfList(),request.getSource());
        for (OpenPersonRequest dto : request.getSelfList()) {
        //插入sa_user表 给人员设置用户
        String id = openPersonBizMapper.selectIdByTelephone(dto.getTelephone());
        OpenUserRequest userDto = new OpenUserRequest();
        userDto.setId(id);
        userDto.setUserName(dto.getUserName());
        userDto.setSource(dto.getSource());
        openPermissionService.setUser(userDto);
        }
        //删除sa_user_role_map中sa_user中没有的角色对应数据
        openPersonBizMapper.deleteUserRole();
        return "插入成功";
    }

    /**
     * 编辑用户信息（新增/修改）
     *
     * @param request
     * @return String
     */
    @Transactional(rollbackFor = Exception.class)
    public String editPersonInfo(OpenPersonRequest request) throws BaseException {
        // 没有id就新增
        if (StringUtils.isBlank(request.getId())) {
            // 检查参数
            if (StringUtils.isEmpty(request.getOrgId().trim())) {
                throw new BaseException("机构id为空");
            }
            if (request.getMail() != null && checkMail(request.getMail(), null)) {
                throw new BaseException("邮箱:"+request.getMail()+"已存在");
            }
            if (request.getTelephone() != null && checkTelephone(request.getTelephone(), null)) {
                throw new BaseException("手机号:"+request.getTelephone()+"已存在");
            }
            if (request.getUserName() != null && checkUserName(request.getUserName(), null)) {
                throw new BaseException("用户名:"+request.getUserName()+"已存在");
            }
            openPersonBizMapper.insert(request);
            //插入sa_user表 给人员设置用户
            String id = openPersonBizMapper.selectIdByTelephone(request.getTelephone());
            OpenUserRequest userDto = new OpenUserRequest();
            userDto.setId(id);
            userDto.setUserName(request.getUserName());
            userDto.setSource(request.getSource());
            openPermissionService.setUser(userDto);
            return "创建成功";
        } else {
            if (request.getMail() != null && !checkMail(request.getMail(), request.getId())) {
                if (checkMail(request.getMail(), null)) {
                    throw new BaseException("邮箱:"+request.getMail()+"已存在");
                }
            }
            if (request.getTelephone() != null && !checkTelephone(request.getTelephone(), request.getId())) {
                if (checkTelephone(request.getTelephone(), null)) {
                    throw new BaseException("手机号:"+request.getTelephone()+"已存在");
                }
            }
            if (request.getUserName() != null && checkUserName(request.getUserName(), request.getId())) {
                if (checkUserName(request.getUserName(), null)) {
                    throw new BaseException("用户名:"+request.getUserName()+"已存在");
                }
            }
            int i = openPersonBizMapper.updateByPrimaryKeySelective(request);
            if (i < 1) {
                OpenPersonResponse openPersonResponse = openPersonBizMapper.updateCheck(request);
                return "修改错误,修改失败人员为:" + openPersonResponse.toString();
            }
            if (StringUtils.isNotEmpty(request.getUserName())) {
                //更新sa_user表
                openPersonBizMapper.updateUser(request);
            }
            return "修改成功";
        }

    }

    /**
     * 删除人员id
     * 删除人员所绑定的用户信息
     *
     * @param request
     * @return String
     */
    @Transactional(rollbackFor = Exception.class)
    public String deletePersonByIds(OpenPersonRequest request) {
        String personIds = request.getPersonIds();
        List<String> splist = Arrays.stream(personIds.split(",")).collect(Collectors.toList());

        int i = openPersonBizMapper.deleteByPrimaryKey(splist, request.getSource());
        //删除sa_user相关信息
        openPersonBizMapper.deleteUserByPrimaryKey(splist, request.getSource());
        //删除sa_user_role_map相关信息
        openPersonBizMapper.deleteUserRoleByPrimaryKey(splist, request.getSource());
        if (i < splist.size()) {
            OpenPersonResponse openPersonResponse = openPersonBizMapper.deleteCheck(splist);
            return "删除错误,删除失败人员为:" + openPersonResponse.toString();
        }

        return "删除成功";
    }

    /**
     * 检查手机号
     *
     * @param telephone id
     */
    private Boolean checkTelephone(String telephone, String id) {
        if (StringUtils.isEmpty(telephone.trim())){
            throw new BaseException("手机号为空");
        }
        // 判断手机号是否存在
        return openPersonBizMapper.selectByTelephone(telephone, id) > 0;
    }

    /**
     * 检查邮箱
     *
     * @param mail id
     */
    private Boolean checkMail(String mail, String id) {
        return openPersonBizMapper.selectByMail(mail, id) > 0;
    }


    /**
     * 检查用户名
     *
     * @param userName id
     */
    private Boolean checkUserName(String userName, String id) {
        if (StringUtils.isEmpty(userName.trim())){
            throw new BaseException("用户名为空");
        }
        return openPersonBizMapper.checkUserName(userName, id) > 0;
    }
}
