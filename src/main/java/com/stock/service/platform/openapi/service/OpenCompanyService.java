package com.stock.service.platform.openapi.service;

import com.stock.core.exception.BaseException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.constant.CompanyConstant;
import com.stock.service.platform.openapi.dao.OpenCompanyMapper;
import com.stock.service.platform.openapi.request.OpenCompanyRequest;
import com.stock.service.platform.openapi.response.OpenCompanyResponse;
import com.stock.service.platform.openapi.response.OpenOrgResponse;
import com.stock.service.platform.openapi.response.OpenUserResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OpenCompanyService extends BaseService {

    @Autowired
    private OpenCompanyMapper openCompanyMapper;


    public Map<String, List<OpenCompanyResponse>> queryCompany(OpenCompanyRequest dto) {
        List<OpenCompanyResponse> list = openCompanyMapper.queryCompany(dto);
        Map<String, List<OpenCompanyResponse>> map = list.stream().collect(Collectors.groupingBy(OpenCompanyResponse::getSource));
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public String createOrUpdate(OpenCompanyRequest dto) throws BaseException {
        if (StringUtils.isBlank(dto.getId())) {
            checkCompanyParam(dto);//检查参数是否为空
            if (CollectionUtils.isEmpty(openCompanyMapper.selectOldCompany(dto))) {//检查公司是否存在
                openCompanyMapper.insertSelective(dto);
                // org表
                OpenOrgResponse record = new OpenOrgResponse();
                record.setCompanyId(dto.getId());
                record.setOrgName(dto.getZhName());
                record.setSource(dto.getSource());
                record.setOrgType("4");
                openCompanyMapper.insertOrgSelective(record);
                return "新增上市公司成功";
            } else {
                throw new BaseException("上市公司已存在");
            }
        } else {
            if (CompanyConstant.SYSTEM_COMPANY_ID.equals(dto.getId())) {
                throw new BaseException("基础公司不可修改");
            } else {
                int i = openCompanyMapper.updateByPrimaryKeySelective(dto);//检查更新个数
                if (i<1){
                    OpenCompanyResponse openCompanyResponse=openCompanyMapper.checkUpdateOrDelete(dto);
                    return "更新上市公司错误,未更新成功公司为"+openCompanyResponse.toString();
                }
                OpenOrgResponse record = new OpenOrgResponse();
                record.setSource(dto.getSource());
                record.setCompanyId(dto.getId());
                record.setpOrgId(dto.getOrganisationNo());
                record.setOrgName(dto.getZhName());
                openCompanyMapper.updateOrgByPrimaryKeySelective(record);
            }
            return "更新上市公司成功";
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public String deleteCompanyById(OpenCompanyRequest dto) throws BaseException {
        if (StringUtils.isEmpty(dto.getId())) {
            throw new BaseException("公司id为空");
        }
        //查询上市公司下是否已经配置人员
        List<OpenUserResponse> userList = openCompanyMapper.queryCompanyUsers(dto.getId());
        if (CollectionUtils.isEmpty(userList)) {
            int i = openCompanyMapper.deleteByPrimaryKeys(dto);//检查删除个数
            if (i<1){
                OpenCompanyResponse openCompanyResponse=openCompanyMapper.checkUpdateOrDelete(dto);
                throw new BaseException("删除上市公司错误,未删除成功公司为"+openCompanyResponse.toString());
            }
           //删除sa_org表
            openCompanyMapper.deleteSaOrgByPrimaryKeys(dto);
            return "删除成功";
        } else {
            throw new BaseException("公司:"+dto.getCompanyCode()+"已存在所属用户,请先进行迁移再删除");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String setCompanyList(OpenCompanyRequest request) throws BaseException{
        List<OpenCompanyRequest> selfList = request.getSelfList();
        // 检查上市公司代码是否有重复
        List<String> collect;
        List<String> companyCodeCollect = selfList.stream().map(OpenCompanyRequest::getCompanyCode).collect(Collectors.toList());
        collect=companyCodeCollect.stream().distinct().collect(Collectors.toList());
        if (collect.size()<companyCodeCollect.size()){
            throw new BaseException("公司代码参数有重复,请重新填写");
        }
        for (OpenCompanyRequest dto : selfList) {
            // 检查上市公司代码是否存在
            if (dto.getCompanyCode() != null && CollectionUtils.isNotEmpty(openCompanyMapper.selectOldCompany(dto))) {
                throw new BaseException("上市公司:"+dto.getCompanyCode()+"已存在");
            }
        }
        for (OpenCompanyRequest dto : selfList) {
            checkCompanyParam(dto);//检查参数是否为空
            dto.setSource(request.getSource());
            //插入
            openCompanyMapper.insertSelective(dto);
            // org表
            OpenOrgResponse record = new OpenOrgResponse();
            record.setCompanyId(dto.getId());
            record.setOrgName(dto.getZhName());
            record.setSource(dto.getSource());
            record.setOrgType("4");
            openCompanyMapper.insertOrgSelective(record);
        }
        return "新增上市公司成功";
    }
    public void checkCompanyParam(OpenCompanyRequest dto) throws BaseException{
        if (StringUtils.isEmpty(dto.getCompanyCode().trim())){
            throw new BaseException("证券代码参数为空");
        }
        if (StringUtils.isEmpty(dto.getZhName().trim())){
            throw new BaseException("公司中文名称参数为空");
        }
        if (StringUtils.isEmpty(dto.getOrganisationNo().trim())){
            throw new BaseException("组织机构代码参数为空");
        }
        if (StringUtils.isEmpty(dto.getZhSortName().trim())){
            throw new BaseException("公司简称参数为空");
        }
        if (StringUtils.isEmpty(dto.getCompanyStockName().trim())){
            throw new BaseException("证券名称参数为空");
        }
        if (StringUtils.isEmpty(dto.getMarketStockType().trim())){
            throw new BaseException("市场参数为空");
        }
    }
}

