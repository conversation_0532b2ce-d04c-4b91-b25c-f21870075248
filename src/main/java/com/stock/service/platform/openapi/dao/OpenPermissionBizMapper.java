package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.openapi.request.OpenRoleRequest;
import com.stock.service.platform.openapi.request.OpenUserRequest;
import com.stock.service.platform.openapi.response.OpenPermissionResponse;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import com.stock.service.platform.openapi.response.OpenUserRoleResponse;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OpenPermissionBizMapper {
    List<OpenPermissionResponse> queryRouterListByRoleId(String roleId);

    int createRoleRouter(String roleId,List<String> splist,String source);

    int checkRoleRouter(String roleId,String routerId);

    int selectByPrimaryKey(String userId);

    int selectByUserName(String userName);

    int insertUser(OpenUserRequest userDto);

    int checkUserRole(String userId, List<String> splist);

    int insertUserRole(String userId, List<String> splist, String source);

    int deleteUserRole(String userId, List<String> splist, String source);

    List<OpenUserRoleResponse> selectUserRole(String userId, List<String> splist);

    List<OpenUserRoleResponse> selectUserRolesByUserId(OpenUserRequest dto);

    int deleteRoleRouter(String roleId, List<String> splist, String source);

    List<OpenPermissionResponse> selectRoleRouter(String roleId, List<String> splist);

    List<OpenUserRequest> getRoleOfUsersByRoleId(OpenRoleRequest dto);

    List<OpenRoleResponse> queryRoleListByRouterId(String id);
}
