package com.stock.service.platform.openapi.service;

import com.stock.core.dao.RedisDao;
import com.stock.core.dto.UserInfo;
import com.stock.core.exception.AuthenticationException;
import com.stock.core.security.token.AccessTokenStoreStrategy;
import com.stock.service.platform.openapi.constant.OpenAPIConstant;
import com.stock.service.platform.openapi.response.OpenLoginResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpenCommonService {
    @Autowired
    private RedisDao redisDao;

    @Autowired
    private AccessTokenStoreStrategy accessTokenStoreStrategy;

    private OpenLoginResponse getResponseByToken(String token) {
        OpenLoginResponse result = new OpenLoginResponse();
        UserInfo info = this.accessTokenStoreStrategy.getAccessTokenInfo(token);
        if(info != null && StringUtils.isEmpty(info.getUsername())) {
            result.setUsername(info.getUsername());
            result.setPhone(info.getTelephone());
            return result;
        }
        return null;
    }

    public OpenLoginResponse validateCode(String code) {
        if(StringUtils.isEmpty(code)) {
            throw new AuthenticationException("error.undefined", "未获取到code");
        }
        Object obj = redisDao.getObject(code + OpenAPIConstant.LOGIN_CODE_SUFFIX);
        if (obj == null) {
            throw new AuthenticationException("error.undefined", "code过期或失效");
        }
        String token = String.valueOf(obj);
        OpenLoginResponse result = this.getResponseByToken(token);
        if (result == null) {
            throw new AuthenticationException("error.undefined", "token过期或失效");
        }
        // 临时码用完删除
        redisDao.delObject(code + OpenAPIConstant.LOGIN_CODE_SUFFIX);
        return result;
    }

    public OpenLoginResponse validateToken(String token) {
        if(StringUtils.isEmpty(token)) {
            throw new AuthenticationException("error.undefined", "未获取到token");
        }
        OpenLoginResponse result = this.getResponseByToken(token);
        if (result == null) {
            throw new AuthenticationException("error.undefined", "token过期或失效");
        }
        return result;
    }
}
