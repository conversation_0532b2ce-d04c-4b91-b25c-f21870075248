package com.stock.service.platform.openapi.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 人员信息
 */
@ApiModel(value = "人员dto",description = "人员dto")
public class OpenPersonRequest implements Serializable {
    private String userId;//对应系统用户id
    private String id;
    private String companyCode;
    private List<OpenPersonRequest> selfList;

    public List<OpenPersonRequest> getSelfList() {
        return selfList;
    }

    public void setSelfList(List<OpenPersonRequest> selfList) {
        this.selfList = selfList;
    }

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id",example = "1",notes = "更新时必填")
    @NotBlank(message = "人员id为空", groups = {UpdatePersonGroup.class,QueryPersonInfoGroup.class})
    private String personId;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别",example = "1")
    private String sex;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id",example = "01")
    private String orgId;

    /**
     * 认知记录ID
     */
    @ApiModelProperty(value = "认知记录ID",example = "01")
    private String resumeId;

    /**
     * 搜索字符串
     */
    @ApiModelProperty(value = "搜索字符串（姓名，手机等）",example = "张三")
    @Length(min = 0,max = 30 ,message = "搜索字符串过长")
    private String searchString;

    /**
     * 用户锁状态
     */
    @ApiModelProperty(value = "用户锁定状态",example = "1")
    @NotBlank(message = "更新锁定状态为空",groups = {UpdateUserLockStatusGroup.class})
    private String userLockStatus;

    /**
     * 用户可用状态
     */
    @ApiModelProperty(value = "用户可用状态",example = "1")
    @NotBlank(message = "更新可用状态为空",groups = {UpdateUserAvailableStatusGroup.class})
    private String userAvailableStatus;

    /**
     * 人员名
     */
    @ApiModelProperty(value = "人员名字",example = "张三")
    @NotBlank(message = "人员名字为空", groups = {InsertPersonGroup.class, UpdatePersonGroup.class})
    private String personName;

    /**
     * 用户名字
     */
    @ApiModelProperty(value = "用户名字",example = "zhang.san")
    @NotBlank(message = "用户名为空",groups = {UpdateUserAvailableStatusGroup.class,UpdateUserLockStatusGroup.class})
    @Length(message = "用户名过长",groups = {UpdateUserAvailableStatusGroup.class,UpdateUserLockStatusGroup.class},min = 1,max = 20)
    private String userName;

    /**
     * 人员手机
     */
    @ApiModelProperty(value = "人员手机号",example = "13000000000")
    @NotBlank(message = "手机号为空",groups = SearchTelephoneGroup.class)
    @Length(message = "手机号过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 20)
    private String telephone;

    /**
     * 机构名字
     */
    @ApiModelProperty(value = "机构名字",example = "董事会")
    private String orgNames;

    /**
     * 身份名字
     */
    @ApiModelProperty(value = "身份名字",example = "董事长")
    private String identityNames;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源 0-新增，1-OA",example = "1")
    private String source;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注",example = "这是备注")
    @Length(message = "备注过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 30)
    private String remark;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱",example = "<EMAIL>")
    @NotBlank(message = "邮箱为空",groups = SearchMailGroup.class)
    @Length(message = "邮箱过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 64)
    private String mail;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址",example = "<EMAIL>")
    @Length(message = "头像地址过长",groups = {InsertPersonGroup.class,UpdatePersonGroup.class},min = 0,max = 100)
    private String pictureUrl;

    /**
     * 限制机构权限
     */
    @ApiModelProperty(value = "限制机构权限")
    private String orgLimitList;


    /**
     * 人员id列表
     */
    @ApiModelProperty(value = "人员id列表")
    @NotNull
    private String personIds;

    /**
     * 人员id列表
     */
    @ApiModelProperty(value = "人员idSeriakey列表")
    private List<Map<String,String>> personIdSeriakeys;

    @ApiModelProperty(value = "机构类别")
    private String orgType;

    @ApiModelProperty(value = "内部/外部账号")
    private String userType;

    @ApiModelProperty(value = "所属机构类别")
    private String isMgr;

    @ApiModelProperty(value = "无用字段")
    private String seriakey;

    private String status; //人员状态

    @Override
    public String toString() {
        return "OpenPersonRequest{" +
                "userId='" + userId + '\'' +
                ", id='" + id + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", personId='" + personId + '\'' +
                ", sex='" + sex + '\'' +
                ", orgId='" + orgId + '\'' +
                ", resumeId='" + resumeId + '\'' +
                ", searchString='" + searchString + '\'' +
                ", userLockStatus='" + userLockStatus + '\'' +
                ", userAvailableStatus='" + userAvailableStatus + '\'' +
                ", personName='" + personName + '\'' +
                ", userName='" + userName + '\'' +
                ", telephone='" + telephone + '\'' +
                ", orgNames='" + orgNames + '\'' +
                ", identityNames='" + identityNames + '\'' +
                ", source='" + source + '\'' +
                ", remark='" + remark + '\'' +
                ", mail='" + mail + '\'' +
                ", pictureUrl='" + pictureUrl + '\'' +
                ", orgLimitList='" + orgLimitList + '\'' +
                ", personIds='" + personIds + '\'' +
                ", personIdSeriakeys=" + personIdSeriakeys +
                ", orgType='" + orgType + '\'' +
                ", userType='" + userType + '\'' +
                ", isMgr='" + isMgr + '\'' +
                ", seriakey='" + seriakey + '\'' +
                ", status='" + status + '\'' +
                '}';
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }


    public List<Map<String, String>> getPersonIdSeriakeys() {
        return personIdSeriakeys;
    }

    public void setPersonIdSeriakeys(List<Map<String, String>> personIdSeriakeys) {
        this.personIdSeriakeys = personIdSeriakeys;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSearchString() {
        return searchString;
    }

    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }

    public String getUserLockStatus() {
        return userLockStatus;
    }

    public void setUserLockStatus(String userLockStatus) {
        this.userLockStatus = userLockStatus;
    }

    public String getUserAvailableStatus() {
        return userAvailableStatus;
    }

    public void setUserAvailableStatus(String userAvailableStatus) {
        this.userAvailableStatus = userAvailableStatus;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        if(StringUtils.isBlank(telephone)){
            this.telephone = null;
            return;
        }
        this.telephone = telephone;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getIdentityNames() {
        return identityNames;
    }

    public void setIdentityNames(String identityNames) {
        this.identityNames = identityNames;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        if(StringUtils.isBlank(mail)){
            this.mail = null;
            return;
        }
        this.mail = mail;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonIds() {
        return personIds;
    }

    public void setPersonIds(String personIds) {
        this.personIds = personIds;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    /*public String getSourceString() {
        return sourceString;
    }

    public void setSourceString(String sourceString) {
        this.sourceString = sourceString;
    }

    public String getUserAvailableStatusString() {
        return userAvailableStatusString;
    }

    public void setUserAvailableStatusString(String userAvailableStatusString) {
        this.userAvailableStatusString = userAvailableStatusString;
    }

    public String getUserLockStatusString() {
        return userLockStatusString;
    }

    public void setUserLockStatusString(String userLockStatusString) {
        this.userLockStatusString = userLockStatusString;
    }*/

    /**
     * <AUTHOR>
     * 新增用户校验组
     */
    public static interface InsertPersonGroup {
    }

    /**
     * <AUTHOR>
     * 修改用户校验组
     */
    public static interface UpdatePersonGroup {
    }

    /**
     * <AUTHOR>
     * 修改用户可用状态用户组
     */
    public static interface UpdateUserAvailableStatusGroup{
    }

    /**
     * <AUTHOR>
     * 修改用户锁定状态用户组
     */
    public static interface UpdateUserLockStatusGroup{
    }

    /**
     * <AUTHOR>
     * 查询人员信息组
     */
    public static interface QueryPersonInfoGroup{
    }

    /**
     * <AUTHOR>
     * 删除人员信息组
     */
    public static interface DeletePersonInfoGroup{
    }

    /**
     * <AUTHOR>
     * 查询手机号检验组
     */
    public static interface SearchTelephoneGroup{
    }

    /**
     * <AUTHOR>
     * 查询邮箱校验组
     */
    public static interface SearchMailGroup{
    }

    public String getResumeId() {
        return resumeId;
    }

    public void setResumeId(String resumeId) {
        this.resumeId = resumeId;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getOrgLimitList() {
        return orgLimitList;
    }

    public void setOrgLimitList(String orgLimitList) {
        this.orgLimitList = orgLimitList;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getIsMgr() {
        return isMgr;
    }

    public void setIsMgr(String isMgr) {
        this.isMgr = isMgr;
    }
}
