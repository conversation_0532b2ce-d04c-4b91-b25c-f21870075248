<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.openapi.dao.OpenPersonBizMapper">
    <insert id="insert">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_person (
        id,
        user_id,
        <if test="personName !=null">
            person_name,
        </if>
        <if test="orgId !=null">
            org_id,
        </if>
        <if test="telephone !=null">
            telephone,
        </if>
        <if test="mail !=null">
            mail,
        </if>
        <if test="source !=null">
            source,
        </if>
        status
        )
        values (
        #{id,jdbcType=VARCHAR},
        #{id,jdbcType=VARCHAR},
        <if test="personName !=null">
            #{personName,jdbcType=VARCHAR},
        </if>
        <if test="orgId !=null">
            #{orgId,jdbcType=VARCHAR},
        </if>
        <if test="telephone !=null">
            #{telephone,jdbcType=VARCHAR},
        </if>
        <if test="mail !=null">
            #{mail,jdbcType=VARCHAR},
        </if>
        <if test="source !=null">
            #{source,jdbcType=VARCHAR},
        </if>
        '1')
    </insert>

    <insert id="setPersonInfoList">
        <foreach collection="request" item="item" separator=";">
            SELECT @custom_uuid := CONCAT(UUID_SHORT(),'');
            insert into sa_person(
            id,user_id,
            <if test="item.personName !=null">
                person_name,
            </if>
            <if test="item.orgId !=null">
                org_id,
            </if>
            <if test="item.telephone !=null">
                telephone,
            </if>
            <if test="item.mail !=null">
                mail,
            </if>
            source,status)
            values (
            @custom_uuid,@custom_uuid,
            <if test="item.personName !=null">
                #{item.personName,jdbcType=VARCHAR},
            </if>
            <if test="item.orgId !=null">
                #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.telephone !=null">
                #{item.telephone,jdbcType=VARCHAR},
            </if>
            <if test="item.mail !=null">
                #{item.mail,jdbcType=VARCHAR},
            </if>
            #{source,jdbcType=VARCHAR},'1')
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update sa_person
        <set>
            <if test="personName != null">
                person_name = #{personName,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=VARCHAR},
            </if>
            <if test="telephone != null">
                telephone = #{telephone,jdbcType=VARCHAR},
            </if>
            <if test="mail != null">
                mail = #{mail,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
        <if test="source != null">
            and source = #{source,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="updateUser">
        update sa_user
        <set>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="personName != null">
                real_name = #{personName},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR} and update_user=#{source,jdbcType=VARCHAR}
    </update>


    <delete id="deleteByPrimaryKey">
        delete from sa_person
        where id in
        <foreach collection="personIdStr" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            and source = #{source}
        </if>
    </delete>
    <delete id="deleteUserByPrimaryKey">
        delete from sa_user
        where id in
        <foreach collection="personIdStr" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            and update_user = #{source}
        </if>
    </delete>
    <delete id="deleteUserRoleByPrimaryKey">
        delete from sa_user_role_map
        where user_id in
        <foreach collection="personIdStr" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            and update_user = #{source}
        </if>
    </delete>
    <delete id="deleteBySource">
        delete from sa_person where source = #{source};
        delete from sa_user where update_user = #{source};
    </delete>
    <delete id="deleteUserRole">
        DELETE from sa_user_role_map WHERE user_id NOT in (SELECT id  from sa_user)
    </delete>
    <select id="deleteCheck" parameterType="java.lang.String"
            resultType="com.stock.service.platform.openapi.response.OpenPersonResponse">
        select id, person_name, source
        from sa_person
        where id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>
    <select id="updateCheck" parameterType="com.stock.service.platform.openapi.request.OpenPersonRequest"
            resultType="com.stock.service.platform.openapi.response.OpenPersonResponse">
        select id, person_name, source
        from sa_person
        where id = #{id}

    </select>
    <select id="listPersonInfo" parameterType="com.stock.service.platform.openapi.request.OpenPersonRequest"
            resultType="com.stock.service.platform.openapi.response.OpenPersonResponse">
        SELECT
        p.id as id,
        p.user_id as userId,
        p.person_name as personName,
        so.org_name as orgNames,
        u.user_name as userName,
        p.telephone as telephone,
        u.lock_state as userLockStatus,
        u.status as userAvailableStatus,
        p.org_id as orgId,
        so.org_type as orgType,
        IFNULL(p.source,'plf') as source
        FROM sa_person p
        LEFT JOIN sa_user u on p.user_id = u.id
        LEFT JOIN sa_org so on so.id = p.org_id
        LEFT JOIN sa_company sc on so.company_id = sc.id
        <where>
            p.status != '0'
            <if test="userName != null and userName != ''">
                and u.user_name = #{userName}
            </if>
            <if test="companyCode != null and companyCode != ''">
                and sc.company_code = #{companyCode}
            </if>
            <if test="orgId != null and orgId != ''">
                and p.org_id = #{orgId}
            </if>
            <if test="userLockStatus != null and userLockStatus != ''">
                and u.lock_state = #{userLockStatus}
            </if>
            <if test="userAvailableStatus != null and userAvailableStatus != ''">
                and u.status = #{userAvailableStatus}
            </if>
        </where>

        order by p.create_time desc

    </select>
    <select id="exportUserInfo" resultType="com.stock.service.platform.person.dto.ExcelUserManage">
        SELECT
        p.person_name as personName,
        so.org_name as orgNames,
        u.user_name as userName,
        case when u.lock_state='0' then '未锁定'
        when u.lock_state='1' then '已锁定'
        else '--' end as userLockStatus,
        r.role_name as roleName,
        p.remark as remark,
        case when so.org_type = '1' or so.org_type = '2' or so.org_type = '3' then '券商账号'
        else '上市公司账号' end as isQs
        FROM sa_person p
        LEFT JOIN sa_user u on p.user_id = u.id
        LEFT JOIN sa_org so on so.id = p.org_id
        left join sa_user_role_map m on p.user_id = m.user_id
        left join sa_role r on r.id = m.role_id
        <where>
            p.status != '0'
            <if test="orgId != null and orgId != ''">
                and p.org_id = #{orgId}
            </if>
            <if test="userLockStatus != null and userLockStatus != ''">
                and u.lock_state = #{userLockStatus}
            </if>
            <if test="userAvailableStatus != null and userAvailableStatus != ''">
                and u.status = #{userAvailableStatus}
            </if>
            <if test="searchString != null and searchString != ''">
                and (
                u.user_name like concat('%',#{searchString},'%')
                or
                p.person_name like concat('%',#{searchString},'%')
                or
                p.telephone like concat('%',#{searchString},'%')
                )
            </if>
            <if test="orgLimitList == null or orgLimitList != 'total'.toString()">
                and find_in_set(p.org_id,#{orgLimitList})
            </if>
            <if test="userType != null and userType != ''">
                <choose>
                    <when test="userType == '0'.toString()">
                        and so.org_type in ('1','2','3')
                    </when>
                    <otherwise>
                        and so.org_type = '4'
                    </otherwise>
                </choose>
            </if>
        </where>

        order by p.create_time desc
    </select>


    <select id="getCodeLabelByNo" parameterType="java.util.Map"
            resultType="com.stock.service.platform.common.dto.SelectTreeTagDto">
        select t.code_value labelValue,
        t.code_name labelName,
        t.code_name name,
        t.id id,
        0 pId
        from sa_code t
        where t.code_no = #{labelCode}
        and t.valid_flag = '1'
        <if test="codeValueList != null and codeValueList.size !=0">
            and t.code_value in
            <foreach collection="codeValueList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by t.sort_no
    </select>
    <select id="selectByTelephone" resultType="java.lang.Integer">
        select count(1)
        from sa_person
        where telephone = #{telephone,jdbcType=VARCHAR}
        <if test="id!=null">
            and id=#{id,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectByMail" resultType="java.lang.Integer">
        select count(1)
        from sa_person
        where mail = #{mail,jdbcType=VARCHAR}
        <if test="id!=null">
            and id=#{id,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectIdByTelephone" resultType="java.lang.String">
        select id
        from sa_person
        where telephone = #{telephone,jdbcType=VARCHAR}
    </select>
    <select id="checkUserName" resultType="java.lang.Integer">
        select count(1)
        from sa_user
        where user_name = #{userName,jdbcType=VARCHAR}
        <if test="id!=null">
            and id=#{id,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>