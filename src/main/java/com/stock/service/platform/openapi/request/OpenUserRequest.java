package com.stock.service.platform.openapi.request;

import com.stock.service.platform.user.dto.UserDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 用户信息dto
 */
@ApiModel(value = "用户dto", description = "用户dto")
public class OpenUserRequest implements Serializable {

    /**
     * 用户id-即人员id
     */
    private String id;

    /**
     * 用户名-登录使用
     */
    private String userName;
    private String updateUser;
    private Date updateTime;
    private String source; //来源

    @ApiModelProperty(value = "电话号码")
    private String telephone;

    /**
     * 旧密码
     */
    @NotBlank(message = "旧密码为空", groups = UserDto.ChangePasswordGroup.class)
    @ApiModelProperty(value = "旧密码", notes = "修改密码时使用", example = "bin.han")
    private String oldPassword;

    /**
     * 新密码
     */

    @ApiModelProperty(value = "新密码", notes = "修改密码时使用，设置用户时使用", example = "bin.han")
    private String newPassword;

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 修改密码校验组
     */
    public static interface ChangePasswordGroup {
    }

    /**
     * 设置用户校验组
     */
    public static interface SetUserGroup {
    }

    /**
     * 检查用户名校验组
     */
    public static interface CheckUserNameGroup {
    }

}
