package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.common.dto.SelectTreeTagDto;
import com.stock.service.platform.openapi.request.OpenPersonRequest;
import com.stock.service.platform.openapi.response.OpenPersonResponse;
import com.stock.service.platform.person.dto.ExcelUserManage;
import com.stock.service.platform.person.dto.PersonDto;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 人员业务mapper
 */
public interface OpenPersonBizMapper {

    /**
     * 显示人员信息列表
     * @param queryInfo
     * @return
     */
    List<OpenPersonResponse> listPersonInfo(OpenPersonRequest request);

    List<ExcelUserManage> exportUserInfo(PersonDto personDto);

    List<SelectTreeTagDto> getCodeLabelByNo(Map<String,Object> paramMap);

    int insert(OpenPersonRequest request);

    int selectByTelephone(@Param("telephone")String telephone,@Param("id") String id);

    int selectByMail(@Param("mail")String mail,@Param("id")String id);

    int updateByPrimaryKeySelective(OpenPersonRequest request);

    int deleteByPrimaryKey(List<String> personIdStr,String source);

    int updateUser(OpenPersonRequest request);

    int deleteUserByPrimaryKey(List<String> personIdStr,String source);

    int deleteUserRoleByPrimaryKey(List<String> personIdStr,String source);

    OpenPersonResponse updateCheck(OpenPersonRequest request);

    OpenPersonResponse deleteCheck(List<String> splist);

    String selectIdByTelephone(String telephone);

    int setPersonInfoList(List<OpenPersonRequest> request,String source);

    int deleteBySource(String source);

    int deleteUserRole();

    int checkUserName(String userName, String id);
}
