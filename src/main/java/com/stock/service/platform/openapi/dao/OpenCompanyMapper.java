package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.openapi.request.OpenCompanyRequest;
import com.stock.service.platform.openapi.response.OpenCompanyResponse;
import com.stock.service.platform.openapi.response.OpenOrgResponse;
import com.stock.service.platform.openapi.response.OpenUserResponse;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OpenCompanyMapper {

    List<OpenCompanyResponse> queryCompany(OpenCompanyRequest dto);

    int deleteByPrimaryKeys(OpenCompanyRequest dto);

    int deleteSaOrgByPrimaryKeys(OpenCompanyRequest dto);

    List<OpenCompanyResponse> selectOldCompany(OpenCompanyRequest openCompanyRequest);

    int insertSelective(OpenCompanyRequest insertCompany);

    int updateByPrimaryKeySelective(OpenCompanyRequest updateCompany);

    List<OpenUserResponse> queryCompanyUsers(String id);

    SaCompanyDto getCompanyByCodePlate(String companyCode);

    int insertOrgSelective(OpenOrgResponse record);

    int updateOrgByPrimaryKeySelective(OpenOrgResponse record);

    OpenCompanyResponse checkUpdateOrDelete(OpenCompanyRequest dto);
}