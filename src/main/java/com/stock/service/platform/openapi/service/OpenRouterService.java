package com.stock.service.platform.openapi.service;

import com.stock.core.dto.JsonResponse;
import com.stock.core.service.BaseService;
import com.stock.service.platform.openapi.dao.OpenRouterBizMapper;
import com.stock.service.platform.openapi.request.OpenRouterRequest;
import com.stock.service.platform.openapi.response.OpenRouterResponse;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@Transactional
public class OpenRouterService extends BaseService {
    @Autowired
    OpenRouterBizMapper openRouterMapper;


    public JsonResponse<List<OpenRouterResponse>> queryRoleListService(OpenRouterRequest dto) {
        JsonResponse<List<OpenRouterResponse>> jsonResponse =new JsonResponse<>();
        List<OpenRouterResponse> list = openRouterMapper.queryRouterList(dto);
        jsonResponse.setResult(list);
        return jsonResponse;
    }

    public JsonResponse<Boolean> editRouter(OpenRouterRequest router) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        if (router.getId() == null) {
            if (!routerNameCheck(router)) {
                jsonResponse.setResult(false);
                jsonResponse.setErrorMsg("路由名称已存在");
                return jsonResponse;
            }
            openRouterMapper.insert(router);
        } else {
            openRouterMapper.updateByPrimaryKeySelective(router);
        }
        jsonResponse.setResult(true);
        return jsonResponse;
    }

    public Boolean routerNameCheck(OpenRouterRequest router) {
        int roles = openRouterMapper.selectByName(router);
        return roles < 1;
    }


    public JsonResponse<Boolean> deleteRouter(String idList) {
        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
        if (StringUtils.isNotEmpty(idList)) {
            String[] split = idList.split(",");
            for (String id : split) {
                openRouterMapper.deleteByPrimaryKey(id);
                //删除sa_role_router_map对应数据
                openRouterMapper.deleteByRouterId(id);
            }
            jsonResponse.setResult(true);
            return jsonResponse;
        }else {
            jsonResponse.setResult(false);
            jsonResponse.setErrorMsg("参数不正确");
            return jsonResponse;
        }

    }




}
