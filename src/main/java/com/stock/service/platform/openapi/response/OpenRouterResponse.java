package com.stock.service.platform.openapi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 路由dto
 */
@ApiModel(value = "路由菜单dto", description = "路由菜单dto")
public class OpenRouterResponse implements Serializable {

    private String roleId;

    /**
     * 路由id
     */
    @ApiModelProperty(value = "路由id", example = "1")
    private String id;

    /**
     * 路由夫id，为空就是一级
     */
    @ApiModelProperty(value = "父路由id", example = "1")
    private String pRouterId;

    /**
     * 路由描述
     */
    @ApiModelProperty(value = "路由描述", example = "菜单管理")
    private String routerDes;

    /**
     * 是否拥有权限
     * 0-未拥有，1-拥有
     */
    @ApiModelProperty(value = "是否拥有权限，0-未拥有，1-拥有", example = "0")
    private Boolean hasPermission;

    /**
     * 子路由列表
     */
    @ApiModelProperty(value = "子路由列表", example = "0")
    private List<OpenRouterResponse> routerList;

    private String routerFolderFlag; //是否菜单夹，0是：菜单夹；1否：菜单项。
    private String routerType;
    private String routerName;
    private String routerIcon;
    private String path;
    private String meta;
    private Integer sortNo;
    private String status;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;

    /**
     * 资源id列表
     */
    private List<String> resourceIdList;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRouterFolderFlag() {
        return routerFolderFlag;
    }

    public void setRouterFolderFlag(String routerFolderFlag) {
        this.routerFolderFlag = routerFolderFlag;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getpRouterId() {
        return pRouterId;
    }

    public void setpRouterId(String pRouterId) {
        this.pRouterId = pRouterId;
    }

    public String getRouterDes() {
        return routerDes;
    }

    public void setRouterDes(String routerDes) {
        this.routerDes = routerDes;
    }

    public Boolean getHasPermission() {
        return hasPermission;
    }

    public void setHasPermission(Boolean hasPermission) {
        this.hasPermission = hasPermission;
    }

    public List<OpenRouterResponse> getRouterList() {
        return routerList;
    }

    public void setRouterList(List<OpenRouterResponse> routerList) {
        this.routerList = routerList;
    }

    public String getRouterType() {
        return routerType;
    }

    public void setRouterType(String routerType) {
        this.routerType = routerType;
    }

    public String getRouterName() {
        return routerName;
    }

    public void setRouterName(String routerName) {
        this.routerName = routerName;
    }

    public String getRouterIcon() {
        return routerIcon;
    }

    public void setRouterIcon(String routerIcon) {
        this.routerIcon = routerIcon;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<String> getResourceIdList() {
        return resourceIdList;
    }

    public void setResourceIdList(List<String> resourceIdList) {
        this.resourceIdList = resourceIdList;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }
}
