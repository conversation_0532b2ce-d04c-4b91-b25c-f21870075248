package com.stock.service.platform.openapi.aspect;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.common.constant.SystemConfigConstant;
import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.common.entity.SystemConfigExample;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.SystemConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Aspect
@Component
public class OpenAPIRequestAspect {

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private SystemConfigService systemConfigService;

    private static List<String> IGNORE_METHOD_PATH = new ArrayList<>();

    // Scan path
    @Pointcut("execution(public * com.stock.service.platform.openapi..*Controller.*(..))")
    public void requestCheck() {  }

    @Before("requestCheck()")
    public void doBefore(JoinPoint joinPoint) throws Exception {
        try {
            // Get request content
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request;
            if (attributes != null) {
                request = attributes.getRequest();
                String methodPath = joinPoint.getTarget().getClass().getName() + "." + joinPoint.getSignature().getName();
                if(!IGNORE_METHOD_PATH.contains(methodPath)) {
                    String source = null;
                    Map<String, String[]> paramMap = request.getParameterMap();
                    if(paramMap != null && paramMap.containsKey("source")) {
                        source = paramMap.get("source")[0];
                    }
                    if(StringUtils.isEmpty(source)) {
                        for(Object obj : joinPoint.getArgs()){
                            if(obj != null) {
                                String objStr = JSONObject.toJSONString(obj);
                                Map<String, String> map = JSONObject.parseObject(objStr, Map.class);
                                if(map.containsKey("source")) {
                                    source = map.get("source");
                                    break;
                                }
                            }
                        }
                    }
                    if(StringUtils.isEmpty(source)) { throw new BaseException("未获取到系统来源！"); }
                    // 判断是否为白名单IP
                    boolean flag = false;
                    String IP = CommonService.getIp(request);
                    if(StringUtils.isNotEmpty(IP)) {
                        // 获取允许的IP
                        SystemConfigExample example = new SystemConfigExample();
                        example.createCriteria().andSysCodeEqualTo(source);
                        List<SystemConfig> configs = systemConfigMapper.selectByExampleWithBLOBs(example);
                        if (CollectionUtils.isNotEmpty(configs)) {
                            SystemConfig config = configs.get(0);
                            if(StringUtils.isNotEmpty(config.getSysConfig())) {
                                // 获取当前环境配置
                                Map<String, Object> configMap = systemConfigService.getCurrentEnvironmentConfig(config);
                                String allowIPs = MapUtils.getString(configMap, SystemConfigConstant.SYS_CONFIG_CALL_API_IP_ADDR);
                                if(StringUtils.isNotEmpty(allowIPs)) {
                                    String[] IPArr = allowIPs.split(",");
                                    for(String item: IPArr) { if(item.equals(IP)) { flag = true; break;} }
                                }
                            }
                        }
                    }
                    if(!flag) { throw new BaseException("无权限访问！"); }
                }
            }
        } catch (BaseException e) {
            throw new BaseException("请求失败！，" + e.getMessage());
        }
        catch (Exception e) {
            e.printStackTrace();
            throw new Exception("系统报错：" + e.getMessage());
        }
    }
}
