package com.stock.service.platform.openapi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 角色权限相关dto
 */
@ApiModel(value = "角色权限dto",description = "角色权限相关dto")
public class OpenPermissionResponse implements Serializable {

    /**
     * 传入的授权路由列表
     */
    @ApiModelProperty(value = "路由参数",notes = "路由id")
    private String routerId;

    /**
     * 传入的角色id
     */
    @ApiModelProperty(value = "角色id",notes = "角色id")
    private String roleId;
    private String updateUser;
    private Date updateTime;
    private String routerDes;
    private String routerName;

    @Override
    public String toString() {
        return "{" +
                "routerId='" + routerId + '\'' +
                ", roleId='" + roleId + '\'' +
                ", updateUser='" + updateUser + '\'' +
                '}';
    }

    public String getRouterDes() {
        return routerDes;
    }

    public void setRouterDes(String routerDes) {
        this.routerDes = routerDes;
    }

    public String getRouterName() {
        return routerName;
    }

    public void setRouterName(String routerName) {
        this.routerName = routerName;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRouterId() {
        return routerId;
    }

    public void setRouterId(String routerId) {
        this.routerId = routerId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
