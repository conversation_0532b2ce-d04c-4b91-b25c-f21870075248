<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.openapi.dao.OpenPermissionBizMapper">
    <insert id="createRoleRouter">
        insert into sa_role_router_map
        (id,role_id,router_id,update_user,update_time)
        VALUES
        <foreach collection="splist" item="item" separator=",">
            (uuid_short(),#{roleId},#{item},#{source},now())
        </foreach>
    </insert>
    <insert id="insertUser">
        insert into sa_user(id,
                            user_name,
                            password,
                            real_name,
                            update_time,
                            update_user,
                            status)
        select #{id}, #{userName}, #{newPassword},person_name, now(), #{source}, '1'
        from sa_person
        where id = #{id}
    </insert>
    <insert id="insertUserRole">
        insert into sa_user_role_map
        (id,role_id,user_id,update_user,update_time)
        VALUES
        <foreach collection="splist" item="item" separator=",">
            (uuid_short(),#{item},#{userId},#{source},now())
        </foreach>
    </insert>
    <delete id="deleteUserRole">
        delete from sa_user_role_map
        where role_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and user_id=#{userId} and update_user=#{source}
    </delete>
    <delete id="deleteRoleRouter">
        delete from sa_role_router_map
        where router_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and role_id=#{roleId} and update_user=#{source}
    </delete>

    <select id="queryRouterListByRoleId"
            resultType="com.stock.service.platform.openapi.response.OpenPermissionResponse">
        select srrm.router_id,
               sr.router_des,
               sr.router_name,
               IFNULL(srrm.update_user, 'plf') update_user
        from sa_role_router_map srrm
                 LEFT JOIN sa_router sr ON srrm.router_id = sr.id
        where role_id = #{roleId}
    </select>
    <select id="checkRoleRouter" resultType="java.lang.Integer">
        select count(*)
        from sa_role_router_map
        where role_id = #{roleId}
        and router_id = #{routerId}
    </select>
    <select id="selectByPrimaryKey" resultType="java.lang.Integer">
        select count(*)
        from sa_user
        where id = #{userId}
    </select>
    <select id="selectByUserName" resultType="java.lang.Integer">
        select count(*)
        from sa_user
        where user_name = #{userName}
    </select>
    <select id="checkUserRole" resultType="java.lang.Integer">
        select count(*) from sa_user_role_map
        where role_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and user_id=#{userId}
    </select>
    <select id="selectUserRole"
            resultType="com.stock.service.platform.openapi.response.OpenUserRoleResponse">
        select id,role_id,user_id,update_user,update_time
        from sa_user_role_map
        where role_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and user_id=#{userId}
    </select>
    <select id="selectUserRolesByUserId"
            resultType="com.stock.service.platform.openapi.response.OpenUserRoleResponse">
        select
               role_id,
               sr.role_name,
               sr.role_des,
               IFNULL(surm.update_user, 'plf') update_user
        from sa_user_role_map surm
                 LEFT JOIN sa_role sr ON surm.role_id = sr.id
        where user_id = #{id}
    </select>
    <select id="selectRoleRouter"
            resultType="com.stock.service.platform.openapi.response.OpenPermissionResponse">
        select id,role_id,router_id,update_user
        from sa_role_router_map
        where router_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and role_id=#{roleId}
    </select>
    <select id="getRoleOfUsersByRoleId"
            resultType="com.stock.service.platform.openapi.request.OpenUserRequest">
        select
               user_id id,
               su.user_name,
               su.telephone,
               IFNULL(surm.update_user, 'plf') update_user
        from sa_user_role_map surm
                 LEFT JOIN sa_user su ON surm.user_id = su.id
        where role_id =  #{id}

    </select>
    <select id="queryRoleListByRouterId"
            resultType="com.stock.service.platform.openapi.response.OpenRoleResponse">
        select srrm.role_id id,
               sr.role_name,
               sr.role_des,
               IFNULL(srrm.update_user, 'plf') update_user
        from sa_role_router_map srrm
                 LEFT JOIN sa_role sr ON srrm.role_id = sr.id
        where router_id = #{id}

    </select>
</mapper>