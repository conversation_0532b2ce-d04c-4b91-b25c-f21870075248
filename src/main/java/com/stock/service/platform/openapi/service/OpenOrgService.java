package com.stock.service.platform.openapi.service;

import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.dao.OpenOrgMapper;
import com.stock.service.platform.openapi.request.OpenOrgRequest;
import com.stock.service.platform.openapi.response.OpenOrgResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OpenOrgService {

    @Autowired
    private OpenOrgMapper openOrgMapper;

    public List<OpenOrgResponse> queryOrg(OpenOrgRequest dto) {
        List<OpenOrgResponse> list=openOrgMapper.queryOrg(dto);
        return list;
    }

    public String editOrg(OpenOrgRequest dto) throws BaseException{
        //参数校验 companyId 和 orgCode 不能同时拥有
        if (StringUtils.isNotEmpty(dto.getCompanyId()) && StringUtils.isNotEmpty(dto.getOrgCode())){
            throw new BaseException("参数错误");
        }
        //判断pOrgId的orgType 是否小于当前orgType
        if (StringUtils.isEmpty(dto.getOrgType().trim())){
            throw new BaseException("组织类型为空");
        }
        if (StringUtils.isEmpty(dto.getpOrgId().trim())){
            throw new BaseException("上级组织id为空");
        }
        if (StringUtils.isEmpty(dto.getOrgName().trim())){
            throw new BaseException("机构名称为空");
        }
        String orgType = openOrgMapper.checkOrgType(dto.getpOrgId());
        if (Integer.parseInt(orgType)>=Integer.parseInt(dto.getOrgType())){
            throw new BaseException("上级组织机构填写错误");
        }
        // 没有id就新增
        if (StringUtils.isBlank(dto.getId())) {
            //检查orgCode是否重复
            if (openOrgMapper.checkOrgCode(dto.getOrgCode(),null)>0){
                throw new BaseException("机构代码重复");
            }
            openOrgMapper.insertSelective(dto);
            return "创建成功";
        }else {
            //判断修改的是否为基础公司
            if ("2".equals(dto.getId())){ throw new BaseException("基础公司不可修改"); }
            //检查orgCode是否重复
            if (!(openOrgMapper.checkOrgCode(dto.getOrgCode(),dto.getId())>0)){
                if (openOrgMapper.checkOrgCode(dto.getOrgCode(),null)>0){
                    throw new BaseException("机构代码重复");
                }
            }
            int i = openOrgMapper.updateByPrimaryKeySelective(dto);
            List<OpenOrgResponse> list = queryOrg(dto);
            if (i<1 && list.size()>0){
                return "修改失败 失败对象为"+list.get(0).toString();
            }
            return "修改成功";
        }

    }

    public String deleteOrg(OpenOrgRequest dto) throws BaseException{
        if (StringUtils.isBlank(dto.getId()) && "2".equals(dto.getId())){
            throw new BaseException("参数不正确");
        }
        int i = openOrgMapper.deleteByPrimaryKey(dto);
        List<OpenOrgResponse> list = queryOrg(dto);
        if (i<1 && list.size()>0){
            return "删除失败 失败对象为"+list.get(0).toString();
        }
        return "删除成功";
    }
}
