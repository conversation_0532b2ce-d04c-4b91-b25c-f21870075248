package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.request.*;
import com.stock.service.platform.openapi.response.OpenPermissionResponse;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import com.stock.service.platform.openapi.response.OpenUserRoleResponse;
import com.stock.service.platform.openapi.service.OpenPermissionService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 权限相关controller
 */
@Api(value = "权限管理", description = "人员列表-用户权限相关接口")
@RestController
@RequestMapping("openapi/permission")
public class OpenPermissionController extends BaseController {

    @Autowired
    OpenPermissionService openPermissionService;


    /**
     * 根据角色id查询对应权限
     *
     * @param role
     * @return
     */
    @PostMapping("/queryRouterListByRoleId")
    public JsonResponse<Map<String, List<OpenPermissionResponse>>> queryRouterListByRoleId(@RequestBody OpenRoleRequest role) {
        JsonResponse<Map<String, List<OpenPermissionResponse>>> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.listRouterByRoleId(role.getId()));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("查询角色对应权限信息列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }
    /**
     * 根据权限id查询对应角色信息
     *
     * @param router
     * @return
     */
    @PostMapping("/queryRoleListByRouterId")
    public JsonResponse<Map<String, List<OpenRoleResponse>>> queryRoleListByRouterId(@RequestBody OpenRouterRequest router) {
        JsonResponse<Map<String, List<OpenRoleResponse>>> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.listRoleByRouterId(router.getId()));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("查询角色对应权限信息列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 创建或删除角色对应权限
     *
     * @param dto
     * @return
     */
    @PostMapping("/createOrDeleteRoleRouters")
    public JsonResponse<String> createOrDeleteRoleRouters(@RequestBody OpenPermissionRequest dto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.createOrDeleteRoleRouters(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("添加角色权限列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }


    /**
     * 全量插入角色对应权限 先删后插
     *
     * @param request
     * @return
     */
    @PostMapping("setRoleRoutersList")
    public JsonResponse<String> setRoleRoutersList(@RequestBody OpenPermissionRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.setRoleRoutersList(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("全量插入角色对应权限错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 获取用户已有角色
     */
    @PostMapping(value = "getUserRoles")
    public JsonResponse<Map<String,List<OpenUserRoleResponse>>> getUserOfRolesByUserId(@RequestBody OpenUserRequest dto) {
        JsonResponse<Map<String,List<OpenUserRoleResponse>>> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.getUserOfRolesByUserId(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("获取用户已有角色错误:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 获取拥有该角色的用户
     */
    @PostMapping(value = "getRoleUsers")
    public JsonResponse<Map<String,List<OpenUserRequest>>> getRoleOfUsersByRoleId(@RequestBody OpenRoleRequest dto) {
        JsonResponse<Map<String,List<OpenUserRequest>>> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.getRoleOfUsersByRoleId(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("获取拥有该角色的用户错误:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 创建或删除用户对应角色
     */
    @PostMapping(value = "createOrDeleteUserRoles")
    public JsonResponse<String> createOrDeleteUserRoles(@RequestBody OpenUserRoleRequest dto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.createOrDeleteUserRoles(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("设置用户对应角色错误:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }


    /**
     * 全量插入用户对应角色先删后插
     *
     * @param request
     * @return
     */
    @PostMapping("setUserRolesList")
    public JsonResponse<String> setUserRolesList(@RequestBody OpenUserRoleRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openPermissionService.setUserRolesList(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("全量插入用户对应角色错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

}
