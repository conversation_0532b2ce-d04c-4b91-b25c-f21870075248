package com.stock.service.platform.openapi.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OpenCompanyRequest implements Serializable {
    private String id;

    private String companyCode;

    private String zhName;

    private String zhSortName;

    private String corporate;

    private String organisationNo;

    private String registerAddress;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerDate;

    private String companyMail;

    private BigDecimal registerCapital;

    private String phone;

    private String relaCode;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String stockType;

    private String liststate;

    private String deleteflag;

    private String statusName;

    private String orgform;

    private String businessDepartmentName;

    private String companyStockName;

    private String corporateIdNumber;

    private String remark;

    private String marketStockType;

    private List<String> companyCodeList;

    private String seriakey;

    private String industryCSRCStr;

    private String industrySWStr;
    private String logoUrl;
    private String compprofileDc;
    private String controStr;
    private String comStatus;

    private String equState;
    private String repState;
    private String cplState;
    private String source;//系统来源
    private List<OpenCompanyRequest> selfList;


    private static final long serialVersionUID = 1L;

    public List<OpenCompanyRequest> getSelfList() {
        return selfList;
    }

    public void setSelfList(List<OpenCompanyRequest> selfList) {
        this.selfList = selfList;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getCplState() {
        return cplState;
    }

    public void setCplState(String cplState) {
        this.cplState = cplState;
    }

    public String getSeriakey() {
        return seriakey;
    }

    public void setSeriakey(String seriakey) {
        this.seriakey = seriakey;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName == null ? null : zhName.trim();
    }

    public String getZhSortName() {
        return zhSortName;
    }

    public void setZhSortName(String zhSortName) {
        this.zhSortName = zhSortName == null ? null : zhSortName.trim();
    }

    public String getCorporate() {
        return corporate;
    }

    public void setCorporate(String corporate) {
        this.corporate = corporate == null ? null : corporate.trim();
    }

    public String getOrganisationNo() {
        return organisationNo;
    }

    public void setOrganisationNo(String organisationNo) {
        this.organisationNo = organisationNo == null ? null : organisationNo.trim();
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress == null ? null : registerAddress.trim();
    }

    public Date getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    public String getCompanyMail() {
        return companyMail;
    }

    public void setCompanyMail(String companyMail) {
        this.companyMail = companyMail == null ? null : companyMail.trim();
    }

    public BigDecimal getRegisterCapital() {
        return registerCapital;
    }

    public void setRegisterCapital(BigDecimal registerCapital) {
        this.registerCapital = registerCapital;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getBusinessDepartmentName() {
        return businessDepartmentName;
    }

    public void setBusinessDepartmentName(String businessDepartmentName) {
        this.businessDepartmentName = businessDepartmentName;
    }

    public String getRelaCode() {
        return relaCode;
    }

    public void setRelaCode(String relaCode) {
        this.relaCode = relaCode == null ? null : relaCode.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType == null ? null : stockType.trim();
    }

    public String getListstate() {
        return liststate;
    }

    public void setListstate(String liststate) {
        this.liststate = liststate == null ? null : liststate.trim();
    }

    public String getDeleteflag() {
        return deleteflag;
    }

    public void setDeleteflag(String deleteflag) {
        this.deleteflag = deleteflag == null ? null : deleteflag.trim();
    }

    public String getOrgform() {
        return orgform;
    }

    public void setOrgform(String orgform) {
        this.orgform = orgform == null ? null : orgform.trim();
    }

    public String getCompanyStockName() {
        return companyStockName;
    }

    public void setCompanyStockName(String companyStockName) {
        this.companyStockName = companyStockName == null ? null : companyStockName.trim();
    }

    public String getCorporateIdNumber() {
        return corporateIdNumber;
    }

    public void setCorporateIdNumber(String corporateIdNumber) {
        this.corporateIdNumber = corporateIdNumber == null ? null : corporateIdNumber.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getMarketStockType() {
        return marketStockType;
    }

    public void setMarketStockType(String marketStockType) {
        this.marketStockType = marketStockType == null ? null : marketStockType.trim();
    }

    public List<String> getCompanyCodeList() {
        return companyCodeList;
    }

    public void setCompanyCodeList(List<String> companyCodeList) {
        this.companyCodeList = companyCodeList;
    }

    public String getIndustryCSRCStr() {
        return industryCSRCStr;
    }

    public void setIndustryCSRCStr(String industryCSRCStr) {
        this.industryCSRCStr = industryCSRCStr;
    }

    public String getIndustrySWStr() {
        return industrySWStr;
    }

    public void setIndustrySWStr(String industrySWStr) {
        this.industrySWStr = industrySWStr;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getCompprofileDc() {
        return compprofileDc;
    }

    public void setCompprofileDc(String compprofileDc) {
        this.compprofileDc = compprofileDc;
    }

    public String getControStr() {
        return controStr;
    }

    public void setControStr(String controStr) {
        this.controStr = controStr;
    }

    public String getComStatus() {
        return comStatus;
    }

    public void setComStatus(String comStatus) {
        this.comStatus = comStatus;
    }

    public String getEquState() {
        return equState;
    }

    public void setEquState(String equState) {
        this.equState = equState;
    }

    public String getRepState() {
        return repState;
    }

    public void setRepState(String repState) {
        this.repState = repState;
    }
}