package com.stock.service.platform.openapi.service;

import com.stock.core.exception.BaseException;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.RoleMapper;
import com.stock.service.platform.common.entity.Role;
import com.stock.service.platform.common.entity.RoleExample;
import com.stock.service.platform.openapi.dao.OpenRoleBizMapper;
import com.stock.service.platform.openapi.request.OpenRoleRequest;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Transactional
public class OpenRoleService extends BaseService {
    @Autowired
    OpenRoleBizMapper openRoleBizMapper;

    @Autowired
    RoleMapper roleMapper;

    public Map<String, List<OpenRoleResponse>> queryRoleListService(OpenRoleRequest dto) {
        List<OpenRoleResponse> list = openRoleBizMapper.queryRoleList(dto);
        list.removeIf(openRoleResponse -> "1".equals(openRoleResponse.getId()));
        Map<String, List<OpenRoleResponse>> map = list.stream().collect(Collectors.groupingBy(OpenRoleResponse::getSource));
        return map;
    }

    public String editRole(OpenRoleRequest role) throws BaseException {
        if (role.getId() == null) {
            if (!roleNameCheck(role)) {
                throw new BaseException("角色名:" + role.getRoleName() + "已存在");
            }
            if (StringUtils.isEmpty(role.getRoleName().trim())) {
                throw new BaseException("角色名为空");
            }
            if (StringUtils.isEmpty(role.getRoleDes().trim())) {
                throw new BaseException("角色描述为空");
            }
            openRoleBizMapper.insert(role);
            return "创建成功";
        } else {
            if (!roleNameCheck(role)) {
                throw new BaseException("角色名:" + role.getRoleName() + "已存在");
            }
            if ("1".equals(role.getId())) {//判断修改的是不是管理员
                throw new BaseException("修改错误,没有权限");
            }
            int i = openRoleBizMapper.updateByPrimaryKeySelective(role);
            if (i < 1) {
                OpenRoleResponse openRoleResponse = openRoleBizMapper.updateCheck(role);
                return "修改错误,未修改成功角色为:" + openRoleResponse.toString();
            }
            return "修改成功";
        }

    }

    public String deleteRole(OpenRoleRequest role) throws BaseException {
        // 判断用户是否有分配该角色
        if (StringUtils.isNotEmpty(role.getIds())) {
            List<String> splist = Arrays.stream(role.getIds().split(",")).collect(Collectors.toList());

            int i = openRoleBizMapper.deleteByPrimaryKey(splist, role.getSource());
            //删除sa_role_router_map对应数据
            openRoleBizMapper.deleteByRoleId(splist, role.getSource());
            // 删除角色 同时删除用户对应角色
            openRoleBizMapper.deleteUserRoleByRoleId(splist, role.getSource());
            if (i < splist.size()) { //检查未删除角色
                List<OpenRoleResponse> list = openRoleBizMapper.deleteCheck(splist);
                return "删除错误,未删除成功角色为:" + list.toString();
            }
            return "删除成功";
        } else {
            throw new BaseException("参数不正确");
        }
    }

    /**
     * 角色名称校验
     */
    public Boolean roleNameCheck(OpenRoleRequest role) {
        RoleExample example = new RoleExample();
        example.createCriteria().andRoleNameEqualTo(role.getRoleName());
        List<Role> roles = roleMapper.selectByExample(example);
        if (roles.size() == 1) {
            if (roles.get(0).getId().equals(role.getId())) {
                return true;
            } else {
                return false;
            }
        } else if (roles.size() > 1) {
            return false;
        } else {
            return true;
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public String setRoleList(OpenRoleRequest dto) throws BaseException{
        //先清空sa_role表
        openRoleBizMapper.deleteBySource(dto.getSource());
        //检查角色名是否有重复
        for (OpenRoleRequest request : dto.getSelfList()) {
            if (!roleNameCheck(request)) {
                throw new BaseException("角色名:" + request.getRoleName() + "已存在");
            }
            if (StringUtils.isEmpty(request.getRoleName().trim())) {
                throw new BaseException("角色名为空");
            }
            if (StringUtils.isEmpty(request.getRoleDes().trim())) {
                throw new BaseException("角色描述为空");
            }
        }
        //再插入
        openRoleBizMapper.setRoleList(dto.getSelfList(),dto.getSource());
        //删除sa_role_router_map中sa_role中没有的角色对应数据
        openRoleBizMapper.deleteRoleRouter();
        //删除sa_user_role_map中sa_role中没有的角色对应数据
        openRoleBizMapper.deleteUserRole();
        return "插入成功";
    }
}
