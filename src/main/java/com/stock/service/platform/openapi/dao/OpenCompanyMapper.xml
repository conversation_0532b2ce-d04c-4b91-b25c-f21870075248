<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.stock.service.platform.openapi.dao.OpenCompanyMapper">

    <select id="queryCompany"
            resultType="com.stock.service.platform.openapi.response.OpenCompanyResponse"
            parameterType="com.stock.service.platform.openapi.request.OpenCompanyRequest">
        select
        sc.id id,
        sc.company_code companyCode,
        sc.zh_name zhName,
        sc.zh_sort_name zhSortName,
        sc.corporate corporate,
        sc.organisation_no organisationNo,
        org.org_name businessDepartmentName,
        sc.company_stock_name companyStockName,
        (case sc.deleteflag when '0' then '正常' ELSE '注销' END) statusName,
        sc.market_stock_type marketStockType,
        IFNULL(sc.source,'plf') source
        from sa_company sc
        LEFT JOIN sa_org org ON find_in_set(org.id, sc.organisation_no)
        <where>
            <if test="companyCode != null and companyCode !='' ">
                sc.company_code = #{companyCode,jdbcType=VARCHAR}
            </if>
            <if test="zhName != null and zhName !='' ">
                and sc.zh_name = #{zhName,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY sc.id
        order by sc.update_time desc
    </select>

    <delete id="deleteByPrimaryKeys">
        delete
        from sa_company
        where id = #{id}
        <if test="source != null">
            and source = #{source}
        </if>
    </delete>

    <delete id="deleteSaOrgByPrimaryKeys">
        delete
        from sa_org
        where company_id = #{id}
        <if test="source != null">
            and source = #{source,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="selectOldCompany" resultType="com.stock.service.platform.openapi.response.OpenCompanyResponse">
        select
        id,
        company_code,
        zh_name
        from sa_company
        <where>
            <if test="null != companyCode and companyCode != ''">
                company_code = #{companyCode,jdbcType=VARCHAR}
            </if>
            <if test="null != id and id != ''">
                and id = #{id,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.stock.service.platform.openapi.request.OpenCompanyRequest">
        update sa_company
        <set>
            <if test="zhName != null">
                zh_name = #{zhName,jdbcType=VARCHAR},
            </if>
            <if test="zhSortName != null">
                zh_sort_name = #{zhSortName,jdbcType=VARCHAR},
            </if>
            <if test="corporate != null">
                corporate = #{corporate,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                organisation_no = #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                register_address = #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                register_date = #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyMail != null">
                company_mail = #{companyMail,jdbcType=VARCHAR},
            </if>
            <if test="registerCapital != null">
                register_capital = #{registerCapital,jdbcType=DECIMAL},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteflag != null">
                deleteflag = #{deleteflag,jdbcType=VARCHAR},
            </if>
            <if test="orgform != null">
                orgform = #{orgform,jdbcType=VARCHAR},
            </if>
            <if test="companyStockName != null">
                company_stock_name = #{companyStockName,jdbcType=VARCHAR},
            </if>
            <if test="corporateIdNumber != null">
                corporate_id_number = #{corporateIdNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="marketStockType != null">
                market_stock_type = #{marketStockType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
        <if test="source != null">
            and source = #{source}
        </if>

    </update>
    <update id="updateOrgByPrimaryKeySelective">
        update sa_org
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="pOrgId != null">
                p_org_id = #{pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null">
                org_type = #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where company_id = #{companyId,jdbcType=VARCHAR}
        <if test="updateUser != null">
            and update_user = #{updateUser,jdbcType=VARCHAR}
        </if>
    </update>
    <select id="checkUpdateOrDelete" parameterType="com.stock.service.platform.openapi.request.OpenCompanyRequest"
            resultType="com.stock.service.platform.openapi.response.OpenCompanyResponse">
        select id, company_code, zh_name, source
        from sa_company
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <insert id="insertSelective" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT CONCAT(uuid_short(),'')
        </selectKey>
        insert into sa_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            hidden_key,
            <if test="companyCode != null">
                company_code,
            </if>
            <if test="zhName != null">
                zh_name,
            </if>
            <if test="zhSortName != null">
                zh_sort_name,
            </if>
            <if test="corporate != null">
                corporate,
            </if>
            <if test="organisationNo != null">
                organisation_no,
            </if>
            <if test="registerAddress != null">
                register_address,
            </if>
            <if test="registerDate != null">
                register_date,
            </if>
            <if test="companyMail != null">
                company_mail,
            </if>
            <if test="registerCapital != null">
                register_capital,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleteflag != null">
                deleteflag,
            </if>
            <if test="orgform != null">
                orgform,
            </if>
            <if test="companyStockName != null">
                company_stock_name,
            </if>
            <if test="corporateIdNumber != null">
                corporate_id_number,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="marketStockType != null">
                market_stock_type,
            </if>
            <if test="source != null">
                source
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            uuid(),
            <if test="companyCode != null">
                #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="zhName != null">
                #{zhName,jdbcType=VARCHAR},
            </if>
            <if test="zhSortName != null">
                #{zhSortName,jdbcType=VARCHAR},
            </if>
            <if test="corporate != null">
                #{corporate,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyMail != null">
                #{companyMail,jdbcType=VARCHAR},
            </if>
            <if test="registerCapital != null">
                #{registerCapital,jdbcType=DECIMAL},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteflag != null">
                #{deleteflag,jdbcType=VARCHAR},
            </if>
            <if test="orgform != null">
                #{orgform,jdbcType=VARCHAR},
            </if>
            <if test="organisationNo != null">
                #{organisationNo,jdbcType=VARCHAR},
            </if>
            <if test="companyStockName != null">
                #{companyStockName,jdbcType=VARCHAR},
            </if>
            <if test="corporateIdNumber != null">
                #{corporateIdNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="marketStockType != null">
                #{marketStockType,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source}
            </if>
        </trim>
    </insert>
    <insert id="insertOrgSelective">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="companyId != null">
                company_id,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="pOrgId != null">
                p_org_id,
            </if>
            <if test="orgType != null">
                org_type,
            </if>
            <if test="source != null">
                source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
            <if test="orgCode != null">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="pOrgId != null">
                #{pOrgId,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null">
                #{orgType,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="queryCompanyUsers" resultType="com.stock.service.platform.openapi.response.OpenUserResponse">
        SELECT su.id
        FROM sa_user su
        WHERE su.id IN (SELECT sp.id
                        FROM sa_person sp
                        WHERE sp.org_id IN (SELECT so.id FROM sa_org so WHERE so.company_id = #{id}))
    </select>

    <select id="getCompanyByCodePlate" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        select scc.belongs_plate   marketStockType,
               zh_name          as zhName,
               zh_sort_name     as zhSortName,
               orgform,
               register_date    as registerDate,
               register_address as registerAddress,
               register_capital as registerCapital,
               company_mail     as companyMail,
               ipo_date         as listingDate,
               ipo_price        as issuePrice,
               equity_num       as capitalStockTotal,
               license_no as socialUnifiedCreditCode,
               corporate,
               phone
        from `capital-repmanage-jt`.sa_company_cloud scc
        where scc.company_code = #{companyCode} limit 1
    </select>

</mapper>