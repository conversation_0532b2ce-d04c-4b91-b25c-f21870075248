package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.request.OpenRoleRequest;
import com.stock.service.platform.openapi.response.OpenRoleResponse;
import com.stock.service.platform.openapi.service.OpenRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("openapi/role")
public class OpenRoleController extends BaseController {

    @Autowired
    OpenRoleService openRoleService;


    /**
     * 查询角色信息列表
     */
    @PostMapping(value = "queryRoleList")
    public JsonResponse<Map<String,List<OpenRoleResponse>>> queryRoleList(@RequestBody OpenRoleRequest dto) {
        JsonResponse<Map<String,List<OpenRoleResponse>>> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openRoleService.queryRoleListService(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("查询角色信息列表失败:" + e.getMessage());
            e.printStackTrace();
        }
        catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }



    /**
     * 全量插入角色信息列表
     */
    @PostMapping(value = "setRoleList")
    public JsonResponse<String> setRoleList(@RequestBody OpenRoleRequest dto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openRoleService.setRoleList(dto));
        } catch (BaseException e){
            jsonResponse.setErrorMsg("查询角色信息列表失败:" + e.getMessage());
            e.printStackTrace();
        }
        catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 创建角色信息
     */
    @PostMapping("createRole")
    public JsonResponse<String> createRole(@RequestBody OpenRoleRequest role) {
        JsonResponse<String> jsonResponse =new JsonResponse<>();
        // 创建设置参数为null
        role.setId(null);
        try {
            jsonResponse.setResult(openRoleService.editRole(role));
        }catch (BaseException e){
            jsonResponse.setErrorMsg("创建角色信息列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 编辑角色信息
     */
    @PostMapping("modifyRole")
    public JsonResponse<String> modifyRole(@RequestBody OpenRoleRequest role) {
        JsonResponse<String> jsonResponse =new JsonResponse<>();
        try {
             jsonResponse.setResult(openRoleService.editRole(role));
        }catch (BaseException e){
            jsonResponse.setErrorMsg("编辑角色信息列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 删除角色
     */
    @PostMapping("deleteRole")
    public JsonResponse<String> deleteRole(@RequestBody OpenRoleRequest role) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openRoleService.deleteRole(role));
        }catch (BaseException e){
            jsonResponse.setErrorMsg("删除角色信息列表失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }




}
