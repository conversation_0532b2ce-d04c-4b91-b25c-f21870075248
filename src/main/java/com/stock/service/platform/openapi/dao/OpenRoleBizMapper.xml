<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.openapi.dao.OpenRoleBizMapper">

    <resultMap id="BaseResultMap" type="com.stock.service.platform.openapi.response.OpenRoleResponse">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_des" jdbcType="VARCHAR" property="roleDes"/>
        <result column="curr_status" jdbcType="VARCHAR" property="currStatus"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <insert id="insert" parameterType="com.stock.service.platform.openapi.request.OpenRoleRequest">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into sa_role (id, role_name, role_des,
        status,source
        )
        values (#{id,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{roleDes,jdbcType=VARCHAR},
        '1',#{source,jdbcType=VARCHAR}
        )
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.stock.service.platform.openapi.request.OpenRoleRequest">
        update sa_role
        <set>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleDes != null">
                role_des = #{roleDes,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
        <if test="source != null">
            AND source = #{source,jdbcType=VARCHAR}
        </if>
    </update>
    <insert id="setRoleList">
        <foreach collection="selfList" item="item" separator=";">
            SELECT @custom_uuid := CONCAT(UUID_SHORT(),'');
            insert into sa_role(
            id,
            <if test="item.roleName !=null">
                role_name,
            </if>
            <if test="item.roleDes !=null">
                role_des,
            </if>
            source)
            values (
            @custom_uuid,
            <if test="item.roleName !=null">
                #{item.roleName,jdbcType=VARCHAR},
            </if>
            <if test="item.roleDes !=null">
                #{item.roleDes,jdbcType=VARCHAR},
            </if>
            #{source,jdbcType=VARCHAR})
        </foreach>


    </insert>
    <select id="updateCheck" resultType="com.stock.service.platform.openapi.response.OpenRoleResponse">
        SELECT id, role_name, role_des, source
        FROM sa_role
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.util.ArrayList">
        delete from sa_role
        where
        id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            AND source = #{source,jdbcType=VARCHAR}
        </if>
    </delete>
    <!--   系统来源就是更新人-->
    <delete id="deleteByRoleId">
        delete from sa_role_router_map
        where role_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            AND update_user = #{source,jdbcType=VARCHAR}
        </if>
    </delete>
    <delete id="deleteUserRoleByRoleId">
        delete from sa_user_role_map
        where role_id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="source != null">
            AND update_user = #{source,jdbcType=VARCHAR}
        </if>
    </delete>
    <delete id="deleteBySource">
        delete
        from sa_role
        where source = #{source}
    </delete>
    <delete id="deleteRoleRouter">
        delete
        from sa_role_router_map
        WHERE role_id NOT in (SELECT id from sa_role)
    </delete>
    <delete id="deleteUserRole">
        delete
        from sa_user_role_map
        WHERE role_id NOT in (SELECT id from sa_role)
    </delete>

    <select id="queryRoleList" parameterType="com.stock.service.platform.openapi.request.OpenRoleRequest"
            resultMap="BaseResultMap">
        SELECT id, role_name, role_des, IFNULL(source,'plf') source
        FROM sa_role
        <where>
            <if test="roleDes != null and roleDes != ''">
                and role_des = #{roleDes}
            </if>
            <if test="roleName != null and roleName != ''">
                and role_name= #{roleName}
            </if>
        </where>
    </select>
    <select id="selectByName" resultType="java.lang.Integer">
        select count(*)
        from sa_role
        where role_name = #{roleName}
    </select>
    <select id="deleteCheck"
            resultType="com.stock.service.platform.openapi.response.OpenRoleResponse">
        SELECT id, role_name, role_des,source
        FROM sa_role
        where id in
        <foreach collection="splist" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>