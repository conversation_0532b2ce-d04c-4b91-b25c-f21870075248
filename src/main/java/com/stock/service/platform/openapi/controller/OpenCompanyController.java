package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.request.OpenCompanyRequest;
import com.stock.service.platform.openapi.response.OpenCompanyResponse;
import com.stock.service.platform.openapi.service.OpenCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("openapi/company")
public class OpenCompanyController extends BaseController {

    @Autowired
    private OpenCompanyService openCompanyService;


    /**
     * 查询上市公司
     */
    @PostMapping(value = "queryCompany")
    public JsonResponse<Map<String,List<OpenCompanyResponse>>> queryCompany(@RequestBody OpenCompanyRequest dto) {
        JsonResponse<Map<String, List<OpenCompanyResponse>>> jsonResponse = new JsonResponse<>();
        Map<String, List<OpenCompanyResponse>> openCompanyResponses = null;
        try {
            openCompanyResponses = openCompanyService.queryCompany(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        jsonResponse.setResult(openCompanyResponses);
        return jsonResponse;
    }

    /**
     * 创建或修改上市公司
     *
     * @param
     * @return JsonResponse<String>
     */
    @PostMapping(value = "/createOrUpdate")
    public JsonResponse<String> createOrUpdate(@RequestBody OpenCompanyRequest dto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openCompanyService.createOrUpdate(dto));
        } catch (BaseException e) {
            jsonResponse.setResult("新增或更新上市公司失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setResult("系统错误:" + e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 全量插入公司信息
     *
     * @param request
     * @return
     */
    @PostMapping("setCompanyList")
    public JsonResponse<String> setCompanyList(@RequestBody OpenCompanyRequest request){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openCompanyService.setCompanyList(request));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("全量插入公司信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /***
     * 根据ID删除公司
     * For
     * @param
     * @return
     */
    @PostMapping(value = "/deleteCompanyById")
    public JsonResponse<String> deleteCompanyById(@RequestBody OpenCompanyRequest dto) {
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openCompanyService.deleteCompanyById(dto));
        } catch (BaseException e) {
            jsonResponse.setResult("删除上市公司失败:" + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            jsonResponse.setResult("系统错误:" + e.getMessage());
            e.printStackTrace();
        }

        return jsonResponse;
    }

}
