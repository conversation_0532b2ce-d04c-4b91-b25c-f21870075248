package com.stock.service.platform.openapi.dao;

import com.stock.service.platform.openapi.request.OpenRouterRequest;
import com.stock.service.platform.openapi.response.OpenRouterResponse;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface OpenRouterBizMapper {
    /**
     * 查询router路由列表，带有用户是否拥有的标记
     * @param roleId
     * @param pRouterId
     * @return
     */
    List<OpenRouterRequest> queryRouterListByRoleId(OpenRouterRequest dto);

    /**
     * 插入 roleRouterList
     * @param roleId
     * @param routerIdSet
     * @return
     */
    int insertRoleRouterList(@Param("roleId") String roleId, @Param("routerIdSet") Set<String> routerIdSet);

    /**
     * 插入 roleRouterList
     * @param roleId
     * @param routerId
     * @return
     */
    int insertRoleRouter(@Param("roleId") String roleId, @Param("routerId") String routerId);

    List<OpenRouterResponse> queryRouterList(OpenRouterRequest dto);

    int insert(OpenRouterRequest router);

    int updateByPrimaryKeySelective(OpenRouterRequest router);

    int selectByName(OpenRouterRequest router);

    int deleteByPrimaryKey(String id);

    int deleteByRouterId(String id);
}
