package com.stock.service.platform.openapi.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.BaseException;
import com.stock.service.platform.openapi.request.OpenOrgRequest;
import com.stock.service.platform.openapi.response.OpenOrgResponse;
import com.stock.service.platform.openapi.service.OpenOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("openapi/org")
public class OpenOrgController extends BaseController {

    @Autowired
    private OpenOrgService openOrgService;


    /**
     * 查询组织机构
     */
    @PostMapping(value = "queryOrg")
    public JsonResponse<List<OpenOrgResponse>> queryOrg(@RequestBody OpenOrgRequest dto) {
        JsonResponse<List<OpenOrgResponse>> jsonResponse = new JsonResponse<>();
        List<OpenOrgResponse> OpenOrgResponse = null;
        try {
            OpenOrgResponse = openOrgService.queryOrg(dto);
        } catch (BaseException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        jsonResponse.setResult(OpenOrgResponse);
        return jsonResponse;
    }


    /**
     * 创建组织机构信息
     *
     * @param dto
     * @return
     */
    @PostMapping("createOrg")
    public JsonResponse<String> createOrg(@RequestBody OpenOrgRequest dto){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        // 创建设置参数为null
        dto.setId(null);
        try {
            jsonResponse.setResult(openOrgService.editOrg(dto));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("创建组织机构信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }
    /**
     * 修改组织机构信息
     *
     * @param dto
     * @return
     */
    @PostMapping("updateOrg")
    public JsonResponse<String> updateOrg(@RequestBody OpenOrgRequest dto){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openOrgService.editOrg(dto));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("修改组织机构信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

    /**
     * 删除组织机构信息
     *
     * @param dto
     * @return
     */
    @PostMapping("deleteOrg")
    public JsonResponse<String> deleteOrg(@RequestBody OpenOrgRequest dto){
        JsonResponse<String> jsonResponse = new JsonResponse<>();
        try {
            jsonResponse.setResult(openOrgService.deleteOrg(dto));
        } catch (BaseException e) {
            jsonResponse.setErrorMsg("删除组织机构信息错误:"+e.getMessage());
            e.printStackTrace();
        }catch (Exception e) {
            jsonResponse.setErrorMsg("系统错误:"+e.getMessage());
            e.printStackTrace();
        }
        return jsonResponse;
    }

}
