package com.stock.service.platform.block.dto;

import java.io.Serializable;

public class EquityAllPlantDto implements Serializable {
    private String planId; //计划id

    private String state;//1进行中，2已完成，3终止

    private String planName;//计划名称

    private String equNumSum;//权益总量

    private String personNum;//授予人数

    private String equStokeNum;//实际授予权益

    private String priceVolumeNum;//实际授予权益  量调后

    private String exerciseStartDate; //解除限售日期

    private String companyCode; // 公司代码

    private String token;

    private String planDetailId;

    private String lockSta;

    private String lockEnd;

    private String equStepName; //期权阶段

    private String equType; //股票类型

    private String proportion; //行权比例

    private int unlockStockNum;//实际可行权数量

    private int exerciseRights;// 已行权数量

    public String getPriceVolumeNum() {
        return priceVolumeNum;
    }

    public void setPriceVolumeNum(String priceVolumeNum) {
        this.priceVolumeNum = priceVolumeNum;
    }

    public String getEquType() {
        return equType;
    }

    public void setEquType(String equType) {
        this.equType = equType;
    }

    public String getProportion() {
        return proportion;
    }

    public void setProportion(String proportion) {
        this.proportion = proportion;
    }

    public int getUnlockStockNum() {
        return unlockStockNum;
    }

    public void setUnlockStockNum(int unlockStockNum) {
        this.unlockStockNum = unlockStockNum;
    }

    public int getExerciseRights() {
        return exerciseRights;
    }

    public void setExerciseRights(int exerciseRights) {
        this.exerciseRights = exerciseRights;
    }

    public String getEquStepName() {
        return equStepName;
    }

    public void setEquStepName(String equStepName) {
        this.equStepName = equStepName;
    }

    public void setLockSta(String lockSta) {
        this.lockSta = lockSta;
    }

    public void setLockEnd(String lockEnd) {
        this.lockEnd = lockEnd;
    }

    public String getLockSta() {
        return lockSta;
    }

    public String getLockEnd() {
        return lockEnd;
    }

    public void setPlanDetailId(String planDetailId) {
        this.planDetailId = planDetailId;
    }

    public String getPlanDetailId() {
        return planDetailId;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public String getToken() {
        return token;
    }

    public void setExerciseStartDate(String exerciseStartDate) {
        this.exerciseStartDate = exerciseStartDate;
    }

    public String getExerciseStartDate() {
        return exerciseStartDate;
    }

    public void setPersonNum(String personNum) {
        this.personNum = personNum;
    }

    public String getPersonNum() {
        return personNum;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }


    public void setState(String state) {
        this.state = state;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public void setEquNumSum(String equNumSum) {
        this.equNumSum = equNumSum;
    }



    public void setEquStokeNum(String equStokeNum) {
        this.equStokeNum = equStokeNum;
    }

    public String getPlanId() {
        return planId;
    }

    public String getState() {
        return state;
    }

    public String getPlanName() {
        return planName;
    }

    public String getEquNumSum() {
        return equNumSum;
    }



    public String getEquStokeNum() {
        return equStokeNum;
    }
}
