package com.stock.service.platform.block.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.block.dto.HttpsClientRequestFactory;
import com.stock.service.platform.block.service.CplTransService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("brokerInfo/cplTrans")
public class CplTransController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CplTransController.class);

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CplTransService cplTransService;

    @Value("${externalUrl.compliance}")
    private String externalUrl;

    /**
     * 获取合规交易管理相关数据
     * @param map 包含companyCode
     * @return
     */
    @RequestMapping("getCplTransInfo")
    @ResponseBody
    public JsonResponse<Map<String,Object>> getCplTransInfo(@RequestBody Map<String, Object> map) {
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();
        Map<String, Object> result = new HashMap<>();
        try {
            // 调用接口获取 已监控股东 管控股份 管控交易数
            String url1 = externalUrl + "/interfaceControl/getAllPerson";
            LOGGER.info("getAllPerson合规url：------" + url1);
            ResponseEntity<Map> resEntity1 = restTemplate.postForEntity(url1, map, Map.class);
            if(resEntity1 != null) {
                LOGGER.info("getAllPerson合规返回结果：------" + resEntity1.getBody().toString());
                Map<String, Object> data = (Map) resEntity1.getBody().get("result");
                String resultCode = (String) data.get("RESULT_CODE");
                Map<String,Object> dataDetails = (Map<String, Object>) data.get("data");
                if ("0".equals(resultCode) && !ObjectUtils.isEmpty(dataDetails)) {
                //if("0".equals(resultCode)&&dataDetails.size()>0) {
                    result.put("personCount", dataDetails.get("personCount"));//股东数量--已监控股东
                    result.put("personShareCount", dataDetails.get("personShareCount"));//股东共计持股数--管控股份
                    result.put("numbers", dataDetails.get("numbers"));//合规调用数量--管控交易数
                    response.setResult(result);
                }else {
                    response.setErrorMsg(String.valueOf(data.get("RESULT_MSEEAGE")));
                }
            }else {
                response.setErrorMsg("合规交易管理接口调用失败，未返回信息");
            }
        } catch (ResourceAccessException e) {
            LOGGER.info("合规交易管理接口调用报错");
            e.printStackTrace();
            response.setErrorMsg("合规交易管理接口调用失败，无法获取信息");
        } catch (Exception e) {
            LOGGER.info("合规交易管理接口调用报错");
            e.printStackTrace();
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }

    /**
     * 获取合规交易管理相关数据
     * @param map 包含companyCode
     * @return
     */
    @RequestMapping("getCplTransDetail")
    @ResponseBody
    public JsonResponse<Map<String,Object>> getCplTransDetail(@RequestBody Map<String, Object> map) {
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();
        Map<String, Object> result = new HashMap<>();
        try {
            String companyCode = (String) map.get("companyCode");
            //String tradeDate =cplTransService.getNewTradeDate();
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String tradeDate = currentDate.format(formatter);
            String url3 = externalUrl + "/htTranComplianceInterFace/getQueryList?algorithmType=0&tradeDate="+tradeDate+"&searchFlag=1&companyCode="+companyCode+"&draw=0&length=6&start=0";
            LOGGER.info("getQueryList合规url：------" + url3);
            HttpsClientRequestFactory factory = new HttpsClientRequestFactory();
            factory.setConnectTimeout(60000 * 1000);
            factory.setReadTimeout(5 * 60000 * 1000);
            RestTemplate restTemplate = new RestTemplate(factory);
            result=restTemplate.getForObject(url3,Map.class);
            LOGGER.info("getQueryList合规返回结果：------" + result.toString());
            response.setResult(result);
        } catch (ResourceAccessException e) {
            LOGGER.info("合规交易管理接口调用报错");
            e.printStackTrace();
            response.setErrorMsg("合规交易管理接口调用失败，无法获取信息");
        } catch (Exception e) {
            LOGGER.info("合规交易管理接口调用报错");
            e.printStackTrace();
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }
}
