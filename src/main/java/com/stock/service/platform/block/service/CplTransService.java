package com.stock.service.platform.block.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.block.dao.CplTransMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 券商相关数据处理
 */
@Service
public class CplTransService extends BaseService {

    @Autowired
    private CplTransMapper cplTransMapper;

    /**
     * 查询公司信息
     *
     * @return
     */
    public String getNewTradeDate() {
        // 递归获取组织机构节点信息 根节点是“0”
        String tradeDate = cplTransMapper.getNewTradeDate();
        return tradeDate;
    }


}
