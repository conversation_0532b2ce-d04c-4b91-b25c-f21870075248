package com.stock.service.platform.block.dto;

import java.util.Date;

/**
 * <AUTHOR>
 * 文件dto
 */
public class ReqManageDto {

    private Date nowDate;
    private String sumMoney;
    private String shareLowRatio;
    private String repStartDateNum;
    private String repShareTotalRatio;
    private Date endDate;
    private String avgPrice;
    private String shareHighRatio;
    private String planStatus;
    private String repDateNum;
    private String lowHighRatio;
    private String timeRatio;
    private String repShareLow;
    private String sumShare;
    private String noExceedMonth;
    private String minPrice;
    private String maxPrice;
    private String repShareHigh;
    private String repShareTotal;
    private Date startDate;
    private String repEndDateNum;

    private String token;

    private String selCompanyCode;

    private String errorCode;

    private String errorMsg;

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSelCompanyCode() {
        return selCompanyCode;
    }

    public void setSelCompanyCode(String selCompanyCode) {
        this.selCompanyCode = selCompanyCode;
    }

    public Date getNowDate() {
        return nowDate;
    }

    public void setNowDate(Date nowDate) {
        this.nowDate = nowDate;
    }

    public String getSumMoney() {
        return sumMoney;
    }

    public void setSumMoney(String sumMoney) {
        this.sumMoney = sumMoney;
    }

    public String getShareLowRatio() {
        return shareLowRatio;
    }

    public void setShareLowRatio(String shareLowRatio) {
        this.shareLowRatio = shareLowRatio;
    }

    public String getRepStartDateNum() {
        return repStartDateNum;
    }

    public void setRepStartDateNum(String repStartDateNum) {
        this.repStartDateNum = repStartDateNum;
    }

    public String getRepShareTotalRatio() {
        return repShareTotalRatio;
    }

    public void setRepShareTotalRatio(String repShareTotalRatio) {
        this.repShareTotalRatio = repShareTotalRatio;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(String avgPrice) {
        this.avgPrice = avgPrice;
    }

    public String getShareHighRatio() {
        return shareHighRatio;
    }

    public void setShareHighRatio(String shareHighRatio) {
        this.shareHighRatio = shareHighRatio;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public String getRepDateNum() {
        return repDateNum;
    }

    public void setRepDateNum(String repDateNum) {
        this.repDateNum = repDateNum;
    }

    public String getLowHighRatio() {
        return lowHighRatio;
    }

    public void setLowHighRatio(String lowHighRatio) {
        this.lowHighRatio = lowHighRatio;
    }

    public String getTimeRatio() {
        return timeRatio;
    }

    public void setTimeRatio(String timeRatio) {
        this.timeRatio = timeRatio;
    }

    public String getRepShareLow() {
        return repShareLow;
    }

    public void setRepShareLow(String repShareLow) {
        this.repShareLow = repShareLow;
    }

    public String getSumShare() {
        return sumShare;
    }

    public void setSumShare(String sumShare) {
        this.sumShare = sumShare;
    }

    public String getNoExceedMonth() {
        return noExceedMonth;
    }

    public void setNoExceedMonth(String noExceedMonth) {
        this.noExceedMonth = noExceedMonth;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public String getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(String maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getRepShareHigh() {
        return repShareHigh;
    }

    public void setRepShareHigh(String repShareHigh) {
        this.repShareHigh = repShareHigh;
    }

    public String getRepShareTotal() {
        return repShareTotal;
    }

    public void setRepShareTotal(String repShareTotal) {
        this.repShareTotal = repShareTotal;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getRepEndDateNum() {
        return repEndDateNum;
    }

    public void setRepEndDateNum(String repEndDateNum) {
        this.repEndDateNum = repEndDateNum;
    }
}
