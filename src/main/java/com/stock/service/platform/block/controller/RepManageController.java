package com.stock.service.platform.block.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.block.dto.ReqManageDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("repManage")
public class RepManageController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RepManageController.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    CommonService commonService;

    @Value("${externalUrl.repmanage}")
    private String repmanage;

    @Autowired
    private Environment env;

//    @Value("${spring.profiles.active}")

    @RequestMapping("getRepManageInfo")
    @ResponseBody
    public JsonResponse<Map<String,Object>> getRepManageInfo(HttpServletRequest request,@RequestBody ReqManageDto reqManageDto) throws Exception{
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();

        //Profiles profiles = Profiles.of("dev");
        //String url = "";
        //if(env.acceptsProfiles(profiles)) {
        //    url = "http://localhost:8782/repmanage/external/getRepManageInfo";
        //} else {
        //    url = repmanage+"/external/getRepManageInfo";
        //}
        String url = repmanage+"/external/getRepManageInfo";

        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        reqManageDto.setToken(token);
        Map<String, Object> map = new HashMap<>();
        try {
            map  = ((Map<String, Object>) restTemplate.postForObject(url,reqManageDto,Object.class));
        }
        catch (Exception e){
            LOGGER.info(e.getMessage());
        }
        response.setResult(map);
        return response;
    }
}
