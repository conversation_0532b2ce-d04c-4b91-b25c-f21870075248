package com.stock.service.platform.block.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("listedCompany")
public class EquityController extends BaseController {


    @Autowired
    private RestTemplate restTemplate;
    @Value("${externalUrl.equity}")
    private String equity;


    /**
     * 查询券商下上市公司计划 第一类,第二类限制性股票和期权股票
     **/
//    @RequestMapping(value = "/listAllStock", method = RequestMethod.POST)
//    @ResponseBody
//    public Object listAllStock(HttpServletRequest request, @RequestBody Map<String,String> companyCode) {
//        EquityAllPlantDto equityAllPlantDto =new  EquityAllPlantDto();
//        String url=equity+"/external/listAllStock";
//        String token= request.getHeader(HttpHeaders.AUTHORIZATION);
//        equityAllPlantDto.setCompanyCode(companyCode.get("companyCode"));
//        equityAllPlantDto.setToken(token);
//        JsonResponse<Map<String, Object>> response =restTemplate.postForObject(url,equityAllPlantDto ,JsonResponse.class);
//        return response;
//
//    }

    /**
     * poc接口对接
     * @param request
     * @param companyCode
     * @return
     */
    @RequestMapping(value = "/listAllStock", method = RequestMethod.POST)
    @ResponseBody
    public Object listAllStock(HttpServletRequest request, @RequestBody Map<String,String> companyCode) {

        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        String url=equity+"/data/soi/queryPlanMsgByCompanyCode?accessToken=" + token;
        log.info("股权激励listAllStock----------->url：" + url);
//        String orgType = (String) getUserInfo().getInfo().get("orgType");
//        if (!("4".equals(orgType))) {
//            companyCode.put("companyCode","");
//        }
        log.info("股权激励listAllStock----------->传参：" + companyCode);

        JsonResponse<Map<String, Object>> response =restTemplate.postForObject(url,companyCode ,JsonResponse.class);

        log.info("股权激励listAllStock----------->结果：" + response);

        return response;

    }
}
