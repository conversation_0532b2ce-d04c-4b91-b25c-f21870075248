package com.stock.service.platform.block.dto;

public class CplTransDto {
    private String personCount;
    private String personShareCount;
    private String numbers;
    private String buyFlag;
    private String saleFlag;
    private String contentBuy;
    private String contentSale;
    private String identityType;
    private String inOutStatus;
    private String personName;
    private String shareCount;
    private String shareholding;
    private String algorithmType;
    private String searchFlag;

    public String getPersonCount() {
        return personCount;
    }

    public void setPersonCount(String personCount) {
        this.personCount = personCount;
    }

    public String getPersonShareCount() {
        return personShareCount;
    }

    public void setPersonShareCount(String personShareCount) {
        this.personShareCount = personShareCount;
    }

    public String getNumbers() {
        return numbers;
    }

    public void setNumbers(String numbers) {
        this.numbers = numbers;
    }

    public String getBuyFlag() {
        return buyFlag;
    }

    public void setBuyFlag(String buyFlag) {
        this.buyFlag = buyFlag;
    }

    public String getSaleFlag() {
        return saleFlag;
    }

    public void setSaleFlag(String saleFlag) {
        this.saleFlag = saleFlag;
    }

    public String getContentBuy() {
        return contentBuy;
    }

    public void setContentBuy(String contentBuy) {
        this.contentBuy = contentBuy;
    }

    public String getContentSale() {
        return contentSale;
    }

    public void setContentSale(String contentSale) {
        this.contentSale = contentSale;
    }

    public String getIdentityType() {
        return identityType;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    public String getInOutStatus() {
        return inOutStatus;
    }

    public void setInOutStatus(String inOutStatus) {
        this.inOutStatus = inOutStatus;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getShareCount() {
        return shareCount;
    }

    public void setShareCount(String shareCount) {
        this.shareCount = shareCount;
    }

    public String getShareholding() {
        return shareholding;
    }

    public void setShareholding(String shareholding) {
        this.shareholding = shareholding;
    }

    public String getAlgorithmType() {
        return algorithmType;
    }

    public void setAlgorithmType(String algorithmType) {
        this.algorithmType = algorithmType;
    }

    public String getSearchFlag() {
        return searchFlag;
    }

    public void setSearchFlag(String searchFlag) {
        this.searchFlag = searchFlag;
    }
}
