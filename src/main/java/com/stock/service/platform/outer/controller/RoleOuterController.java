package com.stock.service.platform.outer.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.common.dao.OuterRoleMapper;
import com.stock.service.platform.common.entity.OuterRole;
import com.stock.service.platform.common.entity.OuterRoleExample;
import com.stock.service.platform.common.entity.User;
import com.stock.service.platform.common.system.service.InfoSyncService;
import com.stock.service.platform.mqManage.service.MQManagerService;
import com.stock.service.platform.outer.service.RoleOuterService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("role/outer")
public class RoleOuterController extends BaseController {

    @Resource
    private RoleOuterService service;

    @Resource
    private InfoSyncService infoSyncService;

    @Resource
    private MQManagerService mqManagerService;

    @Resource
    private OuterRoleMapper outerRoleMapper;

    @PostMapping("getRoleList")
    @ResponseBody
    public JsonResponse<Object> getRoleList(HttpServletRequest request, @RequestBody OuterRole param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.getRoleList(request, param));
        }catch (Exception e) {
            response.setErrorMsg("获取角色列表失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("cuRoleInfo")
    @ResponseBody
    public JsonResponse<Object> cuRoleInfo(@RequestBody OuterRole param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.cuRoleInfo(param));
        }catch (Exception e) {
            response.setErrorMsg("新建或更新角色失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("delRoleInfo")
    @ResponseBody
    public JsonResponse<Object> delRoleInfo(@RequestBody OuterRole param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.delRoleInfo(param));
        }catch (Exception e) {
            response.setErrorMsg("删除角色失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("getRoleListGroupBySystem")
    @ResponseBody
    public JsonResponse<Object> getRoleListGroupBySystem(@RequestBody User param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.getRoleListGroupBySystem(param));
        }catch (Exception e) {
            response.setErrorMsg("获取角色列表失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("saveOuterRoleUserMap")
    @ResponseBody
    public JsonResponse<Object> saveOuterRoleUserMap(@RequestBody Map<String, Object> param) {
        JsonResponse<Object> response = new JsonResponse<>();
        try {
            response.setResult(service.saveOuterRoleUserMap(param));
        }catch (Exception e) {
            response.setErrorMsg("保存角色失败");
            e.printStackTrace();
        }
        return response;
    }

    @PostMapping("checkGQJLRole")
    @ResponseBody
    public JsonResponse<Map<String, Object>> checkGQJLRole(HttpServletRequest request, @RequestBody Map<String, Object> param) {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        JsonResponse<Map<String, Object>> response = service.checkGQJLRole(param, token);
        return response;
    }

    /**
     * 导入角色，上线后废弃
     *
     * @param files 文件
     */
    @PostMapping("/importRoleInfo")
    public JsonResponse<Integer> importRoleInfo(@RequestParam("files") List<MultipartFile> files) {
        UserInfo userInfo = getUserInfo();
        JsonResponse<Integer> jsonResponse = new JsonResponse<>();
        int result;
        try {
            mqManagerService.setButtonStatus("role","导入角色","0",userInfo.getPersonName());
            result = infoSyncService.importRoleInfo(files);
            jsonResponse.setSuccess(true);
            jsonResponse.setResult(result);
        } catch (Exception e) {
            // 清表
            OuterRoleExample outerRoleExample = new OuterRoleExample();
            outerRoleExample.createCriteria();
            outerRoleMapper.deleteByExample(outerRoleExample);
            mqManagerService.setButtonStatus("role","导入角色","1",userInfo.getPersonName());
            jsonResponse.setSuccess(false);
            jsonResponse.setErrorMsg(e.getMessage());
        }
        return jsonResponse;
    }

    @PostMapping("/getStockRoleList")
    public JsonResponse<List<OuterRole>> getStockRoleList() {
        JsonResponse<List<OuterRole>> jsonResponse = new JsonResponse<>();
        jsonResponse.setResult(service.getStockRoleList());
        return jsonResponse;
    }


}
