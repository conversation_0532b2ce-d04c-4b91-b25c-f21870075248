package com.stock.service.platform.outer.service;

import com.alibaba.fastjson.JSONObject;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.Page;
import com.stock.core.dto.TreeDto;
import com.stock.core.service.BaseService;
import com.stock.core.util.BeanUtil;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.CodeTableConstant;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dao.*;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.*;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.service.QuestionnaireService;
import com.stock.service.platform.common.sysCodeConvert.CodeConvert;
import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.common.util.SaSystemUtil;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.person.dto.PersonDto;
import com.stock.service.platform.user.dto.RoleListDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RoleOuterService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(RoleOuterService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OuterRoleMapper outerRoleMapper;

    @Autowired
    private OuterRouterMapper outerRouterMapper;

    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private OuterRoleUserMapMapper outerRoleUserMapMapper;

    @Autowired
    private SaSystemUtil saSystemUtil;

    @Autowired
    private TopicProducer topicProducer;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private PersonMapper personMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private SaCompanyMapper saCompanyMapper;

    @Autowired
    private CompanyContactMapper companyContactMapper;

    @Resource
    private CompanyMapper companyMapper;

    @Resource
    private OuterRoleBizMapper outerRoleBizMapper;
    @Resource
    private QuestionnaireService questionnaireService;
    @Autowired
    private Environment env;

    @Value("${externalUrl.equity}")
    private String equity;

    @Resource
    private LogService logService;

    @Resource
    private UserRoleMapMapper userRoleMapMapper;


    public Object getRoleList(HttpServletRequest request, OuterRole param) {
        param.setStartRow((param.getStartRow()-1)* param.getPageSize());
        Map<String, Object> result = new HashMap<>();
        OuterRoleExample example = new OuterRoleExample();
        OuterRoleExample.Criteria criteria = example.createCriteria();
        criteria.andSysCodeEqualTo(param.getSysCode());
        if(StringUtils.isNotEmpty(param.getRoleName())) {
            criteria.andRoleNameLike("%" + param.getRoleName() +"%");
        }
        if(StringUtils.isNotEmpty(param.getRoleDesc())) {
            criteria.andRoleDescLike("%" + param.getRoleDesc() +"%");
        }
        Page<OuterRole> list = this.getPageList(param.getStartRow(), param.getPageSize(),
                "com.stock.service.platform.common.dao.OuterRoleMapper.selectByExample", example);
        List<OuterRole> data = list.getData();
        result.put("total", list.getTotal());
        result.put("tableData", data);
        // 获取当前系统的所有权限列表
        OuterRouterExample routerExample = new OuterRouterExample();
        routerExample.createCriteria().andSysCodeEqualTo(param.getSysCode());
        long count = outerRouterMapper.countByExample(routerExample);
        result.put("needAuth", count > 0);
        return result;
    }

    public Object cuRoleInfo(OuterRole param) throws Exception {
        if (StringUtils.isEmpty(param.getSysCode())) {
            throw new Exception("未获取到系统来源");
        }
        OuterRole record = new OuterRole();
        record.setId(param.getId());
        record.setRoleName(param.getRoleName());
        record.setRoleDesc(param.getRoleDesc());
        if(StringUtils.isNotEmpty(param.getId())) {
            outerRoleMapper.updateByPrimaryKeySelective(record);
//            //向其他微服发送消息
//            String uid = UUID.randomUUID().toString();
//            MQMsgDto<OuterRole> send = new MQMsgDto<>();
//            send.setUid(uid);
//            send.setSystemType(param.getSysCode());
//            send.setBusinessType(TopicConstant.BUS_TYPE_ROLE);
//            send.setOperationType(TopicConstant.OP_TYPE_UPDATE);
//            send.setInfo(record);
//            saSystemUtil.insertMqMessage(send);
//            topicProducer.sendTopic(record, param.getSysCode(), TopicConstant.BUS_TYPE_ROLE, TopicConstant.OP_TYPE_UPDATE,uid);
        }else {
            record.setSysCode(param.getSysCode());
            record.setSource(CodeTableConstant.DATA_STATUS_0);
            outerRoleMapper.insertSelective(record);
//            //向其他微服发送消息
//            String uid = UUID.randomUUID().toString();
//            MQMsgDto<OuterRole> send = new MQMsgDto<>();
//            send.setUid(uid);
//            send.setSystemType(param.getSysCode());
//            send.setBusinessType(TopicConstant.BUS_TYPE_ROLE);
//            send.setOperationType(TopicConstant.OP_TYPE_INSERT);
//            send.setInfo(record);
//            saSystemUtil.insertMqMessage(send);
//            topicProducer.sendTopic(record, param.getSysCode(), TopicConstant.BUS_TYPE_ROLE, TopicConstant.OP_TYPE_INSERT,uid);
        }
        return true;
    }

    public Object delRoleInfo(OuterRole param) throws Exception {
        if (StringUtils.isEmpty(param.getSysCode())) {
            throw new Exception("未获取到系统来源");
        }
        outerRoleMapper.deleteByPrimaryKey(param.getId());
        return true;
    }

    public Object getRoleListGroupBySystem(User param) {
        Map<String, Object> result = new HashMap<>();
        // 获取所有接入系统的角色
        List<OuterRole> list = outerRoleMapper.selectRole(param.getUserType());
        String parentCompanyId = "";
        if ("1".equals(param.getUserType())) {
            parentCompanyId = outerRoleMapper.getParentCompanyId(param.getId());
        }
        List<TreeDto> roleTree = new ArrayList<>();
        if (StringUtils.isNotEmpty(parentCompanyId)) {
            list.forEach(item -> {
                if (!"TDGF".equals(item.getSysCode())) {
                    TreeDto dto = new TreeDto();
                    dto.setId(item.getId());
                    dto.setName(item.getRoleName());
                    dto.setValue(item.getId());
                    dto.setFlag(item.getSysCode());
                    roleTree.add(dto);
                }
            });
        } else {
            list.forEach(item -> {
                TreeDto dto = new TreeDto();
                dto.setId(item.getId());
                dto.setName(item.getRoleName());
                dto.setValue(item.getId());
                dto.setFlag(item.getSysCode());
                roleTree.add(dto);
            });
        }
        // 按系统分组
        Map<String, List<TreeDto>> map = roleTree.stream().collect(Collectors.groupingBy(TreeDto::getFlag));
        // 获取所有系统
        SystemConfigExample configExample = new SystemConfigExample();
        configExample.createCriteria().andSysCodeIsNotNull();
        List<SystemConfig> configList = systemConfigMapper.selectByExample(configExample);
        // 将系统转换为树的首层
        List<TreeDto> roles = new ArrayList<>();
        if (StringUtils.isNotEmpty(parentCompanyId)) {
            configList.forEach(item -> {
                if (!"TDGF".equals(item.getSysCode())) {
                    TreeDto dto = new TreeDto();
                    dto.setId(item.getSysCode());
                    dto.setValue(item.getSysCode());
                    dto.setName(item.getSysName());
                    dto.setChildren(map.get(item.getSysCode()));
                    roles.add(dto);
                }
            });
        } else {
            configList.forEach(item -> {
                TreeDto dto = new TreeDto();
                dto.setId(item.getSysCode());
                dto.setValue(item.getSysCode());
                dto.setName(item.getSysName());
                dto.setChildren(map.get(item.getSysCode()));
                roles.add(dto);
            });
        }
        result.put("roles", roles);
        // 获取用户拥有的所有接入系统角色
        // 获取角色树
        OuterRoleUserMapExample mapExample = new OuterRoleUserMapExample();
        mapExample.createCriteria().andUserIdEqualTo(param.getId());
        List<OuterRoleUserMap> mapList = outerRoleUserMapMapper.selectByExample(mapExample);
        List<String> checkedRoleIds = mapList.stream().map(OuterRoleUserMap::getRoleId).collect(Collectors.toList());
        result.put("checkedRoleIds", checkedRoleIds);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object saveOuterRoleUserMap(Map<String, Object> param) throws IllegalAccessException {
        String userId = MapUtils.getString(param, "userId");
        if(StringUtils.isNotEmpty(userId)) {
            // 先删除这个用户对应的权限
            OuterRoleUserMapExample mapExample = new OuterRoleUserMapExample();
            mapExample.createCriteria().andUserIdEqualTo(userId);
            List<OuterRoleUserMap> outerRoleUserMaps = outerRoleUserMapMapper.selectByExample(mapExample);
            List<String> roleIdsOld = outerRoleUserMaps.stream().map(OuterRoleUserMap::getRoleId).collect(Collectors.toList());
            //String roleIdsOldStr = String.join(",", roleIdsOld);
            outerRoleUserMapMapper.deleteByExample(mapExample);
            // 获取所有选中的角色编号
            List<String> checkedRoleIds = (List<String>) param.get("checkedRoleIds");
            //String roleIdsNewStr = String.join(",", checkedRoleIds);
            User user = userMapper.selectByPrimaryKey(userId);
            if(CollectionUtils.isNotEmpty(checkedRoleIds)) {
                // 插入
                OuterRoleExample roleExample = new OuterRoleExample();
                roleExample.createCriteria().andIdIn(checkedRoleIds);
                List<OuterRole> roleList = outerRoleMapper.selectByExample(roleExample);
                List<OuterRoleUserMap> recordList = new ArrayList<>();
                List<String> systemStateCode = new ArrayList<>();
                roleList.forEach(item -> {
                    OuterRoleUserMap record = new OuterRoleUserMap();
                    record.setRoleId(item.getId());
                    record.setUserId(userId);
                    record.setSource(CodeTableConstant.DATA_STATUS_0);
                    record.setSysCode(item.getSysCode());
                    systemStateCode.add(item.getSysCode());
                    logger.info(MapUtils.getString(param, "userType") + "==============================================================");
                    if ("1".equals(MapUtils.getString(param, "userType"))) {
                        record.setCompanyCode(outerRoleUserMapMapper.getCompanyCodeByUserId(userId));
                    }
                    outerRoleUserMapMapper.insertSelective(record);
                    recordList.add(record);
                });
                // 将合并历史改为不可解合并
                outerRoleUserMapMapper.updateMergeFlag(userId);
                //向其他微服发送消息
                String sysCodes = "";
                for (OuterRole item : roleList) {
                    if (!(sysCodes.contains(item.getSysCode()))) {
                        sysCodes += item.getSysCode() + ",";
                    }
                }
                sysCodes = sysCodes.substring(0, sysCodes.length() - 1);
                PersonWithBLOBs personWithBLOBs = personMapper.selectByUserId(user.getId());
                OrgExample example = new OrgExample();
                example.createCriteria().andIdIn(Arrays.asList(personWithBLOBs.getOrgId().split(",")));
                List<Org> orgList = orgMapper.selectByExample(example);
                Org org = orgList.get(0);
                //List<Org> orgListTopic = selPersonOrg(personWithBLOBs.getOrgId());
                OrgExample orgExample = new OrgExample();
                orgExample.createCriteria().andIdIn(Arrays.asList(personWithBLOBs.getOrgId().split(",")));
                List<Org> orgListTopic = orgMapper.selectByExample(orgExample);
                PersonDto personDto = new PersonDto();
                BeanUtil.copy(personWithBLOBs, personDto);
                Profiles profiles1 = Profiles.of("dev", "prepub-sz");
                if (!env.acceptsProfiles(profiles1)) {
                    personDto.setTelephone(questionnaireService.deCodeAes(personDto.getTelephone()));
                } else {
                    personDto.setTelephone(personDto.getTelephone());
                }
                personDto.setOrgIdList(Arrays.asList(personDto.getOrgId().split(",")));
                personDto.setUserName(user.getUserName());
                personDto.setUserType(user.getUserType());
                personDto.setUserTypeCode(user.getUserType());
                personDto.setIsAdmin(user.getIsAdmin());
                personDto.setLockType(user.getLockType());
                personDto.setRealName(user.getRealName());
                personDto.setPostion(personDto.getJobs());
                personDto.setPosition(personDto.getJobs());
                personDto.setPhone(personDto.getTelephone());
                personDto.setCompanyId(user.getCompanyId());
                personDto.setIsMgr(orgListTopic.get(0).getOrgType());
                Map<String,Object> topic = new HashMap<>();
                Map<String,Object> topicTD = new HashMap<>();
                topic.put("user", user);
                topic.put("oldUserName", personDto.getUserName());
                topic.put("roleList", roleList);
                topic.put("roleUserMapList", recordList);
                topic.put("person",personDto);
                topicTD.put("user", user);
                topicTD.put("person",personDto);
                topicTD.put("oldUserName", personDto.getUserName());

                //判断内外部
                if ("4".equals(org.getOrgType())) {
                    SaCompanyDto saCompany = saCompanyMapper.getCompanyById(org.getCompanyId());
                    Map<String, String> subsystemCodes = CodeConvert.convertCode(saCompany.getMarketStockType(), "marketStockType");
                    saCompany.setMarketStockTypeGQJL(subsystemCodes.get("GQJL"));
                    List<String> systemStateName = outerRoleUserMapMapper.getSystemStateName(systemStateCode);
                    Set<String> uniqueSystemStateNames = new HashSet<>(systemStateName); // 将List转换为Set以去除重复元素
                    String companySystem = saCompany.getSystemState();
                    if (StringUtils.isNotEmpty(companySystem)) {
                        uniqueSystemStateNames.addAll(Arrays.asList(companySystem.split(","))); // 添加新的元素，如果有重复，将被忽略
                    }
                    List<String> distinctSystemStateName = new ArrayList<>(uniqueSystemStateNames);
                    CompanyExample companyExample = new CompanyExample();
                    companyExample.createCriteria().andIdEqualTo(org.getCompanyId());
                    Company company = new Company();
                    company.setSystemState(String.join(",", distinctSystemStateName));
                    companyMapper.updateByExampleSelective(company, companyExample);
                    CompanyContactExample coCoExample = new CompanyContactExample();
                    coCoExample.createCriteria().andCompanyCodeEqualTo(saCompany.getCompanyCode());
                    List<CompanyContact> companyContacts = companyContactMapper.selectByExample(coCoExample);
                    StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompany);
                    topic.put("saCompany",saCompany);
                    topic.put("saCompanyContacts",companyContacts);
                    topicTD.put("saCompany",saCompany);
                    topicTD.put("saCompanyContacts",companyContacts);

                }
                topic.put("org",orgListTopic);
                topicTD.put("org",orgListTopic);
                int maxLevel;
                String id;
                List<Org> orgPList = new ArrayList<>();
                for (Org orgTopic : orgListTopic) {
                    id = orgTopic.getpOrgId();
                    maxLevel = orgMapper.selOrgLevel();
                    for (int i = 0; i <= maxLevel; i++) {
                        Org orgP = orgMapper.selParentById(id);
                        if (orgP == null) {
                            break;
                        }
                        orgPList.add(orgP);
                        if ("1".equals(orgP.getLevel()) || "1".equals(orgP.getOrgType())) {
                            break;
                        }
                        id = orgP.getpOrgId();
                    }
                }
                topic.put("orgP", orgPList);
                topic.put("userId", getUserInfo().getInfo().get("id"));
                topicTD.put("orgP", orgPList);
                topicTD.put("userId", getUserInfo().getInfo().get("id"));
                String uid = UUID.randomUUID().toString();
                String uidTD = UUID.randomUUID().toString();
                MQMsgDto<Map<String,Object>> send = new MQMsgDto<>();
                send.setUid(uid);
                send.setSystemType(sysCodes);
                send.setBusinessType(TopicConstant.BUS_TYPE_PERMISSION);
                send.setOperationType(TopicConstant.OP_TYPE_INSERT);
                send.setInfo(topic);
                MQMsgDto<Map<String,Object>> sendTD = new MQMsgDto<>();
                sendTD.setUid(uidTD);
                sendTD.setSystemType("TDGF");
                sendTD.setBusinessType(TopicConstant.BUS_TYPE_USER);
                sendTD.setOperationType(TopicConstant.OP_TYPE_INSERT);
                sendTD.setInfo(topicTD);
                try {
                    if (sysCodes.contains("TDGF")) {
                        // 给合规专用的
                        saSystemUtil.insertMqMessage(sendTD);
                        topicProducer.sendTopic(topicTD, "TDGF", TopicConstant.BUS_TYPE_USER, TopicConstant.OP_TYPE_INSERT,uidTD);
                    }
                    saSystemUtil.insertMqMessage(send);
                    topicProducer.sendTopic(topic, sysCodes, TopicConstant.BUS_TYPE_PERMISSION, TopicConstant.OP_TYPE_INSERT,uid);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            //操作日志
            try {
                RoleListDto roleListNew = new RoleListDto();
                roleListNew.setRoleList(userRoleMapMapper.getRoleNameByRoleIds(checkedRoleIds));
                RoleListDto roleListOld = new RoleListDto();
                roleListOld.setRoleList(userRoleMapMapper.getRoleNameByRoleIds(roleIdsOld));

                MessageUtil messageUtil = new MessageUtil();
                MessageInfo messageInfo = messageUtil.getMessageContext(roleListNew, roleListOld);
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(param),
                                null, "设置接入系统角色",
                                "系统管理/人员信息", "RoleOuterService.saveOuterRoleUserMap",
                                "设置用户" + user.getUserName() + "的接入系统角色{" + messageInfo.getOperationRemark() + "}", ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResponse<Map<String, Object>> checkGQJLRole( Map<String, Object> param, String token) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        if ("1".equals(param.get("userType"))) {
            String userId = MapUtils.getString(param, "userId");
            String userName = MapUtils.getString(param, "userName");
            String userType = MapUtils.getString(param, "userType");
            List<String> checkedRoleIds = (List<String>) param.get("checkedRoleIds");

            OuterRoleExample roleExample = new OuterRoleExample();
            roleExample.createCriteria().andIdIn(checkedRoleIds);
            List<OuterRole> roleList = outerRoleMapper.selectByExample(roleExample);

            List<OuterRoleUserMap> recordList = new ArrayList<>();

            roleList.forEach(item -> {
                OuterRoleUserMap record = new OuterRoleUserMap();
                record.setRoleId(item.getId());
                record.setUserId(userId);
                record.setSource(CodeTableConstant.DATA_STATUS_0);
                record.setSysCode(item.getSysCode());
                if ("1".equals(userType)) {
                    record.setCompanyCode(outerRoleUserMapMapper.getCompanyCodeByUserId(userId));
                }
                recordList.add(record);
            });

            String url = equity + "/data/soi/checkSignRole?accessToken=" + token;
            logger.info("股权激励校验角色接口----------->url：" + url);

            Map<String, Object> map = new HashMap<>();
            map.put("userName", userName);
            map.put("rolesMaps", recordList);
            logger.info("股权激励校验角色接口----------->传参：" + map);
            response = restTemplate.postForObject(url,map ,JsonResponse.class);
            logger.info("股权激励校验角色接口----------->结果：" + response);
        } else {
            response.setSuccess(true);
        }
        return response;
    }

    public List<OuterRole> getStockRoleList() {
        return outerRoleBizMapper.getStockRoleList();
    }
}
