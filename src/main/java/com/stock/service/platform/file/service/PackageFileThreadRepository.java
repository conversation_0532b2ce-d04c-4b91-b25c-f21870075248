package com.stock.service.platform.file.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Service
public class PackageFileThreadRepository {

    private static final Logger logger = LoggerFactory.getLogger(PackageFileThreadRepository.class);

    private final Object lock = new Object();

    private HashMap<String, Thread> storage = new HashMap<>(32);

    /**
     * 放入
     * @param id
     * @param thread
     * @return
     */
    public Thread put(String id,Thread thread){
        synchronized (lock){
            return storage.put(id,thread);
        }
    }

    /**
     * 获取线程
     * @param id
     */
    public Thread get(String id){
        Thread thread = null;
        synchronized (lock) {
            thread = storage.get(id);
            logger.info("thread is {},", thread);
        }
        return thread;
    }

    /**
     * 移除线程
     * @param id
     * @return
     */
    public Thread remove(String id){
        synchronized (lock) {
            return storage.remove(id);
        }
    }


}
