package com.stock.service.platform.file.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 文件dto
 */
public class FileDto {

    /**
     * id
     */
    private String id;

    /**
     * 文件id
     */
    private String attUrl;

    /**
     * 文件名字
     */
    private String attName;

    /**
     * 文件大小
     */
    private String size;

    /**
     * 文件类型
     */
    private String documentType;

    /**
     * 版本号
     */
    private String version;

    /**
     * 事项id
     */
    @NotBlank(message = "事项id为空", groups = TransferFileGroup.class)
    private String businessId;


    /**
     * id列表
     */
    @NotNull(message = "id列表为空", groups = TransferFileGroup.class)
    private List<String> attUrls;

    /**
     * 是否逻辑删除
     */
    private boolean logicDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 业务类型
     */
    private String businessType;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAttUrl() {
        return attUrl;
    }

    public void setAttUrl(String attUrl) {
        this.attUrl = attUrl;
    }

    public String getAttName() {
        return attName;
    }

    public void setAttName(String attName) {
        this.attName = attName;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public List<String> getAttUrls() {
        return attUrls;
    }

    public void setAttUrls(List<String> attUrls) {
        this.attUrls = attUrls;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public boolean isLogicDelete() {
        return logicDelete;
    }

    public void setLogicDelete(boolean logicDelete) {
        this.logicDelete = logicDelete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public static interface TransferFileGroup {

    }

    public static interface DeleteFileGroup {

    }
}
