package com.stock.service.platform.file.service;

import com.stock.core.dao.RedisDao;
import com.stock.service.platform.common.constant.RedisKeyConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class CancelPackageSubscriberService implements MessageListener {

    @Autowired
    PackageFileThreadRepository repository;

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    RedisDao redisDao;


    @Override
    public void onMessage(Message message, byte[] pattern) {
        byte[] body = message.getBody();
        byte[] channel = message.getChannel();
        // 请使用valueSerializer
        String id = (String) redisTemplate.getStringSerializer().deserialize(body);
        String topic = (String) redisTemplate.getStringSerializer().deserialize(channel);
        if(Objects.equals("cancelPackage",topic)){
            id= id.replaceAll("\"","");
            Thread thread = repository.get(id.replaceAll("\"",""));
            if(thread != null){
                thread.interrupt();
                // 设置错误信息
                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"success","false");
                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"finish","true");
                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"errorMsg","主动取消打包任务");
            }
        }
    }
}
