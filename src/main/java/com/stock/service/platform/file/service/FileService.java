//package com.stock.servicePlatform.file.service;
//
//import com.stock.core.dao.RedisDao;
//import com.stock.core.file.FileServer;
//import com.stock.core.util.BeanUtil;
//import com.stock.core.util.JsonUtil;
//import com.stock.servicePlatform.common.constant.RedisKeyConstant;
//import com.stock.servicePlatform.common.dao.AttachmentMapper;
//import com.stock.servicePlatform.common.entity.Attachment;
//import com.stock.servicePlatform.common.util.PrimaryKeyUtils;
//import com.stock.servicePlatform.file.dto.FileDto;
//import com.stock.servicePlatform.file.dto.FilePackageStatusDto;
//import com.stock.servicePlatform.file.listener.event.PackageFileEvent;
//import org.apache.commons.compress.archivers.ArchiveEntry;
//import org.apache.commons.compress.archivers.zip.Zip64Mode;
//import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
//import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
//import org.apache.commons.io.IOUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.ApplicationEventPublisher;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.ServletOutputStream;
//import javax.servlet.http.HttpServletResponse;
//import java.io.*;
//import java.net.URL;
//import java.net.URLConnection;
//import java.nio.charset.Charset;
//import java.nio.charset.StandardCharsets;
//import java.util.*;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipOutputStream;
//
//import static org.springframework.util.StreamUtils.BUFFER_SIZE;
//
//@Service
//public class FileService{
//
//    private static Logger logger = LoggerFactory.getLogger(FileService.class);
//
//    @Autowired
//    private AttachmentMapper attachmentMapper;
//
//    @Autowired
//    private FileServer fileServer;
//
//    @Autowired
//    RedisDao redisDao;
//
//    @Autowired
//    private ApplicationEventPublisher applicationEventPublisher;
//
//    @Value("${file.basePath}")
//    private String basePath;
//
//    /**
//     * 上传临时文件
//     *
//     * @param fileList 文件列表
//     * @return
//     */
//    public List<FileDto> uploadTempFile(List<MultipartFile> fileList) throws IOException {
//        List<FileDto> fileDtoList = new ArrayList<>();
//        for (MultipartFile file : fileList) {
//            FileDto fileDto = commonUploadFiles(file, "temp");
//            if (fileDto != null) {
//                fileDtoList.add(fileDto);
//            }
//        }
//        return fileDtoList;
//    }
//
//    /**
//     * 上传正式文件
//     *
//     * @param fileList   文件列表
//     * @param businessId 业务id
//     * @return
//     */
//    public List<FileDto> uploadFile(List<MultipartFile> fileList, String businessId, String businessType) throws IOException {
//        List<FileDto> fileDtoList = new ArrayList<>();
//        for (MultipartFile file : fileList) {
//            FileDto fileDto = commonUploadFiles(file, "formal");
//            // 处理添加的实体
//            doAddFileDto(fileDtoList, fileDto, null, businessType, businessId);
//        }
//        return fileDtoList;
//    }
//
//
//    /**
//     * 上传文件
//     * 从测试 -> 正式
//     *
//     * @param fileList   文件列表
//     * @param businessId 业务id
//     * @return
//     */
//    public List<FileDto> transferTempToFormal(List<String> fileList, String businessId, String businessType) throws IOException {
//        List<FileDto> fileDtoList = new ArrayList<>();
//        for (String fileId : fileList) {
//            // 获取临时文件
//            FileDto fileDto = getFileInfo(fileId);
//            if (fileDto != null) {
//                InputStream in = null;
//                FileDto dto = null;
//                try {
//                    in = getFile(fileId);
//                    // 推入到正式文件桶中
//                    dto = uploadSingleFile(in, fileDto.getAttName(), fileDto.getDocumentType());
//                } finally {
//                    if (in != null) {
//                        in.close();
//                    }
//                }
//                if (dto != null) {
//                    doAddFileDto(fileDtoList, dto, fileDto, businessType, businessId);
//                }
//            }
//        }
//        return fileDtoList;
//    }
//
//    private FileDto uploadSingleFile(InputStream in, String attName, String documentType) throws IOException {
//        InputStream metaInputStream = null;
//        try (ByteArrayOutputStream copy = new ByteArrayOutputStream()) {
//            IOUtils.copy(in, copy);
//            byte[] bytes = copy.toByteArray();
//            // 取得新的文件名
//            String newFileId = fileServer.handleFileName(attName);
//            // 上传文件到目录
//            String fileId = fileServer.upload(newFileId, "/formal", new ByteArrayInputStream(bytes));
//            FileDto fileDto = new FileDto();
//            fileDto.setAttUrl(fileId);
//            fileDto.setVersion("0");
//            fileDto.setCreateTime(new Date());
//            fileDto.setAttName(attName);
//            fileDto.setDocumentType(documentType);
//
//            // 获取文件的大小
//            fileDto.setSize(String.valueOf(bytes.length));
//            metaInputStream = new ByteArrayInputStream(JsonUtil.toJson(fileDto).getBytes(StandardCharsets.UTF_8));
//            // 上传 .meta文件
//            fileServer.upload(newFileId + ".meta", "/formal", metaInputStream);
//            return fileDto;
//        } finally {
//            if (in != null) {
//                in.close();
//            }
//            if (metaInputStream != null) {
//                metaInputStream.close();
//            }
//        }
//    }
//
//    /**
//     * 增加fileDto的返回
//     *
//     * @param fileDtoList
//     * @param fileDto
//     * @param businessType
//     * @param businessId
//     */
//    private void doAddFileDto(List<FileDto> fileDtoList, FileDto fileDto, FileDto tempResult, String businessType, String businessId) throws IOException {
//        if (fileDto != null) {
//            // 设置附件
//            Attachment attachment = getAttachmentWithFileObjectResult(fileDto, tempResult, businessId, businessType);
//            // 新增
//            attachmentMapper.insertSelective(attachment);
//            BeanUtil.copy(attachment, fileDto);
//            fileDto.setBusinessType(attachment.getAttType());
//            fileDtoList.add(fileDto);
//        }
//    }
//
//    /**
//     * 通过上传的文件结果生成attachment
//     *
//     * @param result     正式
//     * @param tempResult 临时
//     * @param businessId 业务id
//     * @param attType
//     * @return
//     */
//    private Attachment getAttachmentWithFileObjectResult(FileDto result, FileDto tempResult, String businessId, String attType) {
//        Attachment attachment = new Attachment();
//        attachment.setId(result.getId());
//        attachment.setAttName(result.getAttName());
//        attachment.setAttUrl(result.getAttUrl());
//        attachment.setBusinessId(businessId);
//        attachment.setAttType(attType);
//        attachment.setDocumentType(result.getDocumentType());
//        attachment.setSize(Long.valueOf(result.getSize()));
//        attachment.setVersion(result.getVersion());
//        // 临时结果不为空，说明是转移的文件
//        if (tempResult != null) {
//            attachment.setCreateTime(tempResult.getCreateTime());
//        } else {
//            attachment.setCreateTime(result.getCreateTime());
//        }
//        return attachment;
//    }
//
//    /**
//     * 文件id列表
//     *
//     * @param fileIdList
//     * @return
//     */
//    public boolean deleteFile(List<String> fileIdList) {
//        for (String fileId : fileIdList) {
//            fileServer.delete(fileId);
//        }
//        return true;
//    }
//
//    /**
//     * 获取文件
//     *
//     * @param filePath
//     * @return
//     */
//    public InputStream getFile(String filePath) throws IOException {
//        return fileServer.download(filePath);
//    }
//
//    /**
//     * 获取文件
//     *
//     * @param attUrl
//     * @return
//     */
//    public FileDto getFileInfo(String attUrl) throws IOException {
//        InputStream inputStream = null;
//        try {
//            inputStream = fileServer.download(attUrl + ".meta");
//            byte[] bytes = IOUtils.toByteArray(inputStream);
//            return JsonUtil.fromJson(new String(bytes, Charset.defaultCharset()), FileDto.class);
//        } finally {
//            if (inputStream != null) {
//                inputStream.close();
//            }
//        }
//    }
//
//    /**
//     * 删除正式文件
//     *
//     * @param attachmentId
//     * @return
//     * <AUTHOR>
//     * @date 2021年11月3日 上午11:05:08
//     */
//    public boolean deleteFile(String attachmentId) {
//        Attachment attachment = attachmentMapper.selectByPrimaryKey(attachmentId);
//        if (attachment != null) {
//            // 删除文件
//            fileServer.delete(attachment.getAttUrl());
//            // 删除附件表数据
//            int deleteFlag = attachmentMapper.deleteByPrimaryKey(attachmentId);
//            return deleteFlag > 0;
//        }
//        return true;
//    }
//
//    public FileDto commonUploadFiles(MultipartFile file, String filePath) throws IOException {
//        InputStream metaInputStream = null;
//        InputStream inputStream = null;
//        FileDto fileDto;
//        try (ByteArrayOutputStream copy = new ByteArrayOutputStream()) {
//            inputStream = file.getInputStream();
//            IOUtils.copy(inputStream, copy);
//            byte[] bytes = copy.toByteArray();
//            // 取得新的文件名
//            String newFileId = fileServer.handleFileName(file.getOriginalFilename());
//            // 上传文件到目录
//            String attUrl = fileServer.upload(newFileId, filePath, new ByteArrayInputStream(bytes));
//
//            fileDto = new FileDto();
//            fileDto.setId(PrimaryKeyUtils.getPrimaryKeyForString());
//            fileDto.setAttUrl(attUrl);
//            fileDto.setVersion("0");
//            fileDto.setCreateTime(new Date());
//            fileDto.setAttName(file.getOriginalFilename());
//            fileDto.setDocumentType(file.getContentType());
//
//            // 获取文件的大小
//            fileDto.setSize(String.valueOf(bytes.length));
//            metaInputStream = new ByteArrayInputStream(JsonUtil.toJson(fileDto).getBytes(StandardCharsets.UTF_8));
//            // 上传 .meta文件
//            fileServer.upload(newFileId + ".meta", filePath, metaInputStream);
//            return fileDto;
//        } finally {
//            if (inputStream != null) {
//                inputStream.close();
//            }
//            if (metaInputStream != null) {
//                metaInputStream.close();
//            }
//        }
//    }
//
//    /**
//     * 异步打包
//     * 返回id
//     *
//     * @return
//     */
//    public String packageZipAsync() {
//        String id = UUID.randomUUID().toString();
//        applicationEventPublisher.publishEvent(new PackageFileEvent(this, id));
//        return id;
//    }
//
//    /**
//     * 获取打包状态
//     *
//     * @return
//     */
//    public FilePackageStatusDto getPackageStatus(String id) {
//        Map<String, Object> result = redisDao.getHashObject(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX + id);
//        if (result == null || result.isEmpty()) {
//            FilePackageStatusDto dto = new FilePackageStatusDto();
//            dto.setFinish(false);
//            return dto;
//        } else {
//            return JsonUtil.fromJson(JsonUtil.toJson(result), FilePackageStatusDto.class);
//        }
//    }
//
//    public Boolean cancelPackage(String id) {
//        redisDao.publishMessage("cancelPackage", id);
//        return true;
//    }
//
//    /**
//     * 打包方法
//     * 云端下载文件 本地打包成zip 上传到自己的文件服务器上
//     */
//    public String packageZip() throws IOException, InterruptedException {
//        ByteArrayOutputStream outputStream = null;
//        InputStream in = null;
//        ZipOutputStream zipOutputStream = null;
//        FileInputStream fileInputStream = null;
//        FileInputStream newInputStream = null;
//        File file = null;
//        try {
//            URL url = new URL("https://dev-sz.valueonline.cn/capital-cloud//open/da90c89f-5004-40cf-83ed-f3c5f27ae772.mp4");
//            URLConnection connection = url.openConnection();
//            connection.setConnectTimeout(999999999);
//            connection.setReadTimeout(999999999);
//            connection.connect();
//            in = connection.getInputStream();
//            outputStream = new ByteArrayOutputStream();
//            IOUtils.copy(in, outputStream);
//            if(Thread.currentThread().isInterrupted()){
//                throw new InterruptedException();
//            }
//            zipOutputStream = new ZipOutputStream(outputStream);
//            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
//            zipOutputStream.putNextEntry(new ZipEntry("a.zip"));
//            file = new File("d:\\a.zip");
//            if(!file.exists()){
//                file.createNewFile();
//            }
//            fileInputStream = new FileInputStream(file);
//            int len;
//            byte[] buf = new byte[BUFFER_SIZE];
//            while ((len = fileInputStream.read(buf)) != -1) {
//                zipOutputStream.write(buf, 0, len);
//            }
//            file = new File("d:\\a.zip");
//            if(!file.exists()){
//                file.createNewFile();
//            }
//
//            newInputStream = new FileInputStream(file);
//            if(Thread.currentThread().isInterrupted()){
//                throw new InterruptedException();
//            }
//            return fileServer.upload(fileServer.handleFileName("xxx.zip"), "/formal", newInputStream);
//        } finally {
//            try {
//                if (file != null && file.exists()) {
//                    file.delete();
//                }
//                if (outputStream != null) {
//                    outputStream.close();
//                }
//                if (in != null) {
//                    in.close();
//                }
//                if (zipOutputStream != null) {
//                    zipOutputStream.closeEntry();
//                    zipOutputStream.close();
//                }
//                if (fileInputStream != null) {
//                    fileInputStream.close();
//                }
//                if (newInputStream != null) {
//                    newInputStream.close();
//                }
//            } catch (IOException e) {
//            }
//        }
//    }
//
//    /**
//     * 多个文件打包下载
//     *
//     */
//    public void packageZip(List<Attachment> list, String zipName, HttpServletResponse response) throws Exception{
//        // 设置response参数
//        response.reset();
//        response.setContentType("application/octet-stream;charset=UTF-8");
//        response.setHeader("Content-Disposition", "attachment;filename=" + new String((zipName + ".zip").getBytes(), "iso-8859-1"));
//        ServletOutputStream out = response.getOutputStream();
//        ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream (out);
//        zipOut.setUseZip64(Zip64Mode.AsNeeded);
//        for (Attachment attachment: list) {
//            File file = new File(basePath + attachment.getAttUrl());
//
//            String fileName = attachment.getAttName();
//            InputStream inputStream = new FileInputStream(file);
//
//            ByteArrayOutputStream baOut = new ByteArrayOutputStream();
//            byte[] buffer = new byte[BUFFER_SIZE];
//            int len;
//            while ((len = inputStream.read(buffer)) != -1) {
//                baOut.write(buffer, 0, len);
//            }
//            if (baOut != null) { baOut.flush(); }
//
//            byte[] bytes = baOut.toByteArray();
//            //设置文件名
//            ArchiveEntry entry = new ZipArchiveEntry(fileName);
//            zipOut.putArchiveEntry(entry);
//            zipOut.write(bytes);
//            zipOut.closeArchiveEntry();
//            if (baOut != null) { baOut.close(); }
//        }
//
//        if(zipOut!=null) { zipOut.close(); }
//    }
//}
