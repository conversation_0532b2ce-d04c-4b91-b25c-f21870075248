package com.stock.service.platform.file.listener.event;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * 上传文件事件
 */
public class PackageFileEvent extends ApplicationEvent {

    private String id;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public PackageFileEvent(Object source) {
        super(source);
    }

    public PackageFileEvent(Object source, String id) {
        super(source);
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
