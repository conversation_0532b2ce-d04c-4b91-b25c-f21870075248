//package com.stock.servicePlatform.file.controller;
//
//import com.stock.core.controller.BaseController;
//import com.stock.core.dto.JsonResponse;
//import com.stock.core.web.DownloadView;
//import com.stock.servicePlatform.common.dao.AttachmentMapper;
//import com.stock.servicePlatform.common.entity.Attachment;
//import com.stock.servicePlatform.common.service.CommonService;
//import com.stock.servicePlatform.file.dto.FileDto;
//import com.stock.servicePlatform.file.dto.FilePackageStatusDto;
//import com.stock.servicePlatform.file.service.FileService;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Controller;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.servlet.ModelAndView;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.UnsupportedEncodingException;
//import java.util.List;
//
///**
// * <AUTHOR>
// * 文件管理
// */
//@Controller
//@RequestMapping("/file")
//public class FileController extends BaseController {
//
//    @Resource
//    private FileService fileService;
//
//    @Resource
//    private AttachmentMapper attachmentMapper;
//
//    @Resource
//    private CommonService commonService;
//
//    /**
//     * 上传临时文件
//     *
//     * @param multipartFileList
//     * @return
//     */
//    @ResponseBody
//    @PostMapping("uploadTempFiles")
//    public JsonResponse<List<FileDto>> uploadTempFiles(@RequestParam("files") List<MultipartFile> multipartFileList) throws IOException {
//        JsonResponse<List<FileDto>> jsonResponse = new JsonResponse<>();
//        jsonResponse.setResult(fileService.uploadTempFile(multipartFileList));
//        return jsonResponse;
//    }
//
//    /**
//     * 上传文件，带有事项的id
//     *
//     * @param multipartFileList
//     * @param businessId
//     * @return
//     */
//    @ResponseBody
//    @PostMapping("uploadFiles")
//    public JsonResponse<List<FileDto>> uploadFiles(@RequestParam("files") List<MultipartFile> multipartFileList,
//                                                   @RequestParam("businessId") String businessId,
//                                                   @RequestParam("businessType") String businessType) throws IOException {
//        JsonResponse<List<FileDto>> jsonResponse = new JsonResponse<>();
//        jsonResponse.setResult(fileService.uploadFile(multipartFileList, businessId, businessType));
//        return jsonResponse;
//    }
//
//    /**
//     * 上传文件，带有事项的id
//     * 从 临时文件 -> 正式文件
//     *
//     * @return
//     */
//    @ResponseBody
//    @PostMapping("transferTempToFormal")
//    public JsonResponse<List<FileDto>> transferTempToFormal(@Validated(FileDto.TransferFileGroup.class) @RequestBody FileDto fileDto) throws IOException {
//        JsonResponse<List<FileDto>> jsonResponse = new JsonResponse<>();
//        jsonResponse.setResult(fileService.transferTempToFormal(fileDto.getAttUrls(), fileDto.getBusinessId(), fileDto.getBusinessType()));
//        return jsonResponse;
//    }
//
//
//    /**
//     * 删除文件
//     *
//     * @return
//     */
//    @ResponseBody
//    @PostMapping("deleteFiles")
//    public JsonResponse<Boolean> uploadFiles(@Validated(FileDto.DeleteFileGroup.class) @RequestBody FileDto fileDto) {
//        JsonResponse<Boolean> jsonResponse = new JsonResponse<>();
//        jsonResponse.setResult(fileService.deleteFile(fileDto.getAttUrls()));
//        return jsonResponse;
//    }
//
//    /**
//     * 下载文件
//     *
//     * @param attUrl
//     * @return
//     */
//    @GetMapping("downloadFile")
//    public ModelAndView downloadFile(@RequestParam("attUrl") String attUrl) throws IOException {
//        ModelAndView mv = new ModelAndView();
//        FileDto result = null;
//        if (StringUtils.isNotBlank(attUrl)) {
//            result = fileService.getFileInfo(attUrl);
//        }
//        if (result != null) {
//            mv.addObject(DownloadView.EXPORT_FILE, fileService.getFile(attUrl));
//            mv.addObject(DownloadView.EXPORT_FILE_NAME, result.getAttName());
//            mv.addObject(DownloadView.EXPORT_FILE_TYPE, result.getDocumentType());
//            mv.addObject(DownloadView.EXPORT_FILE_SIZE, result.getSize());
//            mv.setView(new DownloadView());
//        }
//        return mv;
//    }
//
//    /**
//     * 打包文件成zip
//     *
//     * @return
//     */
//    @PostMapping("packageZip")
//    @ResponseBody
//    public JsonResponse<String> packageZip() {
//        JsonResponse<String> response = new JsonResponse<>();
//        response.setResult(fileService.packageZipAsync());
//        return response;
//    }
//
//    /**
//     * 获取打包状态
//     *
//     * @param id
//     * @return
//     */
//    @PostMapping("getPackageStatus")
//    @ResponseBody
//    public JsonResponse<FilePackageStatusDto> getPackageStatus(String id) {
//        JsonResponse<FilePackageStatusDto> response = new JsonResponse<>();
//        response.setResult(fileService.getPackageStatus(id));
//        return response;
//    }
//
//    /**
//     * 取消打包
//     *
//     * @param id
//     * @return
//     */
//    @PostMapping("cancelPackage")
//    @ResponseBody
//    public JsonResponse<Boolean> cancelPackage(String id) {
//        JsonResponse<Boolean> response = new JsonResponse<>();
//        response.setResult(fileService.cancelPackage(id));
//        return response;
//    }
//
//
//
//    /**
//     * 通过attId下载文件
//     *
//     * @param attId
//     * @return
//     */
//    @RequestMapping("downloadFileByAttId")
//    public ModelAndView downloadFileByAttId(HttpServletResponse response, @RequestParam("attId") String attId) throws IOException {
//        ModelAndView mv = new ModelAndView();
//        mv.setView(new DownloadView());
//        Attachment attachment = attachmentMapper.selectByPrimaryKey(attId);
//        if (attachment != null) {
//            mv.addObject(DownloadView.EXPORT_FILE, fileService.getFile(attachment.getAttUrl()));
//            mv.addObject(DownloadView.EXPORT_FILE_NAME, attachment.getAttName());
//            mv.addObject(DownloadView.EXPORT_FILE_TYPE, attachment.getDocumentType());
//            mv.addObject(DownloadView.EXPORT_FILE_SIZE, attachment.getSize());
//        }
//        try {
//            response.setHeader("fileName", java.net.URLEncoder.encode(attachment.getAttName(), "utf-8"));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        return mv;
//    }
//
//
//}
