package com.stock.service.platform.file.listener;

import com.stock.core.dao.RedisDao;
import com.stock.service.platform.file.service.PackageFileThreadRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 上传文件listener
 */
@Component
public class PackageFileEventListener {

    public ConcurrentHashMap<String,Thread> map = null;

//    @Autowired
//    FileService fileService;

    @Autowired
    PackageFileThreadRepository repository;

    @Autowired
    RedisDao redisDao;

    private static final Logger logger = LoggerFactory.getLogger(PackageFileEventListener.class);

    @PostConstruct
    public void post(){
        map = new ConcurrentHashMap<>();
    }

//    @Async
//    @EventListener
//    public void handlePackageFileEvent(PackageFileEvent event) {
//        String id = event.getId();
//        try {
//            repository.put(id,Thread.currentThread());
//            logger.info("开始进行打包操作，id:【{}】",id);
//
//            // 打包后的结果
//            String fileUrl = fileService.packageZip();
//
//            logger.info("打包操作结束，id:【{}】，fileUrl:【{}】",id,fileUrl);
//
//            // 设置完成信息
//            redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"success","true");
//            redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"finish","true");
//            redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX+id,"filePath",fileUrl);
//        } catch (Exception e){
//
//            if(!(e instanceof InterruptedException)) {
//                // 获取异常信息
//                String message = "打包出现异常";
//                // 设置错误信息
//                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX + id, "success", "false");
//                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX + id, "finish", "true");
//                redisDao.setHashObjectAttr(RedisKeyConstant.FILE_PACKAGE_ASYNC_PREFIX + id, "errorMsg", message);
//            }
//        } finally {
//            repository.remove(id);
//        }
//
//
//
//    }

    public ConcurrentHashMap<String, Thread> getMap() {
        return map;
    }

    public void setMap(ConcurrentHashMap<String, Thread> map) {
        this.map = map;
    }

    public static void main(String[] args) {
        Thread current = Thread.currentThread();
        HashMap map = new HashMap();
        map.put("a",current);
        System.out.println(map.get("a"));
    }

}
