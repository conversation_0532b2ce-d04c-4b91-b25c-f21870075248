package com.stock.service.platform.mqManage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stock.service.platform.common.util.Message;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ActivemqContactDto implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 联系人姓名
     */
    @Message(title = "联系人姓名", primary = "true")
    private String contactName;

    /**
     * 联系人邮箱
     */
    @Message(title = "联系人邮箱", primary = "true")
    private String contactEmail;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;


}
