package com.stock.service.platform.mqManage.dao;

import com.stock.service.platform.mqManage.dto.ActivemqContactDto;
import com.stock.service.platform.mqManage.dto.ActivemqTopicDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivemqContactBizMapper {

    int insertContact(ActivemqContactDto activemqContactDto);

    ActivemqContactDto selectContact(ActivemqContactDto activemqContactDto);

    int updateContact(ActivemqContactDto activemqContactDto);

    List<ActivemqContactDto> getActivemqContact();

    ActivemqContactDto getActivemqContactByName(ActivemqContactDto activemqContactDto);

    List<ActivemqContactDto> getContactList();

    int deleteContact(ActivemqContactDto activemqContactDto);
}