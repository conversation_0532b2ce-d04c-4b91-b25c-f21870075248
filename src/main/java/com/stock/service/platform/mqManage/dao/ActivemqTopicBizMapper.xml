<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.mqManage.dao.ActivemqTopicBizMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.mqManage.dto.ActivemqTopicDto">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="sys_code" jdbcType="VARCHAR" property="sysCode" />
    <result column="info" jdbcType="VARCHAR" property="info" />
    <result column="is_success" jdbcType="VARCHAR" property="isSuccess" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, message_id, sys_code, info, is_success, error_message, business_type, operation_type, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from activemq_topic
    where id = #{id,jdbcType=VARCHAR}
  </select>


  <select id="getTotal" resultType="java.lang.Integer">
    select count(*) from activemq_topic a
    left join sa_system_config b on a.sys_code = b.sys_code
    <where>
      1=1
      <if test="isSuccess != null and isSuccess != ''">
        and a.is_success = #{isSuccess,jdbcType=VARCHAR}
      </if>
      <if test="sysCode != null and sysCode != ''">
        and a.sys_code = #{sysCode,jdbcType=VARCHAR}
      </if>
      <if test="businessType != null and businessType != ''">
        and a.business_type = #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="operationType != null and operationType != ''">
        and a.operation_type = #{operationType,jdbcType=VARCHAR}
      </if>
      <if test="info != null and info != ''">
        and a.info like CONCAT('%',#{info,jdbcType=VARCHAR},'%')
      </if>
      <if test="messageType != null and messageType != ''">
        and a.message_type = #{messageType,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <select id="getMessageQueue" resultType="com.stock.service.platform.mqManage.dto.ActivemqTopicDto">
    select
      a.id, a.message_id, a.sys_code, b.sys_name, a.info, a.is_success, a.error_message,
      a.business_type, a.operation_type, a.create_time, a.update_time
      from activemq_topic a
      left join sa_system_config b on a.sys_code = b.sys_code
    <where>
      1=1
      <if test="isSuccess != null and isSuccess != ''">
        and a.is_success = #{isSuccess,jdbcType=VARCHAR}
      </if>
      <if test="sysCode != null and sysCode != ''">
        and a.sys_code = #{sysCode,jdbcType=VARCHAR}
      </if>
      <if test="businessType != null and businessType != ''">
        and a.business_type = #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="operationType != null and operationType != ''">
        and a.operation_type = #{operationType,jdbcType=VARCHAR}
      </if>
      <if test="info != null and info != ''">
        and a.info like CONCAT('%',#{info,jdbcType=VARCHAR},'%')
      </if>
      <if test="messageType != null and messageType != ''">
        and a.message_type = #{messageType,jdbcType=VARCHAR}
      </if>
    </where>
    order by a.create_time desc
    limit #{startRow}, #{limit}
  </select>



  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from activemq_topic
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.stock.service.platform.mqManage.dto.ActivemqTopicDto" useGeneratedKeys="true">
    insert into activemq_topic (message_id, sys_code, info, 
      is_success, error_message, business_type, 
      operation_type, create_time, update_time
      )
    values (#{messageId,jdbcType=VARCHAR}, #{sysCode,jdbcType=VARCHAR}, #{info,jdbcType=VARCHAR}, 
      #{isSuccess,jdbcType=VARCHAR}, #{errorMessage,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{operationType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.stock.service.platform.mqManage.dto.ActivemqTopicDto" useGeneratedKeys="true">
    insert into activemq_topic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        message_id,
      </if>
      <if test="sysCode != null">
        sys_code,
      </if>
      <if test="info != null">
        info,
      </if>
      <if test="isSuccess != null">
        is_success,
      </if>
      <if test="errorMessage != null">
        error_message,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="sysCode != null">
        #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="info != null">
        #{info,jdbcType=VARCHAR},
      </if>
      <if test="isSuccess != null">
        #{isSuccess,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.stock.service.platform.mqManage.dto.ActivemqTopicDto">
    update activemq_topic
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="sysCode != null">
        sys_code = #{sysCode,jdbcType=VARCHAR},
      </if>
      <if test="info != null">
        info = #{info,jdbcType=VARCHAR},
      </if>
      <if test="isSuccess != null">
        is_success = #{isSuccess,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.stock.service.platform.mqManage.dto.ActivemqTopicDto">
    update activemq_topic
    set message_id = #{messageId,jdbcType=VARCHAR},
      sys_code = #{sysCode,jdbcType=VARCHAR},
      info = #{info,jdbcType=VARCHAR},
      is_success = #{isSuccess,jdbcType=VARCHAR},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>