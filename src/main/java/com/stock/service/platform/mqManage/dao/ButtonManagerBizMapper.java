package com.stock.service.platform.mqManage.dao;


import com.stock.service.platform.mqManage.dto.ButtonManagerDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ButtonManagerBizMapper {

    int insertButton(ButtonManagerDto buttonManagerDto);

    List<ButtonManagerDto> selectButtonList();

    int updateButtonStatus(ButtonManagerDto buttonManagerDto);

    int deleteButton(ButtonManagerDto buttonManagerDto);

    int updateButton(ButtonManagerDto buttonManagerDto);

    ButtonManagerDto selectButtonInfo(ButtonManagerDto buttonManagerDto);

    ButtonManagerDto selectButtonByName(ButtonManagerDto buttonManagerDto);

}