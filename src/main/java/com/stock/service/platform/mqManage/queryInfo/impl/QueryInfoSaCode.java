package com.stock.service.platform.mqManage.queryInfo.impl;

import com.stock.service.platform.common.dao.CodeMapper;
import com.stock.service.platform.common.entity.CodeExample;
import com.stock.service.platform.mqManage.queryInfo.QueryInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述
 * @date 2023/10/26
 */
@Component
public class QueryInfoSaCode implements QueryInfo {

    @Resource
    private CodeMapper codeMapper;


    @Override
    public void handleQueryInfo(Map<String, Object> map) {
        CodeExample codeExample = new CodeExample();
        codeExample.createCriteria().andCodeNoEqualTo("MESSAGE_TYPE");
        map.put("messageType", codeMapper.selectByExample(codeExample));
    }
}
