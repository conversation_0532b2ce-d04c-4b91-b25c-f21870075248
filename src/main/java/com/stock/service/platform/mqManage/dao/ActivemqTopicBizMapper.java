package com.stock.service.platform.mqManage.dao;

import com.stock.service.platform.mqManage.dto.ActivemqTopicDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivemqTopicBizMapper {
    int deleteByPrimaryKey(String id);

    int insert(ActivemqTopicDto record);

    int insertSelective(ActivemqTopicDto record);

    ActivemqTopicDto selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ActivemqTopicDto record);

    int updateByPrimaryKey(ActivemqTopicDto record);

    List<ActivemqTopicDto> getMessageQueue(ActivemqTopicDto activemqTopicDto);

    int getTotal(ActivemqTopicDto activemqTopicDto);
}