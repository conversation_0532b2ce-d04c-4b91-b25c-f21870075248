package com.stock.service.platform.mqManage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * activemq_topic
 */
@Data
public class ActivemqTopicDto implements Serializable {
    private String id;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 目标微服（发送给哪个微服）
     */
    private String sysCode;
    private String sysName;
    /**
     * 发送内容
     */
    private String info;

    /**
     * 0失败，1成功，2已发送
     */
    private String isSuccess;

    /**
     * 消息类型
     */
    private String messageType;
    private String codeName;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    private int startRow;
    private int limit;

    private static final long serialVersionUID = 1L;
}