<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.mqManage.dao.ButtonManagerBizMapper">
    <resultMap id="BaseResultMap" type="com.stock.service.platform.mqManage.dto.ButtonManagerDto">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="button_name" jdbcType="VARCHAR" property="buttonName"/>
        <result column="zh_name" jdbcType="VARCHAR" property="zhName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        module_name,
        button_name,
        zh_name,
        status,
        update_time,
        update_user
    </sql>
    <insert id="insertButton" parameterType="com.stock.service.platform.mqManage.dto.ButtonManagerDto">
        <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
            SELECT uuid_short()
        </selectKey>
        insert into button_manager(
        id,
        module_name,
        zh_name,
        button_name,
        status,
        update_user,
        update_time
        )values(
        #{id,jdbcType=VARCHAR},
        #{moduleName,jdbcType=VARCHAR},
        #{zhName,jdbcType=VARCHAR},
        #{buttonName,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{updateUser,jdbcType=VARCHAR},
        NOW()
        )
    </insert>

    <update id="updateButtonStatus">
        update button_manager
        set status = #{status,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR},
        update_time = NOW()
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateButton">
        update button_manager
        set module_name = #{moduleName,jdbcType=VARCHAR},
            zh_name     = #{zhName,jdbcType=VARCHAR},
            button_name = #{buttonName,jdbcType=VARCHAR},
            update_user = #{updateUser,jdbcType=VARCHAR},
            update_time = NOW()
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <delete id="deleteButton">
        delete from button_manager
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <select id="selectButtonList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from button_manager
    </select>
    <select id="selectButtonInfo" resultType="com.stock.service.platform.mqManage.dto.ButtonManagerDto">
        select
        <include refid="Base_Column_List" />
        from button_manager
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectButtonByName" resultType="com.stock.service.platform.mqManage.dto.ButtonManagerDto">
        select
        <include refid="Base_Column_List" />
        from button_manager
        where module_name = #{moduleName,jdbcType=VARCHAR}
        and button_name = #{buttonName,jdbcType=VARCHAR}
    </select>


</mapper>