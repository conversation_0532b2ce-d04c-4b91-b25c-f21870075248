package com.stock.service.platform.mqManage.queryInfo.impl;

import com.stock.service.platform.common.dao.SystemConfigMapper;
import com.stock.service.platform.common.entity.SystemConfig;
import com.stock.service.platform.mqManage.queryInfo.QueryInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 表 sa_system_config
 * @date 2023/10/24
 */
@Component
public class QueryInfoSaSystemConfig implements QueryInfo {

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @Override
    public void handleQueryInfo(Map<String, Object> map) {
        List<SystemConfig> systemConfigs = systemConfigMapper.selectByExample(null);
        map.put("sysCode", systemConfigs);
    }
}
