<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.mqManage.dao.ActivemqContactBizMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.mqManage.dto.ActivemqContactDto">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,contact_name,contact_email,update_user,update_time
  </sql>
  <delete id="deleteContact">
    delete from activemq_contact
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <select id="selectContact" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from activemq_contact
    where id = #{id,jdbcType=VARCHAR}
  </select>


  <insert id="insertContact" parameterType="com.stock.service.platform.mqManage.dto.ActivemqContactDto">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT uuid_short()
    </selectKey>
    insert into activemq_contact (
    id,
    contact_name,
    contact_email,
    update_user,
    update_time)
    values (
    #{id,jdbcType=VARCHAR},
    #{contactName,jdbcType=VARCHAR},
    #{contactEmail,jdbcType=VARCHAR},
    #{updateUser,jdbcType=VARCHAR},
    NOW())
  </insert>
  <update id="updateContact">
    update activemq_contact
    set contact_name = #{contactName,jdbcType=VARCHAR},
    contact_email = #{contactEmail,jdbcType=VARCHAR},
    update_user = #{updateUser,jdbcType=VARCHAR},
    update_time = NOW()
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getActivemqContact" resultType="com.stock.service.platform.mqManage.dto.ActivemqContactDto">
    select
      contact_name contactName,
      contact_email contactEmail
    from activemq_contact
  </select>
  <select id="getContactList" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from activemq_contact
    order by update_time desc
  </select>
  <select id="getActivemqContactByName"
          resultType="com.stock.service.platform.mqManage.dto.ActivemqContactDto">
    select
      <include refid="Base_Column_List" />
    from activemq_contact
    where contact_name = #{contactName,jdbcType=VARCHAR}
  </select>

</mapper>