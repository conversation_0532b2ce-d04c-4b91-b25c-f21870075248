package com.stock.service.platform.mqManage.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ButtonManagerDto implements Serializable {

    /**
     * 按钮ID
     */
    private String id;

    /**
     * 按钮名称
     */
    private String buttonName;

    /**
     * 按钮所在模块名
     */
    private String moduleName;

    /**
     * 按钮状态
     */
    private String status;

    /**
     * 按钮状态更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 按钮状态更新人
     */
    private String updateUser;

    /**
     * 模块中文名称
     */
    private String zhName;
}
