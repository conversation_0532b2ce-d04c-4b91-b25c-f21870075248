package com.stock.service.platform.mqManage.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.dto.UserInfo;
import com.stock.service.platform.mqManage.dto.ActivemqContactDto;
import com.stock.service.platform.mqManage.dto.ActivemqTopicDto;
import com.stock.service.platform.mqManage.dto.ButtonManagerDto;
import com.stock.service.platform.mqManage.service.MQManagerService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 类描述
 * @date 2023/10/23
 */
@RestController
@RequestMapping("/mqManager")
public class MQManagerController extends BaseController {

    @Resource
    private MQManagerService mqManagerService;

    /**
     * 根据条件获取所有发送的消息
     * @param activemqTopicDto
     * @return
     */
    @PostMapping("getMessageQueue")
    public JsonResponse<Map<String, Object>> getMessageQueue(@RequestBody ActivemqTopicDto activemqTopicDto) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(mqManagerService.getMessageQueue(activemqTopicDto));
        return  response;
    }

    /**
     * 获取查询条件
     * @return
     */
    @PostMapping("getQueryInfo")
    public JsonResponse<Map<String, Object>> getQueryInfo() {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(mqManagerService.getQueryInfo());
        return  response;
    }

    /**
     * 重新发送消息
     * @param activemqTopicDto
     * @return
     */
    @PostMapping("resendMessage")
    public JsonResponse<Boolean> resendMessage(@RequestBody ActivemqTopicDto activemqTopicDto) throws Exception {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(mqManagerService.resendMessage(activemqTopicDto));
        return response;
    }

    /**
     * 联系人维护
     * @param activemqContactDto
     * @return
     */
    @PostMapping("maintainContact")
    public JsonResponse<Boolean> maintainContact(@RequestBody ActivemqContactDto activemqContactDto){
        JsonResponse<Boolean> response = new JsonResponse<>();
        activemqContactDto.setUpdateUser(getUserInfo().getPersonName());
        response.setResult(mqManagerService.maintainContact(activemqContactDto));
        return  response;
    }

    /**
     * 获取联系人信息
     * @param activemqContactDto 联系人信息
     * @return
     */
    @PostMapping("getContactInfo")
    public JsonResponse<Map<String, Object>> getContactInfo(@RequestBody ActivemqContactDto activemqContactDto){
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();
        response.setResult(mqManagerService.getContactInfo(activemqContactDto));
        return response;
    }

    /**
     * 获取联系人信息列表
     * @return
     */
    @PostMapping("getContactList")
    public JsonResponse<Map<String, Object>> getContactList(){
        JsonResponse<Map<String,Object>> response = new JsonResponse<>();
        response.setResult(mqManagerService.getContactList());
        return response;
    }
    /**
     * 删除联系人信息
     * @return
     */
    @PostMapping("deleteContact")
    public JsonResponse<Boolean> deleteContact(@RequestBody ActivemqContactDto activemqContactDto){
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(mqManagerService.deleteContact(activemqContactDto));
        return response;
    }






    @PostMapping("insertButton")
    public JsonResponse<Boolean> insertButton(@RequestBody ButtonManagerDto buttonManagerDto){
        JsonResponse<Boolean> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        buttonManagerDto.setUpdateUser(userInfo.getPersonName());
        response.setResult(mqManagerService.insertButton(buttonManagerDto));
        return response;
    }

    /**
     * 获取按钮信息集合
     * @return
     */
    @PostMapping("getButtonInfo")
    public JsonResponse<List<ButtonManagerDto>> getButtonInfo(){
        JsonResponse<List<ButtonManagerDto>> response = new JsonResponse<>();
        response.setResult(mqManagerService.selectButtonList());
        return response;
    }


    /**
     * 修改按钮状态
     * @return
     */
    @PostMapping("updateButtonStatus")
    public JsonResponse<Boolean> updateButtonStatus(@RequestBody ButtonManagerDto buttonManagerDto){
        JsonResponse<Boolean> response = new JsonResponse<>();
        UserInfo userInfo = getUserInfo();
        buttonManagerDto.setUpdateUser(userInfo.getPersonName());
        response.setResult(mqManagerService.updateButtonStatus(buttonManagerDto));
        return response;
    }

    /**
     * 删除按钮
     * @return
     */
    @PostMapping("deleteButton")
    public JsonResponse<Boolean> deleteButton(@RequestBody ButtonManagerDto buttonManagerDto){
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(mqManagerService.deleteButton(buttonManagerDto));
        return response;
    }

    /**
     * 获取按钮信息
     * @return
     */
    @PostMapping("selectButtonInfo")
    public JsonResponse<ButtonManagerDto> selectButtonInfo(@RequestBody ButtonManagerDto buttonManagerDto){
        JsonResponse<ButtonManagerDto> response = new JsonResponse<>();
        response.setResult(mqManagerService.selectButtonInfo(buttonManagerDto));
        return response;
    }

    /**
     * 获取按钮信息
     * @return
     */
    @PostMapping("selectButtonByName")
    public JsonResponse<ButtonManagerDto> selectButtonByName(@RequestBody ButtonManagerDto buttonManagerDto){
        JsonResponse<ButtonManagerDto> response = new JsonResponse<>();
        response.setResult(mqManagerService.selectButtonByName(buttonManagerDto));
        return response;
    }

}
