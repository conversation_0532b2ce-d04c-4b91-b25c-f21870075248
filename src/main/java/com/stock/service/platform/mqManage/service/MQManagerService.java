package com.stock.service.platform.mqManage.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.stock.service.platform.besMQ.component.TopicProducer;
import com.stock.service.platform.common.constant.TopicConstant;
import com.stock.service.platform.common.dto.MQMsgDto;
import com.stock.service.platform.common.entity.MessageInfo;
import com.stock.service.platform.common.log.OperationLogEvent;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.common.service.LogService;
import com.stock.service.platform.common.system.service.DepartmentService;
import com.stock.service.platform.common.util.MessageUtil;
import com.stock.service.platform.mqManage.dao.ActivemqContactBizMapper;
import com.stock.service.platform.mqManage.dao.ActivemqTopicBizMapper;
import com.stock.service.platform.mqManage.dao.ButtonManagerBizMapper;
import com.stock.service.platform.mqManage.dto.ActivemqContactDto;
import com.stock.service.platform.mqManage.dto.ActivemqTopicDto;
import com.stock.service.platform.mqManage.dto.ButtonManagerDto;
import com.stock.service.platform.mqManage.queryInfo.QueryInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.stock.core.security.SecurityHelper.getUserInfo;

/**
 * <AUTHOR>
 * @Description 类描述
 * @date 2023/10/23
 */
@Service
public class MQManagerService {

    private static final Logger logger = LoggerFactory.getLogger(MQManagerService.class);
    @Resource
    private ActivemqTopicBizMapper activemqTopicBizMapper;

    @Resource
    private ActivemqContactBizMapper activemqContactBizMapper;

    @Resource
    private ButtonManagerBizMapper buttonManagerBizMapper;

    @Resource
    private List<QueryInfo> queryInfoList; // 获取所有需要查询的条件类

    @Resource
    private TopicProducer topicProducer;

    @Resource
    private LogService logService;

    public Map<String, Object> getMessageQueue(ActivemqTopicDto activemqTopicDto) {
        Map<String, Object> messageQueue = new HashMap<>();
        activemqTopicDto.setStartRow((activemqTopicDto.getStartRow()-1)*activemqTopicDto.getLimit());
        messageQueue.put("messages",  activemqTopicBizMapper.getMessageQueue(activemqTopicDto));
        messageQueue.put("total", activemqTopicBizMapper.getTotal(activemqTopicDto));
        return messageQueue;
    }

    public Map<String, Object> getQueryInfo() {
        Map<String, Object> queryInfo = new HashMap<>();
        queryInfoList.forEach(item -> item.handleQueryInfo(queryInfo));
        queryInfo.put("businessType", TopicConstant.BUS_TYPE_LIST_MAP);
        queryInfo.put("operationType", TopicConstant.OP_TYPE_LIST_MAP);
        return queryInfo;
    }

    @Transactional
    public Boolean resendMessage(ActivemqTopicDto activemqTopicDto) throws Exception {
        // 消息
        Map<String,Object> personTopic = JSON.parseObject(activemqTopicDto.getInfo(), new TypeReference<Map<String, Object>>(){});
        // 更新状态
        activemqTopicDto.setIsSuccess("2");
        activemqTopicDto.setUpdateTime(new Date());
        activemqTopicBizMapper.updateByPrimaryKey(activemqTopicDto);
        // 发送消息
        topicProducer.sendTopic(personTopic, activemqTopicDto.getSysCode(),  activemqTopicDto.getBusinessType(),
                    activemqTopicDto.getOperationType(), activemqTopicDto.getMessageId());
        try {
            String remark = getRemark(activemqTopicDto);
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(activemqTopicDto),
                            null, "重新发送",
                            "系统管理/消息管理", "MQManagerService.resendMessage",
                            "重新发送{" + remark + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return true;
    }

    public String getRemark(ActivemqTopicDto activemqTopicDto) {
        String remark = activemqTopicDto.getSysName();
        switch (activemqTopicDto.getBusinessType()) {
            case "company":
                remark += "-公司";
                break;
            case "logout":
                remark += "-退出";
                break;
            case "role":
                remark += "-角色";
                break;
            case "user":
                remark += "-用户";
                break;
            case "permission":
                remark += "-用户授权";
                break;
            case "org":
                remark += "-组织架构";
                break;
            case "log":
                remark += "-日志";
                break;
            case "timedTasks":
                remark += "-定时任务";
                break;
        }
        switch (activemqTopicDto.getOperationType()) {
            case "insert":
                remark += "-新增";
                break;
            case "lock":
                remark += "-锁定";
                break;
            case "delete":
                remark += "-删除";
                break;
            case "update":
                remark += "-更新";
                break;
            case "setuser":
                remark += "-设置用户(回购专用)";
                break;
            case "merge":
                remark += "-外部用户合并";
                break;
            case "outUser":
                remark += "-外部用户导入";
                break;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        remark += "(" + formatter.format(activemqTopicDto.getCreateTime()) + ")";
        return remark;
    }

    public Boolean maintainContact(ActivemqContactDto activemqContactDto) {
        ActivemqContactDto getInfo = activemqContactBizMapper.selectContact(activemqContactDto);
        if(getInfo != null){
            // 这是更新
            activemqContactDto.setId(getInfo.getId());
            boolean flag = activemqContactBizMapper.updateContact(activemqContactDto) > 0;
            try {
                MessageUtil messageUtil = new MessageUtil();
                MessageInfo messageInfo = messageUtil.getMessageContext(activemqContactDto, getInfo);
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                String ip = CommonService.getIp(request);
                logService.asyncSaveOperationLog(
                        new OperationLogEvent(JSONObject.toJSONString(activemqContactDto),
                                messageInfo.getOperationContext(), "编辑联系人",
                                "系统管理/消息管理", "MQManagerService.maintainContact",
                                messageInfo.getOperationRemark(), ip, getUserInfo()));
            } catch (Exception e) {
                logger.info(e.getMessage());
            }
            return flag;
        }else{
            // 这是新增
            if (activemqContactDto.getContactName() != null && !activemqContactDto.getContactName().isEmpty() && activemqContactDto.getContactEmail() != null && !activemqContactDto.getContactEmail().isEmpty()){
                activemqContactBizMapper.insertContact(activemqContactDto);
                try {
                    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                    HttpServletRequest request = attributes.getRequest();
                    String ip = CommonService.getIp(request);
                    logService.asyncSaveOperationLog(
                            new OperationLogEvent(JSONObject.toJSONString(activemqContactDto),
                                    null, "新增联系人",
                                    "系统管理/消息管理", "MQManagerService.maintainContact",
                                    "新增联系人{联系人姓名:" + activemqContactDto.getContactName() + "}", ip, getUserInfo()));
                } catch (Exception e) {
                    logger.info(e.getMessage());
                }
                return true;
            }
            return false;
        }
    }

    public Map<String, Object> getContactInfo(ActivemqContactDto activemqContactDto) {
        Map<String, Object> map = new HashMap<>();
        map.put("contact", activemqContactBizMapper.selectContact(activemqContactDto));
        return map;
    }

    public Map<String, Object> getContactList() {
        Map<String, Object> map = new HashMap<>();
        map.put("contactList", activemqContactBizMapper.getContactList());
        return map;
    }
    public Boolean deleteContact(ActivemqContactDto activemqContactDto) {
        boolean flag = activemqContactBizMapper.deleteContact(activemqContactDto) > 0;
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(activemqContactDto.getId(),
                            null, "删除联系人",
                            "系统管理/消息管理", "MQManagerService.deleteContact",
                            "删除联系人{id:" + activemqContactDto.getId() + "}", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return flag;
    }


    public Boolean insertButton(ButtonManagerDto buttonManagerDto) {
        if (buttonManagerDto.getButtonName() == null || buttonManagerDto.getButtonName().isEmpty() || buttonManagerDto.getModuleName() == null || buttonManagerDto.getModuleName().isEmpty()){
            return false;
        }else{
            if(buttonManagerDto.getStatus() == null || buttonManagerDto.getStatus().isEmpty()){
                buttonManagerDto.setStatus("1");
            }
            if(buttonManagerDto.getId() == null || buttonManagerDto.getId().isEmpty()){
                // 新增
                return buttonManagerBizMapper.insertButton(buttonManagerDto) > 0;
            }else{
                // 走修改
                ButtonManagerDto getInfo = buttonManagerBizMapper.selectButtonInfo(buttonManagerDto);
                if (getInfo != null){
                    return buttonManagerBizMapper.updateButton(buttonManagerDto) > 0;
                }else{
                    return buttonManagerBizMapper.insertButton(buttonManagerDto) > 0;
                }
            }
        }
    }

    public List<ButtonManagerDto> selectButtonList() {
        return buttonManagerBizMapper.selectButtonList();
    }

    public Boolean updateButtonStatus(ButtonManagerDto buttonManagerDto){
        // 修改按钮状态
        if(buttonManagerDto.getStatus() == null || buttonManagerDto.getStatus().equals("") || buttonManagerDto.getStatus().equals("0")){
            buttonManagerDto.setStatus("1");
        }else{
            buttonManagerDto.setStatus("0");
        }
        boolean flag = buttonManagerBizMapper.updateButtonStatus(buttonManagerDto) > 0;
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String ip = CommonService.getIp(request);
            logService.asyncSaveOperationLog(
                    new OperationLogEvent(JSONObject.toJSONString(buttonManagerDto),
                            null, "修改按钮状态",
                            "系统管理/消息管理", "MQManagerService.updateButtonStatus",
                            "修改["+ buttonManagerDto.getButtonName() + "]按钮状态[status:" + buttonManagerDto.getStatus() + "]", ip, getUserInfo()));
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return flag;
    }

    public Boolean deleteButton(ButtonManagerDto buttonManagerDto){
        if(buttonManagerDto.getId() == null || buttonManagerDto.getId().isEmpty()){
            return false;
        }
        return buttonManagerBizMapper.deleteButton(buttonManagerDto) > 0;
    }

    public ButtonManagerDto selectButtonInfo(ButtonManagerDto buttonManagerDto){
        return buttonManagerBizMapper.selectButtonInfo(buttonManagerDto);
    }

    public ButtonManagerDto selectButtonByName(ButtonManagerDto buttonManagerDto){
        return buttonManagerBizMapper.selectButtonByName(buttonManagerDto);
    }

    public Boolean setButtonStatus(String moduleName, String buttonName,String status,String updateUser) {
        ButtonManagerDto buttonManagerDto = new ButtonManagerDto();
        buttonManagerDto.setModuleName(moduleName);
        buttonManagerDto.setButtonName(buttonName);
        buttonManagerDto.setStatus(status);
        buttonManagerDto.setUpdateUser(updateUser);
        ButtonManagerDto getInfo = selectButtonByName(buttonManagerDto);
        buttonManagerDto.setId(getInfo.getId());
        return buttonManagerBizMapper.updateButtonStatus(buttonManagerDto) > 0;
    }


}
