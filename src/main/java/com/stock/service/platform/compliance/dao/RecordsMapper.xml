<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.compliance.dao.RecordsMapper">
    <select id="getTableList" parameterType="com.stock.service.platform.compliance.dto.RecordsDto" resultType="com.stock.service.platform.compliance.dto.RecordsDto">
        select
        qa.id,
        sp.real_name as realName,
        qa.question_content as questionContent,
        qa.answer_content as answerContent,
        sc_channel.code_name as questionChannel,
        sc_users.code_name as userType,
        qa.user_name as userName,
        qa.ai_type as aiType,
        CASE
            WHEN qa.`status` = '0' THEN '等待回答'
            WHEN qa.`status` = '1' THEN '回答成功'
            WHEN qa.`status` = '2' THEN '回答超时'
            WHEN qa.`status` = '3' THEN '回复报错'
            WHEN qa.`status` = '6' THEN '停止生成'
            ELSE '' END AS answerStatus,
        CASE
        WHEN qa.feedback_type = '0' THEN '赞'
        WHEN qa.feedback_type = '1' THEN '踩'
        ELSE '' END AS feedbackType,
        qa.question_time as questionTime,
        qa.tokens as tokens,
        qa.answer_time as answerTime,
        qa.prompt as prompt
        from chat_content qa
        left join chat_record cr on qa.record_id = cr.id
        left join sa_code sc_channel on sc_channel.code_value = cr.question_channel and sc_channel.code_no = 'CHANNEL_TYPE'
        left join sa_user  sp on qa.user_id = sp.id
        left join sa_person  ss on qa.user_id = ss.id
        left join sa_code sc_users on sc_users.code_value = ss.person_type and sc_users.code_no = 'RECORDS_USER_TYPE'
        <where>
            <include refid="common_where_if"/>
        </where>
        order by
        <choose>
            <when test="sortField == 'questionTime'">question_time</when>
            <otherwise>question_time</otherwise> <!-- 默认排序字段 -->
        </choose>
        <choose>
            <when test="sortOrder == 'asc'">ASC</when>
            <when test="sortOrder == 'desc'">DESC</when>
            <otherwise>DESC</otherwise> <!-- 默认降序 -->
        </choose>
        <if test="pageSize != 0 ">
            limit #{startRow},#{pageSize}
        </if>
    </select>
    <sql id="common_where_if">
        cr.`status` = 0
        <if test="realName != null and realName != ''">
            AND sp.real_name  = #{realName}
        </if>
        <if test="questionContent != null and questionContent != ''">
            AND qa.question_content LIKE CONCAT ('%', #{questionContent}, '%')
        </if>
        <if test="answerContent != null and answerContent != ''">
            AND qa.answer_content LIKE CONCAT ('%', #{answerContent}, '%')
        </if>
        <if test="questionChannel != null and questionChannel != ''">
            AND cr.question_channel = #{questionChannel}
        </if>
        <if test="userType != null and userType != ''">
            AND ss.person_type = #{userType}
        </if>
        <if test="userName != null and userName != ''">
            AND sp.user_name LIKE CONCAT ('%', #{userName}, '%')
        </if>
        <if test="aiType != null and aiType != ''">
            AND qa.ai_type = #{aiType}
        </if>
        <if test="startTime != null and endTime != null">
            AND qa.question_time BETWEEN #{startTime} AND #{endTime}
        </if>

    </sql>
    <select id="getCount" parameterType="com.stock.service.platform.compliance.dto.RecordsDto" resultType="java.lang.Integer">
        select
        count(qa.id)
        from
        chat_content qa
        left join chat_record cr on qa.record_id = cr.id
        left join sa_code sc_channel on sc_channel.code_value = cr.question_channel and sc_channel.code_no = 'CHANNEL_TYPE'
        left join sa_user sp on qa.user_id = sp.id
        left join sa_person  ss on qa.user_id = ss.id
        left join sa_code sc_user on sc_user.code_value = sp.user_type and sc_user.code_no = 'RECORDS_USER_TYPE'
        left join sa_code sc_ai on sc_ai.code_value = qa.ai_type and sc_ai.code_no = 'AI_TYPE'
        <where>
            <include refid="common_where_if"/>
        </where>
    </select>
    <select id="getCodeList" parameterType="java.lang.String" resultType="com.stock.service.platform.compliance.dto.CodeDto">
        select
            sc.id,
            sc.code_no as codeNo,
            sc.code_name as codeName,
            sc.code_value as codeValue,
            sc.sort_no as sortNo
        from sa_code sc
        where  sc.code_no = #{codeNo}
        order by sc.sort_no
    </select>
    <select id="selectExportFieldInfo" parameterType="com.stock.service.platform.compliance.dto.FileDto"
            resultType="com.stock.service.platform.compliance.dto.FileDto">
        select
        qa.id,
        sp.real_name as realName,
        qa.question_content as questionContent,
        qa.answer_content as answerContent,
        sc_channel.code_name as questionChannel,
        sc_users.code_name as userType,
        sp.user_name as userName,
        qa.ai_type as aiType,
        CASE
        WHEN qa.`status` = 0 THEN '等待回答'
        WHEN qa.`status` = 1 THEN '回答成功'
        WHEN qa.`status` = 2 THEN '回答超时'
        WHEN qa.`status` = 3 THEN '回复报错'
        WHEN qa.`status` = 6 THEN '停止生成'
        ELSE '' END AS answerStatus,
        CASE
        WHEN qa.feedback_type = 0 THEN '赞'
        WHEN qa.feedback_type = 1 THEN '踩'
        ELSE '' END AS feedbackType,
        date_format(qa.question_time,'%Y-%m-%d %H:%i:%s') as questionTime,
        qa.tokens as tokens,
        qa.answer_time as answerTime,
        qa.prompt as prompt
        from chat_content qa
        left join chat_record cr on qa.record_id = cr.id
        left join sa_code sc_channel on sc_channel.code_value = cr.question_channel and sc_channel.code_no = 'CHANNEL_TYPE'
        left join sa_user  sp on qa.user_id = sp.id
        left join sa_person  ss on qa.user_id = ss.id
        left join sa_code sc_users on sc_users.code_value = ss.person_type and sc_users.code_no = 'RECORDS_USER_TYPE'
        <where>
            <include refid="common_where_if"/>
        </where>
        order by
        <choose>
            <when test="sortField == 'questionTime'">question_time</when>
            <otherwise>question_time</otherwise> <!-- 默认排序字段 -->
        </choose>
        <choose>
            <when test="sortOrder == 'asc'">ASC</when>
            <when test="sortOrder == 'desc'">DESC</when>
            <otherwise>DESC</otherwise> <!-- 默认降序 -->
        </choose>
    </select>
    <select id="getAiTypeList" resultType="java.lang.String">
        SELECT ai_type FROM chat_content where ai_type is not null GROUP BY ai_type
    </select>
</mapper>