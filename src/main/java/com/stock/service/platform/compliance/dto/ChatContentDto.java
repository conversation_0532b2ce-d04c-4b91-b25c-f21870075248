package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class ChatContentDto implements Serializable {


    /**
     * 主键
     */
    private String id;

    /**
     * 聊天记录id
     */
    private String recordId;

    /**
     * 模型类型
     */
    private String aiType;


    /**
     * 问题内容
     */
    private String questionContent;

    /**
     * 回答内容
     */
    private String answerContent;

    /**
     * 回答内容2
     */
    private String answerContentOther;

    /**
     * 引用回答
     */
    private String citeContent;

    /**
     * prompt内容
     */
    private String prompt;


    /**
     * 资料排序
     */
    private String dataOrder;

    /**
     * 回答状态（0：等待回答，1：回答成功，2：回答超时，3：模型接口报错，4：回答内容为空, 5:意图识别报错 6:停止生产）
     */
    private Integer status;


    private String belongsPlate;


    /**
     * 反馈状态：0赞   1踩
     */
    private String feedbackType;

    /**
     * 提问时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date questionTime;

    /**
     * 会话时间（用于展示页面会话时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chatTime;

    /**
     * 回答时间
     */
    private Date answerTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否出错
     */
    private Integer isError;


    private List<String>ThinkList;

    private Boolean thinkFlag;

}
