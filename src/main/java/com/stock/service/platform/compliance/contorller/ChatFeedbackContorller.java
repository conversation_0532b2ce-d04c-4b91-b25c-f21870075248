package com.stock.service.platform.compliance.contorller;

import com.stock.core.dto.JsonResponse;
import com.stock.core.web.DownloadView;
import com.stock.service.platform.compliance.dto.ChatFeedbackDto;
import com.stock.service.platform.compliance.service.ChatFeedbackService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/feedbackReply")
public class ChatFeedbackContorller {

    @Resource
    private ChatFeedbackService chatFeedbackService;

    /**
     * 查询表格
     */
    @PostMapping("getTableList")
    public JsonResponse <Map<String, Object>> getTableList(@RequestBody ChatFeedbackDto feedbackDto) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(chatFeedbackService.getTableList(feedbackDto));
        return  response;
    }

    /**
     * 导出列表
     */
    @RequestMapping(value = "exportFieldList")
    @ResponseBody
    public ModelAndView exportFieldList(@RequestBody ChatFeedbackDto feedbackDto, HttpServletResponse response) throws IOException {
        Map<String,Object> resultMap = chatFeedbackService.exportFieldList(feedbackDto);
        ModelAndView mv = new ModelAndView();
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, resultMap.get("inputStream"));
        mv.addObject(DownloadView.EXPORT_FILE_NAME,  resultMap.get("fileName"));
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        response.setHeader("fileName", java.net.URLEncoder.encode(resultMap.get("fileName").toString(), "utf-8"));
        return mv;
    }

    /**
     * 答复反馈
     */
    @PostMapping("replyFeedback")
    public JsonResponse<Boolean> replyFeedback(@RequestBody Map<String, String> params) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        String feedbackId = params.get("feedbackId");
        String content = params.get("content");
        response.setResult(chatFeedbackService.replyFeedback(feedbackId, content));
        return response;
    }

}
