package com.stock.service.platform.compliance.contorller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.exception.ApplicationException;
import com.stock.service.platform.compliance.dto.GetMessageNonFlowRequest;
import com.stock.service.platform.compliance.dto.GetMessageResponse;
import com.stock.service.platform.compliance.dto.GetTokenRequest;
import com.stock.service.platform.compliance.dto.ModelDto;
import com.stock.service.platform.compliance.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/complianceAPI")
@RequiredArgsConstructor
public class ComplianceAPI extends BaseController {

    private final ChatService chatService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    //   非流式问答
    @PostMapping("/getMessage")
    public JsonResponse<GetMessageResponse> getMessage(@RequestBody GetMessageNonFlowRequest request) throws JsonProcessingException {
        // 校验不为空
        if (StringUtils.isAnyBlank(request.getAiType(),request.getQuestion(),request.getClientIP(),request.getPersonName(),request.getUserId(),request.getOrigin())){
            throw new ApplicationException("compliance.001");
        }
        // 校验模型
        List<ModelDto> modelListRedis = chatService.getModelListRedis(request.getUserId());
        List<String> collect = modelListRedis.stream().map(ModelDto::getModelId).collect(Collectors.toList());
        if (!collect.contains(request.getAiType())){
            throw new ApplicationException("compliance.003");
        }
        JsonResponse<GetMessageResponse> response = new JsonResponse<>();
        GetMessageResponse message = chatService.getMessage(request);
        if (message == null){
            throw new ApplicationException("compliance.002");
        }
        response.setResult(message);
        return response;
    }

    //   获取token
    @PostMapping("/getAccessToken")
    public JsonResponse<Map<String, String>> getAccessToken(@RequestBody GetTokenRequest request) {
        if (StringUtils.isAnyBlank(request.getClientIP(),request.getPersonName(),request.getUserId(),request.getOrigin())){
            throw new ApplicationException("compliance.001");
        }
        if(!StringUtils.equalsAny(request.getOrigin(),"zxjt01", "zxjt02","zxjt03")){
            throw new ApplicationException("compliance.002");
        }

        JsonResponse<Map<String, String>> response = new JsonResponse<>();
        String token  = chatService.getAccessToken(request.getUserId());
        Map<String, String> map = new HashMap<>();
        map.put("accessToken", token);
        response.setResult(map);
        return response;
    }
}
