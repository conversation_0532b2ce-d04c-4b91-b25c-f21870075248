package com.stock.service.platform.compliance.dao;

import com.stock.service.platform.compliance.dto.ChatContentDto;
import com.stock.service.platform.compliance.dto.ChatDongFormDto;
import com.stock.service.platform.compliance.dto.ChatRecordDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ChatContentBizMapper{

    // 获取问答字数
    Integer getAllWordCount(ChatDongFormDto param);

    List<ChatContentDto> getHistoryWordCount(ChatDongFormDto param);

    void updateRecordStatus(ChatRecordDto dto);

    void updateContentStatus(ChatContentDto dto);

    List<ChatContentDto> selectChatContentList(List<String> list);

    List<ChatContentDto> selectChatContentListByRecordId(String recordId);
    // 更新问答类型-根据记录Id
    void updateContentQaTypeByRecordId(ChatContentDto dto);

    List<String> getContentIdListByRecordId(String recordId);

    ChatDongFormDto getAnswerContentByContentId(String contentId);

    void deleteByContentId(String contentId);

    List<ChatContentDto> selectSingleConversation(ChatDongFormDto chatDongFormDto);

    void updateFeedbackType(Map<String, String> map);

    String selectContent(Map<String, String> map);

}
