package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@Data
@Builder
@JsonInclude(NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class MaterialResponse implements Serializable {
    private List<Object> lawsInfoList;
    private List<Object> solicitLawsList;
    private List<Object> answerList;
    private List<Object> violateList;
    private String dataOrder;
}
