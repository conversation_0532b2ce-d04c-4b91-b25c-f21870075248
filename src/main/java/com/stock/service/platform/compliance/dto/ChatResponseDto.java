package com.stock.service.platform.compliance.dto;

import lombok.Data;

import java.util.List;

@Data
public class ChatResponseDto {

//    @ApiModelProperty("聊天记录id")
    String chatId;

//    @ApiModelProperty("单条聊天问答id")
    String chatContentId;

//    @ApiModelProperty("聊天名称")
    String chatName;

//    @ApiModelProperty("用户id")
    String userId;
//    @ApiModelProperty("今天聊天记录")
    List<ChatRecordDto> chatRecordListToday;

//    @ApiModelProperty("最近7天聊天记录")
    List<ChatRecordDto> chatRecordList7Days;

//    @ApiModelProperty("最近30天聊天记录")
    List<ChatRecordDto> chatRecordList30Days;

//    @ApiModelProperty("更早聊天记录")
    List<ChatRecordDto> chatRecordListOld;

//    @ApiModelProperty("聊天全部内容List")
    List<ChatContentDto> chatContentList;

//    @ApiModelProperty("聊天设置-角色下拉列表")
    List<CodeDto> roleDropDownList;


//    @ApiModelProperty("聊天回答内容List")
    List<String> answerContentList;

//    @ApiModelProperty("聊天回答内容")
    String answerContent;

//    @ApiModelProperty("自动续问接口返回-问题列表")
    List<String> autoAskQuestionList;

//    @ApiModelProperty("回答状态（0：等待回答，1：回答成功，2：回答超时，3：模型接口报错，4：回答内容为空）")
    private Integer status;

//    @ApiModelProperty("前台会话关键词定位")
    private Integer keywordIndex;

//    @ApiModelProperty("展示状态")
    private Boolean seeDataFlag;

//    @ApiModelProperty("赞 踩状态")
    private String feedbackType;

//    @ApiModelProperty("知识库问答状态（true 有文件，false 无文件）")
    private Boolean useKnowlegeStatus;
}
