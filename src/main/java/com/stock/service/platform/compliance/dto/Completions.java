package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@Data
@Builder
@JsonInclude(NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public final class Completions {
    private String id;                      // id
    @Builder.Default
    private String content = "";            // 内容
    private String type;                    // 消息类型
    private String think;                   // 思考
    private String model;                     // 模型
    private TokenUsage tokenUsage;          //token使用情况
    private String dataOrder; // 中信建投返回资料顺序

    private List<String> intentions; // 意图
    private List<String> prompt; // 上下文
    @Builder.Default
    private Boolean isEnd = false;          // 是否结束
    private Boolean error;                // 是否错误

}