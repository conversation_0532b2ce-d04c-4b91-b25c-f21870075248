package com.stock.service.platform.compliance.contorller;

import com.stock.core.dto.JsonResponse;
import com.stock.core.web.DownloadView;
import com.stock.service.platform.compliance.dto.FileDto;
import com.stock.service.platform.compliance.dto.RecordsDto;
import com.stock.service.platform.compliance.service.RecordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/records")
public class RecordsContorller {
    @Autowired
    RecordsService recordsService;

    /**
     * 查询表格
     * @param recordsDto
     * @return
     */
    @PostMapping("getTableList")
    public JsonResponse <Map<String, Object>> getTableList(@Valid @RequestBody RecordsDto recordsDto) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(recordsService.getTableList(recordsDto));
        return  response;
    }

    /**
     * 获取基本信息
     * @param recordsDto
     * @return
     */
    @RequestMapping(value = "getInformation")
    @ResponseBody
    public JsonResponse<Map<String, Object>> getInformation(@RequestBody RecordsDto recordsDto){
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(recordsService.getInformation(recordsDto));
        return response;
    }
    /**
     * 导出指标列表
     */
    @RequestMapping(value = "exportFieldList")
    @ResponseBody
    public ModelAndView exportFieldList(@RequestBody FileDto fileDto, HttpServletResponse response) throws IOException {
        Map<String,Object> resultMap = recordsService.exportFieldList(fileDto);
        ModelAndView mv = new ModelAndView();
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, resultMap.get("inputStream"));
        mv.addObject(DownloadView.EXPORT_FILE_NAME,  resultMap.get("fileName"));
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        response.setHeader("fileName", java.net.URLEncoder.encode(resultMap.get("fileName").toString(), "utf-8"));
        return mv;
    }

    /**
     * 查询当天点过同意否
     */
    @PostMapping("getChatLoginRecord")
    public JsonResponse <Boolean> getChatLoginRecord() {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(recordsService.getChatLoginRecord());
        return  response;
    }

    /**
     * 查询当天点过同意否
     */
    @PostMapping("setChatLoginRecord")
    public JsonResponse <Boolean> setChatLoginRecord(HttpServletRequest request) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        recordsService.setChatLoginRecord(request);
        response.setResult(true);
        return  response;
    }

}
