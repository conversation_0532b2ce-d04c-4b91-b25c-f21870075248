package com.stock.service.platform.compliance.dao;

import com.stock.service.platform.compliance.dto.CodeDto;
import com.stock.service.platform.compliance.dto.FileDto;
import com.stock.service.platform.compliance.dto.RecordsDto;

import java.util.List;

public interface RecordsMapper {
    List<RecordsDto> getTableList(RecordsDto recordsDto);

    Integer getCount(RecordsDto recordsDto);

    List<CodeDto> getCodeList(String codeNo);

    List<FileDto> selectExportFieldInfo(FileDto fileDto);

    List<String> getAiTypeList();
}
