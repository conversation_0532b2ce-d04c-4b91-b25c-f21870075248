package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class FileDto {
    @ExcelColumn(name = "会话ID", order = 8)
    private String id;
    private String userId;
    @ExcelColumn(name = "姓名", order = 7)
    private String realName;
    @ExcelColumn(name = "问题", order = 1)
    private String questionContent;
    @ExcelColumn(name = "回复", order = 2)
    private String answerContent;
    @ExcelColumn(name = "提问时间", order = 11)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date questionTime;
    @ExcelColumn(name = "渠道", order = 4)
    private String questionChannel;
    @ExcelColumn(name = "用户类型", order = 5)
    private String userType;
    @ExcelColumn(name = "用户名", order = 6)
    private String userName;
    @ExcelColumn(name = "回复模型", order = 10)
    private String aiType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date requestTime;

    private Date updateTime;

    private int startRow;

    private int pageSize;

    private List<String> userTypeList;

    private List<String> questionChannelList;

    private List<String> aiTypeList;

    private String sortOrder;
    @ExcelColumn(name = "回复状态", order = 14)
    private String answerStatus;

    private String sortField;

    private List<String> idList;
    @ExcelColumn(name = "Tokens", order = 9)
    private String tokens;
    @ExcelColumn(name = "上下文", order = 3)
    private String prompt;
    @ExcelColumn(name = "响应时间", order = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date answerTime;
    @ExcelColumn(name = "回复用时", order = 13)
    private String  waitTime;
    @ExcelColumn(name = "赞、踩", order = 15)
    private String  feedbackType;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private Date startTime;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private Date endTime;


}
