package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天反馈对话记录DTO
 */
@Data
public class ChatFeedbackConversationRecordDto implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * chat_feedback表id
     */
    private String feedbackId;

    /**
     * 内容
     */
    private String content;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 答复/反馈标识(答复:0    反馈:1)
     */
    private String replyFeedbackStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    private static final long serialVersionUID = 1L;
}
