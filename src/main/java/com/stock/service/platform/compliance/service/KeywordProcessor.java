package com.stock.service.platform.compliance.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.lang.String.valueOf;
import static java.util.stream.Collectors.toList;

public class KeywordProcessor {
    private final String keyword;
    private Integer termsInTrie;
    private final JSONObject keywordTrieDict;
    private final boolean caseSensitive;
    private final List<String> wordBoundaries = Arrays.asList(" ", "\t", "\n", ",", ".");

    public KeywordProcessor() {
        this.keyword = "_keyword_";
        this.keywordTrieDict = new JSONObject();
        this.caseSensitive = false;
        this.termsInTrie = 0;
    }

    private Integer size() {
        return this.termsInTrie;
    }

    /**
     * 检查 keyword_trie_dict 中是否存在关键词
     *
     * @param word 您要检查的字符串单词
     * @return 如果单词存在于 keywords_trie_dict 中，那么我们返回 True，否则返回 False
     */
    private boolean contains(String word) {
        if (!this.caseSensitive)
            word = word.toLowerCase();
        JSONObject currentDict = this.keywordTrieDict;
        int lenCovered = 0;
        char[] chars = word.toCharArray();
        for (char ch : chars) {
            if (currentDict.containsKey(valueOf(ch))) {
                currentDict = (JSONObject) currentDict.get(valueOf(ch));
                lenCovered += 1;
            } else
                break;
        }

        return currentDict.containsKey(this.keyword) && lenCovered == word.length();

    }


    /**
     * 如果word存在于keyword_trie_dict中，则返回它的干净名称。
     *
     * @param word 您要检查的单词
     * @return 如果单词存在于 keywords_trie_dict 中，那么我们返回映射到它的关键字。
     */
    private Map<String, Object> getItem(String word) {
        if (!this.caseSensitive)
            word = word.toLowerCase();
        JSONObject current_dict = this.keywordTrieDict;
        int len_covered = 0;
        char[] chars = word.toCharArray();
        for (char ch : chars) {
            if (current_dict.containsKey(valueOf(ch))) {
                current_dict = (JSONObject) current_dict.get(valueOf(ch));
                len_covered += 1;
            } else
                break;
        }
        if (current_dict.containsKey(this.keyword) && len_covered == word.length())
            return (JSONObject) current_dict.get(this.keyword);

        return null;
    }


    /**
     * 将关键字添加到词典中
     * 传递关键字及其映射到的干净名称。
     *
     * @param keyword   你想要识别的关键字
     * @param cleanName 该关键字的干净术语，您希望在返回或替换时获得返回
     */
    private boolean setItem(String keyword, String cleanName) {
        boolean status = false;
        if ((cleanName == null || cleanName.isEmpty()) && !keyword.isEmpty())
            cleanName = keyword;

        if (!keyword.isEmpty())
            if (!this.caseSensitive)
                keyword = keyword.toLowerCase();

        JSONObject currentDict = this.keywordTrieDict;
        char[] chars = keyword.toCharArray();

        for (char ch : chars) {
            if (!currentDict.containsKey(valueOf(ch)))
                currentDict.put(valueOf(ch), new JSONObject());
            currentDict = (JSONObject) currentDict.get(valueOf(ch));
        }
        if (!currentDict.containsKey(this.keyword)) {
            status = true;
            this.termsInTrie += 1;
        }
        currentDict.put(this.keyword, cleanName);

        return status;
    }


    /**
     * 从字典中删除关键字
     * 传递关键字及其映射到的干净名称。
     *
     * @param keyword 你想要删除的关键字
     */
    private boolean delItem(String keyword) {
        boolean status = false;
        if (!keyword.isEmpty()) {
            if (!this.caseSensitive)
                keyword = keyword.toLowerCase();

            JSONObject currentDict = this.keywordTrieDict;
            List<List<Object>> characterTrieList = new ArrayList<>();
            char[] chars = keyword.toCharArray();

            for (char ch : chars) {
                if (currentDict.containsKey(valueOf(ch))) {
                    // revisit
                    List<Object> chMap = new ArrayList<>();
                    chMap.add(ch);
                    chMap.add(currentDict);
                    characterTrieList.add(chMap);
                    currentDict = (JSONObject) currentDict.get(valueOf(ch));
                } else {
                    // if character is not found, break out of the loop
                    currentDict = null;
                    break;
                }
            }
            // remove the characters from trie dict if there are no other keywords with them
            if (currentDict != null && currentDict.containsKey(this.keyword)) {
                // we found a complete match for input keyword.
                List<Object> keywordMap = new ArrayList<>();
                keywordMap.add(this.keyword);
                keywordMap.add(currentDict);

                characterTrieList.add(keywordMap);
                Collections.reverse(characterTrieList);


                for (List<Object> item : characterTrieList) {
                    String key_to_remove = (String) item.get(0);
                    JSONObject dictPointer = (JSONObject) item.get(1);

                    if (dictPointer.size() == 1)
                        dictPointer.remove(key_to_remove);

                    else {
                        // more than one key means more than 1 path.
                        // Delete not required path and keep the other
                        dictPointer.remove(key_to_remove);
                        break;
                    }
                }
                // //  successfully removed keyword
                status = true;
                this.termsInTrie -= 1;
            }

        }
        return status;
    }


    /**
     * 将关键字添加到词典中
     * 传递关键字及其映射到的干净名称。
     *
     * @param keyword    你想要识别的关键字
     * @param clean_name 该关键字的干净术语，您希望在返回或替换时获得返回
     */
    public boolean addKeyword(String keyword, String clean_name) {
        return this.setItem(keyword, clean_name);
    }

    /**
     * 从字典中删除关键字
     * 传递关键字及其映射到的干净名称。
     *
     * @param keyword 你想要删除的关键字
     */
    public boolean removeKeyword(String keyword) {
        return this.delItem(keyword);
    }

    /**
     * 如果word存在于keyword_trie_dict中，则返回它的干净名称。
     *
     * @param word 你想要检查的单词
     * @return 如果word存在于keyword_trie_dict中，则返回它的干净名称。
     */
    public Map<String, Object> getKeyword(String word) {
        return this.getItem(word);
    }

    /**
     * 从词典添加关键字
     *
     * @param keywordDict 以“string”键和（列表“string”）作为值的字典
     */
    public void addKeywordsFromDict(JSONObject keywordDict) {
        for (Map.Entry<String, Object> entry : keywordDict.entrySet()) {
            String cleanName = entry.getKey();
            Object keywords = entry.getValue();

            for (Object keyword : (JSONArray) keywords)
                this.addKeyword("" + keyword, cleanName);
        }
    }

    /**
     * 从词典中删除关键字
     *
     * @param keywordDict 以“string”键和（列表“string”）作为值的字典
     */
    public void removeKeywordsFromDict(JSONObject keywordDict) {
        for (Map.Entry<String, Object> entry : keywordDict.entrySet()) {
            Object keywords = entry.getValue();
            for (Object keyword : (JSONArray) keywords)
                this.removeKeyword("" + keyword);
        }

    }

    /**
     * 从列表中添加关键字
     *
     * @param keywordList 一个字符串列表
     */
    public void addKeywordsFromList(List<String> keywordList) {
        for (String keyword : keywordList)
            this.addKeyword(keyword, null);
    }

    /**
     * 从列表中删除关键字
     *
     * @param keywordList 一个字符串列表
     */
    public void removeKeywordsFromList(List<String> keywordList) {
        for (String keyword : keywordList)
            this.removeKeyword(keyword);
    }

    /**
     * 递归地构建字典中存在的关键字的字典以及映射到这些关键字的干净名称。
     *
     * @param termSoFar   到目前为止通过添加所有以前的字符构建的术语
     * @param currentDict 字典中当前的递归位置
     * @return 键和值的映射，其中每个键都是 keywords_trie_dict 中的一个术语。映射到它的值是映射到它的干净名称。
     */
    public Map<String, String> getAllKeywords(String termSoFar, Map<String, Object> currentDict) {
        Map<String, String> termsPresent = new HashMap<>();

        if (termSoFar.isEmpty())
            termSoFar = "";

        if (currentDict == null)
            currentDict = this.keywordTrieDict;

        for (Map.Entry<String, Object> entry : currentDict.entrySet()) {
            String key = entry.getKey();
            if (key.equals(this.keyword)) {
                termsPresent.put(termSoFar, (String) currentDict.get(key));
            } else {
                Map<String, String> sub_values = this.getAllKeywords(
                        termSoFar + key,
                        (JSONObject) currentDict.get(key)
                );

                for (Map.Entry<String, String> subentry : sub_values.entrySet()) {
                    String subKey = subentry.getKey();
                    termsPresent.put(subKey, sub_values.get(subKey));
                }

            }
        }

        return termsPresent;

    }

    /**
     * 在字符串中搜索语料库中存在的所有关键字。存在的关键字将添加到列表“keywords_extracted”中并返回。
     *
     * @param sentence 一行文本，我们将在其中搜索关键字
     * @return 匹配我们语料库的句子中找到的术语/关键字的列表
     */
    public List<String> extractKeywords(String sentence) {
        return this.extractKeywords(sentence, false).stream()
                .map(item -> "" + item).collect(toList());
    }

    public List<Object> extractKeywords(String sentence, boolean spanInfo) {
        List<Object> keywordsExtracted = new ArrayList<>();
        if (sentence.isEmpty())
            return keywordsExtracted;

        if (!this.caseSensitive)
            sentence = sentence.toLowerCase();

        JSONObject currentDict = this.keywordTrieDict;
        Integer sequenceStartPos = 0;
        int sequenceEndPos = 0;
        boolean resetCurrentDict = false;
        int idx = 0;
        int sentenceLen = sentence.length();
        while (idx < sentenceLen) {
            char ch = sentence.charAt(idx);

            if (!this.wordBoundaries.contains(valueOf(ch))) {
                // if end is present in currentDict
                if (currentDict.containsKey(this.keyword) || currentDict.containsKey(valueOf(ch))) {
                    // update longest sequence found
                    String longestSequenceFound = null;
                    boolean isLongerSeqFound = false;

                    if (currentDict.containsKey(this.keyword)) {
                        longestSequenceFound = (String) currentDict.get(this.keyword);
                        sequenceEndPos = idx;
                    }

                    // re-look for longest_sequence from this position
                    if (currentDict.containsKey(valueOf(ch))) {
                        JSONObject currentDictContinued = (JSONObject) currentDict.get(valueOf(ch));
                        int idy = idx + 1;
                        labelInnerLoop:
                        {
                            while (idy < sentenceLen) {
                                char innerChar = sentence.charAt(idy);
                                if (!this.wordBoundaries.contains(valueOf(innerChar)) && currentDictContinued.containsKey(this.keyword)) {
                                    // update longest sequence found
                                    longestSequenceFound = (String) currentDictContinued.get(this.keyword);
                                    sequenceEndPos = idy;
                                    isLongerSeqFound = true;
                                }
                                if (currentDictContinued.containsKey(valueOf(innerChar)))
                                    currentDictContinued = (JSONObject) currentDictContinued.get(valueOf(innerChar));
                                else
                                    break labelInnerLoop;
                                idy += 1;
                            }

                            if (currentDictContinued.containsKey(this.keyword)) {
                                // update longest sequence found
                                longestSequenceFound = (String) currentDictContinued.get(this.keyword);
                                sequenceEndPos = idy;
                                isLongerSeqFound = true;
                            }
                        }
                        if (isLongerSeqFound)
                            idx = sequenceEndPos;
                    }
                    currentDict = this.keywordTrieDict;
                    if (longestSequenceFound != null && !longestSequenceFound.isEmpty()) {
                        ArrayList<Object> keywordInfo = new ArrayList<>();
                        keywordInfo.add(longestSequenceFound);
                        keywordInfo.add(sequenceStartPos);
                        keywordInfo.add(idx);
                        keywordsExtracted.add(keywordInfo);
                    }
                } else {
                    currentDict = this.keywordTrieDict;
                }
                resetCurrentDict = true;
            } else if (currentDict.containsKey(valueOf(ch))) {
                //we can continue from this char
                currentDict = (JSONObject) currentDict.get(valueOf(ch));
            } else {
                // we reset currentDict
                currentDict = this.keywordTrieDict;
                resetCurrentDict = true;

                // skip to end of word
                int idy = idx + 1;
                while (idy < sentenceLen) {
                    char chy = sentence.charAt(idy);
                    if (!this.wordBoundaries.contains(valueOf(chy))) {
                        break;
                    }
                    idy += 1;
                }
                idx = idy;
            }
            // 如果我们已经到了句子末尾并发现了一个序列
            if (idx + 1 >= sentenceLen) {
                if (currentDict.containsKey(this.keyword)) {
                    String sequenceFound = (String) currentDict.get(this.keyword);
                    ArrayList<Object> keywordInfo = new ArrayList<>();
                    keywordInfo.add(sequenceFound);
                    keywordInfo.add(sequenceStartPos);
                    keywordInfo.add(sentenceLen);
                    keywordsExtracted.add(keywordInfo);
                }
            }
            idx += 1;
            if (resetCurrentDict) {
                resetCurrentDict = false;
                sequenceStartPos = idx;
            }
        }
        if (spanInfo)
            return keywordsExtracted;
        return keywordsExtracted.stream().map(item -> CollectionUtils.get(item, 0)).collect(toList());
    }

    /**
     * 在字符串中搜索语料库中存在的所有关键字。存在的关键字将替换为干净的名称，并返回一个新字符串。
     *
     * @param sentence 一行文本，我们将在其中搜索关键字
     * @return 用替换关键字的文本行
     */
    public String replaceKeywords(String sentence) {
        if (sentence.isEmpty()) {
            // 如果句子为空或没有，则返回相同的值。
            return sentence;
        }
        StringBuilder newSentence = new StringBuilder();
        String origSentence = sentence;
        if (this.caseSensitive) {
            sentence = sentence.toLowerCase();
        }
        StringBuilder currentWord = new StringBuilder();
        JSONObject currentDict = this.keywordTrieDict;
        String currentWhiteSpace;
        int sequenceEndPos = 0;
        int idx = 0;
        int sentenceLen = sentence.length();
        while (idx < sentenceLen) {
            char ch = sentence.charAt(idx);
            currentWord.append(origSentence.charAt(idx));
            // 当我们到达空白处时
            if (!this.wordBoundaries.contains(valueOf(ch))) {
                currentWhiteSpace = valueOf(ch);

                // 如果 currentDict 中存在 end
                if (currentDict.containsKey(this.keyword) || currentDict.containsKey(valueOf(ch))) {
                    // 更新找到的最长序列
                    Object longestSequenceFound = null;
                    boolean isLongerSeqFound = false;
                    if (currentDict.containsKey(this.keyword)) {
                        longestSequenceFound = currentDict.get(this.keyword);
                        sequenceEndPos = idx;
                    }

                    // 从这个位置重新寻找longest_sequence
                    if (currentDict.containsKey(valueOf(ch))) {
                        JSONObject currentDictContinued = (JSONObject) currentDict.get(valueOf(ch));
                        StringBuilder currentWordContinued = new StringBuilder(currentWord.toString());
                        int idy = idx + 1;
                        labelInnerLoop:
                        {
                            while (idy < sentenceLen) {
                                char innerChar = sentence.charAt(idy);
                                currentWordContinued.append(origSentence.charAt(idy));
                                if (!this.wordBoundaries.contains(valueOf(innerChar)) && currentDictContinued.containsKey(this.keyword)) {
                                    // 更新找到的最长序列
                                    currentWhiteSpace = valueOf(innerChar);
                                    longestSequenceFound = currentDictContinued.get(this.keyword);
                                    sequenceEndPos = idy;
                                    isLongerSeqFound = true;
                                }
                                if (currentDictContinued.containsKey(valueOf(innerChar))) {
                                    currentDictContinued = (JSONObject) currentDictContinued.get(valueOf(innerChar));
                                } else {
                                    break labelInnerLoop;
                                }
                                idy += 1;
                            }
                            if (currentDictContinued.containsKey(this.keyword)) {
                                // 更新找到的最长序列
                                currentWhiteSpace = "";
                                longestSequenceFound = currentDictContinued.get(this.keyword);
                                sequenceEndPos = idy;
                                isLongerSeqFound = true;
                            }
                        }
                        if (isLongerSeqFound) {
                            idx = sequenceEndPos;
                            currentWord = new StringBuilder(currentWordContinued.toString());
                        }
                    }
                    currentDict = this.keywordTrieDict;
                    if (longestSequenceFound != null) {
                        newSentence.append(longestSequenceFound).append(currentWhiteSpace);
                    } else {
                        newSentence.append(currentWord);
                    }
                } else {
                    // 我们重置 currentDict
                    currentDict = this.keywordTrieDict;
                    newSentence.append(currentWord);
                }
                currentWord = new StringBuilder();
            } else if (currentDict.containsKey(valueOf(ch))) {
                // 我们可以从这个字符继续
                currentDict = (JSONObject) currentDict.get(valueOf(ch));
            } else {
                // 重置 currentDict
                currentDict = this.keywordTrieDict;
                // 跳到词尾
                int idy = idx + 1;
                while (idy < sentenceLen) {
                    ch = sentence.charAt(idy);
                    currentWord.append(origSentence.charAt(idy));
                    if (!this.wordBoundaries.contains(valueOf(ch))) {
                        break;
                    }
                    idy += 1;
                }
                idx = idy;
                newSentence.append(currentWord);
                currentWord = new StringBuilder();
                // 如果我们已经到了句子末尾并发现了一个序列
            }
            if (idx + 1 >= sentenceLen) {
                if (currentDict.containsKey(this.keyword)) {
                    String sequenceFound = (String) currentDict.get(this.keyword);
                    newSentence.append(sequenceFound);
                } else {
                    newSentence.append(currentWord);
                }
            }
            idx += 1;
        }
        return newSentence.toString();
    }
}