package com.stock.service.platform.compliance.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GetMessageRequest {
    @NotNull(message = "contentId不能为空")
    private String contentId;
    @NotNull(message = "aiType不能为空")
    private String aiType;
    @NotNull(message = "belongsPlate不能为空")
    private String belongsPlate;
    @NotNull(message = "continuousChat不能为空")
    private String continuousChat; // 是否开启连续问答1开启0关闭
    @NotNull(message = "question不能为空")
    private String question;
    @NotNull(message = "origin不能为空")
    private String origin; // 问题来源
    private List<HistoryMessage> historyMessages;


}
