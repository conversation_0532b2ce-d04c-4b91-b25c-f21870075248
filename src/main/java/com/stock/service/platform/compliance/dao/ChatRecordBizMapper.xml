<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.compliance.dao.ChatRecordBizMapper">
    <resultMap id="BaseResultMap" type="com.stock.service.platform.compliance.dto.ChatRecordDto">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="chat_name" property="chatName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="getChatRecordList" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto"
            resultMap="BaseResultMap">
        select t.id
        , chat_name
        , chat_name
        , create_time
        from chat_record t
        <where>
            status != '1' and t.user_id = #{userId}
            <if test="chatId != null and chatId != ''">
                and t.id = #{chatId}
            </if>
            <if test="createTime != null and createTime != ''">
                and date_format(t.create_time, '%Y-%m-%d') <![CDATA[<]]> #{createTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(t.create_time, '%Y-%m-%d') <![CDATA[>=]]> #{endTime}
            </if>
        </where>
        order by t.create_time desc;
    </select>

    <select id="getChatContentList" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto"
            resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        select t.id,
        t.record_id as recordId,
        t.question_time as questionTime,
        t.create_time createTime,
        t.answer_time as answerTime,
        t.question_content as questionContent,
        t.answer_content as answerContent,
        t.data_order as dataOrder,
        t.cite_content as citeContent,
        t.status,
        t.feedback_type as feedbackType,
        t.ai_type as aiType
        from chat_content t
        <where>
            <if test="chatId != null and chatId != ''">
                and t.record_id = #{chatId}
            </if>
        </where>
        order by t.question_time
    </select>

    <select id="getAllFileSize" parameterType="String" resultType="java.lang.Integer">
        SELECT sum(t.file_size)
        FROM knowledge_base t
        WHERE t.user_id = #{id}
        ORDER BY t.create_time desc
    </select>
    <select id="getRoleDropDownList" resultType="com.stock.service.platform.compliance.dto.CodeDto">
        select t.id as value, t.role_name as code
        from chat_role t;
    </select>

    <select id="queryNl2sqlTable" resultType="java.util.Map">
        select table_data tableData
        from chat_content_nl2sql
        where content_id = #{id}
        <if test="fromIndex != null">
            limit #{fromIndex},#{limit}
        </if>
    </select>

    <select id="queryNl2sqlTableSize" resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        select count(*) tableSize
        from chat_content_nl2sql
        where content_id = #{id}
    </select>

    <select id="getWenChatContentList" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto"
            resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        SELECT t.id,
        t.ai_record_id as recordId,
        t.answer_type answerType,
        t.question_source questionSource,
        t.show_answer showAnswer,
        t.question_time as
        questionTime,
        t.answer_time as answerTime,
        t.create_time as createTime,
        t.qa_type as qaType,
        t.question_content as questionContent,
        t.answer_content as answerContent,
        t.answer_content_other as answerContentOther,
        t.cite_content as citeContent,
        t.status,
        t.data_order as dataOrder,
        t.ai_type as aiType,
        'false' as seeDataFlag,
        t.feedback_type feedbackType,
        t.qa_word_count as qawordCount,
        (select count(1) from answer_info_laws a where a.content_id = t.id) lawsCount,
        (select count(1) from answer_info_violate a where a.content_id = t.id) violateCount,
        (select count(1) from answer_info_qa a where a.content_id = t.id) qaCount,
        (select count(1) from answer_info_year_report a where a.content_id = t.id) reportCount,
        (select count(1) from answer_info_bing a where a.content_id = t.id) bingCount,
        t.financial_table financialTable,
        t.platform_user_document_parsing_map_id as platformUserDocumentParsingMapId,
        t.financial_condition  financialCondition,
        t.cal_flag calFlag
        FROM chat_content t
        <where>
            t.del_status != '1'
            and (t.answer_content is not null or t.answer_content_other is not null)
            <if test="chatId != null and chatId != ''">
                and t.ai_record_id = #{chatId}
            </if>
            <if test="qaType != null and qaType != ''">
                and t.qa_type = #{qaType}
            </if>
            <if test="statusList != null">
                and t.status in
                <foreach collection="statusList" item="result" index="index" open="(" close=")" separator=",">
                    #{result}
                </foreach>
            </if>
            <if test="aiType != null and aiType != ''">
                and t.ai_type = #{aiType}
            </if>
        </where>
        order by t.question_time asc;
    </select>
    <select id="getAssistantChatContent" resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        SELECT t.id,
               t.ai_record_id as recordId,
               t.question_time as questionTime,
               t.question_content as questionContent,
               t.answer_content as answerContent,
               t.status,
               t.ai_type as aiType,
               t.feedback_type feedbackType,
               t.platform_user_document_parsing_map_id as platformUserDocumentParsingMapId
        FROM chat_content t
        where
            t.ai_record_id = #{chatId}
            and t.del_status != '1'
            and (t.answer_content is not null or t.answer_content_other is not null)
        order by t.question_time asc;
    </select>

</mapper>