package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class RecordsDto {

    private String id;

    private String userId;

    private String questionContent;

    private String answerContent;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date questionTime;

    private String questionChannel;

    private String userType;

    private String userName;

    private String aiType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Date updateTime;

    private int startRow;

    private int pageSize;

    private List<String> userTypeList;

    private List<String> questionChannelList;

    private List<String> aiTypeList;

    private String sortOrder;

    private String sortField;

    private List<String> idList;

    private String tokens;

    private String prompt;

    private String answerStatus;

    private String realName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date answerTime;

    private long  waitTime;

    private long totals;

    private String  feedbackType;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private Date startTime;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private Date endTime;

}
