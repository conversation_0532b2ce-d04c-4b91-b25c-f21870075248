package com.stock.service.platform.compliance.schedule;

import com.stock.service.platform.common.system.dao.SaCompanyMapper;
import com.stock.service.platform.common.system.dto.CompanyCodeAndName;
import com.stock.service.platform.compliance.service.ChatService;
import com.stock.service.platform.compliance.service.KeywordProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class CompanyCodeAndNameTask {

    private final ChatService chatService;
    private final SaCompanyMapper saCompanyMapper;

    /**
     * 从一点开始每六小时执行一次
     */
    @PostConstruct
    @Scheduled(cron = "0 0 1/6 * * ?")
    public void run(){
        StopWatch stopWatch = StopWatch.createStarted();
        log.info("开始更新公司名称，简称，Code");
        List<CompanyCodeAndName> codeAndNames = saCompanyMapper.selectAllCompanyCodeAndName();

        List<String> codeAndName = codeAndNames.stream()
                .map(it -> Arrays.asList(it.getCompanyCode(), it.getZhName(), it.getZhSortName()))
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());

        KeywordProcessor keywordProcessor = new KeywordProcessor();
        keywordProcessor.addKeywordsFromList(codeAndName);
        chatService.setKeywordProcessor(keywordProcessor);
        stopWatch.stop();
        log.info("更新公司名称，简称，Code完成，耗时 {}ms", stopWatch.getTime(TimeUnit.MILLISECONDS));
    }

}
