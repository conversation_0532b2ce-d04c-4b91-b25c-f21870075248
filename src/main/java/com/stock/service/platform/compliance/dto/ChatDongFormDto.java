package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stock.core.dto.UserInfo;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ChatDongFormDto implements Serializable {

//    @ApiModelProperty("聊天id")
    private String chatId;

//    @ApiModelProperty("聊天内容id")
    private String chatContentId;

//    @ApiModelProperty("引用内容")
    private String citeContent;

//    @ApiModelProperty("回答内容")
    private String answerContent;

//    @ApiModelProperty("删除内容：0删除问题，1删除回答, 2删除对应文件")
    private int clearType;

//    @ApiModelProperty("单轮0，多轮1")
    private int multipleType;

//    @ApiModelProperty("用户id")
    private String userId;

//    @ApiModelProperty("公司代码")
    private String companyCode;

//    @ApiModelProperty("问题内容")
    private String questionContent;

//    @ApiModelProperty("聊天名称")
    private String chatName;

//    @ApiModelProperty("AI模型")
    private String aiType;

//    @ApiModelProperty("聊天模式（1：日常问答，2：专业模式）")
    private int chatModel;

//    @ApiModelProperty("思维发散度")
    private Float temperature;

//    @ApiModelProperty("勾选根据事实回答（0勾选，1未勾选）")
    private Integer fact;

//    @ApiModelProperty("聊天类型（1: LLM, 2: 知识库问答）")
    private Integer chatType;

//    @ApiModelProperty("问答类型（1：客户提问，2：系统配置，3：超限不展示）")
    private Integer qaType;

    // 问答类型（1：客户提问，2：系统配置，3：超限不展示）
    private Integer notQaType;

//    @ApiModelProperty("回答状态（0：等待回答，1：回答成功，2：回答超时，3：模型接口报错，4：回答内容为空）")
    private Integer status;

//    @ApiModelProperty("回答状态（0：等待回答，1：回答成功，2：回答超时，3：模型接口报错，4：回答内容为空）")
    private List<Integer> statusList;

//    @ApiModelProperty("知识库文件大小")
    private int fileSize;

//    @ApiModelProperty("知识库文件id")
    private String fileId;

//    @ApiModelProperty("阿里云文件id，使用该字段时fileId是对话关联文件id")
    private String fileIds;

//    @ApiModelProperty("知识库文件路径-删除知识库文件用")
    private String filePath;

//    @ApiModelProperty("不查询回答内容为null的数据")
    private int answerContentNotNull;

//    @ApiModelProperty("知识库问答状态（true 有文件，false 无文件）")
    private Boolean useKnowlegeStatus;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String chatTime;

//    @ApiModelProperty("用户设置的promt")
    private String prompt = ""; //提示

//    @ApiModelProperty("用户设置的promt，ai回答的内容-目前只有文心一言有")
    private String promptAnswer = ""; //提示回答内容

//    @ApiModelProperty("是否使用易董plus版(0 否 1是)")
    private String plusStatus;
    private UserInfo userInfo;  //用户信息 内部使用
    private String type; // websocket类型
    private String lastMessageId;
    private Integer limit;
    private Integer limitNumber; // ai日常助手-通用页-限制条数
    private String model;
    private String functionalModule;
    private Date questionTime; //日常助手-通用页-用户提问时间
    private String configId;
    private String feedbackType;
    private Boolean auto;

}
