package com.stock.service.platform.compliance.service;

import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.ChatLoginRecordMapper;
import com.stock.service.platform.common.entity.ChatLoginRecord;
import com.stock.service.platform.common.entity.ChatLoginRecordExample;
import com.stock.service.platform.common.system.dao.SaPersonMapper;
import com.stock.service.platform.compliance.dao.RecordsMapper;
import com.stock.service.platform.compliance.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

@Service
public class RecordsService extends BaseService {
    @Autowired
    RecordsMapper recordsMapper;

    @Autowired
    ChatLoginRecordMapper chatLoginRecordMapper;



    public Map<String, Object> getTableList(RecordsDto recordsDto) {
        Map<String,Object> result = new HashMap<>();
        // 处理startTime和endTime为当天0点和23:59:59
        if (recordsDto.getStartTime() != null) {
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(recordsDto.getStartTime());
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);
            startCal.set(Calendar.MILLISECOND, 0);
            recordsDto.setStartTime(startCal.getTime());
        }
        if (recordsDto.getEndTime() != null) {
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(recordsDto.getEndTime());
            endCal.set(Calendar.HOUR_OF_DAY, 23);
            endCal.set(Calendar.MINUTE, 59);
            endCal.set(Calendar.SECOND, 59);
            endCal.set(Calendar.MILLISECOND, 999);
            recordsDto.setEndTime(endCal.getTime());
        }


        List<RecordsDto> tableList = recordsMapper.getTableList(recordsDto);
        tableList.forEach(dto -> {
            if (dto.getAnswerTime() != null && dto.getQuestionTime() != null) {
                dto.setWaitTime((dto.getAnswerTime().getTime() - dto.getQuestionTime().getTime()) / 1000);
            } else {
                dto.setWaitTime(-1); // 用-1表示无效值
            }
        });
        result.put("tableList",tableList);
        Integer templateCount = recordsMapper.getCount(recordsDto);
        result.put("total",templateCount);
        return result;
    }

    public Map<String, Object> getInformation(RecordsDto recordsDto) {
        Map<String, Object> map = new HashMap<>();
        // 获取渠道
        List<CodeDto> questionChannelList = getCodeList("CHANNEL_TYPE");
        map.put("questionChannelList", questionChannelList);
        // 获取用户类型
        List<CodeDto> userTypeList = getCodeList("RECORDS_USER_TYPE");
        map.put("userTypeList", userTypeList);
        // 获取回复模型
        List<String> modelList = recordsMapper.getAiTypeList();
        List<CodeDto> aiTypeList = new ArrayList<>();
        for (int i = 0; i < modelList.size(); i++) {
            CodeDto codeDto = new CodeDto();
            codeDto.setCodeValue(String.valueOf(i));
            codeDto.setCodeName(modelList.get(i));
            aiTypeList.add(codeDto);
        }
        map.put("aiTypeList", aiTypeList);
        return map;
    }

    private List<CodeDto> getCodeList(String codeNo) {
        return recordsMapper.getCodeList(codeNo);
    }


    public Map<String, Object> exportFieldList(FileDto fileDto) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        SXSSFWorkbook workbook = exportFieldManage(fileDto);
//        定义表名
//        RecordsDto recordsDtos = recordsMapper.getTemplateTableName(recordsDto.getId());
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        workbook.dispose();
        resultMap.put("fileName", "问答记录导出.xlsx");
        resultMap.put("inputStream", new ByteArrayInputStream(os.toByteArray()));
        return resultMap;
    }

    private SXSSFWorkbook exportFieldManage(FileDto fileDto) {
        // 处理startTime和endTime为当天0点和23:59:59
        if (fileDto.getStartTime() != null) {
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(fileDto.getStartTime());
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);
            startCal.set(Calendar.MILLISECOND, 0);
            fileDto.setStartTime(startCal.getTime());
        }
        if (fileDto.getEndTime() != null) {
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(fileDto.getEndTime());
            endCal.set(Calendar.HOUR_OF_DAY, 23);
            endCal.set(Calendar.MINUTE, 59);
            endCal.set(Calendar.SECOND, 59);
            endCal.set(Calendar.MILLISECOND, 999);
            fileDto.setEndTime(endCal.getTime());
        }
        //  初始化工作簿和数据
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        List<FileDto> dataList = recordsMapper.selectExportFieldInfo(fileDto);
        dataList.forEach(dto -> {
            if (dto.getAnswerTime() != null && dto.getQuestionTime() != null) {
                long seconds = (dto.getAnswerTime().getTime() - dto.getQuestionTime().getTime()) / 1000;
                long minutes = seconds / 60;
                long remainingSeconds = seconds % 60;

                String waitTime;
                if (minutes > 0) {
                    waitTime = minutes + "分" + remainingSeconds + "秒";
                } else {
                    waitTime = seconds + "秒";
                }
                dto.setWaitTime(waitTime);
            } else {
                dto.setWaitTime("--");
            }
        });
        //  通过反射获取带注解的字段
        List<Field> fields = Arrays.stream(FileDto.class.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(ExcelColumn.class))
                .sorted(Comparator.comparingInt(f -> f.getAnnotation(ExcelColumn.class).order()))
                .collect(Collectors.toList());
        //  创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        Font headerFont = workbook.createFont();
        headerFont.setFontName("黑体");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        // 4. 创建Sheet和表头
        SXSSFSheet sheet = workbook.createSheet("问答记录导出");
        SXSSFRow headerRow = sheet.createRow(0);
        // 生成中文表头
        for (int i = 0; i < fields.size(); i++) {
            ExcelColumn anno = fields.get(i).getAnnotation(ExcelColumn.class);
            headerRow.createCell(i).setCellValue(anno.name());
            headerRow.getCell(i).setCellStyle(headerStyle);
            sheet.setColumnWidth(i, 4000);
        }
        // 5. 填充数据
        for (int rowIdx = 0; rowIdx < dataList.size(); rowIdx++) {
            FileDto item = dataList.get(rowIdx);
            SXSSFRow row = sheet.createRow(rowIdx + 1);
            for (int colIdx = 0; colIdx < fields.size(); colIdx++) {
                Field field = fields.get(colIdx);
                field.setAccessible(true);
                SXSSFCell cell = row.createCell(colIdx);
                cell.setCellStyle(dataStyle);
                try {
                    Object value = field.get(item);
                    String cellValue = "";
                    if (value != null) {
                        if (value instanceof Date) {
                            cellValue = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value);
                        } else {
                            cellValue = value.toString();
                        }
                    }
                    cell.setCellValue(StringUtils.isBlank(cellValue)? "--" : cellValue);
                } catch (Exception e) {
                    cell.setCellValue("");
                }
            }
        }
        return workbook;
    }

    public Boolean getChatLoginRecord() {
        ChatLoginRecordExample example = new ChatLoginRecordExample();
        LocalDate today = LocalDate.now();

        // 当天的0点
        LocalDateTime startOfDay = today.atStartOfDay();
        Date startOfDayDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());

        // 当天的24点（即第二天的0点）
        LocalDateTime endOfDay = today.plusDays(1).atStartOfDay().minusNanos(1);
        Date endOfDayDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        example.createCriteria().andCreateTimeBetween(startOfDayDate,endOfDayDate).andCreateUserEqualTo(getUserInfo().getUserId());
        List<ChatLoginRecord> chatLoginRecords = chatLoginRecordMapper.selectByExample(example);
        return CollectionUtils.isEmpty(chatLoginRecords);
    }

    public void setChatLoginRecord(HttpServletRequest request) {
        ChatLoginRecord record = new ChatLoginRecord();
        record.setCreateUser(getUserInfo().getUserId());
        record.setCreateTime(new Date());
        record.setUserName(getUserInfo().getUsername());
        record.setIp(getIpAddress(request));
        chatLoginRecordMapper.insert(record);
    }

    public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("x-forwarded-for");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                try {
                    InetAddress inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    // log error
                }
            }
        }
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0];
        }
        return ip;
    }
}

