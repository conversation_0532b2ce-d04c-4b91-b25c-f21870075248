package com.stock.service.platform.compliance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetMessageResponse {
    private String contentId;
    private String content;
    private List<String> prompt;
    private TokenUsage tokenUsage;          //token使用情况
    private Material material;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Material {
        @Builder.Default
        private int laws = 0;
        @Builder.Default
        private int answer = 0;
        @Builder.Default
        private int violate = 0;
    }
}
