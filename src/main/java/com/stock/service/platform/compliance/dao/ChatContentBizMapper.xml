<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.compliance.dao.ChatContentBizMapper">

    <resultMap id="BaseResultMap" type="com.stock.service.platform.compliance.dto.ChatContentDto" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="record_id" property="recordId" jdbcType="VARCHAR" />
        <result column="ai_type" property="aiType" jdbcType="TINYINT" />
        <result column="question_content" property="questionContent" jdbcType="VARCHAR" />
        <result column="answer_content" property="answerContent" jdbcType="VARCHAR" />
        <result column="question_word_count" property="questionWordCount" jdbcType="INTEGER" />
        <result column="answer_word_count" property="answerWordCount" jdbcType="INTEGER" />
        <result column="qa_word_count" property="qaWordCount" jdbcType="INTEGER" />
        <result column="qa_type" property="qaType" jdbcType="TINYINT" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="question_time" property="questionTime" jdbcType="TIMESTAMP" />
        <result column="answer_time" property="answerTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="getAllWordCount" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto" resultType="java.lang.Integer">
        SELECT sum(t.qa_word_count) FROM chat_content t
        WHERE t.record_id = #{chatId}
        <if test="qaType != null and qaType != ''">
            and t.qa_type = #{qaType}
        </if>
<!--        <if test="status != null and status != ''">-->
<!--            and t.status = #{status}-->
<!--        </if>-->
        AND  t.status in
        <foreach collection="statusList" item="result" index="index" open="(" close=")" separator=",">
            #{result}
        </foreach>
        <if test="chatContentId != null and chatContentId != ''">
            or t.id = #{chatContentId}
        </if>
    </select>

    <select id="getHistoryWordCount" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto" resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        SELECT sum(t.qa_word_count) as qaWordCount, sum(t.question_word_count) as questionWordCount, sum(t.answer_word_count) as answerWordCount FROM chat_content t
        WHERE t.record_id = #{chatId}
        <if test="qaType != null and qaType != ''">
            and t.qa_type = #{qaType}
        </if>
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>

        <if test="chatContentId != null and chatContentId != ''">
            or t.id = #{chatContentId}
        </if>

    </select>

    <select id="updateRecordStatus" parameterType="com.stock.service.platform.compliance.dto.ChatRecordDto">
        update chat_record
        set status = #{status}
        where id = #{id}
    </select>

    <select id="updateContentStatus" parameterType="com.stock.service.platform.compliance.dto.ChatContentDto">
        update chat_content
        set del_status = #{status}
        where ai_record_id = #{recordId} or record_id = #{recordId}
    </select>

    <select id="selectChatContentList"
            parameterType="list"
            resultMap="BaseResultMap">
        SELECT cc.id,
        cc.question_content,
        cc.answer_content,
        cc.create_time,
        cc.feedback_type,
        cc.answer_type
        FROM chat_content as cc
        where cc.id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectChatContentListByRecordId"
            parameterType="java.lang.String"
            resultMap="BaseResultMap">
        SELECT
        cc.id,
        cc.ai_type,
        cc.question_content,
        cc.answer_content,
        cc.create_time
        FROM
        chat_content AS cc
        <where>
            and cc.record_id = #{recordId}
            and cc.status = '1'
            and answer_content is not null
            and answer_content != ''
        </where>
        order by cc.create_time desc
        limit 1
    </select>
    <select id="getContentIdListByRecordId" resultType="java.lang.String">
        SELECT cc.id FROM `chat_content` cc
        WHERE cc.record_id = #{recordId} and cc.`status` = '1' and cc.del_status != '1'
    </select>
    <select id="getAnswerContentByContentId"
            resultType="com.stock.service.platform.compliance.dto.ChatDongFormDto">
        select * from chat_content where id = #{contentId}
    </select>

    <update id="updateContentQaTypeByRecordId" parameterType="map">
        update chat_content set qa_type = #{qaType} where record_id = #{recordId} and  ai_record_id = #{recordId} and qa_type = '1'
    </update>

    <update id="deleteByContentId" parameterType="map">
        update chat_content set `del_status` = '1' where id = #{contentId}
    </update>

    <select id="selectSingleConversation" parameterType="com.stock.service.platform.compliance.dto.ChatDongFormDto" resultType="com.stock.service.platform.compliance.dto.ChatContentDto">
        SELECT t.id,
        t.ai_record_id as recordId,
        t.answer_type answerType,
        t.question_source questionSource,
        t.show_answer showAnswer,
        t.question_time as
        questionTime,
        t.answer_time as answerTime,
        t.create_time as createTime,
        t.qa_type as qaType,
        t.question_content as questionContent,
        t.answer_content as answerContent,
        t.answer_content_other as answerContentOther,
        t.cite_content as citeContent,
        t.status,
        t.data_order as dataOrder,
        t.ai_type as aiType,
        'false' as seeDataFlag,
        t.feedback_type feedbackType,
        t.qa_word_count as qawordCount,
        (select count(1) from answer_info_laws a where a.content_id = t.id) lawsCount,
        (select count(1) from answer_info_violate a where a.content_id = t.id) violateCount,
        (select count(1) from answer_info_qa a where a.content_id = t.id) qaCount,
        (select count(1) from answer_info_year_report a where a.content_id = t.id) reportCount,
        (select count(1) from answer_info_bing a where a.content_id = t.id) bingCount,
        t.financial_table financialTable,
        t.platform_user_document_parsing_map_id as platformUserDocumentParsingMapId,
        t.financial_condition  financialCondition,
        t.cal_flag calFlag
        FROM chat_content t
        <where>
           t.id = #{chatContentId}
        </where>
        order by t.question_time asc;
    </select>

    <update id="updateFeedbackType" parameterType="map">
        update chat_content set feedback_type = #{feedbackType}  where id = #{id}
    </update>

    <select id="selectContent" parameterType="map" resultType="java.lang.String">
        select feedback_type from chat_content where id = #{id}
    </select>
</mapper>