package com.stock.service.platform.compliance.dao;


import com.stock.service.platform.compliance.dto.ChatContentDto;
import com.stock.service.platform.compliance.dto.ChatDongFormDto;
import com.stock.service.platform.compliance.dto.ChatRecordDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ChatRecordBizMapper {

    List<ChatRecordDto> getChatRecordList(ChatDongFormDto param);

    List<ChatContentDto> getChatContentList(ChatDongFormDto chatDongFormDto);

    Integer getAllFileSize(String userId);

    ChatContentDto queryNl2sqlTableSize(String id);

    List<ChatContentDto> getWenChatContentList(ChatDongFormDto chatDongFormDto);

    List<ChatContentDto> getAssistantChatContent(ChatDongFormDto chatDongFormDto);
}
