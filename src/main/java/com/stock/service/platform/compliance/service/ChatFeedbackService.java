package com.stock.service.platform.compliance.service;

import com.stock.core.dto.UserInfo;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.ChatFeedbackConversationRecordMapper;
import com.stock.service.platform.common.dao.ChatFeedbackMapper;
import com.stock.service.platform.compliance.dto.ChatFeedbackConversationRecordDto;
import com.stock.service.platform.compliance.dto.ChatFeedbackDto;
import com.stock.service.platform.compliance.dto.ExcelColumn;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class ChatFeedbackService extends BaseService {

    @Resource
    private ChatFeedbackMapper chatFeedbackMapper;

    @Resource
    private ChatFeedbackConversationRecordMapper chatFeedbackConversationRecordMapper;


    public Map<String, Object> getTableList(ChatFeedbackDto feedbackDto) {
        Map<String, Object> map = new HashMap<>();
        feedbackDto.setStartRow((feedbackDto.getStartRow() - 1) * feedbackDto.getPageSize());
        map.put("tableList", chatFeedbackMapper.getTableList(feedbackDto));
        map.put("totalSize", chatFeedbackMapper.getTotalSize(feedbackDto));
        return map;
    }

    public Map<String, Object> exportFieldList(ChatFeedbackDto feedbackDto) throws IOException {
        Map<String, Object> resultMap = new HashMap<>();
        SXSSFWorkbook workbook = exportFieldManage(feedbackDto);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        workbook.write(os);
        workbook.dispose();
        resultMap.put("fileName", "意见反馈答复.xlsx");
        resultMap.put("inputStream", new ByteArrayInputStream(os.toByteArray()));
        return resultMap;
    }

    private SXSSFWorkbook exportFieldManage(ChatFeedbackDto feedbackDto) {
        //  初始化工作簿和数据
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        List<ChatFeedbackDto> dataList = chatFeedbackMapper.getExportList(feedbackDto);
        //  通过反射获取带注解的字段
        List<Field> fields = Arrays.stream(ChatFeedbackDto.class.getDeclaredFields())
                .filter(f -> f.isAnnotationPresent(ExcelColumn.class))
                .sorted(Comparator.comparingInt(f -> f.getAnnotation(ExcelColumn.class).order()))
                .collect(Collectors.toList());
        //  创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        Font headerFont = workbook.createFont();
        headerFont.setFontName("黑体");
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        // 4. 创建Sheet和表头
        SXSSFSheet sheet = workbook.createSheet("意见反馈答复记录导出");
        SXSSFRow headerRow = sheet.createRow(0);
        // 生成中文表头
        for (int i = 0; i < fields.size(); i++) {
            ExcelColumn anno = fields.get(i).getAnnotation(ExcelColumn.class);
            headerRow.createCell(i).setCellValue(anno.name());
            headerRow.getCell(i).setCellStyle(headerStyle);
            sheet.setColumnWidth(i, 4000);
        }
        // 5. 填充数据
        for (int rowIdx = 0; rowIdx < dataList.size(); rowIdx++) {
            ChatFeedbackDto item = dataList.get(rowIdx);
            SXSSFRow row = sheet.createRow(rowIdx + 1);
            for (int colIdx = 0; colIdx < fields.size(); colIdx++) {
                Field field = fields.get(colIdx);
                field.setAccessible(true); // 允许访问私有字段
                SXSSFCell cell = row.createCell(colIdx);
                cell.setCellStyle(dataStyle);
                try {
                    Object value = field.get(item);
                    String cellValue = "";
                    if (value != null) {
                        // 识别需要特殊转换的字段
                        String fieldName = field.getName();
                        if ("feedbackType".equals(fieldName) || "isReply".equals(fieldName)) {
                            // 使用通用转换方法
                            cellValue = convertFieldToString(fieldName, value.toString());
                        } else if (value instanceof Date) {
                            cellValue = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format((Date) value);
                        } else {
                            cellValue = value.toString();
                        }
                    }
                    // 设置单元格值，空值显示为 "--"
                    cell.setCellValue(StringUtils.isBlank(cellValue) ? "--" : cellValue);
                } catch (Exception e) {
                    // 异常处理：设置为空字符串
                    cell.setCellValue("");
                    // 建议记录日志
                    // logger.error("填充单元格数据时出错: 行 {}, 列 {}, 字段 {}", rowIdx + 1, colIdx, field.getName(), e);
                }
            }
        }
        return workbook;
    }
    /**
     * 将特定字段的代码值转换为对应的中文描述
     * @param fieldName 字段名
     * @param value 字段的原始值 (String)
     * @return 对应的中文描述，如果代码不匹配则返回 "未知"
     */
    private String convertFieldToString(String fieldName, String value) {
        switch (fieldName) {
            case "feedbackType":
                switch (value) {
                    case "1":
                        return "回答有误";
                    case "2":
                        return "响应慢";
                    case "3":
                        return "案例有误";
                    case "4":
                        return "法规有误";
                    case "0":
                        return "其他";
                    default:
                        return "";
                }
            case "isReply":
                switch (value) {
                    case "0":
                        return "否";
                    case "1":
                        return "是";
                    default:
                        return "";
                }
            default:
                return "";
        }
    }

    /**
     * 答复反馈
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean replyFeedback(String feedbackId, String content) {
        UserInfo userInfo = getUserInfo();
        Date currentTime = new Date();

        // 1. 插入对话记录
        ChatFeedbackConversationRecordDto conversationRecord = new ChatFeedbackConversationRecordDto();
        conversationRecord.setFeedbackId(feedbackId);
        conversationRecord.setContent(content);
        conversationRecord.setRealName((String) userInfo.getInfo().get("realName"));
        conversationRecord.setReplyFeedbackStatus("0"); // 答复
        conversationRecord.setCreateTime(currentTime);
        conversationRecord.setCreateUser(userInfo.getUserId());
        chatFeedbackConversationRecordMapper.insertSelective(conversationRecord);

        // 2. 更新反馈表
        ChatFeedbackDto feedbackDto = new ChatFeedbackDto();
        feedbackDto.setId(feedbackId);
        feedbackDto.setNewReply(content);
        feedbackDto.setNewReplyTime(currentTime);
        feedbackDto.setIsReply("1");
        feedbackDto.setIsReplyNew("1");
        feedbackDto.setReplyName((String) userInfo.getInfo().get("realName"));
        feedbackDto.setIsFeedbackNew("0");
        feedbackDto.setUpdateTime(currentTime);
        feedbackDto.setUpdateUser(userInfo.getUserId());

        return chatFeedbackMapper.updateContinueFeedback(feedbackDto) > 0;
    }
}
