package com.stock.service.platform.compliance.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ChatRecordDto implements Serializable {

    private String id;

    private String userId;

    private String settingId;

    private String chatName;

    private String aiType;

    private String chatType;

    private Integer status;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private List<ChatContentDto>contentList;

    private int keywordNum;

    private int keywordAll; // 当前会话关键词总数

    private String contentAllText; // 当前会话所有文本内容

    private static final long serialVersionUID = 1L;

}