package com.stock.service.platform.compliance.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDto implements Serializable {
    private String id; //id
    @NotNull(message = "recordId不能为空")
    private String recordId; //id
    @NotNull(message = "question不能为空")
    private String question; //问题
    @NotNull(message = "prompt不能为空")
    private String prompt = ""; //提示
    @NotNull(message = "aiType不能为空")
    private String aiType; //模型
    @NotNull(message = "belongsPlate不能为空")
    private String belongsPlate; //所属板块
    private Integer status; // 状态
    private String origin;
    private String link;
    private String continuousChat; // 是否开启连续问答1开启0关闭
    private List<HistoryMessage> historyMessages;
    private String aiTypeName;
    private String ip;
    private String contentId;
    @Data
    public static class HistoryMessage {
        @NotNull(message = "question不能为空")
        private String question;
        @NotNull(message = "content不能为空")
        private String content;
    }
}
