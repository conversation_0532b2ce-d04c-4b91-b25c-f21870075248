<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.home.dao.ListedCompanyMapper">

<!--    (SELECT COMPPROFILE FROM orga_bi_orgbaseinfo WHERE COMPANYCODE = scc.rela_code) compprofileDc,-->
<!--    <select id="getCompanyInfo" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" parameterType="java.lang.String">-->
<!--        SELECT scc.id,-->
<!--               scc.company_code companyCode,-->
<!--               scc.zh_name zhName,-->
<!--               scc.corporate,-->
<!--               scc.phone,-->
<!--               scc.company_mail companyMail,-->
<!--               scc.tax_no taxNo,-->
<!--               scc.company_website companyWebsite,-->
<!--               scc.register_address registerAddress,-->
<!--               scc.rela_code relaCode,-->
<!--               (SELECT sc.system_state FROM sa_company sc WHERE sc.company_code = scc.company_code LIMIT 1) systemState-->
<!--        FROM `capital-repmanage-jt`.sa_company_cloud scc-->
<!--        <where>-->
<!--            <if test="companyCode != null and companyCode !='' ">-->
<!--                AND scc.company_code LIKE concat('%',#{companyCode,jdbcType=VARCHAR},'%')-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY scc.create_time DESC-->
<!--        LIMIT 1-->
<!--    </select>-->
    <select id="getCompanyInfo" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" parameterType="java.lang.String">
        SELECT
        scc.id,
        sc.company_code companyCode,
        sc.zh_name zhName,
        scc.corporate,
        scc.phone,
        scc.company_mail companyMail,
        scc.tax_no taxNo,
        scc.company_website companyWebsite,
        scc.register_address registerAddress,
        scc.rela_code relaCode,
        sc.system_state systemState
        FROM
        sa_company sc
        LEFT JOIN `capital-repmanage-jt`.sa_company_cloud scc ON sc.company_code = scc.company_code
        <where>
            <if test="companyCode != null and companyCode !='' ">
                AND sc.company_code = #{companyCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY sc.create_time DESC
        LIMIT 1
    </select>

    <!-- 股东人数  -->
    <select id="getDailyInfo" resultType="Map" parameterType="java.lang.String">
        SELECT CONCAT(ROUND(vtsd.share_num / 10000, 2 ),'万人') shareNum
        FROM vc_trad_sk_daily vtsd
        WHERE vtsd.security_cd=#{companyCode} ORDER BY trade_date DESC LIMIT 0,1
    </select>


    <select id="selectCompanyLogUrl" resultType="java.lang.String" parameterType="java.lang.String">
        select t.os_att_url
        from `capital-repmanage-jt`.sa_attachment_com_logo t
        where t.business_id = #{id} order by t.sub_type desc limit 1
    </select>


    <select id="queryCongrol" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto"
            resultType="com.stock.service.platform.home.dto.DongcaiTbDto">
        SELECT
            GROUP_CONCAT( recompanynameDc ) as recompanynameDc,
            RELATIONTYPE,
            reportdateDc
        FROM
            ( SELECT
                  t.recompanyName recompanynameDc, t.relationType, DATE_FORMAT( t.reportDate, '%Y/%m/%d' ) reportdateDc
              FROM LICO_CM_RELPARTY
                       t WHERE t.COMPANYCODE = #{relaCode} AND t.RELATIONTYPE = '018' AND t.EISDEL = '0' GROUP BY t.RECOMPANYNAME
            ) t
        GROUP BY
            t.reportdateDc
        ORDER BY
            t.reportdateDc ASC
    </select>


    <select id="getCompanyStatus" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto" resultType="java.lang.String">
        SELECT status from  sa_company_external where company_code=#{companyCode}
    </select>


    <select id="getComIndustry" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" parameterType="java.lang.String">
        SELECT (SELECT (SELECT ma.label_name FROM `capital-repmanage-jt`.maa_conf_label ma where ma.label_code='INDUSTRY_SW_2021' and label_value=m.IND_CD) industrySWStr FROM `capital-repmanage-jt`.vc_im_inchg m WHERE m.company_code = sc.company_code and m.IND_TYPE='026' and m.IS_NEW = '1' and m.d_flag = '0' limit 1) industrySWStr,
(SELECT  (SELECT ma.label_name FROM `capital-repmanage-jt`.maa_conf_label ma where ma.label_code='INDUSTRY_CSRC_2012' and label_value=m.IND_CD) industryCSRCStr FROM `capital-repmanage-jt`.vc_im_inchg m WHERE m.company_code = sc.company_code and m.IND_TYPE='008' and m.IS_NEW = '1' and m.d_flag = '0' limit 1) industryCSRCStr
        from sa_company sc where sc.company_code=#{companyCode} limit 1
    </select>




    <select id="getComStock" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto" parameterType="com.stock.service.platform.common.system.dto.SaCompanyDto">
        SELECT (SELECT code_name from sa_code where code_no="STOCK_TYPE" and code_value=sc.stock_type) stockType,
               (SELECT code_name from sa_code where code_no="LISTSTATE" and code_value=sc.liststate) liststate,sc.zh_sort_name zhSortName,sc.company_code companyCode
        FROM `capital-repmanage-jt`.sa_company_cloud sc where sc.rela_code= #{relaCode} ORDER BY sc.stock_type
        LIMIT 12
    </select>

    <select id="getTodayClosingPrice" parameterType="com.stock.service.platform.common.system.dto.VcTradSkDto" resultType="com.stock.service.platform.common.system.dto.VcTradSkDto">
        SELECT ROUND((a.NEW * d.AFACTOR / ld.AFACTOR), 4) AS NEW,
        DATE_FORMAT(a.TRADE_DATE,'%Y-%m-%d') tradeDate,
        a.CHG,
        a.TVALCNY,
        a.CJL
        FROM vc_trad_sk_daily a
        JOIN `capital-repmanage-jt`.vc_trad_sk_factor d ON d.SECURITY_CD = a.SECURITY_CD AND d.TRADE_DATE = a.TRADE_DATE
        LEFT JOIN (SELECT d.SECURITY_CD, d.AFACTOR
        FROM `capital-repmanage-jt`.vc_trad_sk_factor d WHERE (d.TRADE_DATE = (SELECT MAX(t.trade_date)
        FROM (SELECT trade_date
        FROM `capital-repmanage-jt`.`trade_date`
        WHERE trade_date &lt;= #{today}
        AND band_type = 'A'
        ORDER BY trade_date DESC
        LIMIT 1) t))) ld ON ld.SECURITY_CD = a.SECURITY_CD
        LEFT JOIN `capital-repmanage-jt`.trade_date td ON a.TRADE_DATE = td.trade_date AND band_type = 'A'
        WHERE a.TRADE_DATE &lt;= #{today}
        AND a.NEW IS NOT NULL
        <if test="companyCode != null and companyCode != ''">
            AND a.SECURITY_CD = #{companyCode}
        </if>
        ORDER BY a.TRADE_DATE DESC
        LIMIT 30
    </select>

    <select id="querySharesInfoPOC" resultType="Map" parameterType="java.util.Map">
        SELECT
            NEW shares,
            CAST(ROUND(NEW * CHG / 100, 2) AS CHAR) upanddown,
            ROUND(CHG, 2) upanddownrate,
            CONCAT(ROUND(vtsd.ZSZ / *********, 2 ),'亿') marketTotal,
               CONCAT(ROUND(vtsd.AGSZBHXS / *********, 2 ),'亿') marketCirculate,
            ROUND(PE9, 2) PERatio,
            ROUND(PB8, 2) PBRatio,
               vtsd.TOTALSHARES totalStockNum,
               CONCAT(ROUND(vtsd.share_num / 10000, 2 ),'万人') shareNum
        FROM vc_trad_sk_daily vtsd
        WHERE vtsd.security_cd=#{companyCode} ORDER BY trade_date DESC LIMIT 0,1
    </select>


    <select id="getMaketId" resultType="java.util.Map" parameterType="java.lang.String">
        SELECT belongs_plate plate,hk_Stock_Plate hkPlate FROM `capital-repmanage-jt`.`sa_company_cloud` where company_code = #{companyCode} limit 1
    </select>
    <select id="getMarketStockType" resultType="java.lang.String">
        SELECT market_stock_type from  sa_company where company_code=#{companyCode}
    </select>
    <select id="getSystemState" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT( sys_name )
        FROM
            sa_system_config
        WHERE
            `status` = '1'
          AND FIND_IN_SET(sys_name, #{systemState})
    </select>
</mapper>