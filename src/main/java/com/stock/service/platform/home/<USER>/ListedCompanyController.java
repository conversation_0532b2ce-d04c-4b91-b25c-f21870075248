package com.stock.service.platform.home.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.home.service.ListedCompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 上市公司信息
 */

@RestController
@RequestMapping("listedCompany")
public class ListedCompanyController extends BaseController {

    @Autowired
    private ListedCompanyService listedCompanyService;

//    @Value("${spring.besmq.broker-url}")
//    private String brokerUrl;

    /**
     * 股票信息图示画面实时股价信息部分
     **/
    @RequestMapping(value = "/querySharesInfo", method = RequestMethod.POST)
    @ResponseBody
    public JsonResponse<List<Map<String, Object>>> querySharesInfo(@RequestBody Map<String, Object> map) {
        JsonResponse<List<Map<String, Object>>> response = new JsonResponse<List<Map<String, Object>>>();
        Map<String, Object> sharesMessageMap = null;
        try {
            sharesMessageMap = listedCompanyService.querySharesInfo(map);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<Map<String, Object>> dateList = new ArrayList<Map<String,Object>>();
        dateList.add(sharesMessageMap);
        response.setResult(dateList);
        return response;
    }


    /**
     * 股票信息图示画面实时股价信息部分
     **/
    @RequestMapping(value = "/queryCompanyInfo", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> queryCompanyInfo(@RequestBody Map<String, Object> map) throws IllegalAccessException {
        Map<String, Object> sharesMessageMap = listedCompanyService.queryCompanyInfo(map);
        return sharesMessageMap;
    }

}