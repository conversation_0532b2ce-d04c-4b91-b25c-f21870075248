package com.stock.service.platform.home.service;

import com.stock.core.dto.Page;
import com.stock.core.service.BaseService;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.entity.OrgExample;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.home.dto.HomeParamDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class HomeService extends BaseService {

    @Resource
    private OrgMapper orgMapper;

    /**
     * 查询公司信息
     *
     * @return
     */
    public Map<String, Object> getCompanyList(HomeParamDto homeParamDto) {
        if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("isAdmin"),"1") || StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"1")) {
            // 超级管理员或者总部用户
            homeParamDto.setOrgIds(null);
        } else if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"2")) {
            // 分公司用户
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andStatusEqualTo("1").andOrgTypeLessThanOrEqualTo("3");
            List<Org> orgList = orgMapper.selectByExample(orgExample);
            List<Org> subsets = findSubsetsById(orgList, Arrays.asList(getUserInfo().getInfo().get("orgId").toString().split(",")), "3");
            List<String> orgIds = subsets.stream().map(Org::getId).collect(Collectors.toList());
            homeParamDto.setOrgIds(orgIds);
        } else if (StringUtils.equals((CharSequence) getUserInfo().getInfo().get("orgType"),"3")) {
            // 营业部用户
            OrgExample orgExample = new OrgExample();
            orgExample.createCriteria().andStatusEqualTo("1").andOrgTypeLessThanOrEqualTo("3");
            List<Org> orgList = orgMapper.selectByExample(orgExample);
            List<String> orgIds = new ArrayList<>(Arrays.asList(getUserInfo().getInfo().get("orgId").toString().split(",")));
            List<Org> orgListByOrgId = orgMapper.selectBySonIds(orgIds);
            for (Org org : orgListByOrgId) {
                // 判断该营业部所属分公司是否同意营业部互相查看
                if ("1".equals(org.getThresholdFlag())) {
                    List<Org> subsets = findSubsetsById(orgList, Arrays.asList(org.getId().split(",")), "3");
                    List<String> orgIdList = subsets.stream().map(Org::getId).collect(Collectors.toList());
                    orgIds.addAll(orgIdList);
                }
            }
            homeParamDto.setOrgIds(orgIds);
        }
        Page<SaCompanyDto> list = this.getPageList(homeParamDto.getStartRow(), homeParamDto.getPageSize(),
                "com.stock.service.platform.home.dao.HomeMapper.getCompanyList", homeParamDto);
        Map<String, Object> result = new HashMap<>();
        result.put("tableData", list.getData());
        result.put("total", list.getTotal());
        result.put("userOrgType", getUserInfo().getInfo().get("orgType"));
        return result;
    }

    public List<Org> findSubsetsById(List<Org> orgList, List<String> parentIds) {
        List<Org> subsets = new ArrayList<>();
        for (String parentId : parentIds) {
            subsets.addAll(findSubsetsById(orgList, parentId));
        }
        return subsets;
    }

    private List<Org> findSubsetsById(List<Org> orgList, String parentId) {
        List<Org> subsets = new ArrayList<>();
        for (Org org : orgList) {
            if (org.getpOrgId() != null && org.getpOrgId().equals(parentId)) {
                subsets.add(org);
                subsets.addAll(findSubsetsById(orgList, org.getId()));
            }
        }
        return subsets;
    }

    public List<Org> findSubsetsById(List<Org> orgList, List<String> parentIds, String orgType) {
        List<Org> subsets = findSubsetsById(orgList, parentIds);
        subsets.removeIf(org -> !org.getOrgType().equals(orgType));
        return subsets;
    }

}
