<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.home.dao.HomeMapper">
    <select id="getCompanyList" resultType="com.stock.service.platform.common.system.dto.SaCompanyDto"
            parameterType="com.stock.service.platform.home.dto.HomeParamDto">
        SELECT
            sc.id id,
            sc.company_code companyCode,
            sc.zh_name zhName,
            sc.corporate corporate,
            sc.organisation_no organisationNo,
            sc.company_stock_name companyStockName,
            sc.corporate_id_number corporateIdNumber,
            sc.zh_sort_name zhSortName,
            sc.deleteflag deleteflag,
            sc.hidden_key seriakey,
            GROUP_CONCAT( DISTINCT ssc.sys_name ) systemState,
            (CASE sc.deleteflag WHEN '0' THEN '正常' WHEN '2' THEN '删除中' ELSE '注销' END) statusName,
            org.org_name businessDepartmentName,
            pr.area_name city,
            (SELECT t.os_att_url FROM `capital-repmanage-jt`.sa_attachment_com_logo t WHERE t.business_id = scc.id ORDER BY t.sub_type DESC LIMIT 1) logoUrl
        FROM sa_company sc
        LEFT JOIN sa_system_config ssc ON FIND_IN_SET(ssc.sys_name, sc.system_state) AND ssc.status = '1'
        LEFT JOIN sa_org org ON FIND_IN_SET(org.id, sc.organisation_no)
        LEFT JOIN `capital-repmanage-jt`.sa_company_cloud scc ON scc.company_code=sc.company_code
        LEFT JOIN `capital-repmanage-jt`.provinces pr ON pr.area_no=scc.city
        LEFT JOIN (SELECT m.IND_CD industrySWStr,m.org_code FROM `capital-repmanage-jt`.vc_im_inchg m WHERE m.IND_TYPE='026' AND m.IS_NEW =
        '1' AND m.d_flag = '0' ) sw ON sw.org_code=scc.rela_code
        LEFT JOIN (SELECT m.IND_CD industryCSRCStr,m.org_code FROM `capital-repmanage-jt`.vc_im_inchg m WHERE m.IND_TYPE='008' AND m.IS_NEW =
        '1' AND m.d_flag = '0' ) csr ON csr.org_code=scc.rela_code
        <where>
            sc.deleteflag ='0'
            AND (sc.parent_id IS NULL OR sc.parent_id = '')
            AND (sc.parent_company_code IS NULL OR sc.parent_company_code = '')
            <if test="orgIds != null and orgIds.size !=0">
                AND org.id IN
                <foreach collection="orgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="zhName != null and zhName !='' ">
                AND (sc.zh_sort_name LIKE concat('%',#{zhName,jdbcType=VARCHAR},'%') OR sc.company_code LIKE
                CONCAT('%',#{zhName,jdbcType=VARCHAR},'%'))
            </if>
            <if test="city != null and city.length !=0">
                AND scc.city IN
                <foreach collection="city" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="industrySWStr != null and industrySWStr.length !=0">
                AND sw.industrySWStr IN
                <foreach collection="industrySWStr" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="industryCSRCStr != null and industryCSRCStr.length !=0">
                AND csr.industryCSRCStr IN
                <foreach collection="industryCSRCStr" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgform != null and orgform.length !=0">
                AND sc.orgform IN
                <foreach collection="orgform" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="companyCodeList != null and companyCodeList.size !=0">
                AND sc.company_code IN
                <foreach collection="companyCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY sc.id
        ORDER BY IFNULL(length(sc.system_state), 0) DESC, sc.company_code
    </select>
<!--    ,-->
<!--    (SELECT GROUP_CONCAT(sl.sys_name SEPARATOR ',') FROM sa_system_config sl WHERE FIND_IN_SET(sl.sys_code, sc.system_state)) systemStateName-->
<!--    LEFT JOIN sa_user_company_map sucm ON sucm.company_id = sc.id-->
<!--    <if test="userId != null and userId !='' ">-->
<!--        AND sucm.user_id = #{userId}-->
<!--    </if>-->

</mapper>