package com.stock.service.platform.home.dto;

import java.io.Serializable;

public class DongcaiTbDto implements Serializable {
    //表：ORGA_BI_ORGBASEINFO
    private String companycodeDc;//东财内码
    private String blnumbDc;//统一信用代码
    private String companynameDc;//公司全称
    private String companysnameDc;//公司简称
    private String fennameDc;//公司英文名称
    private String founddateDc;//成立时间
    private String legaladvisorDc;//律师事务所
    private String accountingfirmDc;//会计师事务所
    private String gmanagerDc;//总经理
    private String bsecretaryDc;//董事会秘书
    private String chairmanDc;//董事长
    private String employmunDc;//人员规模
    private String regaddressDc;//注册地址
    private String officeaddressDc;//办公地址
    private String compprofileDc;//公司简介
    private String mainproductsDc;//主营产品
    private String businscopeDc;//经营范围
    private String compscopeDc;//简史

    //表：CDSY_SECUCODE
    private String securitysnameDc;//简称
    private String securitycodeDc;//代码
    private String trademarketcodeDc;//板块
    private String securitytypecodeDc;//市场类别（a股、b股等）
    private String securitytypecodeNameDc;//市场类别
    private String liststateDc;//上市状态
    private String listdateDc;//上市时间
    private String stockType; // 取票类型 ab股
    private String publicStatus; // 上市状态

    //表：LICO_IM_INCHG
    private String INDTYPE008Dc;//证监会行业
    private String INDTYPE026Dc;//申万行业

    //表：LICO_FP_ISSUEBASICINFO/LICO_FP_ISSUEPART
    private String partynameDc;//主承销商

    //表：LICO_CM_RELPARTY
    private String recompanynameDc;//控制人
    private String reportdateDc;//时间

    //表：LICO_MO_WORKERS
    private String actualitemDc;//员工类型
    private String numemployeesDc;//人数
    private String enddateDc;//日期

    //表：LICO_ES_CPHSSTRUCT
    private String changereasonDc;//股本变动原因
    private String totalshareDc;//总股本(万股)
    private String urshareDc;//无限售流通股(万股)
    private String ashareDc;//流通a股(万股)
    private String bshareDc;//流通b股(万股)
    private String hshareDc;//流通h股(万股)
    private String othercirculatedshareDc;//其他流通股(万股)
    private String rshareDc;//限售流通股(万股)
    private String asharerDc;//限售a股(万股)
    private String statesharerDc;//国家持股(万股)
    private String statelpsharerDc;//国有法人持股(万股)
    private String otherdsharerDc;//其他内资持股合计(万股)
    private String dnotstatelpsharerDc;//其中:境内法人持股(万股)
    private String dnpsharerDc;//其他境内自然人持股(万股)
    private String tmshareDc;//高管持股(万股)
    private String foreigncapitalsharerDc;//外资持股合计(万股)
    private String foreignlpsharerDc;//其中:境外法人持股(万股)
    private String foreignnpsharerDc;//境外自然人持股(万股)
    private String bsharerDc;//限售b股(万股)
    private String hsharerDc;//限售h股(万股)
    private String ncshareDc;//未流通股(万股)
    private String promotershareDc;//发起人股份(万股)
    private String statepromotershareDc;//其中:国有发起人股(万股)
    private String promotersociallpshareDc;//发起社会法人股(万股)
    private String raisershareDc;//募集人股份(万股)
    private String raisestateshareDc;//其中:募集国家股(万股)
    private String raisedlpshareDc;//募集境内法人股(万股)
    private String raiseforignlpshareDc;//募集境外法人股(万股)
    private String noticedateDc;//公告日期
    private String stockListdateDc;//上市时间

    //表：LICO_ES_SXJJSJB
    private String cirdateDc;//上市日期
    private String nowcshareDc;//本期解除限售股数量
    private String notcshareDc;//限售股数量(万股)
    private String csharetypeDc;//流通股份数量类型
    private String ltratioDc;//占a股已流通数量比例(%)
    private String allratioDc;//占a股总股本比例(%)

    //表：LISTINGDATETD
    private String limitsharehdnameDc;//股东名称
    private String addtradenumDc;//本次解禁股份
    private String pluslimitednumDc;//剩余有限售股份
    private String listingDateTdDc;//剩余有限售股份
    private String referCost;//参考成本
    private String limitedType;//=9的都是空

    //表：LICO_ES_SHHDFROZENEW
    private String updatedatePledgeDc;//更新日期
    private String sharehdnameDc;//股东名称
    private String amtsharefrozenDc;//累计质押股数(万股)
    private String amtfrozenratioDc;//占总股本比例(%)
    private String amtshratioDc;//累计质押数量占持股比例(%)
    private String frozenstartdateDc;//质押起始日
    private String freedtaeinadDc;//解押日期
    private String frozeninstDc;//质押方
    private String sharefrozennumDc;//质押股数(万股)
    private String frozenratioDc;//占所持股比例(%)
    private String frozenintotalDc;//占总股本比例(%)
    private String invamountDc;//涉及金额(万元)

    //表：LICO_YS_STOCKVALUE
    private String zszDc;//总市值
    private String agszbhxsDc;//流通总市值

    //表：TRAD_SK_REVALUATION
    private String pe9Dc;//市盈率ttm
    private String pe2Dc;//市盈率lyr
    private String pb8Dc;//市净率mrq
    private String ps9Dc;//市销率ttm
    private String pcfjyxjl9Dc;//市现率ttm

    //表：LICO_FN_ROEANDEPS
    private String basicepsDc;//每股收益

    //表：LICO_FN_ROEANDEPS
    private String bpsDc;//每股净资产
    private String mgjyxjjeDc;//每股现金流
    private String xsjllDc;//销售净利率

    //表：LICO_FN_MAINFINADATA_A
    private String dpnp_yoy_ratioDc;//净利润增长率

    //表：lico_mo_dshjs/lico_mo_busilevel
    private String noticedateReltradeDc;//发生时间
    private String transAffiliateDc;//交易对方
    private String transRelationDc;//关联关系
    private String transIsconrelaDc;//是否存在控制关系
    private String transjineDc;//交易金额
    private String defraymodeDc;//支付方式
    private String currencyDc;//币种
    private String transsummaryDc;//交易简述

    //表：LICO_FN_MAINFINADATA_A
    private String personnameTypeDc;//类别
    private String personnameDc;//姓名
    private String resumeDc;//简介
    private String postDc;//职务
    private String startdateDc;//任职起始日
    private String djgEnddateDc;//任职结束日
    private String sexDc;//性别
    private String countryDc;//国籍
    private String highestdegreeDc;//学历
    private String birthYearDc;//学历
    private String ageDc;//年龄

    //表：LICO_MO_MANHOLDRPAY
    private String anualwageDc;//报酬
    private String jobState;//任职状态
    private String changenumDc;//本次变动数
    private String ehnDc;//变动后持股数
    private String avpriceDc;//成交均价
    private String reasonDc;//变动原因
    private String changePriceDc;//变动市值
    private String changenumRaitDc;//变动比例
    private String ehnRaitDc;//变动后持股比例
    private String zgbDc;

    //表：CDSY_CHANGEINFO/CDSY_CHANGESTATE
    private String changedateDc;//变化时间
    private String reasoncodeDc;//变动原因
    private String reasonTypeDc;//变动类型

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public String getPublicStatus() {
        return publicStatus;
    }

    public void setPublicStatus(String publicStatus) {
        this.publicStatus = publicStatus;
    }

    public String getZgbDc() {
        return zgbDc;
    }

    public void setZgbDc(String zgbDc) {
        this.zgbDc = zgbDc;
    }

    public String getCompanycodeDc() {
        return companycodeDc;
    }

    public void setCompanycodeDc(String companycodeDc) {
        this.companycodeDc = companycodeDc;
    }

    public String getBlnumbDc() {
        return blnumbDc;
    }

    public void setBlnumbDc(String blnumbDc) {
        this.blnumbDc = blnumbDc;
    }

    public String getCompanynameDc() {
        return companynameDc;
    }

    public void setCompanynameDc(String companynameDc) {
        this.companynameDc = companynameDc;
    }

    public String getCompanysnameDc() {
        return companysnameDc;
    }

    public void setCompanysnameDc(String companysnameDc) {
        this.companysnameDc = companysnameDc;
    }

    public String getFennameDc() {
        return fennameDc;
    }

    public void setFennameDc(String fennameDc) {
        this.fennameDc = fennameDc;
    }

    public String getFounddateDc() {
        return founddateDc;
    }

    public void setFounddateDc(String founddateDc) {
        this.founddateDc = founddateDc;
    }

    public String getLegaladvisorDc() {
        return legaladvisorDc;
    }

    public void setLegaladvisorDc(String legaladvisorDc) {
        this.legaladvisorDc = legaladvisorDc;
    }

    public String getAccountingfirmDc() {
        return accountingfirmDc;
    }

    public void setAccountingfirmDc(String accountingfirmDc) {
        this.accountingfirmDc = accountingfirmDc;
    }

    public String getGmanagerDc() {
        return gmanagerDc;
    }

    public void setGmanagerDc(String gmanagerDc) {
        this.gmanagerDc = gmanagerDc;
    }

    public String getBsecretaryDc() {
        return bsecretaryDc;
    }

    public void setBsecretaryDc(String bsecretaryDc) {
        this.bsecretaryDc = bsecretaryDc;
    }

    public String getChairmanDc() {
        return chairmanDc;
    }

    public void setChairmanDc(String chairmanDc) {
        this.chairmanDc = chairmanDc;
    }

    public String getEmploymunDc() {
        return employmunDc;
    }

    public void setEmploymunDc(String employmunDc) {
        this.employmunDc = employmunDc;
    }

    public String getRegaddressDc() {
        return regaddressDc;
    }

    public void setRegaddressDc(String regaddressDc) {
        this.regaddressDc = regaddressDc;
    }

    public String getOfficeaddressDc() {
        return officeaddressDc;
    }

    public void setOfficeaddressDc(String officeaddressDc) {
        this.officeaddressDc = officeaddressDc;
    }

    public String getCompprofileDc() {
        return compprofileDc;
    }

    public void setCompprofileDc(String compprofileDc) {
        this.compprofileDc = compprofileDc;
    }

    public String getMainproductsDc() {
        return mainproductsDc;
    }

    public void setMainproductsDc(String mainproductsDc) {
        this.mainproductsDc = mainproductsDc;
    }

    public String getBusinscopeDc() {
        return businscopeDc;
    }

    public void setBusinscopeDc(String businscopeDc) {
        this.businscopeDc = businscopeDc;
    }

    public String getCompscopeDc() {
        return compscopeDc;
    }

    public void setCompscopeDc(String compscopeDc) {
        this.compscopeDc = compscopeDc;
    }

    public String getSecuritysnameDc() {
        return securitysnameDc;
    }

    public void setSecuritysnameDc(String securitysnameDc) {
        this.securitysnameDc = securitysnameDc;
    }

    public String getSecuritycodeDc() {
        return securitycodeDc;
    }

    public void setSecuritycodeDc(String securitycodeDc) {
        this.securitycodeDc = securitycodeDc;
    }

    public String getTrademarketcodeDc() {
        return trademarketcodeDc;
    }

    public void setTrademarketcodeDc(String trademarketcodeDc) {
        this.trademarketcodeDc = trademarketcodeDc;
    }

    public String getSecuritytypecodeDc() {
        return securitytypecodeDc;
    }

    public void setSecuritytypecodeDc(String securitytypecodeDc) {
        this.securitytypecodeDc = securitytypecodeDc;
    }

    public String getSecuritytypecodeNameDc() {
        return securitytypecodeNameDc;
    }

    public void setSecuritytypecodeNameDc(String securitytypecodeNameDc) {
        this.securitytypecodeNameDc = securitytypecodeNameDc;
    }

    public String getListstateDc() {
        return liststateDc;
    }

    public void setListstateDc(String liststateDc) {
        this.liststateDc = liststateDc;
    }

    public String getListdateDc() {
        return listdateDc;
    }

    public void setListdateDc(String listdateDc) {
        this.listdateDc = listdateDc;
    }

    public String getINDTYPE008Dc() {
        return INDTYPE008Dc;
    }

    public void setINDTYPE008Dc(String INDTYPE008Dc) {
        this.INDTYPE008Dc = INDTYPE008Dc;
    }

    public String getINDTYPE026Dc() {
        return INDTYPE026Dc;
    }

    public void setINDTYPE026Dc(String INDTYPE026Dc) {
        this.INDTYPE026Dc = INDTYPE026Dc;
    }

    public String getPartynameDc() {
        return partynameDc;
    }

    public void setPartynameDc(String partynameDc) {
        this.partynameDc = partynameDc;
    }

    public String getRecompanynameDc() {
        return recompanynameDc;
    }

    public void setRecompanynameDc(String recompanynameDc) {
        this.recompanynameDc = recompanynameDc;
    }

    public String getReportdateDc() {
        return reportdateDc;
    }

    public void setReportdateDc(String reportdateDc) {
        this.reportdateDc = reportdateDc;
    }

    public String getActualitemDc() {
        return actualitemDc;
    }

    public void setActualitemDc(String actualitemDc) {
        this.actualitemDc = actualitemDc;
    }

    public String getNumemployeesDc() {
        return numemployeesDc;
    }

    public void setNumemployeesDc(String numemployeesDc) {
        this.numemployeesDc = numemployeesDc;
    }

    public String getEnddateDc() {
        return enddateDc;
    }

    public void setEnddateDc(String enddateDc) {
        this.enddateDc = enddateDc;
    }

    public String getChangereasonDc() {
        return changereasonDc;
    }

    public void setChangereasonDc(String changereasonDc) {
        this.changereasonDc = changereasonDc;
    }

    public String getTotalshareDc() {
        return totalshareDc;
    }

    public void setTotalshareDc(String totalshareDc) {
        this.totalshareDc = totalshareDc;
    }

    public String getUrshareDc() {
        return urshareDc;
    }

    public void setUrshareDc(String urshareDc) {
        this.urshareDc = urshareDc;
    }

    public String getAshareDc() {
        return ashareDc;
    }

    public void setAshareDc(String ashareDc) {
        this.ashareDc = ashareDc;
    }

    public String getBshareDc() {
        return bshareDc;
    }

    public void setBshareDc(String bshareDc) {
        this.bshareDc = bshareDc;
    }

    public String getHshareDc() {
        return hshareDc;
    }

    public void setHshareDc(String hshareDc) {
        this.hshareDc = hshareDc;
    }

    public String getOthercirculatedshareDc() {
        return othercirculatedshareDc;
    }

    public void setOthercirculatedshareDc(String othercirculatedshareDc) {
        this.othercirculatedshareDc = othercirculatedshareDc;
    }

    public String getRshareDc() {
        return rshareDc;
    }

    public void setRshareDc(String rshareDc) {
        this.rshareDc = rshareDc;
    }

    public String getAsharerDc() {
        return asharerDc;
    }

    public void setAsharerDc(String asharerDc) {
        this.asharerDc = asharerDc;
    }

    public String getStatesharerDc() {
        return statesharerDc;
    }

    public void setStatesharerDc(String statesharerDc) {
        this.statesharerDc = statesharerDc;
    }

    public String getStatelpsharerDc() {
        return statelpsharerDc;
    }

    public void setStatelpsharerDc(String statelpsharerDc) {
        this.statelpsharerDc = statelpsharerDc;
    }

    public String getOtherdsharerDc() {
        return otherdsharerDc;
    }

    public void setOtherdsharerDc(String otherdsharerDc) {
        this.otherdsharerDc = otherdsharerDc;
    }

    public String getDnotstatelpsharerDc() {
        return dnotstatelpsharerDc;
    }

    public void setDnotstatelpsharerDc(String dnotstatelpsharerDc) {
        this.dnotstatelpsharerDc = dnotstatelpsharerDc;
    }

    public String getDnpsharerDc() {
        return dnpsharerDc;
    }

    public void setDnpsharerDc(String dnpsharerDc) {
        this.dnpsharerDc = dnpsharerDc;
    }

    public String getTmshareDc() {
        return tmshareDc;
    }

    public void setTmshareDc(String tmshareDc) {
        this.tmshareDc = tmshareDc;
    }

    public String getForeigncapitalsharerDc() {
        return foreigncapitalsharerDc;
    }

    public void setForeigncapitalsharerDc(String foreigncapitalsharerDc) {
        this.foreigncapitalsharerDc = foreigncapitalsharerDc;
    }

    public String getForeignlpsharerDc() {
        return foreignlpsharerDc;
    }

    public void setForeignlpsharerDc(String foreignlpsharerDc) {
        this.foreignlpsharerDc = foreignlpsharerDc;
    }

    public String getForeignnpsharerDc() {
        return foreignnpsharerDc;
    }

    public void setForeignnpsharerDc(String foreignnpsharerDc) {
        this.foreignnpsharerDc = foreignnpsharerDc;
    }

    public String getBsharerDc() {
        return bsharerDc;
    }

    public void setBsharerDc(String bsharerDc) {
        this.bsharerDc = bsharerDc;
    }

    public String getHsharerDc() {
        return hsharerDc;
    }

    public void setHsharerDc(String hsharerDc) {
        this.hsharerDc = hsharerDc;
    }

    public String getNcshareDc() {
        return ncshareDc;
    }

    public void setNcshareDc(String ncshareDc) {
        this.ncshareDc = ncshareDc;
    }

    public String getPromotershareDc() {
        return promotershareDc;
    }

    public void setPromotershareDc(String promotershareDc) {
        this.promotershareDc = promotershareDc;
    }

    public String getStatepromotershareDc() {
        return statepromotershareDc;
    }

    public void setStatepromotershareDc(String statepromotershareDc) {
        this.statepromotershareDc = statepromotershareDc;
    }

    public String getPromotersociallpshareDc() {
        return promotersociallpshareDc;
    }

    public void setPromotersociallpshareDc(String promotersociallpshareDc) {
        this.promotersociallpshareDc = promotersociallpshareDc;
    }

    public String getRaisershareDc() {
        return raisershareDc;
    }

    public void setRaisershareDc(String raisershareDc) {
        this.raisershareDc = raisershareDc;
    }

    public String getRaisestateshareDc() {
        return raisestateshareDc;
    }

    public void setRaisestateshareDc(String raisestateshareDc) {
        this.raisestateshareDc = raisestateshareDc;
    }

    public String getRaisedlpshareDc() {
        return raisedlpshareDc;
    }

    public void setRaisedlpshareDc(String raisedlpshareDc) {
        this.raisedlpshareDc = raisedlpshareDc;
    }

    public String getRaiseforignlpshareDc() {
        return raiseforignlpshareDc;
    }

    public void setRaiseforignlpshareDc(String raiseforignlpshareDc) {
        this.raiseforignlpshareDc = raiseforignlpshareDc;
    }

    public String getNoticedateDc() {
        return noticedateDc;
    }

    public void setNoticedateDc(String noticedateDc) {
        this.noticedateDc = noticedateDc;
    }

    public String getStockListdateDc() {
        return stockListdateDc;
    }

    public void setStockListdateDc(String stockListdateDc) {
        this.stockListdateDc = stockListdateDc;
    }

    public String getCirdateDc() {
        return cirdateDc;
    }

    public void setCirdateDc(String cirdateDc) {
        this.cirdateDc = cirdateDc;
    }

    public String getNowcshareDc() {
        return nowcshareDc;
    }

    public void setNowcshareDc(String nowcshareDc) {
        this.nowcshareDc = nowcshareDc;
    }

    public String getNotcshareDc() {
        return notcshareDc;
    }

    public void setNotcshareDc(String notcshareDc) {
        this.notcshareDc = notcshareDc;
    }

    public String getCsharetypeDc() {
        return csharetypeDc;
    }

    public void setCsharetypeDc(String csharetypeDc) {
        this.csharetypeDc = csharetypeDc;
    }

    public String getLtratioDc() {
        return ltratioDc;
    }

    public void setLtratioDc(String ltratioDc) {
        this.ltratioDc = ltratioDc;
    }

    public String getAllratioDc() {
        return allratioDc;
    }

    public void setAllratioDc(String allratioDc) {
        this.allratioDc = allratioDc;
    }

    public String getLimitsharehdnameDc() {
        return limitsharehdnameDc;
    }

    public void setLimitsharehdnameDc(String limitsharehdnameDc) {
        this.limitsharehdnameDc = limitsharehdnameDc;
    }

    public String getAddtradenumDc() {
        return addtradenumDc;
    }

    public void setAddtradenumDc(String addtradenumDc) {
        this.addtradenumDc = addtradenumDc;
    }

    public String getPluslimitednumDc() {
        return pluslimitednumDc;
    }

    public void setPluslimitednumDc(String pluslimitednumDc) {
        this.pluslimitednumDc = pluslimitednumDc;
    }

    public String getListingDateTdDc() {
        return listingDateTdDc;
    }

    public void setListingDateTdDc(String listingDateTdDc) {
        this.listingDateTdDc = listingDateTdDc;
    }

    public String getReferCost() {
        return referCost;
    }

    public void setReferCost(String referCost) {
        this.referCost = referCost;
    }

    public String getLimitedType() {
        return limitedType;
    }

    public void setLimitedType(String limitedType) {
        this.limitedType = limitedType;
    }

    public String getUpdatedatePledgeDc() {
        return updatedatePledgeDc;
    }

    public void setUpdatedatePledgeDc(String updatedatePledgeDc) {
        this.updatedatePledgeDc = updatedatePledgeDc;
    }

    public String getSharehdnameDc() {
        return sharehdnameDc;
    }

    public void setSharehdnameDc(String sharehdnameDc) {
        this.sharehdnameDc = sharehdnameDc;
    }

    public String getAmtsharefrozenDc() {
        return amtsharefrozenDc;
    }

    public void setAmtsharefrozenDc(String amtsharefrozenDc) {
        this.amtsharefrozenDc = amtsharefrozenDc;
    }

    public String getAmtfrozenratioDc() {
        return amtfrozenratioDc;
    }

    public void setAmtfrozenratioDc(String amtfrozenratioDc) {
        this.amtfrozenratioDc = amtfrozenratioDc;
    }

    public String getAmtshratioDc() {
        return amtshratioDc;
    }

    public void setAmtshratioDc(String amtshratioDc) {
        this.amtshratioDc = amtshratioDc;
    }

    public String getFrozenstartdateDc() {
        return frozenstartdateDc;
    }

    public void setFrozenstartdateDc(String frozenstartdateDc) {
        this.frozenstartdateDc = frozenstartdateDc;
    }

    public String getFreedtaeinadDc() {
        return freedtaeinadDc;
    }

    public void setFreedtaeinadDc(String freedtaeinadDc) {
        this.freedtaeinadDc = freedtaeinadDc;
    }

    public String getFrozeninstDc() {
        return frozeninstDc;
    }

    public void setFrozeninstDc(String frozeninstDc) {
        this.frozeninstDc = frozeninstDc;
    }

    public String getSharefrozennumDc() {
        return sharefrozennumDc;
    }

    public void setSharefrozennumDc(String sharefrozennumDc) {
        this.sharefrozennumDc = sharefrozennumDc;
    }

    public String getFrozenratioDc() {
        return frozenratioDc;
    }

    public void setFrozenratioDc(String frozenratioDc) {
        this.frozenratioDc = frozenratioDc;
    }

    public String getFrozenintotalDc() {
        return frozenintotalDc;
    }

    public void setFrozenintotalDc(String frozenintotalDc) {
        this.frozenintotalDc = frozenintotalDc;
    }

    public String getInvamountDc() {
        return invamountDc;
    }

    public void setInvamountDc(String invamountDc) {
        this.invamountDc = invamountDc;
    }

    public String getZszDc() {
        return zszDc;
    }

    public void setZszDc(String zszDc) {
        this.zszDc = zszDc;
    }

    public String getAgszbhxsDc() {
        return agszbhxsDc;
    }

    public void setAgszbhxsDc(String agszbhxsDc) {
        this.agszbhxsDc = agszbhxsDc;
    }

    public String getPe9Dc() {
        return pe9Dc;
    }

    public void setPe9Dc(String pe9Dc) {
        this.pe9Dc = pe9Dc;
    }

    public String getPe2Dc() {
        return pe2Dc;
    }

    public void setPe2Dc(String pe2Dc) {
        this.pe2Dc = pe2Dc;
    }

    public String getPb8Dc() {
        return pb8Dc;
    }

    public void setPb8Dc(String pb8Dc) {
        this.pb8Dc = pb8Dc;
    }

    public String getPs9Dc() {
        return ps9Dc;
    }

    public void setPs9Dc(String ps9Dc) {
        this.ps9Dc = ps9Dc;
    }

    public String getPcfjyxjl9Dc() {
        return pcfjyxjl9Dc;
    }

    public void setPcfjyxjl9Dc(String pcfjyxjl9Dc) {
        this.pcfjyxjl9Dc = pcfjyxjl9Dc;
    }

    public String getBasicepsDc() {
        return basicepsDc;
    }

    public void setBasicepsDc(String basicepsDc) {
        this.basicepsDc = basicepsDc;
    }

    public String getBpsDc() {
        return bpsDc;
    }

    public void setBpsDc(String bpsDc) {
        this.bpsDc = bpsDc;
    }

    public String getMgjyxjjeDc() {
        return mgjyxjjeDc;
    }

    public void setMgjyxjjeDc(String mgjyxjjeDc) {
        this.mgjyxjjeDc = mgjyxjjeDc;
    }

    public String getXsjllDc() {
        return xsjllDc;
    }

    public void setXsjllDc(String xsjllDc) {
        this.xsjllDc = xsjllDc;
    }

    public String getDpnp_yoy_ratioDc() {
        return dpnp_yoy_ratioDc;
    }

    public void setDpnp_yoy_ratioDc(String dpnp_yoy_ratioDc) {
        this.dpnp_yoy_ratioDc = dpnp_yoy_ratioDc;
    }

    public String getNoticedateReltradeDc() {
        return noticedateReltradeDc;
    }

    public void setNoticedateReltradeDc(String noticedateReltradeDc) {
        this.noticedateReltradeDc = noticedateReltradeDc;
    }

    public String getTransAffiliateDc() {
        return transAffiliateDc;
    }

    public void setTransAffiliateDc(String transAffiliateDc) {
        this.transAffiliateDc = transAffiliateDc;
    }

    public String getTransRelationDc() {
        return transRelationDc;
    }

    public void setTransRelationDc(String transRelationDc) {
        this.transRelationDc = transRelationDc;
    }

    public String getTransIsconrelaDc() {
        return transIsconrelaDc;
    }

    public void setTransIsconrelaDc(String transIsconrelaDc) {
        this.transIsconrelaDc = transIsconrelaDc;
    }

    public String getTransjineDc() {
        return transjineDc;
    }

    public void setTransjineDc(String transjineDc) {
        this.transjineDc = transjineDc;
    }

    public String getDefraymodeDc() {
        return defraymodeDc;
    }

    public void setDefraymodeDc(String defraymodeDc) {
        this.defraymodeDc = defraymodeDc;
    }

    public String getCurrencyDc() {
        return currencyDc;
    }

    public void setCurrencyDc(String currencyDc) {
        this.currencyDc = currencyDc;
    }

    public String getTranssummaryDc() {
        return transsummaryDc;
    }

    public void setTranssummaryDc(String transsummaryDc) {
        this.transsummaryDc = transsummaryDc;
    }

    public String getPersonnameTypeDc() {
        return personnameTypeDc;
    }

    public void setPersonnameTypeDc(String personnameTypeDc) {
        this.personnameTypeDc = personnameTypeDc;
    }

    public String getPersonnameDc() {
        return personnameDc;
    }

    public void setPersonnameDc(String personnameDc) {
        this.personnameDc = personnameDc;
    }

    public String getResumeDc() {
        return resumeDc;
    }

    public void setResumeDc(String resumeDc) {
        this.resumeDc = resumeDc;
    }

    public String getPostDc() {
        return postDc;
    }

    public void setPostDc(String postDc) {
        this.postDc = postDc;
    }

    public String getStartdateDc() {
        return startdateDc;
    }

    public void setStartdateDc(String startdateDc) {
        this.startdateDc = startdateDc;
    }

    public String getDjgEnddateDc() {
        return djgEnddateDc;
    }

    public void setDjgEnddateDc(String djgEnddateDc) {
        this.djgEnddateDc = djgEnddateDc;
    }

    public String getSexDc() {
        return sexDc;
    }

    public void setSexDc(String sexDc) {
        this.sexDc = sexDc;
    }

    public String getCountryDc() {
        return countryDc;
    }

    public void setCountryDc(String countryDc) {
        this.countryDc = countryDc;
    }

    public String getHighestdegreeDc() {
        return highestdegreeDc;
    }

    public void setHighestdegreeDc(String highestdegreeDc) {
        this.highestdegreeDc = highestdegreeDc;
    }

    public String getBirthYearDc() {
        return birthYearDc;
    }

    public void setBirthYearDc(String birthYearDc) {
        this.birthYearDc = birthYearDc;
    }

    public String getAgeDc() {
        return ageDc;
    }

    public void setAgeDc(String ageDc) {
        this.ageDc = ageDc;
    }

    public String getAnualwageDc() {
        return anualwageDc;
    }

    public void setAnualwageDc(String anualwageDc) {
        this.anualwageDc = anualwageDc;
    }

    public String getJobState() {
        return jobState;
    }

    public void setJobState(String jobState) {
        this.jobState = jobState;
    }

    public String getChangenumDc() {
        return changenumDc;
    }

    public void setChangenumDc(String changenumDc) {
        this.changenumDc = changenumDc;
    }

    public String getEhnDc() {
        return ehnDc;
    }

    public void setEhnDc(String ehnDc) {
        this.ehnDc = ehnDc;
    }

    public String getAvpriceDc() {
        return avpriceDc;
    }

    public void setAvpriceDc(String avpriceDc) {
        this.avpriceDc = avpriceDc;
    }

    public String getReasonDc() {
        return reasonDc;
    }

    public void setReasonDc(String reasonDc) {
        this.reasonDc = reasonDc;
    }

    public String getChangePriceDc() {
        return changePriceDc;
    }

    public void setChangePriceDc(String changePriceDc) {
        this.changePriceDc = changePriceDc;
    }

    public String getChangenumRaitDc() {
        return changenumRaitDc;
    }

    public void setChangenumRaitDc(String changenumRaitDc) {
        this.changenumRaitDc = changenumRaitDc;
    }

    public String getEhnRaitDc() {
        return ehnRaitDc;
    }

    public void setEhnRaitDc(String ehnRaitDc) {
        this.ehnRaitDc = ehnRaitDc;
    }

    public String getChangedateDc() {
        return changedateDc;
    }

    public void setChangedateDc(String changedateDc) {
        this.changedateDc = changedateDc;
    }

    public String getReasoncodeDc() {
        return reasoncodeDc;
    }

    public void setReasoncodeDc(String reasoncodeDc) {
        this.reasoncodeDc = reasoncodeDc;
    }

    public String getReasonTypeDc() {
        return reasonTypeDc;
    }

    public void setReasonTypeDc(String reasonTypeDc) {
        this.reasonTypeDc = reasonTypeDc;
    }
}
