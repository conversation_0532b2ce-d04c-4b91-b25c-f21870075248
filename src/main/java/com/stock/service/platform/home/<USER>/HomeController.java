package com.stock.service.platform.home.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.home.dto.HomeParamDto;
import com.stock.service.platform.home.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 券商信息
 */

@RestController
@RequestMapping("home")
public class HomeController extends BaseController {
    @Autowired
    private CommonService commonService;
    @Autowired
    private HomeService homeService;

    @RequestMapping(value = "selectData")
    @ResponseBody
    public JsonResponse<Map<String, Object>> selectData() {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(commonService.getSelectData());
        return response;
    }

    @PostMapping(value = "getCompanyList")
    @ResponseBody
    public Map<String, Object> getCompanyList(@RequestBody HomeParamDto homeParamDto){
        Map<String, Object> resultMap = new HashMap<>();
        if(Integer.parseInt(getUserInfo().getInfo().get("orgType").toString()) <= 3){
            resultMap = homeService.getCompanyList(homeParamDto);
        }
        return resultMap;
    }

}