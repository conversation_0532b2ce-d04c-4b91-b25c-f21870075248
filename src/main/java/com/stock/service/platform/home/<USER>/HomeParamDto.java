package com.stock.service.platform.home.dto;

import java.io.Serializable;
import java.util.List;

public class HomeParamDto implements Serializable {


    private static final long serialVersionUID = 1L;

    //查询条件
    private String companyCode;//companyCode
    private String zhName;//公司名
    private String[] city;//地区
    private String[] industryCSRCStr;//证监会行业
    private String[] industrySWStr;//申万行业
    private String[] orgform;//公司性质
    private List<String> companyCodeList;
    private int pageSize;
    private int startRow;
    private String userId;
    private List<String> orgIds;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getStartRow() {
        return startRow;
    }

    public void setStartRow(int startRow) {
        this.startRow = startRow;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }

    public String[] getCity() {
        return city;
    }

    public void setCity(String[] city) {
        this.city = city;
    }

    public String[] getIndustryCSRCStr() {
        return industryCSRCStr;
    }

    public void setIndustryCSRCStr(String[] industryCSRCStr) {
        this.industryCSRCStr = industryCSRCStr;
    }

    public String[] getIndustrySWStr() {
        return industrySWStr;
    }

    public void setIndustrySWStr(String[] industrySWStr) {
        this.industrySWStr = industrySWStr;
    }

    public String[] getOrgform() {
        return orgform;
    }

    public void setOrgform(String[] orgform) {
        this.orgform = orgform;
    }

    public List<String> getCompanyCodeList() {
        return companyCodeList;
    }

    public void setCompanyCodeList(List<String> companyCodeList) {
        this.companyCodeList = companyCodeList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }
}