package com.stock.service.platform.home.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.stock.core.dto.JsonResponse;
import com.stock.core.rest.RestClient;
import com.stock.core.service.BaseService;
import com.stock.core.util.DateUtil;
import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.dto.VcTradSkDto;
import com.stock.service.platform.common.util.StripTrailingZerosUtil;
import com.stock.service.platform.home.dao.ListedCompanyMapper;
import com.stock.service.platform.home.dto.DongcaiTbDto;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Description: 上市公司相关业务处理
 */
@Service
public class ListedCompanyService extends BaseService {

    @Autowired
    private ListedCompanyMapper listedCompanyMapper;

    @Autowired
    private RestClient restClient;

    @Value("${api.baseUrl}")
    private String apiBaseUrl;

    @Value("${zs.apikey}")
    private String apikey;

    @Value("${zs.market-quote}")
    private String url;

    private final Logger logger = LoggerFactory.getLogger(ListedCompanyService.class);



    public Map<String, Object> querySharesInfo(Map<String, Object> paramMap) throws IOException {
        // 调用接口获取股票信息
        String companyCode = (String) paramMap.get("companyCode");
        Map<String, Object> sharesMessageMap = new HashMap<>();
        //获取市场代码
        Map<String, String> maketMap = listedCompanyMapper.getMaketId(companyCode);
        if (ObjectUtils.isNotEmpty(maketMap)) {
            String plate = maketMap.get("plate");
            String maketId = "";
            if("06".equals(plate)){
                maketId = "5";
            }else {
                if("00".equals(plate) || "01".equals(plate) || "02".equals(plate)){
                    maketId = "2";
                } else if ("05".equals(plate)) {
                    maketId = "3";
                } else if ("09".equals(plate)) {
                    maketId = "4";
                } else if ("04".equals(plate) || "07".equals(plate)) {
                    maketId = "1";
                }
            }
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url+"?symbol="+companyCode+"&marketCd="+maketId);
            httpPost.setHeader("apikey",apikey);
            httpPost.setHeader("msgId",UUID.randomUUID().toString());
            httpPost.setHeader("content-type","application/json");
            HttpResponse httpResponse = httpClient.execute(httpPost);
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                String responseString = EntityUtils.toString(entity, "UTF-8");
                // 创建 ObjectMapper 对象
                ObjectMapper objectMapper = new ObjectMapper();
                // 将 JSON 字符串转换为 Map<String, String> 对象
                Map<String, Object> response = objectMapper.readValue(responseString, new TypeReference<Map<String, Object>>() {});
                Map<String, String> responseMap = (Map<String, String>) response.get("result");
                //当前股票价格
                //String consecutivePresentPrice = responseMap.get("consecutivePresentPrice");
                //涨跌
                //String tradeIncrease = responseMap.get("tradeIncrease");
                //sharesMessageMap.put("PBRatio",responseMap.get("pbRatio")); // 市净率
                //sharesMessageMap.put("PERatio",responseMap.get("earning")); // 市盈率（动）
                // 接口获取
                sharesMessageMap.put("marketTotal",yuantoyi(responseMap.get("totalMarketVal"))); // 总市值
                sharesMessageMap.put("marketCirculate",yuantoyi(responseMap.get("marketVal"))); // 流通市值
                sharesMessageMap.put("shares",responseMap.get("consecutivePresentPrice")); // 当前股价
                sharesMessageMap.put("upanddown",responseMap.get("stockUD")); // 涨跌数
                sharesMessageMap.put("upanddownrate",responseMap.get("tradeIncrease")); // 涨跌幅
                // 数据库获取
                Map<String, Object> map = new HashMap<>();
                map.put("companyCode", companyCode);
                Map<String, Object> dataMap = listedCompanyMapper.querySharesInfoPOC(map);
                sharesMessageMap.put("totalStockNum", MapUtils.getString(dataMap, "totalStockNum", "--")); // 股票数量
                //股东人数  vc_trad_sk_daily
                Map<String, BigDecimal> dataMap1 = listedCompanyMapper.getDailyInfo(companyCode);
                sharesMessageMap.put("shareNum", MapUtils.getString(dataMap1, "shareNum", "--"));
                logger.info("获取市场代码-"+responseString);
            }
            sharesMessageMap.put("companyCode", companyCode);
            Date endDate = new Date();
            String endString = DateUtil.changeDateFormat(endDate, "HH:mm");
            sharesMessageMap.put("endDate", endString);
        }
        return sharesMessageMap;
    }

    public static String yuantoyi(String numberStr){
        if(numberStr == null || "".equals(numberStr)){
            return "";
        }
        BigDecimal number = new BigDecimal(numberStr);
        BigDecimal divisor = new BigDecimal("*********");
        BigDecimal result = number.divide(divisor);
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");
        String formattedResult = decimalFormat.format(result);
        return formattedResult + " 亿";
    }

    //计算涨跌幅
    public static double calculateTradeIncreaseRatio(String consecutivePresentPrice, String tradeIncrease) {
        // 将字符串转换为浮点数
        tradeIncrease = tradeIncrease.replace("-","");
        double presentPrice = Double.parseDouble(consecutivePresentPrice);
        double increase = Double.parseDouble(tradeIncrease);
        double ratio = 0;
        if(!StringUtils.isEmpty(presentPrice) && !StringUtils.isEmpty(increase)){
            // 计算涨跌比例
            ratio = increase / presentPrice * 100;
        }
        // 使用 DecimalFormat 格式化结果，保留两位小数并进行四舍五入
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        return Double.parseDouble(decimalFormat.format(ratio));
    }

    public Map<String, Object> getCompanyPricesInfo(String companyCode) {
        // 股票异动情报取得
        String url = apiBaseUrl + "app/home/<USER>";
        List<String> particularChangeLst = new ArrayList<String>();
        Map<String, Object> resultMap = Maps.newHashMap();
        try {
            MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
            param.add("companyCode", companyCode);
            ParameterizedTypeReference<JsonResponse<Map<String, Object>>> responseType = new ParameterizedTypeReference<JsonResponse<Map<String, Object>>>() {
            };
            JsonResponse<Map<String, Object>> result = restClient.post(url, param, responseType);
            // 异动情报偏离值取得
            List<Map<String, Object>> companyPricesInfos;
            Map<String, Object> companyPricesInfo;
            if (result.isSuccess()) {
                if (result.getResult().containsKey("companyPricesA")) {
                    if (result.getResult().get("companyPricesA") instanceof List) {
                        companyPricesInfos = (List<Map<String, Object>>) result.getResult().get("companyPricesA");
                        for (Map<String, Object> companyPrices : companyPricesInfos) {
                            if (companyPrices != null) {
                                dealCompanyPricesInfo(companyPrices, resultMap, companyCode);
                            }
                        }
                    } else if (result.getResult().get("companyPricesA") instanceof Map) {
                        companyPricesInfo = (Map<String, Object>) result.getResult().get("companyPricesA");
                        dealCompanyPricesInfo(companyPricesInfo, resultMap, companyCode);
                    }
                } else if (result.getResult().containsKey("companyPricesB")) {
                    if (result.getResult().get("companyPricesB") instanceof List) {
                        companyPricesInfos = (List<Map<String, Object>>) result.getResult().get("companyPricesA");
                        for (Map<String, Object> companyPrices : companyPricesInfos) {
                            if (companyPrices != null)
                                dealCompanyPricesInfo(companyPrices, resultMap, companyCode);
                        }
                    } else if (result.getResult().get("companyPricesB") instanceof Map) {
                        companyPricesInfo = (Map<String, Object>) result.getResult().get("companyPricesB");
                        dealCompanyPricesInfo(companyPricesInfo, resultMap, companyCode);
                    }
                }
            }
        } catch (Exception e) {
            logger.info(Throwables.getStackTraceAsString(e));
        }
        resultMap.put("particularChangeLst", particularChangeLst);
        resultMap.put("companyCode", companyCode);
        return resultMap;
    }

    private void dealCompanyPricesInfo(Map<String, Object> companyPrices, Map<String, Object> resultMap, String companyCode) {
        String[] strArr = {"shares", "marketTotal", "PERatio", "PBRatio", "totalStockNum", "marketCirculate", "opening", "upanddown", "upanddownrate"};
        String[] booleanArr = {"calculateFlag", "offsetTag", "STFlag"};
        String[] doubleArr = {"offsetLimit", "offsetLimitTPre1", "offsetLimitTPre2", "standardDeviation", "offsetBeforeOpening"};
        String[] warningArr = {"offsetWarnRemind", "exchangeWarnRemind", "sameDirection_status", "offsetSerious10T_status"
                , "offsetSerious30T_status", "sameWarning", "tenWarning", "thirtyWarning", "offsetSum10", "offsetSum30"};
        if (companyPrices.containsKey("companyCode")) {
            for (String doubleKey : doubleArr) {
                if (companyPrices.containsKey(doubleKey)) {
                    if (companyPrices.get(doubleKey) instanceof Integer) {
                        Integer temp = (Integer) companyPrices.get(doubleKey);
                        resultMap.put(doubleKey, Double.valueOf(String.valueOf(temp)));
                    } else {
                        if (companyPrices.get(doubleKey) != null && companyPrices.get(doubleKey) != "") {
                            resultMap.put(doubleKey, (Double) companyPrices.get(doubleKey));
                        }
                    }
                }
            }
            for (String booleanKey : booleanArr) {
                if (companyPrices.containsKey(booleanKey)) {
                    if (companyPrices.get(booleanKey) != null) {
                        resultMap.put(booleanKey, (Boolean) companyPrices.get(booleanKey));
                    }
                }
            }
            for (String strKey : strArr) {
                if (companyPrices.containsKey(strKey)) {
                    if (companyPrices.get(strKey) != null) {
                        if ("totalStockNum".equals(strKey)) {
                            if (companyPrices.get(strKey) != null) {
                                resultMap.put(strKey, companyPrices.get(strKey).toString()
                                        .substring(0, companyPrices.get(strKey).toString().indexOf(".")));
                            }
                        } else {
                            resultMap.put(strKey, companyPrices.get(strKey).toString());
                        }
                    }
                }
            }
            for (String strKey : warningArr) {
                if (companyPrices.containsKey(strKey)) {
                    if (companyPrices.get(strKey) != null) {
                        resultMap.put(strKey, companyPrices.get(strKey).toString());
                    }
                }
            }
        } else {
            if (companyPrices.containsKey("shares") && companyPrices.get("shares") != null) {
                resultMap.put("shares", (String) companyPrices.get("shares"));
            }
        }
    }

    public Map<String, Object> queryCompanyInfo(Map<String, Object> paramMap) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        String companyCode = (String) paramMap.get("companyCode");
        //股东人数  vc_trad_sk_daily
        //Map<String, BigDecimal> sharesMessageMap = listedCompanyMapper.getDailyInfo(companyCode);
        //map.put("sharesMessageMap", sharesMessageMap);
        //公司信息
        SaCompanyDto saCompanyDto =  listedCompanyMapper.getCompanyInfo(companyCode);
        if(saCompanyDto!=null){
            //控制权变更（待同步）
            //String controStr = queryCongrol(saCompanyDto);
            //if(controStr!=null && !"".equals(controStr)){
            //    saCompanyDto.setControStr(controStr);
            //}
            //经营状态（待同步）
            //String comStatus = listedCompanyMapper.getCompanyStatus(saCompanyDto);
            //if(comStatus!=null && !"".equals(comStatus)){
            //    saCompanyDto.setComStatus(comStatus);
            //}
            String marketStockType = listedCompanyMapper.getMarketStockType(saCompanyDto);
            if(marketStockType!=null && !"".equals(marketStockType)){
                saCompanyDto.setMarketStockType(marketStockType);
            }
            String systemState = listedCompanyMapper.getSystemState(saCompanyDto.getSystemState());
            if (StringUtils.isEmpty(systemState)) {
                saCompanyDto.setSystemState("");
            } else {
                saCompanyDto.setSystemState(systemState);
            }
            StripTrailingZerosUtil.convertBigDecimalsToPlainString(saCompanyDto);
            map.put("saCompanyDto", saCompanyDto);
            //行业
            SaCompanyDto comIndustry =  listedCompanyMapper.getComIndustry(companyCode);
            map.put("comIndustry", comIndustry);
            //股票列表
            List<SaCompanyDto> comStock =  listedCompanyMapper.getComStock(saCompanyDto);
            map.put("comStock", comStock);
            //公司logo
            String id = saCompanyDto.getId();
            String logoUrl =  listedCompanyMapper.selectCompanyLogUrl(id);
            map.put("logoUrl", logoUrl);
            //收盘价走势图
            VcTradSkDto vcTradSkDto = new VcTradSkDto();
            vcTradSkDto.setCompanyCode(companyCode);
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 获取前一天日期
            LocalDate yesterday = today.minusDays(1);
            // 创建一个DateTimeFormatter格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            // 将日期转换为字符串
            String yesterdayStr = yesterday.format(formatter);
            vcTradSkDto.setToday(yesterdayStr);
            List<VcTradSkDto> echartsList = listedCompanyMapper.getTodayClosingPrice(vcTradSkDto);

            BigDecimal tvaLCnyNum = BigDecimal.ZERO;
            BigDecimal cjlNum = BigDecimal.ZERO;
            for(VcTradSkDto skDto : echartsList){
                tvaLCnyNum = tvaLCnyNum.add(skDto.getTVALCNY() == null ? BigDecimal.ZERO : skDto.getTVALCNY());
                cjlNum = cjlNum.add(skDto.getCJL() == null ? BigDecimal.ZERO : skDto.getCJL());
            }

            String thirtyPrice;
            if (cjlNum.compareTo(BigDecimal.ZERO) != 0) {
                thirtyPrice = tvaLCnyNum.divide(cjlNum, 2, BigDecimal.ROUND_HALF_DOWN).toString();
            } else {
                thirtyPrice = "0";
            }
            map.put("thirtyPrice",thirtyPrice);
            map.put("echartsList",echartsList);
        }
        return map;
    }

    public String queryCongrol(SaCompanyDto saCompanyDto) {
        List<DongcaiTbDto> conList = listedCompanyMapper.queryCongrol(saCompanyDto);
        String item = "";
        if (conList != null) {
            if (conList.size() == 1) {
                item = conList.get(0).getReportdateDc() + " " + conList.get(0).getRecompanynameDc();
            } else {
                for (int i = 0; i < conList.size(); i++) {
                    if (i == 0) {
                        item += conList.get(i).getReportdateDc() + " " + conList.get(i).getRecompanynameDc();
                    } else {
                        item = item + "→" + conList.get(i).getReportdateDc() + " " + conList.get(i).getRecompanynameDc();
                    }
                }
            }
        }
        return item;
    }

    /**
     * poc演示,库里获取日行情数据
     * @param map
     */
    public Map<String, Object> querySharesInfoPOC(Map<String, Object> map) {
        Map<String, Object> sharesMessageMap = listedCompanyMapper.querySharesInfoPOC(map);

        String companyCode = (String) map.get("companyCode");
        sharesMessageMap.put("companyCode", companyCode);
        Date endDate = new Date();
        String endString = DateUtil.changeDateFormat(endDate, "HH:mm");
        sharesMessageMap.put("endDate", endString);

        return sharesMessageMap;
    }
}
