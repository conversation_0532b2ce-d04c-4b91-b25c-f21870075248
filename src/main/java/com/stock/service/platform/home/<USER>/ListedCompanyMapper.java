package com.stock.service.platform.home.dao;


import com.stock.service.platform.common.system.dto.SaCompanyDto;
import com.stock.service.platform.common.system.dto.VcTradSkDto;
import com.stock.service.platform.home.dto.DongcaiTbDto;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface ListedCompanyMapper {

    /**
     * 公司详细信息
     *
     * @return
     */

    Map<String, BigDecimal> getDailyInfo(String companyCode);

    SaCompanyDto getCompanyInfo(String companyCode);

    String selectCompanyLogUrl(String id);

    List<DongcaiTbDto> queryCongrol(SaCompanyDto saCompanyDto);

    String getCompanyStatus(SaCompanyDto saCompanyDto);

    SaCompanyDto getComIndustry(String companyCode);

    List<SaCompanyDto> getComStock(SaCompanyDto saCompanyDto);

    List<VcTradSkDto> getTodayClosingPrice(VcTradSkDto dto);

    Map<String, Object> querySharesInfoPOC(Map<String, Object> map);


    Map<String,String> getMaketId(String companyCode);

    String getMarketStockType(SaCompanyDto saCompanyDto);

    String getSystemState(String systemState);
}