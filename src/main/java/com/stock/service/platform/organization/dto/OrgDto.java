package com.stock.service.platform.organization.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: OrgDto
 * @Description: 组织机构DTO
 * @date 2021-11-05
 */
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
public class OrgDto implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "ID", example = "0")
    @NotBlank(message = "机构id为空", groups = {ExchangePointGroup.class})
    private String id;

    /**
     * 机构代码
     */
    @ApiModelProperty(value = "机构代码", example = "0")
    @NotBlank(message = "机构代码为空", groups = {EditPointGroup.class})
    private String orgCode;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称", example = "0")
    @NotBlank(message = "机构名称为空", groups = {EditPointGroup.class})
    private String orgName;

    /**
     * 上级机构ID
     */
    @ApiModelProperty(value = "上级机构ID", example = "0")
    @NotBlank(message = "上级机构ID为空", groups = {EditPointGroup.class})
    private String pOrgId;

    /**
     * 排序id
     */
    @ApiModelProperty(value = "排序id", example = "0")
    private Integer orderId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级", example = "0")
    private String level;

    /**
     * 机构类型
     */
    @ApiModelProperty(value = "机构类型", example = "0")
    private String orgType;

    /**
     * 机构下是否有人
     */
    @ApiModelProperty(value = "机构下是否存在人员", example = "0")
    private String inUse;

    /**
     * 移动目标组织机构ID
     */
    @ApiModelProperty(value = "移动目标组织机构ID", example = "0")
    @NotBlank(message = "目标机构id为空", groups = {ExchangePointGroup.class})
    private String targetId;

    /**
     * 子节点集合
     */
    @ApiModelProperty(value = "子节点集合", example = "")
    private List<OrgDto> children;

    /**
     * 显示停用 0不显示 1显示
     */
    @ApiModelProperty(value = "显示停用", example = "0")
    private String showDisabled;
    /**
     * 显示停用 0不显示 1显示
     */
    @ApiModelProperty(value = "是否允许分公司所属营业部相互查看", example = "1")
    private boolean checked;
    /**
     * 删除ID集合 0不显示 1显示
     */
    @ApiModelProperty(value = "删除ID集合", example = "0")
    @NotBlank(message = "删除ID集合为空", groups = {DeletePointGroup.class})
    private String ids;

    /**
     * 删除ID集合 0不显示 1显示
     */
    @ApiModelProperty(value = "状态", example = "1")
    @NotBlank(message = "状态为空", groups = {DeletePointGroup.class})
    private String status;
    @ApiModelProperty(value = "公司代码", example = "1")
    private String companyId;
    /**
     * 是否允许所属营业部相互查看 1允许 0不允许
     */
    @ApiModelProperty(value = "是否允许所属营业部相互查看", example = "1")
    private String thresholdFlag;

    private boolean disabled;

    private String label;

    public String getLabel() {
        return this.getOrgName();
    }

    public void setLabel(String label) {
        this.label = label;
    }

    private static final long serialVersionUID = 1L;

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getpOrgId() {
        return pOrgId;
    }

    public void setpOrgId(String pOrgId) {
        this.pOrgId = pOrgId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getInUse() {
        return inUse;
    }

    public void setInUse(String inUse) {
        this.inUse = inUse;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public List<OrgDto> getChildren() {
        return children;
    }

    public void setChildren(List<OrgDto> children) {
        this.children = children;
    }

    public String getShowDisabled() {
        return showDisabled;
    }

    public void setShowDisabled(String showDisabled) {
        this.showDisabled = showDisabled;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getThresholdFlag() {
        return thresholdFlag;
    }

    public void setThresholdFlag(String thresholdFlag) {
        this.thresholdFlag = thresholdFlag;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    /**
     * <AUTHOR>
     * 移动节点校验组
     */
    public static interface ExchangePointGroup {
    }

    /**
     * <AUTHOR>
     * 编辑节点校验组
     */
    public static interface EditPointGroup {
    }

    /**
     * <AUTHOR>
     * 删除节点校验组
     */
    public static interface DeletePointGroup {
    }

}
