<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.organization.dao.OrganizationBizMapper">

  <resultMap id="BaseResultMap" type="com.stock.service.platform.organization.dto.OrgDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="p_org_id" jdbcType="VARCHAR" property="pOrgId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="inUse" jdbcType="VARCHAR" property="inUse" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="threshold_flag" jdbcType="VARCHAR" property="thresholdFlag" />
  </resultMap>

    <resultMap id="selectMap" type="com.stock.service.platform.organization.dto.OrgDto">
        <id column="key" jdbcType="BIGINT" property="id" />
        <result column="value" jdbcType="VARCHAR" property="id" />
        <result column="companyId" jdbcType="VARCHAR" property="companyId" />
        <result column="text" jdbcType="VARCHAR" property="orgName" />
    </resultMap>

<!--  <select id="getOrgList" resultMap="BaseResultMap" parameterType="com.stock.service.platform.organization.dto.OrgDto">-->
<!--    select distinct-->
<!--        so.id ,-->
<!--        so.org_code,-->
<!--        so.org_name ,-->
<!--        so.p_org_id ,-->
<!--        so.order_id ,-->
<!--        so.level ,-->
<!--        so.company_id,-->
<!--        so.org_type ,-->
<!--        case when sp.cnt > 0 or sp1.cnt > 0 then '1' else '0' end as inUse,-->
<!--        #{showDisabled} as showDisabled,-->
<!--        so.status,-->
<!--        so.threshold_flag-->
<!--    from-->
<!--        sa_org so-->
<!--    left join-->
<!--        (select t.org_id ,count(t.id) as cnt from sa_person t group by t.org_id) sp-->
<!--    on sp.org_id  = so.id-->
<!--    or  find_in_set(sp.org_id,(select group_concat( distinct o2.id) from sa_org o2 where o2.p_org_id = so.id or o2.id = so.id))-->
<!--      left join-->
<!--        (select t.org_id ,count(t.id) as cnt from sa_person t where t.status = '1' group by t.org_id) sp1-->
<!--    on sp1.org_id  = so.id-->
<!--    or  find_in_set(sp1.org_id,(select group_concat( distinct o2.id) from sa_org o2 where o2.p_org_id = so.id or o2.id = so.id))-->
<!--      where-->
<!--      so.status = '1' and (so.children_company_flag = '0' or so.children_company_flag is null)-->
<!--    <if test="showDisabled == 1">-->
<!--        or (so.status = '0' and (sp.cnt > 0 or sp1.cnt > 0))-->
<!--    </if>-->
<!--    order by so.order_id-->
<!--  </select>-->
    <select id="getOrgList" resultMap="BaseResultMap" parameterType="com.stock.service.platform.organization.dto.OrgDto">
        select distinct
        so.id ,
        so.org_code,
        so.org_name ,
        so.p_org_id ,
        so.order_id ,
        so.level ,
        so.company_id,
        so.org_type ,
        so.status,
        so.threshold_flag,
        #{showDisabled} as showDisabled
        from sa_org so
        where so.status = '1' and (so.children_company_flag = '0' or so.children_company_flag is null)
        order by so.order_id
    </select>
    <select id="getMaxOrderId" resultType="java.lang.Integer">
        select
            ifnull(max(order_id), 0) + 1
        from
            sa_org so
        where
            so.p_org_id = #{pOrgId}
        and so.status = '1'
    </select>
    <select id="getOrgListByPId" parameterType="com.stock.service.platform.organization.dto.OrgDto" resultMap="selectMap">
        select so.id as `key`, so.id as `value`, so.company_id as `companyId`, so.org_name as text
        from sa_org so
                 left join sa_org pso on pso.id = so.p_org_id and pso.status = '1'
        where so.status = '1'
          and so.org_type = '3'
          and so.p_org_id = #{pOrgId}
    </select>
    <select id="getOrgName" resultType="java.lang.String">
        select org_name from sa_org where id = #{id}
    </select>
    <select id="getOrgNameList" resultType="java.lang.String">
        select org_name from sa_org where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>