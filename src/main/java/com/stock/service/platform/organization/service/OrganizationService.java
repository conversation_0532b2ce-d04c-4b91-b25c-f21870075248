package com.stock.service.platform.organization.service;

import com.stock.core.service.BaseService;
import com.stock.core.util.BeanUtil;
import com.stock.service.platform.common.constant.LogicConstant;
import com.stock.service.platform.common.dao.OrgMapper;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.organization.dao.OrganizationBizMapper;
import com.stock.service.platform.organization.dto.OrgDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @ClassName: OrganizationService
 * @Description: 组织机构相关接口Service
 * <AUTHOR>
 * @date 2021-11-05
 */
@Service
public class OrganizationService extends BaseService {

    @Autowired
    private OrganizationBizMapper organizationBizMapper;

    @Autowired
    private OrgMapper orgMapper;

    /**
     * 组织机构树初始化数据
     *
     * @return
     */
    public List<OrgDto> getOrgTree(OrgDto orgDto) {
        // 递归获取组织机构节点信息 根节点是“0”
        List<OrgDto> orgDtos = organizationBizMapper.getOrgList(orgDto);
        return orgDtos;
    }

    /**
     * 移动节点
     *
     * @param orgDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void movePoint(OrgDto orgDto) {
        // 查询移动目标节点信息
        Org targetOrg = orgMapper.selectByPrimaryKey(orgDto.getTargetId());
        // 查询移动源节点信息
        Org originOrg = orgMapper.selectByPrimaryKey(orgDto.getId());
        // 目标节点排序编号
        Integer targetOrderId = targetOrg.getOrderId();
        // 源节点排序编号
        Integer originOrderId = originOrg.getOrderId();
        // 交换编号
        targetOrg.setOrderId(originOrderId);
        originOrg.setOrderId(targetOrderId);
        // 更新信息
        orgMapper.updateByPrimaryKeySelective(originOrg);
        orgMapper.updateByPrimaryKeySelective(targetOrg);
    }

    /**
     * 新增/保存机构信息
     *
     * @param orgDto 必须穿pOrgId和orgName
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Org editPoint(OrgDto orgDto) {
        Org org = new Org();
        BeanUtil.copy(orgDto, org);
        // 没有ID插入 有ID更新
        if (orgDto.getId() != null) {
            orgMapper.updateByPrimaryKeySelective(org);
        } else {
            Integer orderId = organizationBizMapper.getMaxOrderId(org.getpOrgId());
            org.setOrderId(orderId);
            orgMapper.insertSelective(org);
        }
        return org;
    }

    /**
     * 删除节点（逻辑删）
     *
     * @param orgDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePoint(OrgDto orgDto) {
        if (StringUtils.isNotEmpty(orgDto.getIds())) {
            String[] ids = orgDto.getIds().split(LogicConstant.SEPARATOR_COMMA);
            for (String id : ids) {
                Org org = new Org();
                // 获取节点ID
                org.setId(id);
                // 设置状态为删除
                org.setStatus(orgDto.getStatus());
                orgMapper.updateByPrimaryKeySelective(org);
            }
        }
        return true;
    }


    public String getOrgName(OrgDto orgDto){
        return organizationBizMapper.getOrgName(orgDto);
    }

    public List<String> getOrgNameList(List<String> list){
        return organizationBizMapper.getOrgNameList(list);
    }
}
