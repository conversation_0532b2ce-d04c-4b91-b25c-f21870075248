package com.stock.service.platform.organization.dao;


import com.stock.service.platform.organization.dto.OrgDto;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrganizationBizMapper {

    /**
     * 组织机构树初始化数据
     *
     * @return
     */
    List<OrgDto> getOrgList(OrgDto orgDto);


    /**
     * 获取最大排序id
     *
     * @return
     */
    Integer getMaxOrderId(String pOrgId);


    /**
     * 获取对应分公司下的所有营业部
     * @param orgDto 分公司信息
     * @return
     */
    List<OrgDto> getOrgListByPId(OrgDto orgDto);

    String getOrgName(OrgDto orgDto);

    List<String> getOrgNameList(List<String> list);
}