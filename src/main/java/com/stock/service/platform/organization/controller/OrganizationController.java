package com.stock.service.platform.organization.controller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.service.platform.common.entity.Org;
import com.stock.service.platform.common.service.CommonService;
import com.stock.service.platform.organization.dto.OrgDto;
import com.stock.service.platform.organization.service.OrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName: OrganizationController
 * @Description: 组织机构相关接口Controller
 * <AUTHOR>
 * @date 2021-11-05
 *
 */
@Api(tags = "组织架构")
@RestController
@RequestMapping("organization")
public class OrganizationController extends BaseController {

    @Autowired
    OrganizationService organizationService;
    @Autowired
    CommonService commonService;

    /**
     * 查询组织机构列表
     *
     * @param orgDto
     * @return
     */
    @ApiOperation(value = "查询组织机构列表")
    @PostMapping("/query")
    public JsonResponse<List<OrgDto>> queryTree(@RequestBody OrgDto orgDto) {
        JsonResponse<List<OrgDto>> response = new JsonResponse<>();
        List<OrgDto> data = organizationService.getOrgTree(orgDto);
        response.setResult(data);
        return response;
    }

    /**
     * 移动节点
     *
     * @param orgDto
     * @return
     */
    @ApiOperation(value = "移动节点")
    @PostMapping("/exchangePoint")
    public JsonResponse<Boolean> exchangePoint(@Validated(OrgDto.ExchangePointGroup.class) @RequestBody OrgDto orgDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        organizationService.movePoint(orgDto);
        response.setResult(Boolean.TRUE);
        return response;
    }

    /**
     * 编辑节点
     *
     * @param orgDto
     * @return
     */
    @ApiOperation(value = "编辑节点")
    @PostMapping("/editPoint")
    public JsonResponse<Org> editPoint(@Validated(OrgDto.EditPointGroup.class) @RequestBody OrgDto orgDto) {
        JsonResponse<Org> response = new JsonResponse<>();
        response.setResult(organizationService.editPoint(orgDto));
        return response;
    }

    /**
     * 删除节点
     *
     * @param orgDto
     * @return
     */
    @ApiOperation(value = "删除节点")
    @PostMapping("/deletePoint")
    public JsonResponse<Boolean> deletePoint(@Validated(OrgDto.DeletePointGroup.class) @RequestBody OrgDto orgDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(organizationService.deletePoint(orgDto));
        return response;
    }
    /**
     * 获取部门名称
     *
     * @param orgDto
     * @return
     */
    @ApiOperation(value = "获取部门名称")
    @PostMapping("/getOrgName")
    public JsonResponse<String> getOrgName(@RequestBody OrgDto orgDto) {
        JsonResponse<String> response = new JsonResponse<>();
        response.setResult(organizationService.getOrgName(orgDto));
        return response;
    }

    /**
     * 获取部门名称
     *
     * @param list
     * @return
     */
    @ApiOperation(value = "获取部门名称集合")
    @PostMapping("/getOrgNameList")
    public JsonResponse<List<String>> getOrgNameList(@RequestBody List<String> list) {
        JsonResponse<List<String>> response = new JsonResponse<>();
        response.setResult(organizationService.getOrgNameList(list));
        return response;
    }




}
