# mybatis位置
mybatis:
  config-location: classpath:/persistence/mybatis-config.xml
  mapperLocations: classpath*:/com/stock/core/dao/CommonDao.xml

server:
  servlet:
    context-path: /
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  port: 9999

spring:
  # 应用
  application:
    name: platform
  # 使用环境
  profiles:
      active: dev
  # 是否覆盖
  main:
      allow-bean-definition-overriding: true
  # cloud配置
  cloud:
    config:
      overrideNone: true
  # 序列化
  jackson:
    serialization:
      write-dates-as-timestamps: true
  servlet:
    # 上传
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 500MB
      file-size-threshold: 1MB
  # 任务执行
  task:
    execution:
      pool:
        core-size: 5
    scheduling:
      pool:
        size: 10
  # 数据源基本配置
  datasource:
    druid:
      initial-size: 1
      max-active: 200
      min-idle: 10
      max-wait: 60000
      validation-query: select 'x'
      validation-query-timeout: 600000
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 1800000
      filters: slf4j,wall
      filter:
        wall:
          config:multi-statement-allow: true
  session:
    store-type: none
  mvc:
    throw-exception-if-no-handler-found: true
    format:
      date: yyyy-MM-dd
    log-resolved-exception: true

management:
  endpoints:
    web:
      base-path: /
  health:
    defaults:
      enabled: false
  security:
    enabled: false


# 强制设置一下
api:
  baseUrl:

#app:
#  companyId: 999000
#  licenseFilePath: #E:\code\mr\mr-enteprise\src\main\resources\license\license.lic
#  pubCertFilePath: #/license/privateKeys.keystore


