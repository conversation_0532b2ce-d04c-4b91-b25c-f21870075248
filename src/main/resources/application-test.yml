# 中信建投测试
log:
  appender: file
  home: /data/platform_log
  level: DEBUG

server:
  port: 8891

spring:
  # 数据原配置
  # 数据源
  datasource:
    url: ***************************************************************************************************************************************************
    username: admin
    password: 123qqq...A
  # 缓存配置
  redis:
    cluster:
      nodes:
        - 10.48.93.18:6080
        - 10.48.93.18:6081
        - 10.48.93.19:6080
        - 10.48.93.19:6081
        - 10.48.93.20:6080
        - 10.48.93.20:6081
      max-redirects: 3
    password: Jzzx123
    timeout: 30000 #超时时间
    database: 0 #0-15 16个库 默认0
    transportMode: NIO
    lettuce:
      pool:
        max-active: 8 #最大连接数
        max-wait: -1 #默认-1 最大连接阻塞等待时间
        max-idle: 8 #最大空闲连接 默认8
        min-idle: 0 #最小空闲连接
      cluster:
        refresh:
          adaptive: true
          period: 5000
  # 消息队列
  besmq:
    broker-url: failover:(tcp://10.48.43.3:3600,tcp://10.48.43.3:3600)
    user: admin
    password: admin
    in-memory: false
    max-connections: 5
    idle-timeout: 30000
    expiry-timeout: 0
    pool:
      enabled: true
      max-connections: 5
      idle-timeout: 30000
      expiry-timeout: 0

# nfs配置
file:
  basePath: /data/filetemplate/
  viewPath:

# 强制设置一下
api:
  #  baseUrl: https://dev-sz.valueonline.cn/capital-cloud-api/
  baseUrl: https://cloud-api.valueonline.cn/capital-cloud-api/


service:
  gui:
    baseUrl: http://**********
    clientId: 08903b4b204004e1
    clientSecret: 77e3b5fe08903b4b204004e179b65b74

# 柜台接口配置
broker:
  message:
    url: http://************:8080/sms_send2.do
    corpId: 8888
    corpPwd: 8888
    corpService: 8888
  stock:
    url: /data/checkAuth/
  func:
    op_branch_no: 1000
    operator_no: 6666
    user_type: 1
    op_password: 111111
    op_station: 127.0.0.1
    op_entrust_way: 4
    menu_id:
    function_id:
    branch_no: 1000
    audit_action: 0
    user_token:
  H5Url: http://**************:8080
  domain:
    name: http://localhost:8088
brokerClient:
  config:
    T2SecurityEntrustQryService: T2SecurityEntrustQryLocalServiceImpl
    T2HisEntrustQryService: T2HisEntrustQryLocalServiceImpl

# java定时器执行时间
javaScheduled:
  sycRepPlanTrading: 00 00 03 * * ?

externalUrl:
  equity: https://soi-test.csc.com.cn/csc-soi
  repmanage:  http://**********:18082/repmanage
  compliance: http://***********:8090/csc_listedcompany

bes:
  mq:
    send-queue-name: VO_PF_QUEUE_TEST
    send-topic-name: VO_PF_TOPIC_TEST
    receive-topic-name: VO_PF_TOPIC_PLATFORM_TEST
    receive-USSD: VO_PF_TOPIC_RECEIVE_USSD_TEST

cas:
  server:
    url: https://casgbk.csc108.com/serviceValidate

# 获取股票行情接口
zs:
  apikey: csc-brokersys-esop-inner-test
  market-quote: http://*************/inner/market/quote

# 加密机
aesMachine:
  machineFlag: 1
  machinePath: *************:8018,**************:8018
  aexLogger: logsw = error, debug, info, warn;logPath = /log/;
  aexHost1: linkNum	 = -10;hsmModel = SJJ1310;

# 发送邮件  发送短息
cscInterface:
  pushProviderId: ESOP
  pushAuditorId: CSC13083
  pushChannelMail: mail
  pushTopicId: EsopNotice
  pushSendMessage: http://*************/inner/upush/source
  apikey: csc-brokersys-esop-inner-test
  smsUrl: http://************:8066/json/send-sms
  smsUserId: 'test'
  smsServiceCode: '3'
  smsSign: '098f6bcd4621d373cade4e832627b4f6'

# 滑块验证码或文字点击验证码配置
aj:
  captcha:
    water-mark:
    cacheType: redis
    type: clickWord

resetPassword:
  loginUrl: http://**********/ui/platform/user/login
