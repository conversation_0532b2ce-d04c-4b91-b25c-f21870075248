<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
	PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true" />
        <setting name="lazyLoadingEnabled" value="true" />
        <setting name="aggressiveLazyLoading" value="true" />
        <setting name="multipleResultSetsEnabled" value="true" />
        <setting name="useColumnLabel" value="true" />
        <setting name="mapUnderscoreToCamelCase" value="true" />
        <setting name="useGeneratedKeys" value="false" />
        <setting name="autoMappingBehavior" value="FULL" />
        <setting name="defaultExecutorType" value="REUSE" />
        <setting name="defaultStatementTimeout" value="10" />
    </settings>
    <plugins>
        <plugin interceptor="com.stock.core.dao.pager.PagerInterceptor">
            <property name="dialect" value="mariadb" />
        </plugin>
<!--        <plugin interceptor="AuditInsertInterceptor">-->
<!--        </plugin>-->
<!--        <plugin interceptor="com.stock.core.dao.AuditInterceptor">-->
<!--            &lt;!&ndash;使用强制更新几个固有字段&ndash;&gt;-->
<!--            <property name="forceUpdateExtraField" value="true"/>-->
<!--            &lt;!&ndash; 开启批量拦截插入更新等操作 &ndash;&gt;-->
<!--            <property name="openBatchIntercept" value="true"/>-->
<!--        </plugin>-->

    </plugins>
</configuration>