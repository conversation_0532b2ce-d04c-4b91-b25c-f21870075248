<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 数据库驱动 -->
    <classPathEntry
            location="D:/edm/mvnrepository/mysql/mysql-connector-java/5.1.39/mysql-connector-java-5.1.39.jar" />
    <context id="edm" targetRuntime="MyBatis3">
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin" />
        <commentGenerator>
            <property name="suppressDate" value="true" />
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true" />
        </commentGenerator>
        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************************" userId="agency_hazq" password="123456">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>
        <!-- 生成模型的包名和位置 -->
        <javaModelGenerator targetPackage="com.stock.service.platform.common.entity"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- 生成映射文件的包名和位置 -->
        <sqlMapGenerator targetPackage="com.stock.service.platform.common.dao"
                         targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 生成DAO的包名和位置 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.stock.service.platform.common.dao"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!-- 要生成哪些表 -->
        <table tableName="sa_outer_router" domainObjectName="OuterRouter">
            <generatedKey column="id" sqlStatement="SELECT CONCAT(uuid_short(),'')" identity="id" type="pre" />
        </table>
        <table tableName="sa_outer_role_user_map" domainObjectName="OuterRoleUserMap">
            <generatedKey column="id" sqlStatement="SELECT CONCAT(uuid_short(),'')" identity="id" type="pre" />
        </table>
        <table tableName="sa_outer_role_router_map" domainObjectName="OuterRoleRouterMap">
            <generatedKey column="id" sqlStatement="SELECT CONCAT(uuid_short(),'')" identity="id" type="pre" />
        </table>
        <table tableName="sa_outer_role" domainObjectName="OuterRole">
            <generatedKey column="id" sqlStatement="SELECT CONCAT(uuid_short(),'')" identity="id" type="pre" />
        </table>
    </context>
</generatorConfiguration>