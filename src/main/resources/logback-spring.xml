<?xml version="1.0" encoding="UTF-8"?>

<configuration>
	<springProperty scope="context" name="log.level" source="log.level" defaultValue=""/>
	<springProperty scope="context" name="log.appender" source="log.appender" defaultValue=""/>
	<springProperty scope="context" name="log.home" source="log.home" defaultValue=""/>
	<springProperty scope="context" name="log.stash.address" source="log.stash.address" defaultValue=""/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %X{X-Request-ID} %logger{15} %M - %msg%n</pattern>
        </encoder>
    </appender>

	<springProfile name="dev,prepub-sz,test,uat,prod">
	    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
	        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${log.home}/%d{yyyy-MM-dd}.log</fileNamePattern>
	            <maxHistory>60</maxHistory>
	        </rollingPolicy>
	        <encoder charset="UTF-8">
	            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level] %X{X-Request-ID} %logger{15} %M - %msg%n</pattern>
	        </encoder>
	    </appender>
    </springProfile>

<!--    <springProfile name="prepub-sz">-->
<!--        <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">-->
<!--            <destination>${log.stash.address}</destination>-->
<!--            <keepAliveDuration>5 minutes</keepAliveDuration>-->
<!--            <reconnectionDelay>1 second</reconnectionDelay>-->
<!--            <writeBufferSize>16384</writeBufferSize>-->
<!--            <encoder class="net.logstash.logback.encoder.LogstashEncoder">-->
<!--                <includeCallerData>true</includeCallerData>-->
<!--                <includeMdcKeyName>X-Request-ID</includeMdcKeyName>-->
<!--                <customFields>{"type":"jetty_log","program":"platform-jt"}</customFields>-->
<!--            </encoder>-->
<!--        </appender>-->
<!--    </springProfile>-->

<!--    <springProfile name="prod">-->
<!--        <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">-->
<!--            <destination>${log.stash.address}</destination>-->
<!--            <keepAliveDuration>5 minutes</keepAliveDuration>-->
<!--            <reconnectionDelay>1 second</reconnectionDelay>-->
<!--            <writeBufferSize>16384</writeBufferSize>-->
<!--            <encoder class="net.logstash.logback.encoder.LogstashEncoder">-->
<!--                <includeCallerData>true</includeCallerData>-->
<!--                <includeMdcKeyName>TID</includeMdcKeyName>-->
<!--                <customFields>{"type":"jetty_log","program":"service-platform"}-->
<!--                </customFields>-->
<!--            </encoder>-->
<!--        </appender>-->
<!--    </springProfile>-->

    <root level="${log.level}">
        <appender-ref ref="console" />
        <appender-ref ref="${log.appender}" />
    </root>

    <logger name="org.springframework" level="WARN"></logger>
    <logger name="ma.glasnost" level="WARN"></logger>
    <logger name="org.hibernate" level="WARN"></logger>
    <logger name="org.mybatis" level="WARN"></logger>
    <logger name="org.eclipse" level="WARN"></logger>
    <logger name="org.apache" level="WARN"></logger>
    <logger name="io.lettuce" level="WARN"></logger>
    <logger name="io.netty" level="WARN"></logger>
	<logger name="io.swagger" level="WARN"></logger>
	<logger name="springfox.documentation" level="WARN"></logger>


</configuration>
