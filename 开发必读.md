# 项目结构
## 包名 
### com.stock.server.template
### server和template需要按照自己的需求进行更改

基本包结构如下：
* file
    * controller
      * FileController
    * dao
      * FileBizMapper
    * dto
      * FileDto
    * service
      * FileService
    * listener
* user
    * controller
      * UserController
    * dao
      * UserBizMapper
    * dto
      * UserDto
    * service
      * UserService
    * listener

注意事项：
* controller的json传入传出使用dto进行操作
* 业务包下的dao的包的类命名使用 XXX+BizMapper比如（user包下的dao包下的UserBizMapper）
* 使用插件mybatis-generator生成xml和java代码，目录resource/persistence/mybatis-generator.xml，上面有生成例子  
* 所有的使用的mybatis-generator生成的代码最终都要放在common包下的dao包和entity包下


# 关于数据表中的id生成
使用了AuditInsertInterceptor对数据表的插入进行id替换，替换成雪花算法的生成的id

# 关于请求异常
* 统一使用ApplicationException异常类抛出
* 尽量不要使用try对异常进行捕获
* 抛出的异常信息需要写在resources/message.properties文件下
* message.properties下的异常信息定义为：error+'.'+四位自定义数字模块id+两位自定义数字异常id，比如（error.000101=新密码与确认的新密码不一致）

# 关于请求
* 使用dto进行请求的传入和传出
* 最好使用application/json的形式进行数据传入
* 使用validation对dto中参数进行校验，防止数据不合法导致的问题
  * 建议使用校验组进行校验
* modelAndView返回放在controller层
* 分页查询使用QueryInfo实体类进行传入，返回Page类
* 分层架构要明确，controller只允许引用service，不可以直接引入dao

# 关于权限
* sa_user:负责登录的用户表
* sa_role:角色表
* sa_user_role_map:用户和角色的对应表
* sa_router:路由表（菜单/按钮）
* sa_role_router_map:角色和路由的对应表
* sa_resource:资源表（用于标识接口的使用）
* sa_router_resource_map:路由和资源的对应表

# 关于excel导入导出
+ 构建ExcelMappingRule.xml（personInfo.ExcelMappingRule.xml）
+ 创建一个实体类用于存储excel中的属性
+ 使用read进行excel读取，使用write进行excel的写入
+ 详细请看PersonController中的importFromExcel方法和exportToExcel方法

# 关于文件上传/下载
* 默认使用nfs存储系统进行存储
* 详细请看FileController

# 关于sms短信和邮件
* 引入LocalPushClient对象进行配置
* 如需自己实现，可相应实现LocalMailService接口和LocalSmsService接口
* 将LocalMailService和LocalSmsService通过spring容器注入到LocalPushClient对象中
* 详细请看CommonService中的sendSms方法和sendMail方法（使用公司内部的京天利运营商）







