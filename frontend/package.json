{"name": "service-platform", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode prod", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:uat": "vue-cli-service build --mode uat", "build:prepub-sz": "vue-cli-service build --mode prepub-sz", "build:sim": "vue-cli-service build --mode sim", "build:prod": "vue-cli-service build --mode prod", "build:huawei": "vue-cli-service build --mode huawei", "build-app": "vue-cli-service build app --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@traptitech/markdown-it-katex": "^3.6.0", "axios": "^0.20.0", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "default-passive-events": "^2.0.0", "echarts": "4.1.0", "eslint-plugin-standard": "^4.0.2", "highlight.js": "^11.8.0", "js-cookie": "^2.2.1", "markdown-it": "^11.0.1", "markdown-it-highlightjs": "^4.0.1", "markdown-it-katex-gpt": "^1.1.0", "markdown-it-link-attributes": "^4.0.1", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "stock-ui": "^1.5.64", "stock-vue-plugin": "^1.2.70", "store": "^2.0.12", "swiper": "8.4.4", "vue": "^2.6.12", "vue-marquee-text-component": "^1.2.0", "vue-router": "^3.4.7", "vuex": "^3.5.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.8", "@vue/cli-plugin-eslint": "^4.5.8", "@vue/cli-service": "^4.5.8", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "blueimp-md5": "^2.19.0", "eslint": "^6.8.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^4.0.2", "eslint-plugin-html": "^6.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.14.1", "sass-loader": "^10.0.3", "vue-particles": "^1.0.9", "sass-resources-loader": "^2.1.1", "vue-template-compiler": "^2.6.12"}}