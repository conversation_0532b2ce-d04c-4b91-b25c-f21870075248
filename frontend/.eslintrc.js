module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-new': 0,
    camelcase: 0,
    'new-cap': 0,
    'no-array-constructor': 0,
    'no-caller': 0,
    'no-eval': 0,
    'no-extend-native': 0,
    'no-prototype-builtins': 0,
    'no-undef-init': 0,
    'vue/require-prop-type-constructor': 0,
    'no-dupe-keys': 0,
    'no-redeclare': 0,
    'no-tabs': 0,
    'prefer-const': 0,
    'no-useless-escape': 0,
    'no-control-regex': 0,
    'one-var': 0,
    semi: 0,
    quotes: 0,
    'dot-notation': 0,
      'no-unneeded-ternary':0
  }
}
