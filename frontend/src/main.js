// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
// [Violation] Added non-passive event listener to a scroll-blocking 'mousewheel' event.
import 'default-passive-events';

/** element ui theme **/
import 'stock-ui/lib/theme-chalk/index.css';
import 'stock-ui/lib/theme-chalk/fonts/iconfont.css';
import './styles/index.scss';
/** stock ui **/
import StockUI from 'stock-ui';
// 引入icon
import "../public/assets/icons/iconfont.css";
import "../src/assets/fonts/iconfont.css";
import './styles/iconfont/iconfont.css';

/** capital style **/
/** vue framework **/
import Vue from 'vue';
/** application **/
import App from './app.vue';
import router from './router';

import store from './store';
import StockVuePlugin from 'stock-vue-plugin';

import "./utils/defaultSettings";
import messageJson from './message.json';
import moment from 'moment';
import echarts from 'echarts';
import VueParticles from 'vue-particles'

/** 引用全局共通函数 */
import {
  _amountFormat
} from './api/common-api.js'

Vue.config.productionTip = false;
Vue.prototype.$messageText = messageJson;
Vue.prototype.$moment = moment;
Vue.prototype.$echarts = echarts;

Vue.prototype.amountFormat = _amountFormat;

Vue.use(VueParticles)
Vue.use(StockVuePlugin, {
  tracing: process.env.VUE_APP_TRACING,
  serviceName: process.env.VUE_APP_NAME,
  serviceVersion: process.env.VUE_APP_VERSION,
  baseUrl: window.location.origin + '/platform',
  loginApi: window.location.origin + '/platform/login',
  index: '/index',
  store: store,
  isMicroService: false,
  router: router,
  isExtraHeaders: true,
  whiteList: [
    '/',
    '/user/login',
    '/error/401', '/error/403', '/error/404', '/error/500', '/redirect'
  ],
  permission: true,
  tokenName: 'access_token_platform',
  jurisdictionData: 'platform_jurisdictionData',
  menuDataName: 'platform_menuData'
});

Vue.directive('check', {
  inserted: function (el, binding, vnode) {
    // 聚焦元素
    let value = binding.expression;
    let menuTree = JSON.parse(localStorage.platform_jurisdictionData).menuList
    let id = vnode.context.$route.meta.id;
    if (!checkMenuAuth(menuTree, id, value)) {
      let classList = el.getAttribute("class");
      if (classList && classList.indexOf("el-input") != -1) {
        el.setAttribute("class", el.getAttribute("class") + " is-disabled")
        for (let i = 0; i < el.childNodes.length; i++) {
          if (el.childNodes[i].nodeName == "INPUT") {
            el.childNodes[i].setAttribute("disabled", "disabled")
          }
        }
      } else {
        el.parentNode.removeChild(el);
      }
    }
  }
});

function checkMenuAuth (tree, id, value) {
  for (const router of tree) {
    if (router.para == value && router.authorityFlag == '2' && router.menuType == '2') {
      return true;
    }
    if (router.menuList && router.menuList.length > 0) {
      if (checkMenuAuth(router.menuList, id, value)) {
        return true;
      }
    }
  }
  return false;
}

Vue.use(StockUI, {
  size: 'medium' // set stock-ui default size [mini,small,medium,large]
});

new Vue(
  {
    router,
    store,
    render: h => h(App)
  }
).$mount('#app');
