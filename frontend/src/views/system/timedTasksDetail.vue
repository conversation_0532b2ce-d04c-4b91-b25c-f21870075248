<!--
*@ timedTasksDetail
*<AUTHOR>
*@date 2024/7/31 9:40
-->
<template>
  <el-row>
    <el-table
        :data="tableData"
        class="full"
        style="width: 100%;margin-bottom: 20px;color: #000"
        :font-size="14"
        header-row-class-name	="custom-header-class-list"
    >
      <el-table-column
          type="index"
          width="60px"
          align="center"
          label="序号">
      </el-table-column>
      <el-table-column label="任务名称" prop="info" align="center" min-width="8%">
        <template slot-scope="scope">
          <span>{{scope.row.zhName}}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据日期" prop="tradeDate" align="center" min-width="8%" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ getValue(scope.row.tradeDate)}}
        </template>
      </el-table-column>
      <el-table-column label="错误信息" prop="errorMessage" align="center" min-width="20%" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ getValue(scope.row.errorMessage)}}
        </template>
      </el-table-column>
      <el-table-column label="定时状态" prop="isSuccess" align="center" min-width="8%">
        <template slot-scope="scope">
          <span v-if="scope.row.isSuccess === '0'">失败</span>
          <span v-else-if="scope.row.isSuccess === '1'">成功</span>
          <span v-else-if="scope.row.isSuccess === '2'">执行中</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" prop="createTime" align="center" min-width="10%">
        <template slot-scope="scope">
          <span>{{formatDate(scope.row.createTime)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" prop="updateTime" align="center" min-width="10%">
        <template slot-scope="scope">
          <span>{{formatDate(scope.row.updateTime)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="10%">
        <template slot-scope="scope">
          <span v-if="scope.row.isSuccess === '0' || scope.row.zhName === '交易数据合并'">
            <el-button plain icon="el-icon-s-promotion" @click="repeat(scope.row)">重新执行</el-button>
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        style="text-align: center;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="query.startRow"
        :page-sizes="[10, 20, 50, 200]"
        :page-size="query.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>
  </el-row>
</template>

<script>
import {_getTasksList, _repeat} from "../../api/system/timed-tasks";

export default {
  name: "timedTasksDetail",
  props: {
    timedTasksDetailName: {
      type: String,
      default: ""
    }
  },
  components: {},
  data() {
    return {
      tableData: [],
      total: 0,
      query: {
        // 页码
        startRow: 1,
        // 每页显示多少条数据
        pageSize: 10
      },
    }
  },
  created() {
  },
  mounted() {
    this.search();
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 1
      this.search();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.search();
    },
    formatDate (timestamp) {
      if (!timestamp) {
        return '--';
      }
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getValue (val) {
      if (val === null || val === 'null' || val === undefined || val === 'undefined') {
        val = '--'
      }
      return val
    },
    search () {
      let query = { ...this.query };
      query.sysCode = "PLATFORM"
      query.name = this.timedTasksDetailName
      _getTasksList(query).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.tableData;
          this.total = res.data.result.total;
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    repeat (val) {
      val.sysCode = "PLATFORM"
      _repeat(val).then(res => {
        if (res.data.success) {
          this.$message.info("重新发送完成");
          this.search();
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>