<template>
  <div class="container containerUpdate">
    <div class="containerUpdate-div" style="">
      <div class="page-title">
        <i></i>
        <span>上市公司管理</span>
      </div>
      <el-form status-icon :model="queryList" label-width="100px" style="border: 0px;margin-top: 12px"
      >
        <input type="text" name="disableEnterSubmit" style="display:none"/>
        <el-row style="text-align: center" :gutter="12" class="row-form">
          <el-col :span="4">
            <el-input size="small" v-model="queryList.companyCode" placeholder="证券代码"></el-input>
          </el-col>
          <el-col :span="4">
            <el-input size="small" v-model="queryList.zhName" placeholder="公司名称"></el-input>
          </el-col>
          <el-col :span="4">
            <el-select
                size="small"
                class="full"
                clearable
                filterable
                placeholder="服务营业部"
                :show-overflow-tooltip="true"
                v-model="queryList.orgId"
            >
              <el-option
                  :key="'org' + index"
                  :label="item.orgName"
                  :value="item.orgId"
                  v-for="(item, index) in orgList"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button size="small" type="primary" round @click="queryClick">查询</el-button>
            <el-button size="small" @click="clearClick" round plain>清空条件</el-button>
            <el-button size="small" @click="newClick" round plain>新增</el-button>


            <!--导入上市公司按钮 临时-->
            <el-upload
                style="display: inline-block"
                :http-request="customUpload"
                :multiple="false"
                :show-file-list="false"
                :before-upload="beforeUpload">
              <el-button v-if="importSaCompanyStatus" round type="primary" size="small" style="margin-left: 10px;">导入上市公司</el-button>
            </el-upload>


          </el-col>
        </el-row>
      </el-form>
      <div style="margin-top: 12px">
        <el-table :data="tableData"
                  row-key="id"
                  fit
                  style="width: 100%;margin-bottom: 20px;color: #000"
                  :font-size="14"
                  header-row-class-name="custom-header-class"
                  :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
          <!--          <el-table-column type="index" min-width="10%"></el-table-column>-->
          <el-table-column label="证券代码" prop="companyCode" align="center" min-width="10%"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="证券名称" prop="companyStockName" align="center" min-width="10%"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.companyStockName">{{ scope.row.companyStockName }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="公司名称" prop="zhName" align="center" min-width="20%"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="服务营业部" prop="businessDepartmentName" align="center" min-width="20%"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="法定代表人" prop="corporate" align="center" min-width="10%" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.corporate">{{ scope.row.corporate }}</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="统一社会信用代码" prop="socialUnifiedCreditCode" align="center" min-width="20%"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" align="center" min-width="10%" show-overflow-tooltip>
            <template slot-scope="scope">
              <i class="iconfont icon-ic-modify custom-icon-button" title="编辑"
                 v-if="scope.row.deleteflag === '0'" @click="editClick(scope.row.id,scope.row.seriakey)"></i>
              <i class="iconfont ic-circle-plus-o custom-icon-button" title="新增子公司"
                 v-if="scope.row.deleteflag === '0'" @click="newChildClick(scope.row)"></i>
              <i class="iconfont ic-trash custom-icon-button" title="删除"
                 v-if="scope.row.deleteflag === '0'"
                 @click="deleteClick(scope.row.id,scope.row.seriakey,scope.row.companyCode)"></i>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            style="text-align: center;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryList.startRow"
            :page-sizes="[10, 20, 50, 200]"
            :page-size="queryList.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
        </el-pagination>
        <el-dialog :title="(companyParam.id === undefined || companyParam.id == '') ?'新增上市公司' :'编辑上市公司'"
                   :visible.sync="updateDialogVisible" width="80%"
                   :modal-append-to-body="false"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   class="NewlyCompany"
                   v-if="updateDialogVisible">
          <el-form :model="companyParam" :rules="rules" ref="companyParam"
                   label-width="150px" class="demo-ruleForm" style="border: 0px">
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="证券代码" prop="companyCode">
                  <el-input v-model.trim="companyParam.companyCode" @change="getCompanyInfoByCode()"
                            :disabled="(companyParam.id === undefined || companyParam.id == '') ? false : true"
                            size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="证券名称" prop="companyStockName">
                  <el-input v-model.trim="companyParam.companyStockName" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司名称" prop="zhName">
                  <el-input v-model.trim="companyParam.zhName" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司简称" prop="zhSortName">
                  <el-input v-model.trim="companyParam.zhSortName" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="板块" prop="marketStockType">
                  <el-select v-model="companyParam.marketStockType" style="width:100%" disabled>
                    <el-option v-for="item in stockMarketDataList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司性质" prop="orgform">
                  <el-select v-model="companyParam.orgform" size="small" class="full">
                    <el-option v-for="item in companyNatureList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司登记日期" prop="registerDate">
                  <el-date-picker
                      type="date"
                      placeholder="选择日期"
                      class="full"
                      v-model="companyParam.registerDate"
                      value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="注册地址" prop="registerAddress">
                  <el-input v-model.trim="companyParam.registerAddress" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司注册资金(元)" prop="registerCapital">
                  <el-input v-model.trim="companyParam.registerCapital"
                            size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="Email" prop="companyMail">
                  <el-input v-model.trim="companyParam.companyMail" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司上市日期" prop="listingDate">
                  <el-date-picker
                      type="date"
                      placeholder="选择日期"
                      class="full"
                      v-model="companyParam.listingDate"
                      value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="统一社会信用代码" prop="socialUnifiedCreditCode">
                  <el-input v-model.trim="companyParam.socialUnifiedCreditCode" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="发行价格" prop="issuePrice">
                  <el-input v-model.trim="companyParam.issuePrice" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="A股股本" prop="capitalStockA">
                  <el-input v-model.trim="companyParam.capitalStockA" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="B股股本" prop="capitalStockB">
                  <el-input v-model.trim="companyParam.capitalStockB" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="H股股本" prop="capitalStockH">
                  <el-input v-model.trim="companyParam.capitalStockH" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="总股本" prop="capitalStockTotal">
                  <el-input v-model.trim="companyParam.capitalStockTotal" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="市场" prop="market">
                  <el-select v-model="companyParam.market" style="width:100%">
                    <el-option v-for="item in stockJtList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="法定代表人" prop="corporate">
                  <el-input v-model.trim="companyParam.corporate" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="法人证件号码" prop="corporateIdNumber">
                  <el-input v-model.trim="companyParam.corporateIdNumber" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="服务营业部" prop="organisationNo">
                  <el-select-multiple filterable v-model="companyParam.organisationNo" style="width: 100%;">
                    <el-select-multiple-option
                        v-for="(item,index) in busDepOptions"
                        ::title="item.text"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value">
                    </el-select-multiple-option>
                  </el-select-multiple>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司电话" prop="phone">
                  <el-input v-model.trim="companyParam.phone" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18" :offset="3">
                <el-form-item label="联系人信息" prop="contacts">
                  <el-row>
                    <el-button size="small" type="primary" round @click="addContact">新增</el-button>
                  </el-row>
                  <el-row style="margin-top: 10px">
                    <el-table :data="companyParam.contacts" detail style="width: 100%">
                      <el-table-column prop="contactName" label="联系人" width="160">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactName"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactPhone" label="电话/客户号">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactPhone" @change="contactPhoneCheck(scope.row)"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactEmail" label="邮箱">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactEmail" @change="contactEmailCheck(scope.row)"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactSystem" label="子系统">
                        <template slot-scope="scope">
                          <el-select v-model="scope.row.contactSystem" placeholder="请选择子系统"
                                     style="width: 100% !important;"
                                     class="full" filterable>
                            <el-option
                                v-for="item in sysList"
                                :key="item.sysCode"
                                :label="item.sysName"
                                :value="item.sysCode">
                            </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="160">
                        <template slot-scope="scope">
                          <i class="iconfont ic-trash custom-icon-button" title="删除"
                             @click="deleteContact(scope.row)"></i>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18" :offset="3">
                <el-form-item label="备注信息" prop="remark">
                  <el-input type="textarea" v-model.trim="companyParam.remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="text-center">
              <el-button size="small" @click="cancel">返回</el-button>
              <el-button type="primary" @click="submitForm('companyParam')" size="small">保存</el-button>
            </el-row>
          </el-form>
        </el-dialog>
        <el-dialog :title="(companyParam.id === undefined || companyParam.id == '') ?'新增子公司' :'编辑子公司'"
                   :visible.sync="updateNewDialogVisible" width="80%"
                   :modal-append-to-body="false"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   class="NewlyCompany"
                   v-if="updateNewDialogVisible">
          <el-form :model="companyParam" :rules="childRules" ref="companyParam"
                   label-width="150px" class="demo-ruleForm" style="border: 0px">
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="证券代码" prop="companyCode">
                  <el-input v-model.trim="companyParam.companyCode" @change="getCompanyInfoByCode()"
                            :disabled="(companyParam.id === undefined || companyParam.id == '') ? false : true"
                            size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="证券名称" prop="companyStockName">
                  <el-input v-model.trim="companyParam.companyStockName" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司名称" prop="zhName">
                  <el-input v-model.trim="companyParam.zhName" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司简称" prop="zhSortName">
                  <el-input v-model.trim="companyParam.zhSortName" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="板块" prop="marketStockType">
                  <el-select v-model="companyParam.marketStockType" style="width:100%">
                    <el-option v-for="item in stockMarketDataList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司性质">
                  <el-select v-model="companyParam.orgform" size="small" class="full">
                    <el-option v-for="item in companyNatureList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司登记日期" prop="registerDate">
                  <el-date-picker
                      type="date"
                      placeholder="选择日期"
                      class="full"
                      v-model="companyParam.registerDate"
                      value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="注册地址" prop="registerAddress">
                  <el-input v-model.trim="companyParam.registerAddress" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司注册资金(元)" prop="registerCapital">
                  <el-input v-model.trim="companyParam.registerCapital"
                            size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="Email" prop="companyMail">
                  <el-input v-model.trim="companyParam.companyMail" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="公司上市日期" prop="listingDate">
                  <el-date-picker
                      type="date"
                      placeholder="选择日期"
                      class="full"
                      v-model="companyParam.listingDate"
                      value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="统一社会信用代码" prop="socialUnifiedCreditCode">
                  <el-input v-model.trim="companyParam.socialUnifiedCreditCode" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="发行价格" prop="issuePrice">
                  <el-input v-model.trim="companyParam.issuePrice" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="A股股本" prop="capitalStockA">
                  <el-input v-model.trim="companyParam.capitalStockA" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="B股股本" prop="capitalStockB">
                  <el-input v-model.trim="companyParam.capitalStockB" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="H股股本" prop="capitalStockH">
                  <el-input v-model.trim="companyParam.capitalStockH" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="总股本" prop="capitalStockTotal">
                  <el-input v-model.trim="companyParam.capitalStockTotal" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="市场" prop="market">
                  <el-select v-model="companyParam.market" style="width:100%">
                    <el-option v-for="item in stockJtList" :value="item.labelValue" :label="item.labelName"
                               :key="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="法定代表人" prop="corporate">
                  <el-input v-model.trim="companyParam.corporate" size="small"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="法人证件号码" prop="corporateIdNumber">
                  <el-input v-model.trim="companyParam.corporateIdNumber" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9" :offset="3">
                <el-form-item label="服务营业部" prop="organisationNo">
                  <el-select-multiple filterable v-model="companyParam.organisationNo" style="width: 100%;">
                    <el-select-multiple-option
                        v-for="(item,index) in busDepOptions"
                        ::title="item.text"
                        :key="item.value"
                        :label="item.text"
                        :value="item.value">
                    </el-select-multiple-option>
                  </el-select-multiple>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="公司电话" prop="phone">
                  <el-input v-model.trim="companyParam.phone" size="small"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18" :offset="3">
                <el-form-item label="联系人信息" prop="contacts">
                  <el-row>
                    <el-button size="small" type="primary" round @click="addContact">新增</el-button>
                  </el-row>
                  <el-row style="margin-top: 10px">
                    <el-table :data="companyParam.contacts" detail style="width: 100%">
                      <el-table-column prop="contactName" label="联系人" width="160">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactName"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactPhone" label="电话/客户号">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactPhone" @change="contactPhoneCheck(scope.row)"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactEmail" label="邮箱">
                        <template slot-scope="scope">
                          <el-input v-model="scope.row.contactEmail" @change="contactEmailCheck(scope.row)"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column prop="contactSystem" label="子系统">
                        <template slot-scope="scope">
                          <el-select v-model="scope.row.contactSystem" placeholder="请选择子系统"
                                     style="width: 100% !important;"
                                     class="full" filterable>
                            <el-option
                                v-for="item in sysList"
                                :key="item.sysCode"
                                :label="item.sysName"
                                :value="item.sysCode">
                            </el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" align="center" width="160">
                        <template slot-scope="scope">
                          <i class="iconfont ic-trash custom-icon-button" title="删除"
                             @click="deleteContact(scope.row)"></i>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="18" :offset="3">
                <el-form-item label="备注信息" prop="remark">
                  <el-input type="textarea" v-model.trim="companyParam.remark"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row class="text-center">
              <el-button size="small" @click="cancel">返回</el-button>
              <el-button type="primary" @click="submitForm('companyParam')" size="small">保存</el-button>
            </el-row>
          </el-form>
        </el-dialog>
      </div>
    </div>

  </div>
</template>

<script>

import {
  queryPagingCompanyBySelective,
  queryBusinessDepartmentOptions,
  deleteCompanyById,
  createOrUpdate,
  getBusDepById,
  checkBusDepCode,
  _queryTableData,
  getStaffSelectData,
  checkSocialUnifiedCreditCode,
  getCompanyInfoByCode, checkCompanyCodeConvert
} from '@/api/system/sa-company-api'
import {
  _validateEmail,
  _validateMoney,
  _validateIdCardNo,
  _validateNumEnglish,
  _validateTelephone,
  _validatePhone
} from '@/utils/validate'
import {_importListedCompanyInfo} from "@/api/system/infoSync-api";
import {selectButtonByName} from '../../api/mqManage/MQManage-api'
import { _getOrgListOnlyThree} from "@/api/person-api";
import {showMessage} from "stock-vue-plugin/lib/utils/message";

export default {
  name: "saCompany",
  data() {
    // Email验证规则
    const validateMail = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateEmail(value)) {
        callback(new Error('邮箱地址格式不正确'))
        return
      }
      callback()
    }

    // 身份证号验证
    const validateIdCardNo = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateIdCardNo(value)) {
        callback(new Error('法人证件号码格式不正确'))
        return
      }
      callback()
    }
    // 证券代码校验
    const validateNumEnglish = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateNumEnglish(value)) {
        callback(new Error('只能输入数字和字母'))
        return
      }
      callback()
    }
    // 金钱验证
    const validateMoney = function (rule, value, callback) {
      if (value) {
        value = value.replace(/,/g, '');
      }
      if (value != null && value !== '' && !_validateMoney(value)) {
        callback(new Error('请输入有效金额'))
        return
      } else {
        if (value != null && value !== '' && value.length > 14) {
          callback(new Error('请输入 1 到 14 位数值'))
          return
        }
      }
      callback()
    }
    // 公司电话验证
    const validateTelephone = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateTelephone(value)) {
        callback(new Error('电话格式不正确'))
        return
      }
      callback()
    }
    return {
      importSaCompanyStatus: false,
      stockMarketDataList: [],
      companyNatureList: [],
      stockJtList: [],
      orgList: [],
      queryList: {
        companyCode: '',
        zhName: '',
        orgId: '',
        startRow: 1,
        pageSize: 10
      },
      tableData: [],
      sysList: [],
      userOrgType: null,
      updateDialogVisible: false,
      updateNewDialogVisible: false,
      total: 0,
      currentPage: 1,
      createLoading: '',
      companyParam: {
        id: '',
        companyCode: '',
        zhName: '',
        zhSortName: '',
        companyStockName: '',
        marketStockType: '',
        registerDate: '',
        orgform: '',
        registerCapital: '',
        corporate: '',
        corporateIdNumber: '',
        phone: '',
        deleteflag: '0',
        companyMail: '',
        registerAddress: '',
        organisationNo: '',
        contacts: [], // 联系人列表
        remark: '',
        seriakey: '',
        socialUnifiedCreditCode: '',//统一社会信用代码（新增）
        listingDate: '',//上市日期（新增）
        issuePrice: '',//发行价格（新增）
        capitalStockA: '',//A股股本（新增）
        capitalStockB: '',//B股股本（新增）
        capitalStockH: '',//H股股本（新增）
        capitalStockTotal: '',//总股本（新增）
        market: '',//市场
      },
      // 营业部下拉列表
      busDepOptions: [],
      rules: { // 验证规则
        companyCode: [
          {required: true, message: '请输入证券代码', trigger: 'blur'},
          {min: 6, max: 6, message: '请输入6位证券代码（可包含字母、数字）', trigger: 'blur'},
          {validator: validateNumEnglish, trigger: 'blur'}
        ],
        registerCapital: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        zhName: [
          {required: true, message: '请输入公司名称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              let reg = new RegExp('[\<,\>,\',\"]');
              if (reg.test(value)) {
                callback(new Error("不可输入特殊字符"));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        orgform: [
          {required: true, message: '请选择公司性质', trigger: 'change'}
        ],
        zhSortName: [
          {required: true, message: '请输入公司简称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'}
        ],
        companyStockName: [
          {required: true, message: '请输入证券名称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'}
        ],
        registerAddress: [
          {min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur'}
        ],
        corporate: [
          {required: true, message: '请输入法定代表人', trigger: 'blur'},
          {min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur'}
        ],
        corporateIdNumber: [
          {min: 1, max: 18, message: '长度在 1 到 18 个字符', trigger: 'blur'},
          {validator: validateIdCardNo, trigger: 'blur'}
        ],
        companyMail: [
          {validator: validateMail, trigger: 'blur'},
          {min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur'}
        ],
        remark: [
          {min: 0, max: 500, message: '长度在 0 到 500 个字符', trigger: 'blur'}
        ],
        phone: [
          {min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur'}
        ],
        organisationNo: [
          {required: true, message: '请选择服务营业部', trigger: 'blur'}
        ],
        marketStockType: [
          {required: true, message: '请选择公司所在板块', trigger: 'change'}
        ],
        contacts: [
          {required: true, message: '请填写联系人', trigger: 'blur'}
        ],
        socialUnifiedCreditCode: [
          {required: true, message: '请填写统一社会信用代码', trigger: 'blur'}
        ],
        issuePrice: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockA: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockB: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockH: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockTotal: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        market: [
          {required: true, message: '请选择公司所在市场', trigger: 'change'}
        ]
      },
      childRules: { // 验证规则
        registerCapital: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        zhName: [
          {required: true, message: '请输入公司名称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              let reg = new RegExp('[\<,\>,\',\"]');
              if (reg.test(value)) {
                callback(new Error("不可输入特殊字符"));
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        zhSortName: [
          // {required: true, message: '请输入公司简称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'}
        ],
        companyStockName: [
          {message: '请输入证券名称', trigger: 'blur'},
          {min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur'}
        ],
        registerAddress: [
          {min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur'}
        ],
        corporate: [
          {min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur'}
        ],
        corporateIdNumber: [
          {min: 1, max: 18, message: '长度在 1 到 18 个字符', trigger: 'blur'},
          {validator: validateIdCardNo, trigger: 'blur'}
        ],
        companyMail: [
          {validator: validateMail, trigger: 'blur'},
          {min: 1, max: 128, message: '长度在 1 到 128 个字符', trigger: 'blur'}
        ],
        remark: [
          {min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur'}
        ],
        phone: [
          {min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur'}
        ],
        organisationNo: [
          {required: true, message: '请选择服务营业部', trigger: 'blur'}
        ],
        marketStockType: [
          {message: '请选择公司所在板块', trigger: 'change'}
        ],
        socialUnifiedCreditCode: [
          {required: true, message: '请填写统一社会信用代码', trigger: 'blur'}
        ],
        issuePrice: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockA: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockB: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockH: [
          {validator: validateMoney, trigger: 'blur'}
        ],
        capitalStockTotal: [
          {validator: validateMoney, trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    this.getOrgList(); // 获取服务营业部下拉
    this.queryTableDataInit(); // 列表页初始化
    this.queryBusOptions(); // 获取营业部下拉(详情页)
    this.getSelectData(); // 获取市场,公司性质下拉(详情页)
    this.getTableData(); // 获取子系统下拉(详情页)
    this.checkButtonStatus();// 判断导入上市公司按钮是否显示
  },
  methods: {
    getOrgList() {
      _getOrgListOnlyThree().then((res) => {
        if (res.data.success) {
          this.orgList = res.data.result;
        } else {
          if (res.data.errorMsg) {
            showMessage("error", res.data.errorMsg);
          }
        }
      });
    },
    customUpload(fileObj) {
      // 在这里编写自定义的上传逻辑
      let data = new FormData();
      data.append('files', fileObj.file);
      _importListedCompanyInfo(data).then(res => {
        if (res.data.success) {
          this.$message.info("导入" + res.data.result + "条数据");
          window.location.reload();
        } else {
          // this.importSaCompanyStatus = true;
          this.$message.error("导入失败" + res.data.errorMsg);
        }
      })
      this.checkButtonStatus()
    },
    beforeUpload(file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const isXls = file.type === 'application/vnd.ms-excel';
      if (!isXlsx && !isXls) {
        this.$message.error('只能上传xls、xlsx文件');
        return false; // 取消上传
      }
      return true; // 立即上传文件
    },
    getSelectData() {
      getStaffSelectData().then(res => {
        this.stockMarketDataList = res.data.result.stockMarketDataList;
        this.companyNatureList = res.data.result.companyNatureList;
        this.stockJtList = res.data.result.stockJtList;
      });
    },
    getTableData() {
      _queryTableData({}).then(res => {
        if (res.data.success) {
          this.sysList = res.data.result;
        } else {
          if (res.data.errorMsg) {
            this.$message.error(res.data.errorMsg);
          }
        }
      })
    },
    // 列表查询初始化
    queryTableDataInit() {
      let param = this.queryList;
      queryPagingCompanyBySelective(param).then(res => {
        this.tableData = res.data.tableData;
        this.total = res.data.total;
        this.userOrgType = res.data.userOrgType;
        if (this.tableData.length === 0) {
          param.startRow = 1;
          queryPagingCompanyBySelective(param).then(res => {
            this.tableData = res.data.tableData;
            this.total = res.data.total;
            this.userOrgType = res.data.userOrgType;
          })
        }
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.queryList.pageSize = val
      this.queryList.startRow = 1
      this.queryTableDataInit();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.queryList.startRow = val
      this.queryTableDataInit();
    },
    paperSearch() {
      this.queryTableDataInit();
    },

    // 查询
    queryClick() {
      this.queryList.startRow = 1;
      let param = this.queryList;
      queryPagingCompanyBySelective(param).then(res => {
        this.tableData = res.data.tableData;
        this.total = res.data.total;
      })
    },

    // 新增 或 编辑
    newClick(row) {
      this.companyParam = {
        id: '',
        companyCode: '',//证券代码
        companyStockName: '',//证券名称
        zhName: '',//公司名称
        zhSortName: '',//公司简称
        marketStockType: '',//市场（子公司）
        registerDate: '',//公司注册日期
        orgform: '',//公司性质
        registerCapital: '',//公司资本
        corporate: '',//法人
        corporateIdNumber: '',//法人证件号码
        phone: '',//电话
        deleteflag: '0',
        companyMail: '',//Email
        registerAddress: '',//注册地址
        organisationNo: '',//服务营业部
        contacts: [],//联系人
        remark: '',//备注信息
        socialUnifiedCreditCode: '',//统一社会信用代码（新增）
        listingDate: '',//上市日期（新增）
        issuePrice: '',//发行价格（新增）
        capitalStockA: '',//A股股本（新增）
        capitalStockB: '',//B股股本（新增）
        capitalStockH: '',//H股股本（新增）
        capitalStockTotal: '',//总股本（新增）
        market: '',//市场
        parentId: row.id ? row.id : '',
        parentCompanyCode: row.parentCompanyCode ? row.parentCompanyCode : row.companyCode ? row.companyCode : ''
      }
      this.updateDialogVisible = true;
    },
    newChildClick(row) {
      this.companyParam = {
        id: '',
        companyCode: '',//证券代码
        companyStockName: '',//证券名称
        zhName: '',//公司名称
        zhSortName: '',//公司简称
        marketStockType: '',//市场（子公司）
        registerDate: '',//公司注册日期
        orgform: '',//公司性质
        registerCapital: '',//公司资本
        corporate: '',//法人
        corporateIdNumber: '',//法人证件号码
        phone: '',//电话
        deleteflag: '0',
        companyMail: '',//Email
        registerAddress: '',//注册地址
        organisationNo: '',//服务营业部
        contacts: [],//联系人
        remark: '',//备注信息
        socialUnifiedCreditCode: '',//统一社会信用代码（新增）
        listingDate: '',//上市日期（新增）
        issuePrice: '',//发行价格（新增）
        capitalStockA: '',//A股股本（新增）
        capitalStockB: '',//B股股本（新增）
        capitalStockH: '',//H股股本（新增）
        capitalStockTotal: '',//总股本（新增）
        market: '',//市场
        parentId: row.id ? row.id : '',
        parentCompanyCode: row.parentCompanyCode ? row.parentCompanyCode : row.companyCode ? row.companyCode : ''
      }
      this.updateNewDialogVisible = true;
    },
    // 获取营业部下拉菜单
    queryBusOptions() {
      queryBusinessDepartmentOptions().then(res => {
        if (res.data.success === true) {
          this.busDepOptions = res.data.result;
        }
      })
    },
    // 清空
    clearClick() {
      this.queryList.companyCode = '';
      this.queryList.zhName = '';
      this.queryList.orgId = '';
      this.queryList.pageSize = 10;
      this.queryClick();
    },
    // 编辑
    editClick(id, seriakey) {
      const _self = this
      const param = {
        id: id
      }
      // 获取当前公司信息
      getBusDepById(param).then(res => {
        if (res.data.success) {
          _self.companyParam = res.data.result;
          if (_self.companyParam.registerCapital !== null && _self.companyParam.registerCapital !== '') {
            _self.companyParam.registerCapital = _self.companyParam.registerCapital.toString();
            let arr1 = this.companyParam.registerCapital.split('.');
            let tmp1 = arr1[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr1[1] !== undefined) {
              this.companyParam.registerCapital = tmp1 + '.' + arr1[1];
            } else {
              this.companyParam.registerCapital = tmp1
            }
          }
          if (_self.companyParam.issuePrice !== null && _self.companyParam.issuePrice !== '') {
            _self.companyParam.issuePrice = _self.companyParam.issuePrice.toString();
            let arr2 = this.companyParam.issuePrice.split('.');
            let tmp2 = arr2[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr2[1] !== undefined) {
              this.companyParam.issuePrice = tmp2 + '.' + arr2[1];
            } else {
              this.companyParam.issuePrice = tmp2
            }
          }
          if (_self.companyParam.capitalStockA !== null && _self.companyParam.capitalStockA !== '') {
            _self.companyParam.capitalStockA = _self.companyParam.capitalStockA.toString();
            let arr3 = this.companyParam.capitalStockA.split('.');
            let tmp3 = arr3[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr3[1] !== undefined) {
              this.companyParam.capitalStockA = tmp3 + '.' + arr3[1];
            } else {
              this.companyParam.capitalStockA = tmp3
            }
          }
          if (_self.companyParam.capitalStockB !== null && _self.companyParam.capitalStockB !== '') {
            _self.companyParam.capitalStockB = _self.companyParam.capitalStockB.toString();
            let arr4 = this.companyParam.capitalStockB.split('.');
            let tmp4 = arr4[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr4[1] !== undefined) {
              this.companyParam.capitalStockB = tmp4 + '.' + arr4[1];
            } else {
              this.companyParam.capitalStockB = tmp4
            }
          }
          if (_self.companyParam.capitalStockH !== null && _self.companyParam.capitalStockH !== '') {
            _self.companyParam.capitalStockH = _self.companyParam.capitalStockH.toString();
            let arr5 = this.companyParam.capitalStockH.split('.');
            let tmp5 = arr5[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr5[1] !== undefined) {
              this.companyParam.capitalStockH = tmp5 + '.' + arr5[1];
            } else {
              this.companyParam.capitalStockH = tmp5
            }
          }
          if (_self.companyParam.capitalStockTotal !== null && _self.companyParam.capitalStockTotal !== '') {
            _self.companyParam.capitalStockTotal = _self.companyParam.capitalStockTotal.toString();
            let arr6 = this.companyParam.capitalStockTotal.split('.');
            let tmp6 = arr6[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
            if (arr6[1] !== undefined) {
              this.companyParam.capitalStockTotal = tmp6 + '.' + arr6[1];
            } else {
              this.companyParam.capitalStockTotal = tmp6
            }
          }
          if (_self.companyParam.organisationNo !== null && _self.companyParam.organisationNo !== '') {
            _self.companyParam.organisationNo = _self.companyParam.organisationNo.split(',')
          }
          if (_self.companyParam.parentId !== null && _self.companyParam.parentId !== '') {
            _self.updateNewDialogVisible = true;
          } else {
            _self.updateDialogVisible = true;
          }
          _self.companyParam.seriakey = seriakey;
        } else {
          _self.$message({type: 'error', message: res.data.errorMsg})
        }
      })
    },
    // 删除
    deleteClick(id, seriakey, companyCode) {
      // 确认删除弹出框
      this.$confirm('是否确定进行删除操作？', '确定删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete-confirm-button',
        cancelButtonClass: 'delete-cancel-button',
        customClass: 'delete-confirm-custom'
      }).then(() => { // 确定删除
        const param = {
          id: id,
          seriakey: seriakey,
          companyCode: companyCode
        }
        const _self = this
        deleteCompanyById(param).then(response => {
          if (response.data.success === true) {
            _self.$message({
              type: response.data.result === '删除成功' ? 'info' : 'error',
              message: response.data.result
            })
            _self.queryTableDataInit()
          } else {
            _self.$message({
              type: 'error',
              message: "删除失败"
            })
          }
        })
      }).catch(() => {
      })
    },
    // 新增联系人
    addContact() {
      this.companyParam.contacts.push({
        contactName: null,
        contactPhone: null,
        contactEmail: null,
        contactSystem: null
      });
    },
    // 删除联系人
    deleteContact(item) {
      let index = this.companyParam.contacts.indexOf(item)
      if (index !== -1) {
        this.companyParam.contacts.splice(index, 1);
      }
    },
    // 验证联系人
    validateContacts() {
      let list = this.companyParam.contacts;
      let str = '';
      if (list === undefined || list === null || list.length === 0) {
        if (this.companyParam.parentId === '' || this.companyParam.parentId === null) {
          str = "请填写联系人信息";
        }
      } else {
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          // 验证子系统
          if (item.contactSystem === undefined || item.contactSystem === null || item.contactSystem.trim() === '') {
            str = "请选择子系统";
            break;
          }
          // 回购-联系人、邮箱必填
          if (item.contactSystem === 'HGGL') {
            // 联系人姓名
            if (item.contactName === undefined || item.contactName === null || item.contactName.trim() === '') {
              str = "请填写回购管理系统联系人姓名";
              break;
            } else {
              if (item.contactName.length > 9) {
                str = "回购管理系统联系人姓名不可超过9个字符";
                break;
              }
            }
            // 联系人邮箱
            if (item.contactEmail === undefined || item.contactEmail === null || item.contactEmail.trim() === '') {
              str = "请填写回购管理系统联系人邮箱";
              break;
            } else {
              if (!_validateEmail(item.contactEmail)) {
                str = "回购管理系统联系人邮箱格式不正确";
                break;
              }
            }
            // 联系人电话如果有校验格式
            if (item.contactPhone !== undefined && item.contactPhone !== null && item.contactPhone.trim() !== '') {
              if (!_validatePhone(item.contactPhone)) {
                str = "回购管理系统联系人电话格式不正确";
                break;
              }
            }
          }
          // 激励-联系人、电话必填
          if (item.contactSystem === 'GQJL') {
            // 联系人姓名
            if (item.contactName === undefined || item.contactName === null || item.contactName.trim() === '') {
              str = "请填写股权激励系统联系人姓名";
              break;
            } else {
              if (item.contactName.length > 9) {
                str = "股权激励系统联系人姓名不可超过9个字符";
                break;
              }
            }
            // 联系人电话
            if (item.contactPhone === undefined || item.contactPhone === null || item.contactPhone.trim() === '') {
              str = "请填写股权激励系统联系人电话";
              break;
            } else {
              if (!_validatePhone(item.contactPhone)) {
                str = "股权激励系统联系人电话格式不正确";
                break;
              }
            }
            // 联系人邮箱,如果有验证格式
            if (item.contactEmail !== undefined && item.contactEmail !== null && item.contactEmail.trim() !== '') {
              if (!_validateEmail(item.contactEmail)) {
                str = "股权激励系统联系人邮箱格式不正确";
                break;
              }
            }
          }
          // 合规-联系人、电话、邮箱必填
          if (item.contactSystem === 'TDGF') {
            // 联系人姓名
            if (item.contactName === undefined || item.contactName === null || item.contactName.trim() === '') {
              str = "请填写合规交易系统联系人姓名";
              break;
            } else {
              if (item.contactName.length > 9) {
                str = "合规交易系统联系人姓名不可超过9个字符";
                break;
              }
            }
            // 联系人电话
            if (item.contactPhone === undefined || item.contactPhone === null || item.contactPhone.trim() === '') {
              str = "请填写合规交易系统联系人电话";
              break;
            } else {
              if (!_validatePhone(item.contactPhone)) {
                str = "合规交易系统联系人电话格式不正确";
                break;
              }
            }
            // 联系人邮箱
            if (item.contactEmail === undefined || item.contactEmail === null || item.contactEmail.trim() === '') {
              str = "请填写合规交易系统联系人邮箱";
              break;
            } else {
              if (!_validateEmail(item.contactEmail)) {
                str = "合规交易系统联系人邮箱格式不正确";
                break;
              }
            }
          }
        }
      }
      return str;
    },
    validateContactsCount() {
      let GQJLCount = 0;
      let TDGFCount = 0;
      let HGGLCount = 0;
      this.companyParam.contacts.forEach(item => {
        if (item.contactSystem === 'GQJL') GQJLCount++
        if (item.contactSystem === 'TDGF') TDGFCount++
        if (item.contactSystem === 'HGGL') HGGLCount++
      })
      if (HGGLCount === 0) {
        this.$message({type: 'error', message: "各子系统均需要设置联系人,回购管理子系统暂没有设置联系人"})
        return false;
      }
      if (HGGLCount > 1) {
        this.$message({type: 'error', message: "回购管理系统只能有一位联系人"})
        return false;
      }
      if (GQJLCount === 0) {
        this.$message({type: 'error', message: "各子系统均需要设置联系人,股权激励子系统暂没有设置联系人"})
        return false;
      }
      if (GQJLCount > 1) {
        this.$message({type: 'error', message: "股权激励系统只能有一位联系人"})
        return false;
      }
      if (this.companyParam.id === null || this.companyParam.id === '') {
        if (TDGFCount === 0) {
          this.$message({type: 'error', message: "各子系统均需要设置联系人,合规交易子系统暂没有设置联系人"})
          return false;
        }
        if (TDGFCount > 1) {
          this.$message({type: 'error', message: "合规交易系统只能有一位联系人"})
          return false;
        }
      }
      if (this.companyParam.id !== null && this.companyParam.id !== '') {
        if (TDGFCount === 0) {
          this.$message({type: 'error', message: "各子系统均需要设置联系人,合规交易子系统暂没有设置联系人"})
          return false;
        }
      }
      return true;
    },
    // 保存
    async submitForm(formName) {
      // 查询是否是刷新过的代码
      const checkResult = await checkCompanyCodeConvert({id: this.companyParam.id, companyCode: this.companyParam.companyCode}).then(res => {
        return res.data.result;
      })
      if (checkResult) {
        showMessage('error', checkResult);
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const _self = this;
          // 验证联系人
          let str = this.validateContacts();
          if (str !== '') {
            _self.$message({type: 'warning', message: str})
            return false;
          }
          // 验证联系人数量
          if (this.companyParam.parentId === '' || this.companyParam.parentId === null) {
            let validateContactsCountFlag = this.validateContactsCount();
            if (!validateContactsCountFlag) {
              return false;
            }
          }
          // 处理千分符
          if (_self.companyParam.registerCapital) {
            _self.companyParam.registerCapital = _self.companyParam.registerCapital.replace(/,/g, '');
          }
          if (_self.companyParam.issuePrice) {
            _self.companyParam.issuePrice = _self.companyParam.issuePrice.replace(/,/g, '');
          }
          if (_self.companyParam.capitalStockA) {
            _self.companyParam.capitalStockA = _self.companyParam.capitalStockA.replace(/,/g, '');
          }
          if (_self.companyParam.capitalStockB) {
            _self.companyParam.capitalStockB = _self.companyParam.capitalStockB.replace(/,/g, '');
          }
          if (_self.companyParam.capitalStockH) {
            _self.companyParam.capitalStockH = _self.companyParam.capitalStockH.replace(/,/g, '');
          }
          if (_self.companyParam.capitalStockTotal) {
            _self.companyParam.capitalStockTotal = _self.companyParam.capitalStockTotal.replace(/,/g, '');
          }
          if (_self.companyParam.organisationNo && _self.companyParam.organisationNo.length > 0) {
            _self.companyParam.organisationNo = _self.companyParam.organisationNo.toString()
          }
          // 检查公司代码
          checkBusDepCode({id: _self.companyParam.id, companyCode: _self.companyParam.companyCode}).then(res => {
            if (res.data.success) {
              if (res.data.result !== 0) {
                _self.$message({type: 'error', message: "上市公司代码已存在！"})
              } else {
                checkSocialUnifiedCreditCode({
                  id: _self.companyParam.id,
                  socialUnifiedCreditCode: _self.companyParam.socialUnifiedCreditCode
                }).then(res => {
                  if (res.data.result !== 0) {
                    _self.$message({type: 'error', message: "统一社会信用代码已存在！"})
                  } else {
                    // 保存或修改
                    createOrUpdate(_self.companyParam).then(response => {
                      if (response.data.success === true) {
                        _self.$message({
                          type: response.data.result === '登录用户参数异常!' ? 'error' : 'info',
                          message: response.data.result
                        })
                        _self.updateDialogVisible = false;
                        _self.updateNewDialogVisible = false;
                        _self.queryTableDataInit();
                      } else {
                        _self.$message({
                          type: 'error',
                          message: response.data.errorMsg
                        })
                      }
                    })
                  }
                })
              }
            } else {
              _self.$message({type: 'error', message: "操作失败！"})
            }
          })
        }
      })
    },
    cancel() {
      this.updateDialogVisible = false;
      this.updateNewDialogVisible = false;
    },
    getCompanyInfoByCode() {
      const companyCode = this.companyParam.companyCode;
      if (companyCode.length === 6) {
        const param = {
          companyCode: this.companyParam.companyCode
        }
        if (companyCode) {
          getCompanyInfoByCode(param).then(res => {
            if (res.data.success === true) {
              this.companyParam.marketStockType = res.data.result.marketStockType;
              this.companyParam.zhName = res.data.result.zhName;
              this.companyParam.zhSortName = res.data.result.zhSortName;
              this.companyParam.companyStockName = res.data.result.zhSortName;
              this.companyParam.socialUnifiedCreditCode = res.data.result.socialUnifiedCreditCode;
              // this.companyParam.registerDate = res.data.result.registerDate;
              // this.companyParam.registerAddress = res.data.result.registerAddress;
              // this.companyParam.registerCapital = res.data.result.registerCapital;
              // this.companyParam.companyMail = res.data.result.companyMail;
              // this.companyParam.listingDate = res.data.result.listingDate;
              // this.companyParam.corporate = res.data.result.corporate;
              // if (this.companyParam.registerCapital !== null && this.companyParam.registerCapital !== '') {
              //   this.companyParam.registerCapital = this.companyParam.registerCapital.toString();
              //   let arr1 = this.companyParam.registerCapital.split('.');
              //   let tmp1 = arr1[0].replace(/,/g, "").replace(/，/g, "").replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
              //   if (arr1[1] !== undefined) {
              //     this.companyParam.registerCapital = tmp1 + '.' + arr1[1];
              //   } else {
              //     this.companyParam.registerCapital = tmp1
              //   }
              // }
            } else {
              this.$message({
                type: 'error',
                message: "获取信息失败！"
              })
            }
          })
        }
      }
      this.$refs.companyParam.clearValidate();
    },
    checkButtonStatus() {
      selectButtonByName({moduleName: 'saCompany', zhName: '上市公司管理', buttonName: '导入上市公司'}).then(res => {
        if (res.data.success && res.data.result !== null) {
          if (res.data.result.status === '1') {
            this.importSaCompanyStatus = true;
          } else {
            this.importSaCompanyStatus = false;
          }
        }
      })
    },
    contactPhoneCheck(row) {
      if (!_validatePhone(row.contactPhone)) {
        this.$message({type: 'warning', message: '联系人电话格式不正确'})
      }
    },
    contactEmailCheck(row) {
      if (!_validateEmail(row.contactEmail)) {
        this.$message({type: 'warning', message: '联系人邮箱格式不正确'})
      }
    },

  }
}
</script>

<style lang="scss">
@import '../../styles/table.css';
@import '../../styles/page-title.scss';

.selectText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select_max_length input {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-btn {
  border: none;
  padding: 0;
  width: 20px !important;
}

.containerUpdate {
  .el-form {
    /*border: 0px solid #eeeeee;*/
  }

  .el-row {
    margin: 0;
    height: auto !important;
  }

  .el-form .el-button--default {
    width: auto !important;
  }

  padding: 12px 0;
  background: #F7F8FA;

  .containerUpdate-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    width: 1300px;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;

    .is-plain {
      background: #F4ECEC;
      color: #CF1A1C;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      border: none;

    }

    .row-form {
      .el-input__inner {
        border-radius: 35px;
        background: #F6F7F9;
        border: 1px solid #F6F7F9;
        height: 32px;
        line-height: 32px;

      }
    }

    .custom-header-class {

      th {
        color: #777 !important;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background: #F6F7F9;
        padding: 10px 0px !important;

      }
    }

    .iconfont {
      color: #000000;
    }
  }
}

</style>
<style>
.el-dialog__wrapper {
  position: fixed !important;
  z-index: 2012;
}

.NewlyCompany .el-row {
  margin-bottom: 10px;
}
</style>
