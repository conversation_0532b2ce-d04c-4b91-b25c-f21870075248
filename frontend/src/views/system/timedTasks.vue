<template>
  <div class="container" style="margin: 0 10px;padding: 20px">
    <div class="container-div" style="">
      <div class="main-header">
        <i></i>
        <span>定时任务管理</span>
      </div>
      <el-row class="m-c">
        <el-tabs v-model="activeTab" @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in tabList" :key="index" :name="item.sysCode" :label="item.sysName"></el-tab-pane>
        </el-tabs>
        <el-row class="m-c" v-show="false">
          <el-button @click="stockInfoMergeFirst">初始化交易信息</el-button>
        </el-row>
        <el-row class="m-c">
          <el-table
              :data="tableData"
              class="full"
              style="width: 100%;margin-bottom: 20px;color: #000"
              :font-size="14"
              header-row-class-name	="custom-header-class-list"
          >
            <el-table-column
                type="index"
                width="60px"
                align="center"
                label="序号">
            </el-table-column>
            <el-table-column label="任务名称" prop="info" align="center" min-width="8%">
              <template slot-scope="scope">
                <span v-if="activeTab === 'PLATFORM'">{{scope.row.zhName}}</span>
                <span v-else>{{JSON.parse(scope.row.info).zhName}}</span>
              </template>
            </el-table-column>
            <el-table-column label="最新一次定时状态" prop="isSuccess" align="center" min-width="8%">
              <template slot-scope="scope">
                <span v-if="scope.row.isSuccess === '0'">失败</span>
                <span v-else-if="scope.row.isSuccess === '1'">成功</span>
                <span v-else-if="scope.row.isSuccess === '2'">执行中</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column label="最新一次执行时间" prop="createTime" align="center" min-width="10%">
              <template slot-scope="scope">
                <span>{{formatDate(scope.row.createTime)}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="10%">
              <template slot-scope="scope">
                <i class="iconfont ic-details" title="查看执行记录" @click="openTimedTasksDetail(scope.row)"></i>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
              style="text-align: center;"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="query.startRow"
              :page-sizes="[10, 20, 50, 200]"
              :page-size="query.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
          </el-pagination>
        </el-row>
      </el-row>
    </div>
    <el-dialog
        :title="timedTasksDetailTitle"
        :visible.sync="timedTasksDetailFlag"
        v-if="timedTasksDetailFlag"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="search"
        width="90%">
      <timed-tasks-detail :timedTasksDetailName="timedTasksDetailName"></timed-tasks-detail>
    </el-dialog>
  </div>
</template>

<script>

import common from "@/mixins/common";
import papers from "@/components/Pagination/papers.vue";
import {_getOpenedSystems, stockInfoMergeFirst} from "../../api/common-api";
import {_getTasksGroupList} from "../../api/system/timed-tasks";
import TimedTasksDetail from "./timedTasksDetail.vue";

export default {
  components: {TimedTasksDetail, papers },
  mixins: [common],
  data () {
    return {
      tabList: [],
      activeTab: 'default',
      query: {
        // 页码
        startRow: 1,
        // 每页显示多少条数据
        pageSize: 10
      },
      tableData: [],
      total: 0,
      timedTasksDetailName: '',
      timedTasksDetailTitle: '',
      timedTasksDetailFlag: false,
    }
  },
  mounted () {
    this.getTabList();
  },
  methods: {
    // 初始化交易信息,隐藏按钮
    async stockInfoMergeFirst() {
      stockInfoMergeFirst()
    },
    // 获取tab列表
    getTabList () {
      _getOpenedSystems({}).then(res => {
        if (res.data.success) {
          if (res.data.result.length > 0) {
            this.tabList = [];
            let tab = {
              sysCode: "PLATFORM", sysName: "平台"
            }
            this.tabList.push(tab);
            if (this.activeTab === 'default') {
              this.activeTab = "PLATFORM";
            }
            this.search();
          }
        }
      })
    },
    handleClick () {
      this.search();
    },
    search () {
      let query = { ...this.query };
      query.sysCode = this.activeTab;
      _getTasksGroupList(query).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.tableData;
          this.total = res.data.result.total;
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    formatDate (timestamp) {
      if (!timestamp) {
        return '--';
      }
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 1
      this.search();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.search();
    },
    getValue (val) {
      if (val === null || val === 'null' || val === undefined || val === 'undefined') {
        val = '--'
      }
      return val
    },
    openTimedTasksDetail(row) {
      this.timedTasksDetailName = row.name;
      this.timedTasksDetailTitle = row.zhName;
      this.timedTasksDetailFlag = true;
    }
  }
}

</script>

<style scoped lang="scss">
.container {
  .container-div{
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
}

.main-header {
  position: relative;
  margin: 0;
  // padding-left: 8px;
  // border-left: 4px solid #999999;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: $--el-color-text-primary;
  line-height: 22px;
  span {
    //算上边线本身4px一共12px
    padding-left: 12px;
  }
  //平行四边形边线
  i {
    position: absolute;
    top: 2px;
    display: inline-block;
    transform: skewY(-15deg);
    height: 18px;
    width: 4px;
    background-color: $--el-color-primary;
  }
}

</style>
<style lang="scss">
.custom-header-class-list{

  th{
    color: #777!important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    background: #F6F7F9;
    padding: 10px 0px !important;

  }
}

</style>