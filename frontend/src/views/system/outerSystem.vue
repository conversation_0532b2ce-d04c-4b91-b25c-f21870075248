<template>
  <div class="outer-system">
    <div class="outer-system-div" style="">
      <div class="page-title">
        <i></i>
        <span>接入系统管理</span>
      </div>
  <!--    <el-row style="margin-top: 10px">-->
  <!--      <el-col span="24">-->
  <!--        <el-link :underline="false" type="primary" @click="edit(null)">-->
  <!--          <i class="iconfont icon-gonggaoicon_tianjia"></i>-->
  <!--          <span class="btn">新增</span>-->
  <!--        </el-link>-->
  <!--      </el-col>-->
  <!--    </el-row>-->
      <el-row style="margin-top: 10px">
        <el-col span="24" style="text-align: right">
          <el-button type="primary" @click="getTableData" icon="el-icon-refresh" round>刷新</el-button>
        </el-col>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-col span="24">
          <el-table
              :data="tableData"
              style="width: 100%;margin-bottom: 20px;color: #000"
              :font-size="14"
              header-row-class-name	="custom-header-class-list"
          >
            <el-table-column prop="sysCode" align="center" label="系统代码"></el-table-column>
            <el-table-column prop="sysName" align="center" label="系统名称"></el-table-column>
            <el-table-column prop="status" align="center" label="系统锁定状态">
              <template slot-scope="scope">
                <span v-if="scope.row.status === '0'">已锁定</span>
                <span v-else>未锁定</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" >
              <template slot-scope="scope">
                <i class="el-icon-lock custom-icon-button" title="锁定" v-if="scope.row && scope.row.status === '1'" @click="lockStatusClick(scope.row)"></i>
                <i class="el-icon-unlock custom-icon-button" title="解锁" v-else @click="lockStatusClick(scope.row)"></i>
                <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="edit(scope.row.id)"></i>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-dialog
          v-if="showModal"
          :visible.sync="showModal"
          :title="id == null ?'新增' :'编辑'"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          draggable="true"
          width="50%">
        <el-row gutter="10">
          <el-col span="12">
            <el-input v-model="modalForm.sysCode" prefix-text="系统代码" placeholder="请输入系统代码" :disabled="id != null"></el-input>
          </el-col>
          <el-col span="12">
            <el-input v-model="modalForm.sysName" prefix-text="系统名称" placeholder="请输入系统名称"></el-input>
          </el-col>
        </el-row>
        <el-row style="margin-top: 10px">
          <div role="alert" class="el-alert el-alert--success is-light is-description">
            <div class="el-alert__content">
              <span class="el-alert__title is-bold">配置介绍</span>
              <p class="el-alert__description" v-for="(item, index) in this.apiOptions" :key="index">
                <el-tag size="large" style="margin-right: 6px">{{ item.label }}</el-tag>{{item.desc}}
              </p>
              <p class="el-alert__description">
                路径和接口可包含动态变量，标识符为
                <el-tag type="danger">{{ this.constant['DYNAMIC_PARAM_SIGN'] }}</el-tag>
              </p>
              <p class="el-alert__description">
                目前支持的动态变量
                <el-tag type="warning" style="margin-right: 6px" v-for="(item, index) in this.constant['DYNAMIC_PARAM_CONTENT']" :key="index">{{ item }}</el-tag>
              </p>
            </div>
          </div>
        </el-row>
        <el-row style="margin-top: 10px" v-for="(item, index) in this.apiOptions" :key="index">
          <el-input v-model="sysConfigDetail[item.value]" :prefix-text="item.label" :placeholder="'请输入' + item.label"></el-input>
        </el-row>
        <span slot="footer">
          <el-button @click="close" size="small">取 消</el-button>
          <el-button type="primary" @click="confirm" size="small">确 定</el-button>
        </span>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import { _queryDetailData, _queryTableData, _saveConfigData } from "@/api/system/outer-system-api";
import { _getSystemConfigConstant } from "@/api/common-api";
import common from "@/mixins/common";
import {lockStatusClick} from "../../api/system/outer-system-api";

export default {
  mixins: [common],
  data () {
    return {
      curEnv: '',
      constant: {},
      apiOptions: [],
      tableData: [],
      id: null,
      showModal: false,
      modalForm: {
        sysCode: '',
        sysName: '',
        sysConfig: ''
      },
      sysConfigDetail: {},
      config: {}
    }
  },
  created () {
    this.curEnv = this.getCurrentBackGroundEnv();
    console.log('当前系统环境', this.curEnv)
    _getSystemConfigConstant().then(res => {
      if (res.data.success) {
        this.constant = res.data.result;
        this.apiOptions = this.constant['API_LIST'];
      }
    })
    this.getTableData();
  },
  methods: {
    getTableData () {
      _queryTableData({}).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result;
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    edit (id) {
      _queryDetailData({ id: id }).then(res => {
        if (res.data.success) {
          this.modalForm = res.data.result;
          // 配置解析
          this.explainConfigJSON();
          this.id = id;
          this.showModal = true;
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    close () {
      this.showModal = false;
      this.id = null;
      this.modalForm = null;
      this.config = {};
    },
    confirm () {
      let sysConfig = this.getSysConfig();
      let form = { ...this.modalForm };
      form.sysConfig = sysConfig;
      _saveConfigData(form).then(res => {
        if (res.data.success) {
          this.$message.info("保存成功");
          this.getTableData();
          this.close();
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    explainConfigJSON () {
      let data = { ...this.modalForm };
      if (this.getValue(data.sysConfig) !== '') {
        let config = JSON.parse(data.sysConfig);
        this.config = config[this.curEnv];
        if (this.getValue(this.config) !== '') {
          let sysConfigDetail = {}
          this.apiOptions.forEach(item => {
            if (this.getValue(this.config[item.value]) !== '') {
              sysConfigDetail[item.value] = this.config[item.value];
            }
          });
          this.sysConfigDetail = sysConfigDetail;
        }
      }
    },
    getSysConfig () {
      // 获取原当前环境的配置
      let data = { ...this.modalForm };
      let sysConfigStr = this.getValue(data.sysConfig);
      // 删除原当前环境的配置
      if (sysConfigStr !== '') {
        let config = JSON.parse(sysConfigStr);
        delete config[this.curEnv];
        // 增加当前环境配置
        config[this.curEnv] = this.sysConfigDetail;
        sysConfigStr = JSON.stringify(config);
      } else {
        let config = {}
        // 增加当前环境配置
        config[this.curEnv] = this.sysConfigDetail;
        sysConfigStr = JSON.stringify(config);
      }
      return sysConfigStr;
    },
    lockStatusClick(row) {
      if (row.status === "1") {
        this.$confirm('锁定后该不可进入该系统,是否继续?', '确定锁定', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          confirmButtonClass: 'delete-confirm-button',
          cancelButtonClass: 'delete-cancel-button',
          customClass: 'delete-confirm-custom'
        }).then(() => {
          row.status = "0";
          lockStatusClick(row).then((res) => {
            if (res.data.result) {
              this.$message.info(res.data.errorMsg);
            } else {
              this.$message.error(res.data.errorMsg);
            }
            this.getTableData();
          });
        })
      } else if (row.status === "0") {
        this.$confirm('解锁后可正常进入该系统,是否继续?', '确定解锁', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          confirmButtonClass: 'delete-confirm-button',
          cancelButtonClass: 'delete-cancel-button',
          customClass: 'delete-confirm-custom'
        }).then(() => {
          row.status = "1";
          lockStatusClick(row).then((res) => {
            if (res.data.result) {
              this.$message.info(res.data.errorMsg);
            } else {
              this.$message.error(res.data.errorMsg);
            }
            this.getTableData();
          });
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/page-title.scss';
.outer-system {
  padding: 20px;
  .outer-system-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
}
</style>
<style lang="scss">
.custom-header-class-list{

  th{
    color: #777!important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    background: #F6F7F9;
    padding: 10px 0px !important;

  }
}
</style>
