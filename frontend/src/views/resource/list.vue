<template>
    <div class="user-list">
        <div class="main-header">
            <i></i>
            <span>资源信息</span>
        </div>
        <div class="m-c">
            <el-card class="box-card box-card-search" shadow="never" :body-style="{ border: none }">
                <el-row :gutter="10">
                    <el-col :span="3">
                        <el-input size='small full' placeholder="资源名称" v-model="query.resourceName"></el-input>
                    </el-col>
                    <el-col :span="3">
                        <el-input size='small full' placeholder="ID" v-model="query.id"></el-input>
                    </el-col>
                    <!--<el-col :span="3">-->
                    <!--<el-input placeholder="所属模块" v-model="query.condition.menuName"></el-input>-->
                    <!--</el-col>-->
                    <el-col :span="3">
                        <el-input size='small full' placeholder="资源url" v-model="query.resourceUrl"></el-input>
                    </el-col>
                    <el-col :span="4" class="text-left box-card-search-btns">
                        <el-button @click="search" type="primary" size="small">查询</el-button>
                        <el-button @click="clearClick" size="small">清空条件</el-button>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-col :span="12" class="text-left">
                    <el-link
                            :underline="false"
                            type="primary"
                            @click="openAddResourceModal(null)"
                    >
                        <i class="iconfont icon-gonggaoicon_tianjia"></i>
                        <span class="btn">新增</span>
                    </el-link>
                    <el-link :underline="false" type="primary" @click="batchDelete">
                        <i class="iconfont ic-trash"></i>
                        <span class="btn">批量删除所选资源</span>
                    </el-link>
                </el-col>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-row>
                    <el-col :span="12">
                        资源列表
                    </el-col>
                </el-row>
                <el-table
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        class="m-t-20 full"
                        default-expand-all>
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="ID"
                            prop="id"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="父级ID"
                            prop="pId"
                            width="100">
                    </el-table-column>
                    <el-table-column
                            align="left"
                            label="资源名称"
                            prop="resourceName"
                    >
                    </el-table-column>
                    <el-table-column
                            align="left"
                            label="资源url"
                            prop="resourceUrl"
                    >
                    </el-table-column>
                    <el-table-column
                            align="left"
                            label="资源描述"
                            prop="resourceDes"
                    >
                    </el-table-column>
                    <!--<el-table-column-->
                    <!--align="center"-->
                    <!--label="所属模块"-->
                    <!--prop="menuName">-->
                    <!--</el-table-column>-->
                    <el-table-column
                            align="center"
                            label="操作"
                            width="120px">
                        <template slot-scope="scope">
                          <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="openAddResourceModal(scope.row)"></i>
                          <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteClick(scope.row.id,scope.$index)"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <papers ref="paper" @searchTable="paperSearch" :total="total" :size="10"></papers>
            </el-card>
        </div>
        <add-resource :isShow="showModal" :updateData="currentRow" @addResourceModalClose="addResourceModalClose"></add-resource>
    </div>
</template>
<script>
import { showMessage, showConfirm } from 'stock-vue-plugin/lib/utils/message'
import addResource from '../../components/Modal/addResource'
import { _queryResourceList, _deleteResource } from "@/api/manage-api";
import papers from "@/components/Pagination/papers.vue";

export default {
  components: { addResource, papers },
  data () {
    return {
      // 选中的数据
      multipleSelection: [],
      // 是否打开新增菜单弹出框
      showModal: false,
      // 是否打开授权弹出框
      showMenuList: false,
      // 查询需要的参数
      query: {
        // 菜单名称
        resourceName: "",
        // 授权标识
        id: "",
        // 所属模块
        menuName: "",
        // 资源url
        resourceUrl: '',
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 10
      },
      // 当前编辑行数据
      currentRow: {},
      // table 列表数据
      tableData: []
    }
  },
  mounted () {
    // 获取菜单列表
    this.getResourceData();
  },
  methods: {
    /* 查询
             * */
    search () {
      this.getResourceData();
    },
    paperSearch () {
      let form = this.$refs.paper.submitData;
      this.query.startRow = form.startRow;
      this.query.pageSize = form.pageSize;
      this.getResourceList();
    },

    clearClick () {
      Object.assign(this.query, this.$options.data().query); // 重置数据
      this.getResourceData(); // 重新获取数据
    },

    /* 获取菜单列表数据
              * */
    getResourceData () {
      let form = this.$refs.paper.submitData;
      this.query.startRow = 0;
      this.query.pageSize = form.pageSize;
      this.getResourceList();
    },
    /* 获取菜单列表数据
             * */
    getResourceList () {
      _queryResourceList(this.query).then(res => {
        if (res.status == '200') {
          this.tableData = res.data.tableData;
          this.total = res.data.total;
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    // 批量删除
    batchDelete () {
      console.log(this.multipleSelection)
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage('info', this.$messageText['deleteSelectNone']);
        return;
      }
      let index = -1;
      // 当前只选中了一条数据，查找当前数据的索引
      if (this.multipleSelection.length === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === this.multipleSelection[0].id) {
            index = i;
          }
        }
      }
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: this.multipleSelection,
        index: index
      });
    },
    /* table checkbox 选中事件
                                    * val:选中得数据
                                    * */
    handleSelectionChange (val) {
      console.log(this.multipleSelection);
      this.multipleSelection = val.map((i) => {
        return i.id
      });
    },
    deleteClick (id, index) {
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: [id],
        index: index
      });
    },
    deleteClickData (param) {
      _deleteResource({ idList: param.idList.join(',') }).then(res => {
        if (res.data.success) {
          // // 只删除一条数据并且索引大于-1
          // if (param.idList.length === 1 && param.index >= 0) {
          //     // 从列表中删除数据
          //     this.tableData.splice(param.index, 1);
          // } else {
          // 重新获取数据
          this.getResourceData();
          // }
          // 提示删除成功消息
          showMessage('info', this.$messageText['deleteSuccess']);
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 新增角色
             * */
    openAddResourceModal (item) {
      this.currentRow = item;
      // 打开编辑菜单弹出框
      this.showModal = true;
    },
    /* 关闭新建用户弹出框
                              * flag：是取消还是确定操作
                              * */
    addResourceModalClose (flag) {
      // 关闭弹出框
      this.showModal = false;
      // 确定操作
      if (flag) {
        // 刷新列表
        this.getResourceData();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
    .user-list {
        padding: 20px;
        .user-list__avatar {
            width: 50px;
            height: 50px;
        }
    }

    .main-header {
        position: relative;
        margin: 0;
        // padding-left: 8px;
        // border-left: 4px solid #999999;
        font-family: PingFang SC;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        color: $--el-color-text-primary;
        line-height: 22px;
        span {
            //算上边线本身4px一共12px
            padding-left: 12px;
        }
        //平行四边形边线
        i {
            position: absolute;
            top: 2px;
            display: inline-block;
            transform: skewY(-15deg);
            height: 18px;
            width: 4px;
            background-color: $--el-color-primary;
        }
    }

    a.btn,
    span.btn {
        color: #333333;
        i {
            font-size: 16px;
        }
        .greenColor {
            color: #0fa958 !important;
        }
    }

    .el-card {
        border: none;
    }

    .box-card-btns {
        a {
            margin-right: 13px;
            font-family: PingFang SC;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            i {
                color: #1990fe;
            }
            span {
                margin-left: 0;
            }
        }
    }

    .box-card-search {
        .box-card-search-btns {
            & > :first-child {
                margin-left: 5px;
            }
        }
    }

    .el-table-name {
        display: inline-block;
        width: 40%;
        text-align: left;
    }

    .el-table-name-icon {
        width: 60%;
        text-align: right;
        .male {
            color: #76c3ff;
        }
        .female {
            color: #ff74b2;
        }
    }
</style>
<style lang="scss">
    .el-card__body, .el-main {
        padding: 0;
        text-align: left;
        line-height: 20px;
    }

</style>
