<template>
  <el-dialog
      :before-close="handleClose"
      :title="'设置用户'"
      :visible.sync="showing"
      append-to-body
      class="add-person"
      modal
      modal-append-to-body
      :close-on-click-modal="false"
      width="600px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户名" prop="userName" style="margin-bottom: 20px">
              <el-input :maxLength="10" placeholder="用户名" v-model="formInline.userName"></el-input>
            </el-form-item>
            <!--            <el-form-item label="密码" prop="newPassword" style="margin-bottom: 20px">
                          <el-input :maxLength="20" placeholder="密码" type="password" v-model="formInline.newPassword" show-password></el-input>
                        </el-form-item>
                        <el-form-item label="确认密码" prop="confirmPassword">
                          <el-input :maxLength="20" placeholder="确认密码" type="password" v-model="formInline.confirmPassword" show-password></el-input>
                        </el-form-item>-->
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import {
  _checkUserNameExist, _setUser
} from "@/api/person-api";

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data () {
    return {
      // 是否显示弹出框
      showing: false,
      // 设置用户需要的表单字段
      formInline: {
        // 用户名
        userName: '',
        // 用户id
        id: '',
        telephone: '',
        // 新密码
        newPassword: '',
        // 确认的密码
        confirmPassword: ''
      },
      // 表单验证规则
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (encodeURI(value).indexOf("%E") < 0) {
                  _checkUserNameExist(value).then(res => {
                    if (res.data.success) {
                      if (res.data.result) {
                        callback(new Error("用户名已存在"))
                      } else {
                        callback()
                      }
                    } else {
                      showMessage("error", res.data.errorMsg)
                    }
                  })
                } else {
                  callback(new Error("用户名不可包含中文"))
                }
              }
            },
            trigger: 'change'
          }
        ]
      }
    }
  },

  computed: {},
  methods: {
    /* 关闭弹出框
      * */
    handleClose () {
      this.closeModal(false);
    },
    /* 设置人员信息* */
    sureClick () {
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          if (this.formInline.id === '' || this.formInline.id === null) {
            showMessage("error", "未知错误，请重新刷新页面")
          } else {
            _setUser(this.formInline).then(res => {
              if (res.data.success) {
                showMessage("info", "设置成功");
                this.closeModal(false)
              } else {
                showMessage("error", res.data.errorMsg);
              }
            });
          }
        }
      });
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal (flag) {
      this.$emit('setUserClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
       *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData != null) {
          this.formInline.id = this.updateData.id;
          this.formInline.telephone = this.updateData.telephone;
          /* let autoPass = _createPassword();
           console.log(autoPass)
           this.formInline.newPassword = autoPass;
           this.formInline.confirmPassword = autoPass; */
          console.log(this.updateData)
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss">
.add-user {
}

.aa > > > .el-form-item__content {
  margin-left: 0px !important;
}
</style>
