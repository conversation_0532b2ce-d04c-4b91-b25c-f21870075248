<template>
  <div class="user-list">
    <div class="out-person-list-div" style="">
      <div class="main-header">
        <i></i>
        <span>外部人员信息</span>
      </div>
      <div class="m-c">
        <el-card
            class="box-card box-card-search"
            shadow="never"
            :body-style="{ border: none }"
        >
          <el-row :gutter="10" class="row-form-list">
            <el-col :span="3">
              <el-select
                  size="small"
                  class="full"
                  clearable
                  filterable
                  placeholder="所属上市公司"
                  v-model="query.orgId"
              >
                <el-option
                    :key="'org' + index"
                    :label="item.orgName"
                    :value="item.orgId"
                    v-for="(item, index) in orgList"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select
                  size="small"
                  class="full"
                  clearable
                  filterable
                  placeholder="锁定状态"
                  v-model="query.userLockStatus"
              >
                <el-option
                    :key="'org' + index"
                    :label="item.name"
                    :value="item.value"
                    v-for="(item, index) in lockStatusList"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input
                  size="small"
                  suffix-icon="el-icon-search"
                  placeholder="请输入姓名，账号"
                  v-model="query.searchString"
              ></el-input>
            </el-col>
            <el-col :span="4" class="text-left box-card-search-btns">
              <el-button @click="search" type="primary" size="small" round>查询</el-button>
              <el-button @click="clearClick" size="small" round>清空</el-button>
            </el-col>

            <el-col :span="10">

              <!--导入外部用户--- 临时-->
              <el-upload
                  style="display: inline-block"
                  :http-request="customUpload"
                  :multiple="false"
                  :show-file-list="false"
                  :before-upload="beforeUpload">
                <el-button v-if="importOutPersonStatus" round type="primary" size="small" style="margin-left: 10px;">导入外部用户</el-button>
              </el-upload>

              <el-button v-if="resetOutPersonPassword" round type="primary" size="small" @click="resetPassword" style="margin-left: 10px;">邮件通知(账号、密码、登录地址)</el-button>

            </el-col>

          </el-row>
        </el-card>
        <el-card class="box-card m-t-20 box-card-btns" shadow="never">
          <el-col :span="12" class="text-left">
            <el-link
                :underline="false"
                type="primary"
                @click="openEditPersonModal(null)"
            >
              <i class="iconfont icon-gonggaoicon_tianjia" style="color: #D6001D;"></i>
              <span class="btn">新增</span>
            </el-link>
            <!--            <el-link :underline="false" type="primary" @click="batchDelete">-->
            <!--              <i class="iconfont ic-trash" style="color: #D6001D;"></i>-->
            <!--              <span class="btn">批量删除所选人员</span>-->
            <!--            </el-link>-->
            <el-link :underline="false" type="primary" @click="exportUserInfo">
              <i class="iconfont ic-import" style="color: #D6001D;"></i>
              <span class="btn">导出</span>
            </el-link>
<!--            <el-link :underline="false" type="primary" @click="resetPassword">-->
<!--              <i class="iconfont ic-import" style="color: #D6001D;"></i>-->
<!--              <span class="btn">重置外部人员密码</span>-->
<!--            </el-link>-->

            <el-link :underline="false"
                     type="primary" @click="clickMergeButton">
              <i class="iconfont ic-related" style="color: #D6001D;"></i>
              <span class="btn">合并</span>
            </el-link>

          </el-col>
        </el-card>
        <el-card class="box-card m-t-20 box-card-btns" shadow="never">
          <el-row>
            <el-col :span="12"> 用户列表</el-col>
          </el-row>
          <el-table
              ref="multipleTable"
              :data="tableData"
              @selection-change="handleSelectionChange"
              class="m-t-20 full"
              style="width: 100%;margin-bottom: 20px;color: #000"
              :font-size="14"
              :row-key="(row) => row.id"
              header-row-class-name="custom-header-class-list"
          >
            <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
            <!--<el-table-column label="序号" width="50px" align="center">-->
            <!--<template scope="scope">-->
            <!--<span v-text="getIndex(scope.$index)"></span>-->
            <!--</template>-->
            <!--</el-table-column>-->
            <!--                    <el-table-column label="id" width="100px" align="center" prop="id">-->
            <!--                    </el-table-column>-->
            <el-table-column
                align="center"
                label="姓名"
                min-width="128px"
                sortable
                prop="personName"
            >
              <template slot-scope="scope">
                <span class="el-table-name-icon">
                  <i class="iconfont icon-ic-male male" v-if="scope.row.sex === '1'"> </i>
                  <i class="iconfont icon-ic-female female" v-else-if="scope.row.sex === '2'"></i>
                </span>
                <p class="el-table-name">
                  {{ scope.row.personName }}
                </p>
              </template>
            </el-table-column>
            <el-table-column align="center" label="用户名" prop="userName">
              <template slot-scope="scope">
                <span v-if="scope.row.userName">{{ scope.row.userName }}</span>
                <span v-else>
                    <el-link
                        :underline="false"
                        type="primary"
                        @click="setUserModal(scope.row)"
                    >
                    <span class="btn">设置用户</span>
              </el-link>
                </span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="手机号码"
                prop="telephone"
                width="128"
            >
              <template slot-scope="scope">
                <span>{{ hidePhoneNum(scope.row.telephone) }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="所属上市公司"
                sortable
                prop="orgNames"
                min-width="128px"
            >
            </el-table-column>
            <!--                    <el-table-column align="center" label="身份" prop="identityNames">-->
            <!--                    </el-table-column>-->
            <el-table-column
                align="center"
                label="来源"
                prop="source"
                width="160"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.source === 'HGGL'">回购管理</span>
                <span v-else-if="scope.row.source === 'GQJL'">股权激励</span>
                <span v-else-if="scope.row.source === 'TDGF'">合规交易</span>
                <span v-else>平台新增</span>
                <!--                <span v-if="scope.row.source === '1'">OA</span>-->
                <!--                <span v-else>新增</span>-->
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="用户锁定状态"
                prop="userLockStatus"
                width="116px"
            >
              <template slot-scope="scope">
                <span
                    v-if="
                    scope.row.userName != null && scope.row.userLockStatus === '1'
                  "
                >已锁定</span
                >
                <span v-else-if="scope.row.userName != null">未锁定</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="用户合并状态"
                width="116px">
              <template slot-scope="scope">
                <span v-if="scope.row.mergeStatus==='1'">已合并</span>
                <span v-else-if="scope.row.mergeStatus ==='2'">被合并</span>
                <span v-else-if="scope.row.mergeStatus ==='3'">不可合并</span>
                <span v-else>--</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="操作" min-width="156px">
              <template slot-scope="scope">
                <span v-if="scope.row.userName != null">
                  <i class="el-icon-lock custom-icon-button" title="锁定"
                     v-if="scope.row && scope.row.userLockStatus === '0'" @click="lockStatusClick(scope.row)">
                  </i>
                  <i class="el-icon-unlock custom-icon-button" title="解锁" v-else-if="scope.row.mergeStatus!=='2'"
                     @click="lockStatusClick(scope.row)"></i>  <!--注意被合并了之后不能解锁，不显示解锁按钮  mergeStatus=1合并别人  2被别人合并-->
                </span>
                <i class="iconfont ic-set-person custom-icon-button" title="设置平台角色"
                   v-if="scope.row.userName != null && (currentuserId != scope.row.id || isadmin =='1' )"
                   @click="onSettingsHandler(scope.row.id)">
                </i>
                <i class="iconfont ic-faceid custom-icon-button" title="设置接入系统角色"
                   v-if="scope.row.userName != null && (currentuserId != scope.row.id || isadmin =='1' )"
                   @click="onOuterSettingsHandler(scope.row.id, scope.row.userType, scope.row.userName)">
                </i>
                <i class="iconfont ic-sync1 custom-icon-button" title="密码重置"
                   v-if="scope.row.userName != null"
                   @click="onResetHandler(scope.row.telephone,scope.row.id , scope.row.userName,scope.row.seriakey)">
                </i>
                <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="openEditPersonModal(scope.row)"></i>
                <i class="iconfont ic-import-file custom-icon-button" title="合并历史" v-if="scope.row.id !== '1'"
                   @click="showMergeHistory(scope.row.id)"></i>
                <!--                <i class="iconfont ic-trash custom-icon-button" title="删除"-->
                <!--                   @click="deleteClick(scope.row.id, scope.row.seriakey,scope.$index, scope.row.telephone, scope.row.orgId)"></i>-->
              </template>
            </el-table-column>
          </el-table>
          <!--        <papers ref="paper" @searchTable="paperSearch" :total="total" :size="10"></papers>-->
          <el-pagination
              style="text-align: center;"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="query.startRow"
              :page-sizes="[10, 20, 50, 200]"
              :page-size="query.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
          </el-pagination>
        </el-card>
        <!--E form表单-->
      </div>
      <edit-person
          :isShow="showModal"
          :updateData="currentRow"
          @editPersonClose="editPersonClose"
      ></edit-person>
      <set-user
          :isShow="showSetUserModal"
          :updateData="currentRow"
          @setUserClose="setUserClose"
      >
      </set-user>
      <set-role ref="roleSettings"></set-role>
      <!--用户账户密码重置-->
      <password-reset ref="passwordReset"></password-reset>
      <!-- 设置接入系统角色 -->
      <set-outer-role ref="setOuterRole"></set-outer-role>
      <el-dialog
          title="用户合并历史"
          :visible.sync="showMergeHistoryFlag"
          v-if="showMergeHistoryFlag"
          width="50%">
        <merge-history ref="mergeHistory" :personId="mergeHistoryId" @refreshUser="refreshUser"></merge-history>
      </el-dialog>

      <el-dialog
          title="选择合并用户"
          :visible.sync="showChooseMergeUser"
          v-if="showChooseMergeUser"
          width="50%">
        <el-table
            :data="multipleSelectionObjList">

          <el-table-column
              align="center"
              label="姓名"
              min-width="128px"
              prop="personName">
            <template slot-scope="scope">
                <span class="el-table-name-icon">
                  <i class="iconfont icon-ic-male male" v-if="scope.row.sex === '1'"> </i>
                  <i class="iconfont icon-ic-female female" v-else-if="scope.row.sex === '2'"></i>
                </span>
              <p class="el-table-name">
                {{ scope.row.personName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column align="center" label="用户名" prop="userName">
            <template slot-scope="scope">
              <span>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column
              align="center"
              label="来源"
              prop="source"
              width="100">
            <template slot-scope="scope">
              <span v-if="scope.row.source === 'HGGL'">回购管理</span>
              <span v-else-if="scope.row.source === 'GQJL'">股权激励</span>
              <span v-else-if="scope.row.source === 'TDGF'">合规交易</span>
              <span v-else>平台新增</span>
            </template>
          </el-table-column>

          <el-table-column align="center" label="操作" min-width="100px">
            <template slot-scope="scope">
              <el-button round type="primary" size="mini" style="margin-left: 10px;"  @click="chooseMergeUser(scope.row)" >选此用户为保留用户</el-button>
            </template>
          </el-table-column>

        </el-table>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { showConfirm, showMessage } from "stock-vue-plugin/lib/utils/message";
import papers from "@/components/Pagination/papers.vue";
import {
  _deleteChooseUsers,
  _getOrgListByType,
  _getPersonList,
  _haveOtherSystemRole, _mergePerson,
  _resetPassword, _resetUserAccountPassword,
  _setAvailableStatus,
  _setLockStatus,
  exportUserInfo
} from "@/api/person-api";
import editPerson from "../../views/person/editPersonOut";
import SetUser from "../../views/person/setUser";
import SetRole from "../../views/person/setRole";
import passwordReset from '../../views/person/passwordReset'
import store from "@/store";
import SetOuterRole from "../../views/person/setOuterRole";
import MergeHistory from "@/views/person/mergeHistory.vue";
import { _importOuterUserInfo } from "@/api/system/infoSync-api";
import {selectButtonByName} from "../../api/mqManage/MQManage-api";

export default {
  components: { MergeHistory, SetUser, editPerson, papers, SetRole, passwordReset, SetOuterRole },
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      importOutPersonStatus:false,//控制导入外部用户按钮是否隐藏
      resetOutPersonPassword:false,//控制重置外部用户密码按钮是否隐藏
      // 显示合并用户弹窗
      showChooseMergeUser: false,
      isadmin: store.state.app.info.isAdmin,
      currentuserId: store.state.app.info.id,
      // 选中的数据
      multipleSelection: [],
      multipleSelectionSer: [],
      // 选中的数据对象
      multipleSelectionObjList: [],
      // 新增用户弹出框是否打开
      showModal: false,
      // 设置用户弹出框是否打开
      showSetUserModal: false,
      // 设置权限弹出框是否打开
      showSetPermissionModal: false,
      // 设置绑定弹出框是否打开
      query: {
        // 机构id
        orgId: "",
        // 用户可用状态
        userAvailableStatus: "",
        // 用户锁状态
        userLockStatus: "",
        // 搜索字符串
        searchString: "",
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 10
      },
      // 当前编辑行
      currentRow: {},
      // 数据总数
      total: 10,
      // table 列表数据
      tableData: [],
      // 组织结构列表
      orgList: [],
      // 锁定状态列表
      lockStatusList: [
        { name: "已锁定", value: "1" },
        { name: "未锁定", value: "0" }
      ],
      // 用户状态列表
      availableStatusList: [
        { name: "可用", value: "1" },
        { name: "不可用", value: "0" }
      ],
      mergeHistoryId: '',
      showMergeHistoryFlag: false,
      editorModel: {
        id: undefined,
        telephone: undefined,
        userName: undefined,
        seriakey: undefined,
        // 新密码
        newPassword: '',
        // 确认密码
        confirmPassword: ''
      },
    };
  },
  mounted () {
    // 获取用户列表数据
    this.getUserData();
    this.getOrgList();
    this.checkButtonStatus()
  },
  methods: {
    // 隐藏电话号码中间四位
    hidePhoneNum(phone) {
      if(phone != null){
        var str = String(phone)
        var len = str.length;
        var prev,next;
        if (len >= 7) {
          prev = str.slice(-len,-8)
          next = str.slice(-4)
          str = prev+"****"+next
        } else if (len < 7 && len >= 6) {
          prev = str.slice(-len,-4)
          next = str.slice(-2)
          str = prev + "**" + next
        }
        return str
      }

    },
    // 选定要合并的用户
    chooseMergeUser (row) {

      let param = this.multipleSelectionObjList.map(value => {
        return {
          'userId': value.id,
          'userName': value.userName,
          'mainUser': '',
          'source': value.source,
        }
      })

      for (let paramElement of param) {
        if (paramElement.userId === row.id) {
          paramElement.mainUser = '1'
        }
      }

      _mergePerson(param).then((res) => {
        if (res.data.success && res.data.result) {
          this.$message.info('合并成功');
          this.showChooseMergeUser = false;
          this.getUserData();
        } else {
          this.$message.error('合并失败');
        }
      });

    },
    // 点合并
    clickMergeButton () {
      // 不能为空
      if (this.multipleSelectionObjList.length === 0) {
        this.$message.info("请选择两个待合并用户");
        return;
      }
      // 只能选两个
      if (this.multipleSelectionObjList.length !== 2) {
        this.$message.info("只能选择两个待合并用户");
        return;
      }

      // 来源必须是不同的两个子系统  , source=0 是平台用户，不能合
      let sourceArray = this.multipleSelectionObjList.map(value => value.source);

      // 如果包含0 说明是平台用户 不让合
      if (sourceArray.includes("0")) {
        this.$message.info("您选择的用户中包含来源为平台创建的用户，平台用户不可合并");
        return;
      }

      // 去重 返回set
      let sourceSet = [...new Set(sourceArray)];
      if (sourceSet.length !== 2) {
        this.$message.info("您选择的用户中包含来源为两个相同子系统，相同子系统不可合并");
        return;
      }

      let lockArr = this.multipleSelectionObjList.map(value => value.userLockStatus);
      // 如果包含1 说明是锁定的 不让合
      if (lockArr.includes("1")) {
        this.$message.info("您选择的用户中包含已经锁定的用户，已经锁定的用户不可合并");
        return;
      }

      let mergeStatusArr = this.multipleSelectionObjList.map(value => value.mergeStatus);
      // 如果包含1 或 2 说明是已经被合并或已经合并别人 不让合
      if (mergeStatusArr.includes("1") || mergeStatusArr.includes("2")) {
        this.$message.info("您选择的用户中包含已经合并的用户，已经合并的用户不可合并");
        return;
      }
      if (mergeStatusArr.includes("3")) {
        this.$message.info("您选择的用户中包含已经编辑过的用户，已经编辑过的用户不可合并");
        return;
      }

      // 不同的上市公司不让合并
      let companyArr = this.multipleSelectionObjList.map(value => value.orgId); // 这里取的是sa_org Id
      let companySet = [...new Set(companyArr)];
      if (companySet.length !== 1) {
        this.$message.info("您选择的用户中包含来源为两个上市公司，不同上市公司用户不可合并");
        return;
      }

      let param = this.multipleSelectionObjList.map(value => {
        return { 'userId': value.id, 'source': value.source }
      })
      _haveOtherSystemRole(param).then((res) => {
        if (res.data.success) {
          if (res.data.result) { // 为true说明存在多个子系统角色
            this.$message.info("存在授予了其他子系统权限的用户，不可合并");
          } else {
            // 打开窗口
            this.showChooseMergeUser = true;
          }

        } else {
          this.$message.error('查询错误');
        }
      });

    },
    customUpload (fileObj) {
      // this.importOutPersonStatus = false;
      // 在这里编写自定义的上传逻辑
      let data = new FormData();
      data.append('files', fileObj.file);
      _importOuterUserInfo(data).then(res => {
        if (res.data.success) {
          this.$message.info("导入" + res.data.result + "条数据");
          window.location.reload();
        } else {
          // this.importOutPersonStatus = true;
          this.$message.error("导入失败" + res.data.errorMsg);
        }
      })
      this.checkButtonStatus();
    },
    beforeUpload (file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const isXls = file.type === 'application/vnd.ms-excel';
      if (!isXlsx && !isXls) {
        this.$message.error('只能上传xls、xlsx文件');
        return false; // 取消上传
      }
      return true; // 立即上传文件
    },
    handleSizeChange (val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 0
      this.getUserData();
    },
    handleCurrentChange (val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.getUserData();
    },
    exportUserInfo () {
      exportUserInfo(this.query);
    },
    onResetHandler (telephone, _id, userName, seriakey) {
      this.$confirm('请点击确认按钮进行密码重置，重置后密码将通过邮件方式发送到对应用户的邮箱上，请通知用户注意查收。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.editorModel.id = _id
        this.editorModel.telephone = telephone
        this.editorModel.userName = userName
        this.editorModel.seriakey = seriakey
        _resetUserAccountPassword(this.editorModel).then(res => {
          if (res.data.result) {
            this.$message({
              type: 'info',
              message: '重置成功'
            });
          } else {
            this.$message({
              type: 'error',
              message: '重置失败'
            });
          }
        })
      })
      // this.$refs.passwordReset.dialogOpen(telephone, _id, userName, seriakey)
    },
    // paperSearch () {
    //   let form = this.$refs.paper.submitData;
    //   this.query.startRow = form.startRow == undefined ? 0 : form.startRow;
    //   this.query.pageSize = form.pageSize;
    //   this.getUserData();
    // },
    onSettingsHandler (_id) {
      this.$refs.roleSettings.dialogOpen(_id)
    },
    onOuterSettingsHandler (_id, _userType, _userName) {
      this.$refs.setOuterRole.open(_id, _userType, _userName)
    },
    /* 获取用户列表数据
     * */
    getUserData () {
      // 查询外部用户
      this.query.userType = '1';
      _getPersonList(this.query).then((res) => {
        if (res.status == '200') {
          this.tableData = res.data.tableData;
          this.total = res.data.total;
          if (this.tableData.length === 0) {
            this.query.startRow = 0;
            _getPersonList(this.query).then((res) => {
              this.tableData = res.data.tableData;
              this.total = res.data.total;
            })
          }
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    refreshUser(){
      this.query.startRow = 0;
      this.getUserData();
    },
    getOrgList () {
      _getOrgListByType({ userType: '1' }).then((res) => {
        if (res.data.success) {
          this.orgList = res.data.result;
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    /* 查询
     * */
    search () {
      this.getUserData();
    },
    /* 清空条件
     * */
    clearClick () {
      Object.assign(this.query, this.$options.data().query); // 重置数据
      this.$refs.multipleTable.clearSelection()
      this.getUserData(); // 重新获取数据
    },
    /* 批量删除
     * */
    batchDelete () {
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage("info", this.$messageText["deleteSelectNone"]);
        return;
      }
      let index = -1;
      // 当前只选中了一条数据，查找当前数据的索引
      if (this.multipleSelection.length === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === this.multipleSelection[0].id) {
            index = i;
          }
        }
      }
      showConfirm(
          this.$messageText["deleteContent"],
          this.$messageText["deleteTitle"],
          this,
          "deleteClickData",
          { idList: this.multipleSelection, SeriakeyList: this.multipleSelectionSer, index: index }
      );
    },
    /* table checkbox 选中事件
     * val:选中得数据
     * */
    handleSelectionChange (val) {

      this.multipleSelectionObjList = val;

      this.multipleSelection = val.map((i) => {
        return { id: i.id, telephone: i.telephone, orgId: i.orgId };
      });
      this.multipleSelectionSer = val.map((i) => {
        return { id: i.id, seriakey: i.seriakey };
      });
    },
    /* 关闭更新用户
     * */
    editPersonClose () {
      this.showModal = false;
      this.getUserData();
    },
    /* 关闭设置用户页面
     * */
    setUserClose () {
      this.showSetUserModal = false;
      this.getUserData();
    },
    /** 关闭权限设置页面 **/
    setPermissionClose () {
      this.showSetPermissionModal = false;
    },
    /* 编辑角色
     * item:要编辑的数据
     * */
    openEditPersonModal (item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showModal = true;
    },
    /* 设置用户
     * item:要编辑的数据
     * */
    setUserModal (item) {
      console.log(item);
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showSetUserModal = true;
    },
    /* 设置权限
     * item:要编辑的数据
     * */
    setPermissionModal (item) {
      console.log(item);
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showSetPermissionModal = true;
    },
    // 表格序列号
    getIndex ($index) {
      console.log($index, "序号");
      // 表格序号
      // (当前页 - 1) * 当前显示数据条数 + 当前行数据的索引 + 1
      return (this.pageNum - 1) * this.query.pageSize + $index + 1;
    },
    availableStatusClick (row) {
      if (row.userAvailableStatus === "0") {
        row.userAvailableStatus = "1";
      } else {
        row.userAvailableStatus = "0";
      }
      _setAvailableStatus(row).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    lockStatusClick (row) {
      if (row.userLockStatus === "0") {
        row.userLockStatus = "1";
      } else {
        row.userLockStatus = "0";
      }
      _setLockStatus(row).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    deleteClickData (param) {
      _deleteChooseUsers(param).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    deleteClick (id, seriakey, index, telephone, orgId) {
      let idparam = {
        id: id,
        seriakey: seriakey
      };
      let param = {
        id: id,
        telephone: telephone,
        orgId: orgId
      };
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: [param],
        SeriakeyList: [idparam],
        index: index
      });
    },
    showMergeHistory (id) {
      this.mergeHistoryId = id
      this.showMergeHistoryFlag = true
    },
    // 重置外部人员密码
    resetPassword () {
      // _resetPassword().then((res) => {
      //   if (res.data.success) {
      //     showMessage("info", "正在重置中...");
      //   } else {
      //     showMessage("error", res.data.errorMsg);
      //   }
      // });
      this.$confirm('此操作将会重置全部外部用户的密码并发送邮件通知账号、密码和登录地址。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        // confirmButtonClass: 'delete-confirm-button',
        // cancelButtonClass: 'delete-cancel-button',
        // customClass: 'delete-confirm-custom',
        type: 'warning'
      }).then(() => { // 确定重置
        _resetPassword() // 发送异步请求，不影响用户继续操作
            .then((response) => {
              showMessage("toast", "正在重置中..."); // 不影响用户操作的同时显示提示信息
              // if (response.data.success) {
              // } else {
              //   showMessage("toast", "重置请求发送失败：" + response.data.errorMsg);
              // }
            })
            .catch((error) => {
              showMessage("toast", "重置过程中发生错误");
              console.error("Error while resetting passwords:", error);
            });

        // 请求发送后，允许用户进行其他操作
        this.passwordResetRequestSent = true; // 可以设置一个标志位，方便后续UI控制
      }).catch(() => {
      });
      // this.$confirm('是否重置所有外部人员密码？', '确认重置', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   confirmButtonClass: 'delete-confirm-button',
      //   cancelButtonClass: 'delete-cancel-button',
      //   customClass: 'delete-confirm-custom'
      // }).then(() => { // 确定删除
      //   _resetPassword().then((res) => {
      //     if (res.data.success) {
      //       showMessage("info", "正在重置中...");
      //     } else {
      //       showMessage("error", res.data.errorMsg);
      //     }
      //   });
      // }).catch(() => {})
    },
    checkButtonStatus() {
      selectButtonByName({moduleName:'person',zhName:'外部人员信息',buttonName:'导入外部用户'}).then(res=>{
        if (res.data.success && res.data.result !== null){
          if(res.data.result.status === '1'){
            this.importOutPersonStatus = true;
          }else{
            this.importOutPersonStatus = false;
          }
        }
      })
      selectButtonByName({moduleName:'person',zhName:'外部人员信息',buttonName:'邮件通知(账号、密码、登录地址)'}).then(res=>{
        if (res.data.success && res.data.result !== null){
          if(res.data.result.status === '1'){
            this.resetOutPersonPassword = true;
          }else{
            this.resetOutPersonPassword = false;
          }
        }
      })
    },
  },
  computed: {},

};
</script>
<style lang="scss" scoped>
.user-list {
  padding: 20px;

  .user-list__avatar {
    width: 50px;
    height: 50px;
  }

  .out-person-list-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
}

.main-header {
  position: relative;
  margin: 0;
  // padding-left: 8px;
  // border-left: 4px solid #999999;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: $--el-color-text-primary;
  line-height: 22px;

  span {
    //算上边线本身4px一共12px
    padding-left: 12px;
  }

  //平行四边形边线
  i {
    position: absolute;
    top: 2px;
    display: inline-block;
    transform: skewY(-15deg);
    height: 18px;
    width: 4px;
    background-color: $--el-color-primary;
  }
}

a.btn,
span.btn {
  color: #333333;

  i {
    font-size: 16px;
  }

  .greenColor {
    color: #0fa958 !important;
  }
}

.el-card {
  border: none;
}

/deep/ .el-card__body,
.el-main {
  padding: 0;
}

.box-card-btns {
  a {
    margin-right: 13px;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;

    i {
      color: #1990fe;
    }

    span {
      margin-left: 0;
    }
  }
}

.box-card-search {
  .box-card-search-btns {
    & > :first-child {
      margin-left: 5px;
    }
  }
}

.el-table-name {
  display: inline-block;
  width: 40%;
  text-align: left;
}

.el-table-name-icon {
  width: 60%;
  text-align: right;

  .male {
    color: #76c3ff;
  }

  .female {
    color: #ff74b2;
  }
}
</style>
<style lang="scss">
.el-card__body, .el-main {
  padding: 0;
  text-align: left;
  line-height: 20px;
}

.row-form-list {
  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}

.custom-header-class-list {

  th {
    color: #777 !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    background: #F6F7F9;
    padding: 10px 0px !important;

  }
}

</style>
