<template>
  <el-dialog
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :close-on-press-escape="false"
      title="用户-接入系统角色设置">
    <div class="role-settings">
      <el-row>
        <el-tree
            ref="roleTree"
            :data="roles"
            show-checkbox
            node-key="id"
            :props="{ children: 'children', label: 'name' }"
            :default-checked-keys="checkedRoleIds"
            @check-change="checkChange"
            default-expand-all>
        </el-tree>
      </el-row>
      <el-row gutter="10" class="drawer__footer">
        <el-col span="12">
          <el-button size="small" @click="cancelHandler()" round>取消</el-button>
        </el-col>
        <el-col span="12">
          <el-button type="primary" size="small" @click="confirmHandler()" round>保存</el-button>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>
<script>
import common from "@/mixins/common";
import {_getRoleListGroupBySystem, _saveOuterRoleUserMap, checkGQJLRole} from "@/api/role-outer-api";

export default {
  mixins: [common],
  data () {
    return {
      dialogVisible: false,
      userId: null,
      userType: null,
      userName: null,
      roles: [], // 全部角色
      checkedRoleIds: [] // 选中的角色
    }
  },
  methods: {
    open (userId, userType, userName) {
      console.log(userId, userType, userName)
      this.userId = userId;
      this.userType = userType;
      this.userName = userName;
      _getRoleListGroupBySystem({ id: userId, userType: userType }).then(res => {
        if (res.data.success) {
          this.roles = res.data.result.roles;
          this.roles.forEach(item => {
            if ("TDGF" === item.id) {
              item.disabled = true
            }
          })
          this.checkedRoleIds = res.data.result.checkedRoleIds;
        } else {
          this.$message.error(res.data.errorMsg);
        }
        this.dialogVisible = true;
      })
    },
    confirmHandler () {
      let checkedRoleIds = this.$refs.roleTree.getCheckedKeys();
      if (checkedRoleIds.some(item => item.includes("GQJL"))) {
        checkGQJLRole({ userId: this.userId, checkedRoleIds: checkedRoleIds, userType: this.userType, userName: this.userName }).then(res => {
          if (res.data.success) {
            _saveOuterRoleUserMap({ userId: this.userId, checkedRoleIds: checkedRoleIds, userType: this.userType }).then(res => {
              if (res.data.success) {
                this.$message.info("保存成功");
                this.cancelHandler();
              } else {
                this.$message.error(res.data.errorMsg);
              }
            })
          } else {
            this.$message.error(res.data.errorMsg);
          }
        });
      } else {
        _saveOuterRoleUserMap({ userId: this.userId, checkedRoleIds: checkedRoleIds, userType: this.userType }).then(res => {
          if (res.data.success) {
            this.$message.info("保存成功");
            this.cancelHandler();
          } else {
            this.$message.error(res.data.errorMsg);
          }
        })
      }
    },
    cancelHandler () {
      this.dialogVisible = false;
      this.userId = null;
      this.settingsRoles = [];
    },
    checkChange (data, checked, indeterminate) {
      if (checked && data.flag === 'TDGF') {
        let checkedKeys = this.$refs.roleTree.getCheckedKeys()
        checkedKeys = checkedKeys.filter(item => { return item.indexOf('TDGF') === -1 || item === data.id; });
        this.$refs.roleTree.setCheckedKeys(checkedKeys);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.role-settings {
  //padding: 0 18px 0 18px;
  .drawer__footer {
    position: absolute;
    bottom: 10px;
    left: 40%;
  }
}
</style>
