<template>
  <el-dialog
      v-if="showing"
      :before-close="handleClose"
      :title="formInline.id?'编辑外部人员':'新增外部人员'"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="600px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-row>
          <el-col :span="24">
            <el-form-item label="姓名" prop="personName">
              <el-input :maxLength="20" placeholder="姓名" v-model.trim="formInline.personName"></el-input>
            </el-form-item>
            <el-form-item label="所属上市公司" prop="orgId">
              <el-cascader
                  v-model="formInline.orgId"
                  :options="orgDictList"
                  :props="defaultProps"
                  clearable
                  style="width: 100% !important;"
                  :disabled="this.updateData != null"
                  filterable></el-cascader>
            </el-form-item>
            <el-form-item label="证件号码" prop="certificate">
              <el-input :maxLength="20" placeholder="证件号码" v-model.trim="formInline.certificate"></el-input>
            </el-form-item>
            <el-form-item label="手机" prop="telephone">
              <el-input :maxLength="20" placeholder="手机" v-model.trim="formInline.telephone"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="mail">
              <el-input :maxLength="64" placeholder="邮箱" v-model.trim="formInline.mail"></el-input>
            </el-form-item>
            <el-form-item label="职务" prop="jobs">
              <el-input :maxLength="20" placeholder="职务" v-model.trim="formInline.jobs"></el-input>
            </el-form-item>
            <el-form-item label="电话" prop="phone">
              <el-input :maxLength="20" placeholder="电话" v-model.trim="formInline.phone"></el-input>
            </el-form-item>
            <el-form-item label="传真" prop="fax">
              <el-input :maxLength="20" placeholder="传真" v-model.trim="formInline.fax"></el-input>
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input :maxLength="20" placeholder="地址" v-model.trim="formInline.address" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input :maxLength="30" placeholder="备注" v-model="formInline.remark" type="textarea"></el-input>
            </el-form-item>

          </el-col>
        </el-row>

      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {showMessage} from 'stock-vue-plugin/lib/utils/message'
import {_validatePhone, _validateEmail, _validateIdCardNo} from '@/utils/validate'
import store from '@/store'
import {
  _getIdentityList,
  _insertPersonInfo,
  _queryPersonInfoById,
  _updatePersonInfo,
  getOrgSelection
} from "../../api/person-api";

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data() {
    // 电话号码验证
    return {
      // 头像地址
      avatarUrl: '',
      // 组织结构列表
      orgList: [],
      // 所属机构下拉选项
      orgDictList: [],
      // 身份信息列表
      identityList: [],
      // 是否显示弹出框
      showing: false,
      defaultProps: {
        children: 'children',
        label: 'label',
        id:'id',
        value:'id',
        checkStrictly: true,
        emitPath:false,
        expandTrigger:'hover'
      },
      // 编辑角色需要的字段
      formInline: {
        // 用户类型 0-系统内部用户 1-外部上市公司用户
        userType: "1",
        // 姓名
        personName: "",
        // 用户id
        personId: "",
        // 所属部门 1-总部 2-分公司 3-营业部
        isMgr: "4",
        userName: "",
        // 归属机构
        orgId: "",
        // 职务
        jobs: "",
        // 电话
        telephone: "",
        // 邮箱
        mail: "",
        // 备注
        remark: "",
        // 照片地址
        pictureUrl: "",
        seriakey: "",
        // resumeRecordList
        resumeRecordList: [],
        certificate: '',
        certificateType: 'SF',
        // 固定电话
        phone: '',
        // 传真
        fax: '',
        // 地址
        address: ''
      },
      userOrgType: '1', // 当前登录人机构类别
      userIsAdmin: '0', // 当前登录人是否管理员
      // 允许上传的文件类型列表
      acceptList: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".JPG", ".JPEG", ".PBG", ".GIF", ".BMP"],
      // 表单验证规则
      rules: {
        personName: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        // 合规需要职务必填
        jobs: [
          { required: true, message: '请输入职务', trigger: 'blur' }
        ],
        orgId: [
          {required: true, message: '请选择所属上市公司', trigger: 'blur'}
        ],
        telephone: [
          {required: true, message: '请输入手机号', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (!_validatePhone(value)) {
                  callback(new Error('请输入正确手机号'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        mail: [
          {required: true, message: '请输入邮箱', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (!_validateEmail(value)) {
                  callback(new Error('请输入正确邮箱号'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        certificate: [
          {required: true, message: '请输入证件号码', trigger: 'blur'}
        ],
      }
    }
  },
  computed: {
    // 设置上传头像的请求头
    setHeaders() {
      let param = {
        Authorization: store.getters.token,
        'X-Tenant-Info': store.getters.info
      }
      return param;
    }
  },
  mounted() {
    this.userOrgType = localStorage.getItem("orgType");
    // 登录账号类型为分公司，则查询所属分公司下拉
    this.userTypeAndIsMgrChange();
    this.getOrgDictList();
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 保存人员信息* */
    sureClick() { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          if (this.formInline.id) {
            _updatePersonInfo(this.formInline).then(res => {
              if (res.data.success) {
                let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
                if (this.formInline.userName === data.userName) {
                  data.personName = this.formInline.personName;
                  let updatedData = JSON.stringify(data);
                  window.localStorage.setItem("platform_jurisdictionData", updatedData);
                }
                showMessage('info', '修改人员成功');
                this.handleClose();
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          } else {
            _insertPersonInfo(this.formInline).then(res => {
              if (res.data.success) {
                showMessage('info', '新增人员成功');
                this.handleClose();
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          showMessage("error", "请完善所填写的信息")
        }
      });
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('editPersonClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // this.$refs.tree.clear();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    returnPersonId() {
      return this.formInline.id
    },
    checkBoxChange(item) {
      if (item.leader === '1') {
        item.leader = '0'
      } else {
        item.leader = '1'
      }
    },
    getStatus(item) {
      return item === '1'
    },
    userTypeAndIsMgrChange() {
      this.formInline.orgId = null;
      this.orgDictList = [];
      this.getOrgDictList();
    },
    // 获取机构列表
    getOrgDictList() {
      let params = {
        userType: this.formInline.userType,
        isMgr: this.formInline.isMgr
      }
      getOrgSelection(params).then((result) => {
        if (result && result.data) {
          this.orgDictList = result.data.result;
        }
      })
    },
    findFirstMatchedOrg(orgIds) {
      const ids = orgIds.split(',');
      return this.findInTree(ids, this.orgDictList);
    },
    findInTree(ids,tree) {
      for (const item of tree) {
        if (ids.includes(item.key)) {
          return item.key;
        }
        if (item.children && item.children.length) {
          const found = this.findInTree(ids,item.children);
          if (found) return found;
        }
      }
      return null;
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.showing = this.isShow;
        // 获取当前登录人权限
        this.userOrgType = localStorage.getItem("orgType");
        this.userIsAdmin = localStorage.getItem("isAdmin");
        // 获取机构信息列表
        this.getOrgDictList();
        // 获取身份信息
        _getIdentityList().then(res => {
          if (res.data.success) {
            this.identityList = res.data.result
          } else {
            showMessage('error', res.data.errorMsg);
          }
        })
        // 下载文件
        this.avatarUrl = ''
        if (this.updateData != null) {
          _queryPersonInfoById(this.updateData.id, this.updateData.seriakey).then(res => {
            if (res.data.success) {
              this.formInline = res.data.result;
              this.formInline.userType = '1';
              this.formInline.isMgr = '4';
              this.formInline.userName = this.updateData.userName;
              // this.userTypeAndIsMgrChange();
              this.formInline.orgId = this.findFirstMatchedOrg(this.updateData.orgId);
              this.formInline.resumeId = this.updateData.resumeId;
              this.formInline.seriakey = this.updateData.seriakey;
            } else {
              showMessage("error", res.data.errorMsg)
            }
          })
        } else  {
          this.formInline.id = ''
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss" scoped>
.add-user {
  .add-user__upload {
    .el-upload {
      border: 1px dashed $--el-border-color-lighter;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      height: 178px;
      display: flex;
      align-items: center;
      width: 178px;
      justify-content: center;
      color: $--el-color-text-placeholder;

      .avatar-uploader-icon {
        font-size: 28px;
        color: $--el-color-text-placeholder;
      }
    }
  }

  .add-user__img {
    width: 178px;
    height: 178px;
  }
}

.aa > > > .el-form-item__content {
  margin-left: 0px !important;
}

.cell .el-form-item {
  margin-bottom: 13px;
}

.cell .el-link.el-link--primary {
  margin-bottom: 13px;
}
.pc-sel-area-cascader {
  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active,
  .el-cascader-node.is-selectable.in-checked-path {
    color: red;
  }
}
</style>
<style>

.el-cascader__label .el-cascader-node--leaf.checked,
.el-cascader-panel .el-cascader-node.inactive:hover .el-cascader-node__label,
.el-cascader-panel .el-cascader-node.is-active .el-cascader-node__label {
  color: red !important;
}
.el-cascader-menu .el-cascader-node.is-active:not(.is-disabled)>.el-cascader-node__label,
.el-cascader-menu .el-cascader-node.is-active:not(.is-disabled)>span {
  color: red !important;
}

.el-cascader-panel .el-cascader-node.inactive:hover .el-cascader-node__label {
  color: inherit !important; /* 取消鼠标悬停时的亮蓝色 */
}

.el-cascader-node__label {
  color: initial !important; /* 取消默认选中态的亮蓝色 */
}

.el-cascader-node.is-checked>.el-cascader-node__label {
  color: red !important; /* 选中节点的文字颜色 */
}


</style>