<template>
  <el-dialog
      v-if="showing"
      :before-close="handleClose"
      :title="formInline.id?'编辑内部人员':'新增内部人员'"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="600px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-row>
          <el-col :span="24">
            <el-form-item v-show="formInline.userType == '0'" label="所属部门" prop="isMgr">
              <el-radio-group v-model="formInline.isMgr" @change="userTypeAndIsMgrChange1">
                <el-radio label="1" v-show="userIsAdmin == '1' || userOrgType == '1'">总部</el-radio>
                <el-radio label="2" v-show="userIsAdmin == '1' || userOrgType == '1' || userOrgType == '2'">分公司
                </el-radio>
                <el-radio label="3">营业部</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="(formInline.userType == '0' && formInline.isMgr && formInline.isMgr != '1') || formInline.userType == '1'"
                :label="orgText" prop="orgIdList">
              <el-select v-model="formInline.orgIdList" :placeholder="orgPlaceHodlerText"
                         style="width: 100% !important;"
                         class="full" filterable v-if="formInline.isMgr === '2'" @change="clearChildrenOrg">
                <el-option
                    v-for="item in orgDictList"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value">
                </el-option>
              </el-select>
              <el-select-multiple v-model="formInline.orgIdList" v-else-if="formInline.isMgr === '3'"
                                  :placeholder="orgPlaceHodlerText"
                                  class="full" filterable style="width: 100% !important;">
                <el-select-multiple-option
                    v-for="item in orgDictList"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value">
                </el-select-multiple-option>
              </el-select-multiple>
            </el-form-item>
            <el-form-item label="姓名" prop="personName">
              <el-input :maxLength="20" placeholder="姓名" v-model.trim="formInline.personName"></el-input>
            </el-form-item>
            <el-form-item label="用户名" prop="userName">
              <el-input :maxLength="20" placeholder="用户名" v-model.trim="formInline.userName" :disabled="this.updateData != null">
                <template slot="prepend">CSC</template>
              </el-input>
            </el-form-item>
            <el-form-item label="手机" prop="telephone">
              <el-input :maxLength="20" placeholder="手机" v-model.trim="formInline.telephone"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="mail">
              <el-input :maxLength="64" placeholder="邮箱" v-model.trim="formInline.mail"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input :maxLength="30" placeholder="备注" v-model="formInline.remark"></el-input>
            </el-form-item>
            <el-form-item label="客户号(合规)" prop="weChatId">
              <el-input :maxLength="20" placeholder="客户号" v-model.trim="formInline.weChatId"></el-input>
            </el-form-item>
            <!--            <el-form-item label="公司管理(合规)">-->
            <!--              <el-select-multiple v-model="formInline.orgIds" style="width: 100% !important;" filterable>-->
            <!--                <el-select-multiple-option-->
            <!--                    v-for="(item,index) in orgList"-->
            <!--                    :key="item.companyId"-->
            <!--                    :label="item.orgName"-->
            <!--                    :value="item.companyId">-->
            <!--                </el-select-multiple-option>-->
            <!--              </el-select-multiple>-->
            <!--            </el-form-item>-->
            <el-form-item v-if="editSubAccount && formInline.isMgr === '2'">
              <el-button type="danger" @click="editChildrenInfo" icon="el-icon-plus" plain round>增加子账户</el-button>
            </el-form-item>
            <div v-if="editSubAccountInfo  && formInline.isMgr === '2'" class="childrenInfo">
              <el-form-item label="用户名(子)" prop="childrenUserName">
                <el-input :maxLength="20" placeholder="请输入子账号用户名" v-model.trim="formInline.childrenUserName"
                          disabled>
                  <template slot="prepend">YCSC</template>
                </el-input>
              </el-form-item>
              <el-form-item
                  label="所属营业部" prop="childrenOrgList">
                <el-select-multiple v-model="formInline.childrenOrgList" clearable placeholder="请选择所属营业部"
                                    style="width: 100% !important;"
                                    class="full">
                  <el-select-multiple-option
                      v-for="item in orgDepartmentList"
                      :key="item.id"
                      :label="item.orgName"
                      :value="item.id">
                  </el-select-multiple-option>
                </el-select-multiple>
              </el-form-item>
              <el-form-item
                  label="角色" prop="childrenRoleList">
                <el-select-multiple v-model="formInline.childrenRoleList" clearable placeholder="请选择角色"
                                    style="width: 100% !important;"
                                    class="full">
                  <el-select-multiple-option
                      v-for="item in stockRoleList"
                      :key="item.id"
                      :label="item.roleName"
                      :value="item.id">
                  </el-select-multiple-option>
                </el-select-multiple>
              </el-form-item>
              <el-form-item align="right" class="switch-active">
                <el-switch
                    v-model="formInline.childrenLock"
                    active-text="启用"
                    inactive-text="停用"
                    active-value="0"
                    inactive-value="1">
                </el-switch>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {showMessage} from 'stock-vue-plugin/lib/utils/message'
import {_validatePhone, _validateEmail} from '@/utils/validate'
import store from '@/store'
import {
  _checkUserNameById,
  _insertPersonInfo,
  _queryPersonInfoById,
  _updatePersonInfo,
  getOrgSelection,
  _deleteChildrenInfo,
} from "@/api/person-api";
import {_getOrgList, _getDepartmentTreeList} from "@/api/depart-api";
import {_getStockRoleList} from "@/api/role-outer-api";

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data() {
    // 电话号码验证
    return {
      getOrgIdList: "",
      getChildrenOrg: "",
      // 分公司下属的营业部
      orgDepartmentList: [],
      // 股权激励角色列表
      stockRoleList: [],
      // 所属机构下拉选项
      orgDictList: [],
      // 身份信息列表
      identityList: [],
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 用户类型 0-系统内部用户 1-外部上市公司用户
        userType: "0",
        // 姓名
        personName: "",
        // 用户名
        userName: "",
        // 用户id
        personId: "",
        // 所属部门 1-总部 2-分公司 3-营业部
        isMgr: "1",
        // 归属机构
        orgIdList: [],
        orgIds: [],
        // 客户号
        weChatId: "",
        // 电话
        telephone: "",
        // 邮箱
        mail: "",
        // 备注
        remark: "",
        // 照片地址
        pictureUrl: "",
        seriakey: "",
        // resumeRecordList
        resumeRecordList: [],
        childrenUserName: "",
        childrenOrg: "",
        childrenOrgList: [],
        childrenOrgId: "",
        childrenRole: "",
        childrenRoleList: [],
        childrenLock: "0"
      },
      isInit: true,
      userOrgType: '1', // 当前登录人机构类别
      userIsAdmin: '0', // 当前登录人是否管理员
      orgText: '所属分公司',
      orgPlaceHodlerText: '请选择所属分公司',
      // 允许上传的文件类型列表
      acceptList: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".JPG", ".JPEG", ".PBG", ".GIF", ".BMP"],
      // 表单验证规则
      rules: {
        personName: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        userName: [
          {required: true, message: '请输入用户名', trigger: 'blur'}
        ],
        childrenUserName: [
          {required: true, message: '请输入用户名(子)', trigger: 'blur'}
        ],
        orgIdList: [
          {required: true, message: '请选择所属机构', trigger: 'blur'}
        ],
        childrenOrgList: [
          {required: true, message: '请选择所属营业部', trigger: 'blur'}
        ],
        childrenRoleList: [
          {required: true, message: '请选择角色', trigger: 'blur'}
        ],
        telephone: [
          {required: true, message: '请输入手机号', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (!_validatePhone(value)) {
                  callback(new Error('请输入正确手机号'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        mail: [
          {required: true, message: '请输入邮箱', trigger: 'blur'},
          {
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (!_validateEmail(value)) {
                  callback(new Error('请输入正确邮箱号'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      },
      editSubAccount: true,//是否显示新增子账号按钮
      editSubAccountInfo: false,//是否显示新增子账号表单
    }
  },
  computed: {
    // 设置上传头像的请求头
    setHeaders() {
      let param = {
        Authorization: store.getters.token,
        'X-Tenant-Info': store.getters.info
      }
      return param;
    }
  },
  mounted() {
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 保存人员信息* */
    sureClick() { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          this.isInit = true;
          // if (this.editSubAccount) {
          //   this.formInline.childrenUserName = "";
          //   this.formInline.childrenOrg = "";
          //   this.formInline.childrenId = "";
          //   this.formInline.childrenRole = "";
          //   this.formInline.childrenOrgList = [];
          //   this.formInline.childrenRoleList = [];
          // }
          _checkUserNameById(this.formInline.userName, this.formInline.id).then(res => {
            if (res.data.success) {
              if (res.data.result) {
                showMessage('info', '用户名已存在');
              } else {
                if (this.formInline.isMgr === '1') {
                  this.formInline.orgIdList = ['2']
                }
                if (this.formInline.isMgr === '2') {
                  this.formInline.orgIdList = this.formInline.orgIdList.split(',');
                  this.formInline.childrenOrg = this.getIdList(this.formInline.childrenOrgList);
                  this.formInline.childrenRole = this.getIdList(this.formInline.childrenRoleList);
                }
                if (this.formInline.id) {
                  _updatePersonInfo(this.formInline).then(res => {
                    if (res.data.success) {
                      let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
                      if ('CSC' + this.formInline.userName === data.userName) {
                        data.personName = this.formInline.personName;
                        let updatedData = JSON.stringify(data);
                        window.localStorage.setItem("platform_jurisdictionData", updatedData);
                      }
                      showMessage('info', '修改人员成功');
                      this.handleClose();
                      this.emit("")
                    } else {
                      showMessage('error', res.data.errorMsg);
                    }
                  })
                } else {
                  _insertPersonInfo(this.formInline).then(res => {
                    if (res.data.success) {
                      showMessage('info', '新增人员成功');
                      this.handleClose();
                    } else {
                      showMessage('error', res.data.errorMsg);
                    }
                  })
                }
              }
            } else {
              showMessage("error", res.data.errorMsg)
            }
          })
        } else {
          showMessage("error", "请完善所填写的信息")
        }
      });
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('editPersonClose', flag);
      this.showing = false;
      this.resetForm();
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
      this.clearChildrenInfo();
    },
    returnPersonId() {
      return this.formInline.id
    },
    checkBoxChange(item) {
      if (item.leader === '1') {
        item.leader = '0'
      } else {
        item.leader = '1'
      }
    },
    getStatus(item) {
      return item === '1'
    },
    userTypeAndIsMgrChange() {
      this.formInline.orgIdList = null;
      this.orgDictList = [];
      if (this.formInline.userType == '0') {
        if (this.formInline.isMgr === '1') {
          // this.clearChildrenInfo();
        }
        if (this.formInline.isMgr == '2') {
          this.orgText = "所属分公司";
          this.orgPlaceHodlerText = "请选择所属分公司";
        }
        if (this.formInline.isMgr == '3') {
          this.orgText = "所属营业部";
          this.orgPlaceHodlerText = "请选择所属营业部";
          // this.clearChildrenInfo();
        }
      } else {
        this.orgText = "所属上市公司";
        this.orgPlaceHodlerText = "请选择所属上市公司";
        // this.clearChildrenInfo();
      }
      if (this.formInline.userType != '0' || this.formInline.isMgr != '1') {
        this.getOrgDictList();
      }
    },
    userTypeAndIsMgrChange1() {
      this.formInline.orgIdList = null;
      this.orgDictList = [];
      this.formInline.childrenOrg = "";
      this.formInline.childrenOrgId = "";
      this.formInline.childrenLock = "1"
      this.$set(this.formInline, 'childrenOrgList', []);
      this.orgDepartmentList = []
      if (this.formInline.userType == '0') {
        if (this.formInline.isMgr === '1') {
          // this.clearChildrenInfo();
        }
        if (this.formInline.isMgr == '2') {
          this.orgText = "所属分公司";
          this.orgPlaceHodlerText = "请选择所属分公司";
        }
        if (this.formInline.isMgr == '3') {
          this.orgText = "所属营业部";
          this.orgPlaceHodlerText = "请选择所属营业部";
          // this.clearChildrenInfo();
        }
      } else {
        this.orgText = "所属上市公司";
        this.orgPlaceHodlerText = "请选择所属上市公司";
        // this.clearChildrenInfo();
      }
      if (this.formInline.userType != '0' || this.formInline.isMgr != '1') {
        this.getOrgDictList();
      }
    },
    // 获取机构列表
    getOrgDictList() {
      let params = {
        userType: this.formInline.userType,
        isMgr: this.formInline.isMgr
      }
      getOrgSelection(params).then((result) => {
        if (result && result.data) {
          this.orgDictList = result.data.result;
        }
      })
    },
    /**
     * 获取子账号信息
     */
    editChildrenInfo() {
      this.editSubAccountInfo = true;
      this.editSubAccount = false;
      this.formInline.childrenLock = "0"
      if (this.formInline.orgIdList !== null) {
        _getDepartmentTreeList({pOrgId: this.formInline.orgIdList}).then(res => {
          if (res.data.success) {
            this.orgDepartmentList = res.data.result;
          }
        });
      }
      _getStockRoleList().then(res => {
        if (res.data.success) {
          this.stockRoleList = res.data.result;
        }
      })
    },
    // 清除子账户信息
    clearChildrenInfo() {
      this.formInline.childrenUserName = "";
      this.formInline.childrenOrg = "";
      this.formInline.childrenRole = "";
      this.formInline.childrenOrgList = [];
      this.formInline.childrenRoleList = [];
      this.editSubAccount = true;
      this.editSubAccountInfo = false;
    },
    /**
     * 将数组拼接成字符串
     * @param val
     */
    getIdList(val) {
      if (val !== null && val instanceof Array && val.length > 0) {
        return val.join(',');
      }
    },
    clearChildrenOrg (newValue) {
      this.orgDepartmentList = [];
      this.$set(this.formInline, 'childrenOrgList', []);
      if (this.editSubAccountInfo && newValue !== null) {
        _getDepartmentTreeList({pOrgId: newValue}).then(res => {
          if (res.data.success) {
            this.orgDepartmentList = res.data.result;
          }
        });
      }
    }
  },
  watch: {
    editSubAccountInfo(val) {
      this.formInline.childrenUserName = this.formInline.userName;
    },
    // 'formInline.orgIdList': function (newValue) {
    //   this.orgDepartmentList = [];
    //   this.$set(this.formInline, 'childrenOrgList', []);
    //   if (this.editSubAccountInfo && newValue !== null) {
    //     _getDepartmentTreeList({pOrgId: newValue}).then(res => {
    //       if (res.data.success) {
    //         this.orgDepartmentList = res.data.result;
    //         this.$set(this.formInline, 'childrenOrgList', this.formInline.childrenOrgId.split(','));
    //       }
    //     });
    //   }
    // },
    'formInline.userName': function (val) {
      if (this.editSubAccountInfo) {
        this.formInline.childrenUserName = val;
      }
    },
    isShow(val) {
      if (val) {
        // 数据重置
        this.formInline = {
          // 用户类型 0-系统内部用户 1-外部上市公司用户
          userType: "0",
          // 姓名
          personName: "",
          // 用户名
          userName: "",
          // 用户id
          personId: "",
          // 所属部门 1-总部 2-分公司 3-营业部
          isMgr: "1",
          // 归属机构
          orgIdList: [],
          orgIds: [],
          // 客户号
          weChatId: "",
          // 电话
          telephone: "",
          // 邮箱
          mail: "",
          // 备注
          remark: "",
          // 照片地址
          pictureUrl: "",
          seriakey: "",
          // resumeRecordList
          resumeRecordList: [],
          childrenUserName: "",
          childrenOrg: "",
          childrenRole: "",
          childrenLock: "0"
        }
        this.userOrgType = '1';// 当前登录人机构类别
        this.userIsAdmin = '0';// 当前登录人是否管理员
        this.orgText = '所属分公司';
        this.orgPlaceHodlerText = '请选择所属分公司';
        // 其他操作
        this.showing = this.isShow;
        // 获取当前登录人权限
        let jurisdictionData = JSON.parse(localStorage.getItem("platform_jurisdictionData"));
        this.userOrgType = jurisdictionData.orgType;
        this.userIsAdmin = jurisdictionData.isAdmin;
        // 登录账号类型为分公司，则查询所属分公司下拉
        if (this.userOrgType == '2') {
          this.formInline.isMgr = "2";
        } else if (this.userOrgType == '3') { // 登录账号类型为营业部，则查询所属营业部下拉
          this.formInline.isMgr = "3";
          this.orgText = "所属营业部";
          this.orgPlaceHodlerText = "请选择所属营业部";
        }
        if (this.updateData != null) {
          this.getOrgDictList();
          _queryPersonInfoById(this.updateData.id, this.updateData.seriakey).then(res => {
            if (res.data.success) {
              const orgIds = res.data.result.orgIds
              this.getChildrenOrg = res.data.result.childrenOrgId;
              this.formInline = res.data.result;
              if (this.updateData.orgType == '1') {
                this.formInline.userType = '0';
                this.formInline.isMgr = '1';
              }
              if (this.updateData.orgType == '2') {
                this.formInline.userType = '0';
                this.formInline.isMgr = '2';
              }
              if (this.updateData.orgType == '3') {
                this.formInline.userType = '0';
                this.formInline.isMgr = '3';
              }
              if (this.updateData.orgType == '4') {
                this.formInline.userType = '1';
                if (this.userIsAdmin == '1' || this.userOrgType == '1') {
                  this.formInline.isMgr = '1';
                } else if (this.userOrgType == '2') {
                  this.formInline.isMgr = '2';
                } else if (this.userOrgType == '3') {
                  this.formInline.isMgr = '3';
                }
              }
              if (!this.formInline.userType) {
                this.formInline.userType = '0';
                this.formInline.isMgr = '1';
              }
              this.userTypeAndIsMgrChange();
              this.formInline.orgIds = orgIds
              this.formInline.orgIdList = this.updateData.orgIdList;
              this.formInline.resumeId = this.updateData.resumeId;
              this.formInline.seriakey = this.updateData.seriakey;
              this.formInline.userName = this.updateData.userName.includes("CSC") ? this.updateData.userName.replace('CSC', '') : this.updateData.userName
              if (this.formInline.isMgr === '2') {
                this.formInline.orgIdList = this.formInline.orgId
                _getDepartmentTreeList({pOrgId: this.formInline.orgIdList}).then(res => {
                  if (res.data.success) {
                    this.orgDepartmentList = res.data.result;
                  }
                });
                if (this.formInline.childrenUserName !== null || this.formInline.childrenOrg !== null || this.formInline.childrenRole !== null) {
                  this.editSubAccount = false;
                  this.editSubAccountInfo = true;
                  _getStockRoleList().then(res => {
                    if (res.data.success) {
                      this.stockRoleList = res.data.result;
                    }
                  });
                } else {
                  this.editSubAccount = true;
                }
              }
              this.getOrgList({ids: ['2']});
            } else {
              showMessage("error", res.data.errorMsg)
            }
          })
        } else {
          // this.resumeRecordList = [];
          if (this.userIsAdmin == '1' || this.userOrgType == '1') {
            this.formInline.isMgr = '1';
            // this.getOrgList({ids: ['2']});
          } else if (this.userOrgType == '2') {
            this.formInline.isMgr = '2';
          } else if (this.userOrgType == '3') {
            this.formInline.isMgr = '3';
          }
        }
        // setTimeout(() => {
        // }, 3000);
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss" scoped>
.add-user {
  .add-user__upload {
    .el-upload {
      border: 1px dashed $--el-border-color-lighter;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      height: 178px;
      display: flex;
      align-items: center;
      width: 178px;
      justify-content: center;
      color: $--el-color-text-placeholder;

      .avatar-uploader-icon {
        font-size: 28px;
        color: $--el-color-text-placeholder;
      }
    }
  }

  .add-user__img {
    width: 178px;
    height: 178px;
  }
}

.aa > > > .el-form-item__content {
  margin-left: 0px !important;
}

.cell .el-form-item {
  margin-bottom: 13px;
}

.cell .el-link.el-link--primary {
  margin-bottom: 13px;
}
</style>

<style>
.childrenInfo {
  width: 560px;
  height: 233px;
  top: 599px;
  left: 20px;
  gap: 0px;
  border-radius: 17px 0px 0px 0px;
  background-color: #FAFAFA;
  //margin-top: 32px;
  padding-top: 32px;
}
.switch-active .is-active {
  color: red;
}
</style>
