<template>
    <el-dialog :title="'用户 - 平台角色设置'"
               width="50%"
               :padding="[0, 0]"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :modal-append-to-body="false"
               :append-to-body="true"
               :before-close="dialogPreCloseHandler"
               v-if="dialogVisible">
      <div class="role-settings" v-loading="dialogLoading">
        <el-row>
          <el-col :span="12">
            <div class="role-settings-left">
              <div class="role-settings-title">
                <div class="role-settings-title-text">请选择</div>
                <div class="role-settings-title-input">
                  <el-input placeholder="请输入搜索内容" v-model="pleaseSelectFilter" clearable></el-input>
                </div>
              </div>
              <div class="role-settings-body">
                <div v-for="item in filteredPleaseSelectData" class="role-settings-body-item">
                  <el-radio v-model="radio" :label="item.id" @change="selectedRole(item)">{{ item.roleName }}</el-radio>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="role-settings-right">
              <div class="role-settings-title">
                <div class="role-settings-title-text">已选择</div>
                <div class="role-settings-title-input">
                  <el-input placeholder="请输入搜索内容" v-model="selectedFilter" clearable></el-input>
                </div>
              </div>
              <div class="role-settings-body">
                <div class="role-settings-body-count">
                  已选{{this.selectedData.length}}，
                  <span class="role-settings-body-count-clear" @click="clearSelected">清空</span>
                </div>
                <div class="role-settings-body-tag">
                  <el-tag
                      class="tag-style"
                      :key="item.id"
                      v-for="item in filteredSelectedData"
                      closable
                      :disable-transitions="false"
                      @close="handleClose(item)"
                      type="info"
                      size="medium"
                      color="#F9F9F9"
                      effect="plain">
                    {{item.roleName}}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
<!--        <el-transfer v-model="settingsRoles" :data="roles" :props="{key: 'id',label: 'roleName'}" :titles="['请选择', '已选择']"-->
<!--                     :button-texts="['取消', '选择']"-->
<!--                     :render-content="transferRenderHandler" filterable></el-transfer>-->
      </div>
      <span slot="footer">
            <el-button type="primary" size="small" @click="confirmHandler()">保存</el-button>
            <el-button size="small" @click="cancelHandler()">返回</el-button>
          </span>
    </el-dialog>
</template>
<script>
import {
  _getUserOfRolesByUserId,
  _settingUserRoleMapByUserId
} from "@/api/person-api";
import { _getRoleDataAll } from "@/api/manage-api";

const roleSettings = {
  name: 'roleSettings',
  created: function () {
  },
  updated: function () {
  },
  mounted: function () {
  },
  activated: function () {
  },
  data: function () {
    return {
      // 组件改造
      pleaseSelectFilter: '',
      selectedFilter: '',
      radio: '',
      pleaseSelectData: [],
      selectedData: [],
      // 弹窗控制
      dialogVisible: false,
      dialogLoading: false,
      userId: undefined,
      // 数据
      roles: [],
      settingsRoles: [],
      // transferRenderHandler (h, _item) {
      //   return h(
      //     'span',
      //     {
      //       attrs: { title: _item.roleName }
      //     },
      //     [_item.roleName]
      //   )
      // },
    }
  },
  methods: {
    selectedRole(role) {
      this.radio = ''
      this.pleaseSelectData = [] // 清空请选择
      this.selectedData = [] // 清空已选择
      this.settingsRoles.push(role.id)
      this.roles.forEach(item => {
        if (this.settingsRoles.includes(item.id)) {
          this.selectedData.push(item)
        } else {
          this.pleaseSelectData.push(item)
        }
      })
    },
    clearSelected() {
      this.radio = ''
      this.pleaseSelectData = []
      this.selectedData = []
      this.settingsRoles = []
      this.pleaseSelectData.push(...this.roles)
    },
    handleClose(role) {
      this.radio = ''
      this.pleaseSelectData = []
      this.selectedData = []
      this.settingsRoles.splice(this.settingsRoles.indexOf(role.id), 1)
      this.roles.forEach(item => {
        if (this.settingsRoles.includes(item.id)) {
          this.selectedData.push(item)
        } else {
          this.pleaseSelectData.push(item)
        }
      })
    },
    /** 弹窗操作**/
    dialogPreCloseHandler: function (done) {
      this.dialogClose()
      done()
    },
    dialogOpen: function (_userId) {
      this.userId = _userId
      this.dialogVisible = true
      this.detailRequestHandler(_userId)
      // this.rolesRequestHandler()
    },
    dialogClose: function () {
      this.clearForm()
      this.dialogLoading = false
      this.dialogVisible = false
    },
    clearForm: function () {
      this.userId = undefined
      this.settingsRoles = []
      this.pleaseSelectData = []
      this.selectedData = []
      this.pleaseSelectFilter = ''
      this.selectedFilter = ''
      this.radio = ''
    },
    cancelHandler: function () {
      this.dialogClose()
      this.$emit('cancelEvent', 'cancel')
    },
    confirmHandler: function () {
      const _self = this
      _self.editRequestHandler() // 编辑
    },
    /** 数据请求**/
    editRequestHandler () {
      const _self = this
      // _self.dialogLoading = true
      const _package = {
        param: { userId: this.userId },
        data: this.settingsRoles
      }
      _settingUserRoleMapByUserId(_package).then(response => {
        let data = response.data;
        if (data.message != null && data.message != undefined) {
          this.$message({
            message: data.message,
            duraing: 3000,
            type: 'warning',
            showClose: true
          })
        }
        _self.dialogClose()
        _self.$emit('confirmEvent', 'confirm')
      }).catch(error => {
        // _self.$message.error(_reason.data.errorMsg)
        _self.dialogLoading = false
      })
    },
    detailRequestHandler (_userId) {
      const _self = this
      // _self.dialogLoading = true
      const _query = {
        userId: _userId,
        t: Date.parse(new Date())
      }
      _getUserOfRolesByUserId(_query).then(response => {
        _self.settingsRoles = response.data.result
        _self.dialogLoading = false
        this.rolesRequestHandler()
      }).catch(error => {
        // _self.$message.error(_reason.data.errorMsg)
        _self.dialogLoading = false
      })
    },
    rolesRequestHandler () {
      const _self = this
      _getRoleDataAll().then(response => {
        _self.roles = response.data
        _self.dialogLoading = false
        this.roles.forEach(item => {
          if (this.settingsRoles.includes(item.id)) {
            this.selectedData.push(item)
          } else {
            this.pleaseSelectData.push(item)
          }
        })
      }).catch(() => {
        _self.dialogLoading = false
      })
    }
  },
  components: {},
  computed: {
    // 计算属性，用于过滤数据
    filteredPleaseSelectData() {
      // 如果搜索框为空，返回原始数组
      if (!this.pleaseSelectFilter) {
        return this.pleaseSelectData;
      }
      // 使用filter方法根据搜索框内容过滤数组
      return this.pleaseSelectData.filter(item => {
        // 这里使用toLowerCase()来忽略大小写
        return item.roleName.toLowerCase().includes(this.pleaseSelectFilter.toLowerCase());
      });
    },
    filteredSelectedData() {
      // 如果搜索框为空，返回原始数组
      if (!this.selectedFilter) {
        return this.selectedData;
      }
      // 使用filter方法根据搜索框内容过滤数组
      return this.selectedData.filter(item => {
        // 这里使用toLowerCase()来忽略大小写
        return item.roleName.toLowerCase().includes(this.selectedFilter.toLowerCase());
      });
    }
  },
};
export default roleSettings
</script>
<style lang="scss">
    .role-settings {
      padding: 0 0 10px 0;
      .role-settings-left{
        border-right: 1px solid #EEEEEE;
        border-bottom: 1px solid #EEEEEE;
        .role-settings-title{
          background-color: #FAFAFA;
          height: 88px;
          .role-settings-title-text{
            padding: 10px 20px 10px 20px;
            height: 22px;
            font-weight: 600;
            font-size: 14px;
            line-height: 22px;
            color: #333333;
          }
          .role-settings-title-input{
            padding: 0 12px 16px 12px;
            height: 30px;
          }
        }
        .role-settings-body{
          height: 394px;
          padding: 10px 0 10px 0;
          .role-settings-body-item{
            height: 36px;
            padding: 5px 16px 5px 16px;
            font-weight: 400;
            font-size: 14px;
            line-height: 19.6px;
            color: #333333;
          }
        }
      }
      .role-settings-right{
        border-left: 1px solid #EEEEEE;
        border-bottom: 1px solid #EEEEEE;
        .role-settings-title{
          background-color: #FAFAFA;
          height: 88px;
          .role-settings-title-text{
            padding: 10px 20px 10px 20px;
            height: 22px;
            font-weight: 600;
            font-size: 14px;
            line-height: 22px;
            color: #333333;
          }
          .role-settings-title-input{
            padding: 0 12px 16px 12px;
            height: 30px;
          }
        }
        .role-settings-body{
          height: 394px;
          padding: 10px 0 10px 0;
          .role-settings-body-count{
            padding: 0 16px 10px 20px;
            height: 26px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            color: #333333;
            .role-settings-body-count-clear{
              font-size: 14px;
              font-weight: 400;
              line-height: 18px;
              color: #CF1A1C;
            }
            .role-settings-body-count-clear:hover {
              cursor: pointer;
            }
          }
          .role-settings-body-tag{
            padding: 0 16px 0 16px;
            .tag-style{
              margin: 0 5px 10px 5px;
              padding: 0 10px 0 10px;
              border-radius: 30px;
              font-size: 14px;
              font-weight: 400;
              line-height: 22px;
              color: #333333;
            }
          }
        }
      }
      .el-transfer-panel {
        position: relative;
        width: 45%;
      }
      .el-transfer__buttons {
        margin: 0;
        padding: 0;
        width: 10%;
        BUTTON {
          margin: 0 auto;
          margin-bottom: 10px;
          SPAN {
            font-size: 12px;
          }
        }
      }
    }
</style>
