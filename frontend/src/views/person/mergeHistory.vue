<!--
*@ mergeHistory
*<AUTHOR>
*@date 2024/3/4 14:14
-->
<template>
  <div>
    <el-table
        :data="tableData"
        style="width: 100%;margin-bottom: 20px;color: #000"
        :font-size="14"
    >
      <el-table-column type="index" align="center" label="序号" width="70"></el-table-column>
      <el-table-column align="center" label="被合并账号" prop="userName">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="账号合并时间" prop="createTime">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button round type="primary" size="mini" style="margin-left: 10px;"  @click="clickCancelMerge(scope.row)" v-if="scope.row.mergeFlag === '0'">解除合并</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { _cancelMerge, queryTableDate } from "@/api/person-api";

export default {
  name: "mergeHistory",
  props: {
    personId: {
      type: String,
      default: ''
    }
  },
  components: {

  },
  data() {
    return {
      // table 列表数据
      tableData: [],
    }
  },
  created() {
    this.queryTableDate()
  },
  mounted() {
  },
  methods: {
    clickCancelMerge (row) {

      let param = {
        historyId: row.id,
        userName: row.userName
      }

      _cancelMerge(param).then(res => {
        if (res.data.success && res.data.result) {
          this.$message.info('解除合并成功');
          this.queryTableDate();
          this.$emit('refreshUser');
        } else {
          this.$message.error('解除合并失败');
        }

      })

    },
    queryTableDate () {
      let param = {
        id: this.personId
      }
      queryTableDate(param).then(res => {
        this.tableData = res.data.tableData
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
