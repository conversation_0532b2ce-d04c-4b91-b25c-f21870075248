<style rel="stylesheet/scss" lang="scss" scoped>
</style>
<template>
  <el-dialog :title="'用户 - 密码重置'"
             width="40%"
             :visible.sync="dialogVisible"
             :close-on-click-modal="false"
             :modal-append-to-body="false"
             :append-to-body="true"
             :before-close="dialogPreCloseHandler">
    <div class="password-reset-editor" v-loading="dialogLoading">
      请点击确认按钮进行密码重置，重置后密码将通过手机短信方式发送到对应用户的手机号码上，请注意查收。
      <el-form :model="editorModel" ref="editorForm" label-width="100px" style="border: 0px">
        <el-input v-model="editorModel.id" type="hidden"></el-input>
        <!--        <el-row>
                  <el-col :span="24">
                    <el-form-item label="设置密码" prop="newPassword">
                      <el-input v-model="editorModel.newPassword" :maxlength="20" :minlength="8" type="password" show-password></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="确认密码" prop="confirmPassword">
                      <el-input v-model="editorModel.confirmPassword" :maxlength="20" :minlength="8" type="password" show-password></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>-->
      </el-form>
      <div align="center" style="margin-top:10px">
        <el-button type="primary" size="small" @click="confirmHandler()">确定</el-button>
        <el-button size="small" @click="cancelHandler()">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex'
import md5 from 'blueimp-md5'
import {
  _validatePasswords,
  _validatePasswordsLength,
  _validatePasswordsSameChar,
  _validatePasswordsUserName,
  _validatePasswordsSpace, _validatePassword
} from '../../utils/validate'
import { _createPassword } from "../../utils/createPassword";
import { _resetUserAccountPassword } from "../../api/person-api";

const passwordReset = {
  name: 'passwordReset',
  created: function () {
  },
  updated: function () {
  },
  mounted: function () {
  },
  activated: function () {
  },
  data: function () {
    const _self = this
    const validatePassword = function (rule, value, callback) {
      if (!_self.userName) {
        callback(new Error('未获取到该用户名'))
        return
      }
      if (!_validatePassword(value)) {
        callback(new Error("密码不符合规则：【长度8-32位，需要包含数字、大小写字母、特殊符号】"))
        return
      }
      // if(!_validatePasswordsSpace(value)){
      //   callback(new Error('密码不可包含空格'))
      //   return
      // }
      // if(!_validatePasswordsLength(value)){
      //   callback(new Error('请输入8-20位密码'))
      //   return
      // }
      // if (!_validatePasswords(value)) {
      //   callback(new Error('密码必须包含大写字母、小写字母、数字以及特殊符号'))
      //   return
      // }
      // if(!_validatePasswordsSameChar(value)){
      //   callback(new Error('密码不能包含连续三个相同的字符'))
      //   return
      // }
      // if(!_validatePasswordsUserName(value , _self.userName)){
      //   callback(new Error('密码不能包含用户名'))
      //   return
      // }
      if (_self.editorModel.confirmPassword && value !== _self.editorModel.confirmPassword) {
        callback(new Error('两次密码输入不一致'))
        return
      }
      callback()
    }
    const checkPassword = function (rule, value, callback) {
      if (value !== _self.editorModel.newPassword) {
        callback(new Error('两次密码输入不一致'))
        return
      }
      callback()
    }
    return {
      // 弹窗控制
      dialogVisible: false,
      dialogLoading: false,
      // 数据
      editorModel: {
        id: undefined,
        telephone: undefined,
        userName: undefined,
        seriakey: undefined,
        // 新密码
        newPassword: '',
        // 确认密码
        confirmPassword: ''
      },
      userName: ""
      // 校验
      /* editorRules: {
           newPassword: [
           {required: true, message: '请设置密码', trigger: 'blur'},
           {validator: validatePassword, trigger: 'blur'}
         ],
           confirmPassword: [
           {required: true, message: '请输入确认密码', trigger: 'blur'},
           {validator: checkPassword, trigger: 'blur'}
         ]
       } */
    }
  },
  methods: {
    /** 弹窗操作**/
    dialogPreCloseHandler: function (done) {
      this.dialogClose()
      done()
    },
    dialogOpen: function (telephone, _id, userName, seriakey) {
      this.editorModel.id = _id
      this.editorModel.telephone = telephone
      this.editorModel.userName = userName
      this.editorModel.seriakey = seriakey

      this.userName = userName
      this.dialogVisible = true
    },
    dialogClose: function () {
      this.clearForm()
      this.dialogLoading = false
      this.dialogVisible = false
    },
    clearForm: function () {
      this.$refs.editorForm.clearValidate()
      this.$refs.editorForm.resetFields()
    },
    cancelHandler: function () {
      this.dialogClose()
      this.$emit('cancelEvent', 'cancel')
    },
    confirmHandler: function () {
      const _self = this
      this.$refs.editorForm.validate(function (valid) {
        if (valid) {
          _self.editRequestHandler() // 编辑
        } else {
          return false
        }
      })
    },
    /** 数据请求**/
    editRequestHandler: function () {
      const _self = this;
      /* this.editorModel.newPassword=md5(this.editorModel.newPassword)
       this.editorModel.confirmPassword=md5(this.editorModel.confirmPassword) */
      _resetUserAccountPassword(_self.editorModel).then(response => {
        _self.dialogClose()
        _self.$emit('confirmEvent', 'confirm')
      }).catch(error => {
        _self.$message.error(_reason.data.errorMsg)
        _self.dialogLoading = false
      });
    }
  },
  components: {},
  computed: {
    ...mapGetters([])
  }
}
export default passwordReset
</script>

<style lang="scss" scoped>
</style>
