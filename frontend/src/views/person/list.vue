<template>
  <div class="user-list">
    <div class="person-list-div" style="">
      <div class="main-header">
        <i></i>
        <span>内部人员信息</span>
      </div>
      <div class="m-c">
        <el-card
            class="box-card box-card-search"
            shadow="never"
            :body-style="{ border: none }"
        >
          <el-row :gutter="10" class="row-form-list">
            <el-col :span="3">
              <el-select
                  size="small"
                  class="full"
                  clearable
                  filterable
                  placeholder="所属机构"
                  :show-overflow-tooltip="true"
                  v-model="query.orgId"
              >
                <el-option
                    :key="'org' + index"
                    :label="item.orgName"
                    :value="item.orgId"
                    v-for="(item, index) in orgList"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select
                  size="small"
                  class="full"
                  clearable
                  filterable
                  placeholder="锁定状态"
                  v-model="query.userLockStatus"
              >
                <el-option
                    :key="'org' + index"
                    :label="item.name"
                    :value="item.value"
                    v-for="(item, index) in lockStatusList"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input
                  size="small"
                  suffix-icon="el-icon-search"
                  placeholder="请输入姓名，账号"
                  v-model="query.searchString"
              ></el-input>
            </el-col>
            <el-col :span="4" class="text-left box-card-search-btns">
              <el-button @click="search" type="primary" size="small" round>查询</el-button>
              <el-button @click="clearClick" size="small" round>清空</el-button>
            </el-col>
            <el-col :span="10">


              <!--导入内部用户--- 临时-->
              <el-upload
                  style="display: inline-block"
                  :http-request="customUpload"
                  :multiple="false"
                  :show-file-list="false"
                  :before-upload="beforeUpload">
                <el-button  v-if="importPersonStatus" round type="primary" size="small" style="margin-left: 10px;">导入内部用户</el-button>
              </el-upload>


            </el-col>
          </el-row>
        </el-card>
        <el-card class="box-card m-t-20 box-card-btns" shadow="never">
          <el-col :span="12" class="text-left">
            <el-link
                :underline="false"
                type="primary"
                @click="openEditPersonModal(null)"
            >
              <i class="iconfont icon-gonggaoicon_tianjia" style="color: #D6001D;"></i>
              <span class="btn">新增</span>
            </el-link>
            <!--            <el-link :underline="false" type="primary" @click="batchDelete">-->
            <!--              <i class="iconfont ic-trash" style="color: #D6001D;"></i>-->
            <!--              <span class="btn">批量删除所选人员</span>-->
            <!--            </el-link>-->
            <el-link :underline="false" type="primary" @click="exportUserInfo">
              <i class="iconfont ic-import" style="color: #D6001D;"></i>
              <span class="btn">导出</span>
            </el-link>
          </el-col>
        </el-card>
        <el-card class="box-card m-t-20 box-card-btns" shadow="never">
          <el-row>
            <el-col :span="12"> 用户列表</el-col>
          </el-row>
          <el-table
              :data="tableData"
              :default-sort="{prop: personName, order: descending}"
              @selection-change="handleSelectionChange"
              @sort-change="handleSortChange"
              class="m-t-20 full"
              style="width: 100%;margin-bottom: 20px;color: #000"
              :font-size="14"
              header-row-class-name="custom-header-class-list"
          >
<!--            <el-table-column type="selection" width="55" :selectable="tableSelectable"></el-table-column>-->
            <!--<el-table-column label="序号" width="50px" align="center">-->
            <!--<template scope="scope">-->
            <!--<span v-text="getIndex(scope.$index)"></span>-->
            <!--</template>-->
            <!--</el-table-column>-->
            <!--                    <el-table-column label="id" width="100px" align="center" prop="id">-->
            <!--                    </el-table-column>-->
            <el-table-column
                align="center"
                label="姓名"
                min-width="128px"
                sortable
                prop="personName"
            >
              <template slot-scope="scope">
                <span class="el-table-name-icon">
                  <i class="iconfont icon-ic-male male" v-if="scope.row.sex === '1'"> </i>
                  <i class="iconfont icon-ic-female female" v-else-if="scope.row.sex === '2'"></i>
                </span>
                <p class="el-table-name">
                  {{ scope.row.personName }}
                </p>
              </template>
            </el-table-column>
            <el-table-column align="center" label="用户名" prop="userName">
              <template slot-scope="scope">
                <span>{{ scope.row.userName }}</span>
                <!--              <span v-if="scope.row.userName">{{ scope.row.userName }}</span>-->
                <!--              <span v-else>-->
                <!--                  <el-link-->
                <!--                      :underline="false"-->
                <!--                      type="primary"-->
                <!--                      @click="setUserModal(scope.row)"-->
                <!--                  >-->
                <!--                  <span class="btn">设置用户</span>-->
                <!--            </el-link>-->
                <!--              </span>-->
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="手机号码"
                prop="telephone"
                width="128"
            >
              <template slot-scope="scope">
                <span>{{ hidePhoneNum(scope.row.telephone) }}</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="所属机构"
                sortable
                prop="orgNames"
                min-width="128px"
            >
              <template slot-scope="scope">
                <span>{{ getOrgNamesByOrgIds(scope.row.orgId) }}</span>
              </template>
            </el-table-column>
            <!--                    <el-table-column align="center" label="身份" prop="identityNames">-->
            <!--                    </el-table-column>-->
            <el-table-column
                align="center"
                label="来源"
                prop="source"
                width="160"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.source === 'HGGL'">回购管理</span>
                <span v-else-if="scope.row.source === 'GQJL'">股权激励</span>
                <span v-else-if="scope.row.source === 'TDGF'">合规交易</span>
                <span v-else>平台新增</span>
              </template>
            </el-table-column>
            <el-table-column
                align="center"
                label="用户锁定状态"
                prop="userLockStatus"
                min-width="116px"
            >
              <template slot-scope="scope">
                <span
                    v-if="
                    scope.row.userName != null && scope.row.userLockStatus === '1'
                  "
                >已锁定</span
                >
                <span v-else-if="scope.row.userName != null">未锁定</span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" min-width="156px">
              <template slot-scope="scope">
                <span v-if="scope.row.userName != null">
                  <i class="el-icon-lock custom-icon-button" title="锁定"
                     v-if="scope.row && scope.row.userLockStatus === '0'" @click="lockStatusClick(scope.row)">
                  </i>
                  <i class="el-icon-unlock custom-icon-button" title="解锁" v-else
                     @click="lockStatusClick(scope.row)"></i>
                </span>
                <i class="iconfont ic-set-person custom-icon-button" title="设置平台角色"
                   v-if="scope.row.userName != null && (currentuserId != scope.row.id || isadmin =='1' )"
                   @click="onSettingsHandler(scope.row.id)">
                </i>
                <i class="iconfont ic-faceid custom-icon-button" title="设置接入系统角色"
                   v-if="scope.row.userName != null && (currentuserId != scope.row.id || isadmin =='1' )"
                   @click="onOuterSettingsHandler(scope.row.id, scope.row.userType, scope.row.userName)">
                </i>
<!--                <i class="iconfont ic-refresh custom-icon-button" title="密码重置"-->
<!--                   v-if="scope.row.userName != null"-->
<!--                   @click="onResetHandler(scope.row.telephone,scope.row.id , scope.row.userName,scope.row.seriakey)">-->
<!--                </i>-->
                <i class="iconfont ic-edit custom-icon-button" title="编辑" v-if="scope.row.id !== '1'"
                   @click="openEditPersonModal(scope.row)"></i>
                <!--                <i class="iconfont ic-import-file custom-icon-button" title="合并历史" v-if="scope.row.id !== '1'"
                                   @click="showMergeHistory(scope.row.id)"></i>-->
                <!--                <i class="iconfont ic-trash custom-icon-button" title="删除" v-if="scope.row.id !== '1'"-->
                <!--                   @click="deleteClick(scope.row.id, scope.row.seriakey,scope.$index, scope.row.telephone, scope.row.orgId)"></i>-->
              </template>
            </el-table-column>
          </el-table>
          <!--          <papers ref="paper" @searchTable="paperSearch" :total="total" :size="10"></papers>-->
          <el-pagination
              style="text-align: center;"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="query.startRow"
              :page-sizes="[10, 20, 50, 200]"
              :page-size="query.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
          </el-pagination>
        </el-card>
        <!--E form表单-->
      </div>
      <edit-person
          :isShow="showModal"
          :updateData="currentRow"
          @editPersonClose="editPersonClose"
      ></edit-person>
      <set-user
          :isShow="showSetUserModal"
          :updateData="currentRow"
          @setUserClose="setUserClose"
      >
      </set-user>
      <set-role ref="roleSettings"></set-role>
      <!--用户账户密码重置-->
      <password-reset ref="passwordReset"></password-reset>
      <!-- 设置接入系统角色 -->
      <set-outer-role ref="setOuterRole"></set-outer-role>
      <el-dialog
          title="用户合并历史"
          :visible.sync="showMergeHistoryFlag"
          v-if="showMergeHistoryFlag"
          width="50%">
        <merge-history ref="mergeHistory" :personId="mergeHistoryId"></merge-history>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {showMessage, showConfirm} from "stock-vue-plugin/lib/utils/message";
import papers from "@/components/Pagination/papers.vue";
import {
  _deleteChooseUsers,
  _getPersonList,
  _setAvailableStatus,
  _setLockStatus,
  exportUserInfo,
  _getOrgListByType, _resetUserAccountPassword
} from "@/api/person-api";
import editPerson from "../../views/person/editPerson";
import SetUser from "../../views/person/setUser";
import SetRole from "../../views/person/setRole";
import passwordReset from '../../views/person/passwordReset'
import store from "@/store";
import SetOuterRole from "../../views/person/setOuterRole";
import MergeHistory from "@/views/person/mergeHistory.vue";
import {_importInnerUserInfo} from "@/api/system/infoSync-api";
import {selectButtonByName} from "../../api/mqManage/MQManage-api";

export default {
  components: {MergeHistory, SetUser, editPerson, papers, SetRole, passwordReset, SetOuterRole},
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      importPersonStatus: false,// 控制导入内部用户按钮显隐状态
      isadmin: store.state.app.info.isAdmin,
      currentuserId: store.state.app.info.id,
      // 选中的数据
      multipleSelection: [],
      multipleSelectionSer: [],
      // 新增用户弹出框是否打开
      showModal: false,
      // 设置用户弹出框是否打开
      showSetUserModal: false,
      // 设置权限弹出框是否打开
      showSetPermissionModal: false,
      // 设置绑定弹出框是否打开
      query: {
        // 机构id
        orgId: "",
        // 用户可用状态
        userAvailableStatus: "",
        // 用户锁状态
        userLockStatus: "",
        // 搜索字符串
        searchString: "",
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 10
      },
      // 当前编辑行
      currentRow: {},
      // 数据总数
      total: 10,
      // table 列表数据
      tableData: [],
      // 组织结构列表
      orgList: [],
      // 锁定状态列表
      lockStatusList: [
        {name: "已锁定", value: "1"},
        {name: "未锁定", value: "0"}
      ],
      // 用户状态列表
      availableStatusList: [
        {name: "可用", value: "1"},
        {name: "不可用", value: "0"}
      ],
      mergeHistoryId: '',
      showMergeHistoryFlag: false,
      editorModel: {
        id: undefined,
        telephone: undefined,
        userName: undefined,
        seriakey: undefined,
        // 新密码
        newPassword: '',
        // 确认密码
        confirmPassword: ''
      },
    };
  },
  mounted() {
    // 获取用户列表数据
    this.getUserData();
    this.getOrgList();
    this.checkButtonStatus();// 判断导入上市公司按钮是否显示
  },
  methods: {
    // 隐藏电话号码中间四位
    hidePhoneNum(phone) {
      if (phone != null) {
        var str = String(phone)
        var len = str.length;
        var prev, next;
        if (len >= 7) {
          prev = str.slice(-len, -8)
          next = str.slice(-4)
          str = prev + "****" + next
        } else if (len < 7 && len >= 6) {
          prev = str.slice(-len, -4)
          next = str.slice(-2)
          str = prev + "**" + next
        }
        return str
      }

    },
    customUpload(fileObj) {
      // this.importPersonStatus = false;
      // 在这里编写自定义的上传逻辑
      let data = new FormData();
      data.append('files', fileObj.file);
      _importInnerUserInfo(data).then(res => {
        if (res.data.success) {
          this.$message.info("导入" + res.data.result + "条数据");
          window.location.reload();
        } else {
          this.$message.error("导入失败" + res.data.errorMsg);
          // this.importPersonStatus = true;
        }
      })
      this.checkButtonStatus();
    },
    beforeUpload(file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const isXls = file.type === 'application/vnd.ms-excel';
      if (!isXlsx && !isXls) {
        this.$message.error('只能上传xls、xlsx文件');
        return false; // 取消上传
      }
      return true; // 立即上传文件
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 0
      this.getUserData();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.getUserData();
    },
    exportUserInfo() {
      exportUserInfo(this.query);
    },
    onResetHandler(telephone, _id, userName, seriakey) {
      this.$confirm('请点击确认按钮进行密码重置，重置后密码将通过邮件方式发送到对应用户的邮箱上，请通知用户注意查收。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.editorModel.id = _id
        this.editorModel.telephone = telephone
        this.editorModel.userName = userName
        this.editorModel.seriakey = seriakey
        _resetUserAccountPassword(this.editorModel).then(res => {
          if (res.data.result) {
            this.$message({
              type: 'info',
              message: '重置成功'
            });
          } else {
            this.$message({
              type: 'error',
              message: '重置失败'
            });
          }
        })
      })
      // this.$refs.passwordReset.dialogOpen(telephone, _id, userName, seriakey)
    },
    // paperSearch () {
    //   let form = this.$refs.paper.submitData;
    //   this.query.startRow = form.startRow;
    //   this.query.pageSize = form.pageSize;
    //   this.getUserData();
    // },
    onSettingsHandler(_id) {
      this.$refs.roleSettings.dialogOpen(_id)
    },
    onOuterSettingsHandler(_id, _userType, _userName) {
      this.$refs.setOuterRole.open(_id, _userType, _userName)
    },
    /* 获取用户列表数据
     * */
    getUserData() {
      // 查询内部用户
      this.query.userType = '0';
      _getPersonList(this.query).then((res) => {
        if (res.status == '200') {
          this.tableData = res.data.tableData;
          this.total = res.data.total;
          if (this.tableData.length === 0) {
            this.query.startRow = 0;
            _getPersonList(this.query).then((res) => {
              this.tableData = res.data.tableData;
              this.total = res.data.total;
            })
          }
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    getOrgList() {
      _getOrgListByType({userType: '0'}).then((res) => {
        if (res.data.success) {
          this.orgList = res.data.result;
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    /* 查询
     * */
    search() {
      this.getUserData();
    },
    /* 清空条件
     * */
    clearClick() {
      this.query = {
        // 机构id
        orgId: "",
        // 用户可用状态
        userAvailableStatus: "",
        // 用户锁状态
        userLockStatus: "",
        // 搜索字符串
        searchString: "",
        // 页码
        startRow: 1,
        // 每页显示多少条数据
        pageSize: 10
      },// 重置数据
      this.getUserData(); // 重新获取数据
    },
    /* 批量删除
     * */
    batchDelete() {
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage("info", this.$messageText["deleteSelectNone"]);
        return;
      }
      let index = -1;
      // 当前只选中了一条数据，查找当前数据的索引
      if (this.multipleSelection.length === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === this.multipleSelection[0].id) {
            index = i;
          }
        }
      }
      showConfirm(
          this.$messageText["deleteContent"],
          this.$messageText["deleteTitle"],
          this,
          "deleteClickData",
          {idList: this.multipleSelection, SeriakeyList: this.multipleSelectionSer, index: index}
      );
    },
    /* table checkbox 选中事件
     * val:选中得数据          telephone: telephone,
        orgId: orgId
     * */
    handleSelectionChange(val) {
      this.multipleSelection = val.map((i) => {
        return {id: i.id, telephone: i.telephone, orgId: i.orgId};
      });
      this.multipleSelectionSer = val.map((i) => {
        return {id: i.id, seriakey: i.seriakey};
      });
    },
    /* 关闭更新用户
     * */
    editPersonClose() {
      this.showModal = false;
      this.getUserData();
    },
    /* 关闭设置用户页面
     * */
    setUserClose() {
      this.showSetUserModal = false;
      this.getUserData();
    },
    /** 关闭权限设置页面 **/
    setPermissionClose() {
      this.showSetPermissionModal = false;
    },
    /* 编辑角色
     * item:要编辑的数据
     * */
    openEditPersonModal(item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showModal = true;
    },
    /* 设置用户
     * item:要编辑的数据
     * */
    setUserModal(item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showSetUserModal = true;
    },
    /* 设置权限
     * item:要编辑的数据
     * */
    setPermissionModal(item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showSetPermissionModal = true;
    },
    // 表格序列号
    getIndex($index) {
      // 表格序号
      // (当前页 - 1) * 当前显示数据条数 + 当前行数据的索引 + 1
      return (this.pageNum - 1) * this.query.pageSize + $index + 1;
    },
    availableStatusClick(row) {
      if (row.userAvailableStatus === "0") {
        row.userAvailableStatus = "1";
      } else {
        row.userAvailableStatus = "0";
      }
      _setAvailableStatus(row).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    lockStatusClick(row) {
      if (row.userLockStatus === "0") {
        row.userLockStatus = "1";
      } else {
        row.userLockStatus = "0";
      }
      _setLockStatus(row).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    deleteClickData(param) {
      _deleteChooseUsers(param).then((res) => {
        if (res.data.success) {
          this.getUserData();
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
    },
    deleteClick(id, seriakey, index, telephone, orgId) {
      let idparam = {
        id: id,
        seriakey: seriakey,
      };
      let param = {
        id: id,
        telephone: telephone,
        orgId: orgId
      };
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: [param],
        SeriakeyList: [idparam],
        index: index
      });
    },
    tableSelectable(row) {
      return row.userName != 'admin';
    },
    showMergeHistory(id) {
      this.mergeHistoryId = id
      this.showMergeHistoryFlag = true
    },
    checkButtonStatus() {
      selectButtonByName({moduleName: 'person', zhName: '内部人员信息', buttonName: '导入内部用户'}).then(res => {
        if (res.data.success && res.data.result !== null) {
          if (res.data.result.status === '1') {
            this.importPersonStatus = true;
          } else {
            this.importPersonStatus = false;
          }
        }
      })
    },
    getOrgNamesByOrgIds(orgIds) {
      if (orgIds && orgIds.length > 0) {
        // 将逗号分隔的orgIds字符串转换为数组
        const orgIdsArray = orgIds.split(',');
        // 过滤出存在的orgName并放入新数组
        const orgNames = this.orgList.filter(org => orgIdsArray.includes(org.orgId)).map(org => org.orgName);
        // 将orgName数组转换为逗号分隔的字符串
        return orgNames.join(',');
      } else {
        return '- -'
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.user-list {
  padding: 20px;

  .user-list__avatar {
    width: 50px;
    height: 50px;
  }

  .person-list-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
}

.main-header {
  position: relative;
  margin: 0;
  // padding-left: 8px;
  // border-left: 4px solid #999999;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  color: $--el-color-text-primary;
  line-height: 22px;

  span {
    //算上边线本身4px一共12px
    padding-left: 12px;
  }

  //平行四边形边线
  i {
    position: absolute;
    top: 2px;
    display: inline-block;
    transform: skewY(-15deg);
    height: 18px;
    width: 4px;
    background-color: $--el-color-primary;
  }
}

a.btn,
span.btn {
  color: #333333;

  i {
    font-size: 16px;
  }

  .greenColor {
    color: #0fa958 !important;
  }
}

.el-card {
  border: none;
}

/deep/ .el-card__body,
.el-main {
  padding: 0;
}

.box-card-btns {
  a {
    margin-right: 13px;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;

    i {
      color: #1990fe;
    }

    span {
      margin-left: 0;
    }
  }
}

.box-card-search {
  .box-card-search-btns {
    & > :first-child {
      margin-left: 5px;
    }
  }
}

.el-table-name {
  display: inline-block;
  width: 40%;
  text-align: left;
}

.el-table-name-icon {
  width: 60%;
  text-align: right;

  .male {
    color: #76c3ff;
  }

  .female {
    color: #ff74b2;
  }
}
</style>
<style lang="scss">
.el-card__body, .el-main {
  padding: 0;
  text-align: left;
  line-height: 20px;
}

.row-form-list {
  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}

.custom-header-class-list {

  th {
    color: #777 !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    background: #F6F7F9;
    padding: 10px 0px !important;

  }
}

</style>
