<!--股权激励-->
<template>
  <div class="container">
    <div class="title-con">
      <div class="title-line"></div>
      <div class="title-name">股权激励管理</div>
      <div class="title-more" @click="jumpMore()">
        <div class="title-more-text">更多</div>
        <div class="title-more-img">
          <img src="@/assets/images/homePage/arrowRight.png" alt="更多图标"/>
        </div>
      </div>
    </div>
    <div class="equitycon" v-if="noData == false">
      <div
          class="equitycon-block"
          v-for="item in equityList.slice(0,2)"
          :key="item.id"
      >
        <div class="equity-titsign">
          <div class="equity-tit">{{ item.planName }}</div>
          <div class="equity-titcon">
            <div
                :class="[
                'equity-sign',
                item.state === '1'
                  ? 'signfir'
                  : item.state === '2'
                  ? 'signsec'
                  : 'signthir',
              ]"
            >
              {{
                item.state === "1"
                    ? "进行中"
                    : item.state === "2"
                    ? "已完成"
                    : "已终止"
              }}
            </div>
            <div
                :class="[
                'equity-sign',
                'signfir'
              ]"
            >
              {{
                item.equType === "1"
                    ? "限制性股票"
                    : item.equType === "2"
                        ? "股票期权"
                        : "第二类限制性股票"
              }}
            </div>
<!--            <div-->
<!--                :class="[-->
<!--                'equity-sign',-->
<!--                item.equStepName === '等待期' || item.equStepName === '锁定期'-->
<!--                  ? 'signfir'-->
<!--                  : item.equStepName === '第1个行权期' || item.equStepName === '第1个归属期' || item.equStepName === '第1个解除限售期'-->
<!--                  ? 'signsec'-->
<!--                  : item.equStepName === '第2个行权期' || item.equStepName === '第2个归属期' || item.equStepName === '第2个解除限售期'-->
<!--                  ? 'signthir'-->
<!--                  : item.equStepName === '第3个行权期' || item.equStepName === '第3个归属期' || item.equStepName === '第3个解除限售期'-->
<!--                  ? 'signfir'-->
<!--                  : 'signfir',-->
<!--              ]"-->
<!--            >-->
<!--              {{ item.equStepName }}-->
<!--            </div>-->
          </div>
        </div>
        <div class="equity-datacon">
          <div class="equity-data">权益总量：{{ item.equNumSum ? Number(item.equNumSum).toLocaleString() : "--" }}</div>
          <div class="equity-data">授予人数：{{ item.personNum ? item.personNum : "--" }}人</div>
          <div class="equity-data">
            首次公告日：{{ item.exerciseStartDate == "" ? "--" : item.exerciseStartDate }}
          </div>
          <div class="equity-data">实际授予股数：{{ item.equStockNum ? Number(item.equStockNum).toLocaleString() : "--" }}股</div>
<!--          <div class="equity-data">实际授予股数(量调后)：{{ item.priceVolumeNum ? Number(item.priceVolumeNum).toLocaleString() : "&#45;&#45;" }}股</div>-->
        </div>
        <div class="equity-progresscon">
          <div class="equity-data">
            {{
              item.equType == "1"
                  ? "解锁比例："
                  : item.equType == "2"
                  ? "行权比例："
                  : "归属比例："
            }}
          </div>
          <div class="progressline">
            <el-progress
                :percentage="item.proportion"
                :stroke-width="12"
                color="#CF1A1C"
                :show-text="false"
            ></el-progress>
          </div>
          <div class="progressline-percen">{{ item.proportion ? item.proportion > 100 ? 100 : item.proportion : 0 }}%</div>
        </div>
      </div>
    </div>
    <div class="content" v-if="noData == true" style="text-align: center">
      <div style="margin: 0 auto; padding-bottom: 20px">
        <img src="@/assets/images/noData.png" style="width: 141px"/>
        <div>暂无数据</div>
      </div>
    </div>
    <choice-user
        :isShow="choiceShow"
        :updateData="choiceInfo"
        title="选择登入账户"
        @choiceClose="choiceClose"
        width="30%"/>
  </div>
</template>

<script>
import {listAllStock} from "@/api/block-api";
import {_getOpenedSystems,_getRoleListGroupBySystem} from "@/api/common-api";
import store from "@/store";
import choiceUser from "@/components/Layouts/SlideMenu/choiceUser.vue";
import {_getChildrenInfo} from "@/api/user-api";
import {_getOrgName, _getOrgNameList} from "@/api/organization-api";
import {showMessage} from "stock-vue-plugin/lib/utils/message";

export default {
  name: "equityIncentiveManagement",
  components: {choiceUser},
  props: ["companyCode", "token"],
  data () {
    return {
      systemSign: 'GQJL',
      noData: true,
      mySwiper: null,
      equityList: [
        // {
        //   planName: "2022年股票期权",
        //   state: "1",
        //   equStepName: "等待期",
        //   equNumSum: "100000",
        //   personNum: "65",
        //   equType: "1",
        //   exerciseStartDate: "2022-10-15",
        //   proportion: "89.5",
        // }
      ],
      choiceShow:false,
      choiceInfo:{
        userName:'',
        childrenUserName:'',
        telephone:'',
        orgId:'',
        key:'',
        orgName:'',
        childrenOrgName:'',
      },
      userInfo: {
        key:'',
        userName:'',
        id:'',
        childrenUserName:'',
      },
    };
  },
  methods: {
    choiceClose(){
      this.choiceShow = false;
    },
    jumpMore () {
      var myArray = [];
      if (this.userInfo.userName === 'admin') {
        myArray = ['HGGL','TDGF','GQJL'];
      }
      _getRoleListGroupBySystem({ id: this.userInfo.id, userType: this.userInfo.userType }).then(res => {
        if (res.data.success) {
          let roles = res.data.result.roles;
          let checkedRoleIds = res.data.result.checkedRoleIds;
          checkedRoleIds.forEach(item => {
            roles.forEach(role => {
              if (role.children != undefined) {
                role.children.forEach(child => {
                  if (child.id === item) {
                    myArray.push(child.flag);
                  }
                })
              }
            })
          })
          if (myArray.indexOf("GQJL") === -1){
            showMessage('warning', "您没有操作该菜单的权限！");
          }else{
            this.getOpenedSystems();
          }
        }
      })
    },
    getOpenedSystems(){
      _getOpenedSystems({ menuName: this.systemSign }).then(res => {
        if (res.data.success) {
          if (res.data.result != null && res.data.result.length > 0) {
            let baseUrl = res.data.result[0].path;
            _getChildrenInfo({id: this.userInfo.id}).then(res=>{
              if (res.data.success && res.data.result !== null  && res.data.result.childrenUserName !== null && res.data.result.childrenLock === '0'){
                this.childrenInfo = res.data.result;
                this.choiceInfo.key = baseUrl;
                this.choiceInfo.userName = this.userInfo.userName;
                // this.choiceInfo.userName = this.userInfo.userName.includes("CSC") ? this.userInfo.userName : "CSC" + this.userInfo.userName;
                this.choiceInfo.childrenUserName = this.childrenInfo.childrenUserName.includes("YCSC") ? this.childrenInfo.childrenUserName : "YCSC" + this.childrenInfo.childrenUserName;
                this.choiceInfo.telephone = this.childrenInfo.telephone;
                this.choiceInfo.childrenOrgId = this.childrenInfo.childrenOrg;
                this.choiceInfo.orgId = this.userInfo.orgId;
                _getOrgName({id: this.userInfo.orgId}).then(res=>{
                  this.choiceInfo.orgName = res.data.result;
                })
                _getOrgNameList(this.childrenInfo.childrenOrgList).then(res=>{
                  this.choiceInfo.childrenOrgName = res.data.result.join(",");
                })
                this.choiceShow = true;
              }else{
                window.open(baseUrl + '&userName=' + this.userInfo.userName);
              }
            })
          }
        }
      })
    },
    listAllStock () {
      let param = {
        companyCode: this.companyCode
      };
      listAllStock(param).then((res) => {
        console.log(res,"股权激励")
        this.noData = true;
        if (res.data.result.success) {
          this.equityList = res.data.result.data;
          if (this.equityList && this.equityList.length > 0) {
            this.noData = false;
          }
        } else {
          console.log("接口返回出错:", res.data.result);
        }
      })
    }
  },
  created () {
    this.listAllStock();
    this.userInfo = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
  },
  mounted () {
  }
};
</script>

<style lang="scss" scoped>
.content {
  margin-top: 27px;
  display: flex;
}

.container {
  padding-top: 16px;
  // margin-bottom: 20px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;

  .title-con {
    height: 25px;
    position: relative;

    .title-line {
      position: absolute;
      top: 3.5px;
      left: 20px;
      width: 3px;
      height: 18px;
      background: #E03D3E;
    }

    .title-name {
      position: absolute;
      left: 30px;
      height: 25px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 18px;
      line-height: 25px;
      color: #333333;
    }

    .title-more {
      position: absolute;
      right: 17px;
      width: 44px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title-more-text {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 25px;
        height: 25px;
        color: #CF1A1C;
      }

      .title-more-img {
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 12px;
          height: 12px;
        }
      }
    }

    .title-more:hover {
      cursor: pointer;
    }
  }

  .equitycon {
    padding: 27px 24px 24px 24px;
    box-sizing: border-box;

    .equitycon-block {
      min-height: 144px;
      box-sizing: border-box;
      border: 1px solid #eeeeee;
      border-radius: 4px;
      margin-top: 20px;
      padding: 20px 24px 32px 24px;
      background: #FFFFFF33;
      box-shadow: 0px 10px 12px 0px #A1A3AC19;

    }

    .equitycon-block:nth-child(1) {
      margin-top: 0;
    }

    .equity-data {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }

    .progressline {
      width: 1000px;
      margin-left: 10px;
    }

    .progressline-percen {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-left: 10px;
    }

    .equity-titsign {
      display: flex;
      align-items: center;
    }

    .equity-datacon {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 16px 0;
    }

    .equity-progresscon {
      display: flex;
      align-items: center;
    }

    .equity-titcon {
      display: flex;
    }

    .equity-tit {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-right: 10px;
    }

    .equity-sign {
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0px 8px;
      height: 18px;
      border-radius: 4px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
      margin-right: 6px;
    }

    .signfir {
      background: #E8EDFE;
      border: 1px solid #D0DBFD;
      color: #425BAB;
    }

    .signsec {
      background: #FFF2E6;
      border: 1px solid #FFE5CC;
      color: #FF7D00;
    }

    .signthir {
      color: #FE5461;
      background: #FFEEEE;
      border: 1px solid #FFDDDF;
    }
  }

  /deep/ .el-progress-bar__outer {
    background-color: #FEF1F1!important;
  }
}
</style>
