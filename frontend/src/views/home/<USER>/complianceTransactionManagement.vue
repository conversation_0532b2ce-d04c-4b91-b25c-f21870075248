<!--合规交易-->
<template>
  <div class="container">
    <div class="title-con">
      <div class="title-line"></div>
      <div class="title-name">合规交易管理</div>
      <div class="title-more" @click="jumpMore()">
        <div class="title-more-text">更多</div>
        <div class="title-more-img">
          <img src="@/assets/images/homePage/arrowRight.png" alt="更多图标" />
        </div>
      </div>
    </div>
    <div class="transac" v-if="noData == false">
      <div class="transac-tit">
        <div class="shareholder-con">
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">{{ personCount }}</div>
              <div class="shareholder-unit">人</div>
            </div>
            <div class="shareholder-monit">已监控股东</div>
          </div>
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">{{ personShareCount }}</div>
              <div class="shareholder-unit">股</div>
            </div>
            <div class="shareholder-monit">管控股份</div>
          </div>
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">{{ numbers }}</div>
              <div class="shareholder-unit">次</div>
            </div>
            <div class="shareholder-monit">管控交易数</div>
          </div>
        </div>
      </div>
      <div class="transac-con"  v-loading="hgLoadding">
        <div
          class="transac-block"
          v-for="(item, index) in transactionList"
          :key="index"
        >
          <div class="content-tit">
            <div class="content-transcontro">
              <div class="content-transcontro-name">{{ item.personName }}</div>
<!--              <div class="content-transcontro-frequency">-->
<!--                管控交易数：{{ amountFormat(item.num, 0) }}次-->
<!--              </div>-->
            </div>
          </div>
          <div class="content-buyorsell">
            <div class="content-buy">
<!--              合规模拟客户下单默认就是100股-->
              <div class="content-buy-item">买入股份：100股</div>
              <div class="content-buy-item">买入日期：{{ item.opDate }}</div>
              <div class="content-buy-item itembefortag">买入合规试算</div>
              <el-tooltip :disabled="false" :content="item.contentBuy" placement="bottom" effect="light">
                <div slot="content" style="margin: 1rem;" v-html="item.contentBuy"></div>
              <div
                :class="[
                  'content-buy-tag',
                  item.buyFlag === '4'
                    ? 'tagfir'
                    : item.buyFlag === '1'
                    ? 'tagsec'
                    : item.buyFlag === '2'
                    ? 'tagthir'
                    : '',
                ]"
              >
                <span v-if="item.buyFlag == 1">通过</span>
                <span v-if="item.buyFlag == 2">禁止</span>
                <span v-if="item.buyFlag == 3">报错</span>
                <span v-if="item.buyFlag == 4">披露于暂停</span>
              </div>
              </el-tooltip>
            </div>
            <div class="content-sell">
              <div class="content-buy-item">卖出股份：100股</div>
              <div class="content-buy-item">卖出日期：{{ item.opDate }}</div>
              <div class="content-buy-item itembefortag">卖出合规试算</div>
              <el-tooltip :disabled="false"  placement="bottom"  effect="light">
                <div slot="content" style="margin: 1rem;" v-html="item.contentSale"></div>
              <div
                :class="[
                  'content-buy-tag',
                  item.saleFlag === '4'
                    ? 'tagfir'
                    : item.saleFlag === '1'
                    ? 'tagsec'
                    : item.saleFlag === '2'
                    ? 'tagthir'
                    : '',
                ]"
              >
                <span v-if="item.saleFlag == 1">通过</span>
                <span v-if="item.saleFlag == 2">禁止</span>
                <span v-if="item.saleFlag == 3">报错</span>
                <span v-if="item.saleFlag == 4">披露于暂停</span>
              </div>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content" v-if="noData == true" style="text-align: center">
      <div style="margin: 0 auto">
        <img src="@/assets/images/noData.png" style="width: 141px" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "@/mixins/common";
import { getCplTransInfo, getCplTransDetail } from "@/api/block-api";
import { _getOpenedSystems,_getRoleListGroupBySystem } from "@/api/common-api";
import {showMessage} from "stock-vue-plugin/lib/utils/message";

export default {
  name: "complianceTransactionManagement",
  props: ["companyCode", "token"],
  mixins: [common],
  data () {
    return {
      systemSign: 'TDGF',
      loading: false,
      noData: true,
      hgLoadding: true,
      personCount: '--', // 已监控股东
      personShareCount: '--', // 管控股份
      numbers: '--', // 管控交易数
      transactionList: [
        {
          personName: "",
          num: 0,
          buyNum: 0,
          buyDate: "",
          buyFlag: "",
          contentBuy: "",
          sellNum: 0,
          sellDate: "",
          saleFlag: "",
          contentSale: ""
        }
      ],
      userInfo: {
        key:'',
        userName:'',
        id:'',
        childrenUserName:'',
      },
    };
  },
  methods: {
    getCplTransInfo () {
      this.noData = true;
      getCplTransInfo({ companyCode: this.companyCode }).then((res) => {
        if (res.data.success) {
          // 买入卖出默认100，因为是试算
          this.noData = false
          this.personCount = this.amountFormat(res.data.result.personCount, 0);
          this.personShareCount = this.amountFormat(res.data.result.personShareCount, 2);
          this.numbers = this.amountFormat(res.data.result.numbers, 0);
        } else {
          console.log(res.data.errorMsg);
        }
      })
    },
    getCplTransDetail () {
      getCplTransDetail({ companyCode: this.companyCode }).then((res) => {
        if (res.data.success) {
          this.hgLoadding = false;
          // 买入卖出默认100，因为是试算
          this.transactionList = res.data.result.data;
        } else {
          console.log(res.data.errorMsg);
        }
      })
    },
    jumpMore () {
      var myArray = [];
      if (this.userInfo.userName === 'admin') {
        myArray = ['HGGL','TDGF','GQJL'];
      }
      _getRoleListGroupBySystem({ id: this.userInfo.id, userType: this.userInfo.userType }).then(res => {
        if (res.data.success) {
          let roles = res.data.result.roles;
          let checkedRoleIds = res.data.result.checkedRoleIds;
          checkedRoleIds.forEach(item => {
            roles.forEach(role => {
              if (role.children != undefined) {
                role.children.forEach(child => {
                  if (child.id === item) {
                    myArray.push(child.flag);
                  }
                })
              }
            })
          })
          if (myArray.indexOf("TDGF") === -1){
            showMessage('warning', "您没有操作该菜单的权限！");
          }else{
            this.getOpenedSystems();
          }
        }
      })
    },
    getOpenedSystems(){
      _getOpenedSystems({ menuName: this.systemSign }).then(res => {
        if (res.data.success) {
          if (res.data.result != null && res.data.result.length > 0) {
            // window.open(res.data.result[0].path  + '&userName=' + this.userInfo.userName);
            // 合规测试环境与平台暂时服务器不通,不能通过token校验,先跳过
            // window.open(res.data.result[0].path  + '&YTHUserName=' + this.userInfo.userName);
            window.open(res.data.result[0].path);
          }
        }
      })
    }
  },
  created () {
    this.userInfo = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
  },
  mounted () {
    this.getCplTransInfo();
    this.getCplTransDetail();
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-top: 16px;
  padding-bottom: 16px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  .title-con {
    height: 25px;
    position: relative;
    .title-line {
      position: absolute;
      left: 20px;
      top: 3.5px;
      width: 3px;
      height: 18px;
      background: #E03D3E;
    }
    .title-name {
      position: absolute;
      left: 30px;
      height: 25px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 18px;
      line-height: 25px;
      color: #333333;
    }
    .title-more {
      position: absolute;
      right: 17px;
      width: 44px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-more-text {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 25px;
        height: 25px;
        color: #CF1A1C;
      }
      .title-more-img {
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 12px;
          height: 12px;
        }
      }
    }
    .title-more:hover {
      cursor:pointer;
    }
  }
  .transac {
    padding: 16px 20px 24px 20px;
    box-sizing: border-box;
  }
  .transac-tit {
    background: url("../../../assets/images/homePage/interTranc.png") no-repeat;
    background-size: 100% 100%;
    background-color: rgba(255, 224, 225, 0.1);
    height: 100px;
    width: 1200px;
    display: flex;
    .shareholder-con {
      width: 842px;
      margin: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .shareholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .shareholder-numcon {
        display: flex;
        align-items: baseline;
        margin-bottom: 6px;
      }
      .shareholder-monit {
        color: #666666;
        height: 22px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .shareholder-num {
        //height: 39px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 28px;
        //line-height: 39px;
        color: #000000;
        margin-right: 4px;
      }
      .shareholder-unit {
        font-size: 16px;
        font-weight: 400;
      }
    }
  }
  .transac-con {
    margin: 4px 0 20px 0;
  }
  .transac-block {
    padding: 24px;
    box-sizing: border-box;
    width: 567px;
    height: 151px;
    border: 1px solid #eeeeee;
    border-radius: 4px;
    display: inline-block;
    margin-right: 32px;
    margin-top: 16px;
    .content-tit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }
    .content {
      margin-top: 19px;
      display: flex;
    }
    .content-detail {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-right: 8px;
    }
    .content-detail:hover {
      cursor:pointer;
    }
    .content-transcontro {
      display: flex;
      align-items: center;
      .content-transcontro-name {
        height: 25px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 25px;
        color: #333333;
        margin-right: 20px;
      }
      .content-transcontro-frequency {
        height: 22px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        color: #333333;
      }
    }
    .content-buy,
    .content-sell {
      display: flex;
      align-items: center;
    }
    .content-buy {
      margin-bottom: 14px;
    }
    .content-buy-item {
      //height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      //font-weight: 400;
      font-size: 14px;
      //line-height: 20px;
      color: #666666;
      margin-right: 25px;
    }
    .itembefortag {
      margin-right: 6px;
    }
    .content-buy-tag {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0px 8px;
      border-radius: 4px;
      box-sizing: border-box;
      height: 17px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
    }
    .tagfir {
      background: #fff2e6;
      border: 1px solid #ffe5cc;
      color: #ff7d00;
    }
    .tagsec {
      background: #e8f8fe;
      border: 1px solid #d0f2fd;
      color: #14bcf5;
    }
    .tagthir {
      background: #ffeeee;
      border: 1px solid #ffdddf;
      color: #fe5461;
    }
  }
}
</style>
