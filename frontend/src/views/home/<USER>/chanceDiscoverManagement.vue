<!--商机挖掘管理-->
<template>
  <div class="container">
    <div class="title-con">
      <div class="title-line"></div>
      <div class="title-name">商机挖掘管理</div>
      <div class="title-more" @click="jumpMore()">
        <div class="title-more-text">更多</div>
        <div class="title-more-img">
          <img src="@/assets/images/homePage/arrowRight.png" alt="更多图标" />
        </div>
      </div>
    </div>
    <div class="transac" v-if="noData == false">
      <div class="transac-tit">
        <div class="shareholder-con">
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">62</div>
              <div class="shareholder-unit">件</div>
            </div>
            <div class="shareholder-monit">今日商机件数</div>
          </div>
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">52</div>
              <div class="shareholder-unit">件</div>
            </div>
            <div class="shareholder-monit">今日已提醒件数</div>
          </div>
          <div class="shareholder">
            <div class="shareholder-numcon">
              <div class="shareholder-num">1,264</div>
              <div class="shareholder-unit">件</div>
            </div>
            <div class="shareholder-monit">近30天商机件数</div>
          </div>
        </div>
      </div>
      <div class="transac-con"  v-loading="hgLoadding">
        <div class="repurchase-tit">
          <div class="repurchase-img">
            <img src="@/assets/images/homePage/diamond.png" alt="" />
          </div>
          <div class="repurchase-titext">今日商机</div>
        </div>
        <div class="content">
          <div class="content-detail">
            <el-skeleton
              style="width: 100%"
              :rows="6"
              :loading="loading"
              animated
            >
              <div class="detail">
                <div
                  class="detail-block"
                  v-for="(item, index) in detailList"
                  :key="index"
                  :class="[indexBlock(index)]"
                >
                  <div class="block-title">
                    <div class="block-title-img">
                      <img :src="item.titicon" alt="" />
                    </div>
                    <div class="block-title-text">{{ item.title }}</div>
                  </div>
                  <div class="block-data">
                    <div class="block-data-num">
                      {{ item.num }}
                    </div>
                    <div class="block-data-unit">{{ item.unit }}</div>
                  </div>
                </div>
              </div>
            </el-skeleton>
            <div class="equity-progresscon">
              <div class="equity-data">
                已提醒
              </div>
              <div class="progressline">
                <el-progress
                    :percentage="86"
                    :stroke-width="12"
                    color="#1F7FF4"
                    :show-text="false"
                ></el-progress>
              </div>
              <div class="progressline-percen">86%</div>
            </div>
          </div>
          <div class="content-card">
            <el-row :gutter="12" v-for="(item, index) in ruleResultList" :key="index">
              <el-card shadow="hover" class="card-padding card-text">
                <el-row :gutter="12" style="margin-bottom: 5px;">
                  <el-col :span="12">
                    <span style="font-weight: bold;">规则名称：</span>{{ item.ruleName }}
                  </el-col>
                  <el-col :span="3" :offset="9">
                    <div
                      :class="[
                      'content-buy-tag',
                      item.remindState === '待提醒'
                        ? 'tagfir'
                        : item.remindState === '已提醒'
                        ? 'tagsec'
                        : '',
                      ]"
                      style="width: 70px;"
                    >
                      {{ item.remindState }}
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="12">
                  <el-col :span="20">
                    <span style="font-weight: bold;">提醒内容：</span>{{ item.resultContent }}
                  </el-col>
                  <el-col :span="4" style="text-align: right;">
                    {{ item.remindTime }}
                  </el-col>
                </el-row>
              </el-card>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <div class="content" v-if="noData == true" style="text-align: center">
      <div style="margin: 0 auto">
        <img src="@/assets/images/noData.png" style="width: 141px" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import common from "@/mixins/common";

export default {
  name: "chanceDiscoverManagement",
  props: ["companyCode", "token"],
  mixins: [common],
  data () {
    return {
      loading: false,
      noData: false,
      hgLoadding: true,
      companyCode: this.companyCode,
      detailList: [
        {
          title: "公告",
          num: "32",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_1.png")
        },
        {
          title: "违规案例",
          num: "8",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_4.png")
        },
        {
          title: "问询函件",
          num: "2",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_2.png")
        },
        {
          title: "股价",
          num: "10",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_5.png")
        },
        {
          title: "财务指标",
          num: "6",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_3.png")
        },
        {
          title: "舆情",
          num: "4",
          unit: "件",
          titicon: require("@/assets/images/homePage/share_6.png")
        }
      ],
      ruleResultList: [
        {
          ruleName: "大盘股（流通股本＞X万股）",
          resultContent: "公司流通股本（133099.35万股）大于50000万股，属于大盘股",
          remindContent: "",
          remindTime: "09-21 17:30",
          remindState: "待提醒"
        },
        {
          ruleName: "股价月涨幅＞5%",
          resultContent: "上市公司股价月涨幅: 5.6723% ，已大于5%",
          remindContent: "",
          remindTime: "09-21 16:00",
          remindState: "已提醒"
        },
        {
          ruleName: "上市公司总市值达到一定数值",
          resultContent: "上市公司总市值达到：674018.24万元，已大于600000万元",
          remindContent: "",
          remindTime: "09-21 11:30",
          remindState: "已提醒"
        }
      ]
    };
  },
  methods: {
    jumpMore () {
      this.$message('敬请期待');
    },
    indexBlock (i) {
      let result = "";
      this.detailList.map((item, index) => {
        let classFir = 3 * index - 3;
        let classSec = 3 * index - 2;
        let classThir = 3 * index - 1;
        if (i === classFir) {
          result = "detail-blockzero";
        } else if (i === classSec) {
          result = "detail-blockfir";
        } else if (i === classThir) {
          result = "detail-blocksec";
        }
      });
      return result;
    }
  },
  mounted () {
    this.hgLoadding = false
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-top: 16px;
  padding-bottom: 16px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  .title-con {
    height: 25px;
    position: relative;
    .title-line {
      position: absolute;
      top: 3.5px;
      width: 3px;
      height: 18px;
      background: #1f7ff4;
    }
    .title-name {
      position: absolute;
      left: 20px;
      height: 25px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 18px;
      line-height: 25px;
      color: #333333;
    }
    .title-more {
      position: absolute;
      right: 17px;
      width: 44px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-more-text {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 25px;
        height: 25px;
        color: #1f7ff4;
      }
      .title-more-img {
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 12px;
          height: 12px;
        }
      }
    }
    .title-more:hover {
      cursor:pointer;
    }
  }
  .transac {
    padding: 16px 20px 24px 20px;
    box-sizing: border-box;
  }
  .transac-tit {
    background: url("../../../assets/images/homePage/interTranc.png") no-repeat;
    background-size: 100% 100%;
    background-color: #fafcff;
    height: 100px;
    width: 1200px;
    display: flex;
    .shareholder-con {
      width: 842px;
      margin: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .shareholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .shareholder-numcon {
        display: flex;
        align-items: baseline;
        margin-bottom: 6px;
      }
      .shareholder-monit {
        color: #666666;
        height: 22px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
      }
      .shareholder-num {
        height: 39px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 28px;
        line-height: 39px;
        color: #000000;
        margin-right: 4px;
      }
      .shareholder-unit {
        font-size: 16px;
        font-weight: 400;
      }
    }
  }
  .transac-con {
    margin: 4px 0 20px 0;

    .repurchase-tit {
    display: flex;
    align-items: center;
    .repurchase-img {
      height: 8px;
      width: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .repurchase-titext {
      margin-left: 8px;
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #000000;
    }
  }
  }
  .content {
    margin-top: 19px;
    display: flex;
  }
  .content-detail {
    width: calc(100% - 720px);
    padding-left: 20px;
    padding-bottom: 24px;
    box-sizing: border-box;
    min-height: 185px;
    .detail {
      border-right: 1px solid #e4e4e4;
      min-height: 185px;
    }
    .detail-block {
      width: 150px;
      height: 92.5px;
      border-right: 1px dashed #eeeeee;
      border-top: 1px dashed #eeeeee;
      display: inline-block;
      padding-left: 56px;
      padding-top: 6.5px;
      box-sizing: border-box;
    }
    .detail-block:nth-child(3n) {
      border-right: none;
    }
    .detail-block:nth-child(-n + 3) {
      border-top: none;
      padding-top: 0;
    }
    .detail-blockzero {
      padding-left: 7px;
    }
    .detail-blockfir {
      padding-left: 15px;
      width: 150px;
    }
    .detail-blocksec {
      padding-left: 15px;
    }
    .block-title {
      display: flex;
      align-items: center;
      margin-top: 10px;
      margin-bottom: 8px;
      .block-title-img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        display: flex;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .block-title-text {
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
      }
    }
    .block-data {
      display: flex;
      align-items: flex-end;
      .block-data-num {
        height: 30px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 30px;
        color: #333333;
        margin-right: 8px;
      }
      .block-data-unit {
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }
    }

    .equity-progresscon {
      display: flex;
      align-items: center;
      margin-top: 40px;
    }

    .equity-data {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }

    .progressline {
      width: 80%;
      margin-left: 10px;
    }

    .progressline-percen {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-left: 10px;
    }
  }
  .content-card {
    width: 720px;
    padding-left: 30px;
    margin-top: -4px;
    box-sizing: border-box;

    .card-padding {
      padding: 15px;
      margin-bottom: 15px;
    }
    .card-text {
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      line-height: 20px;
      color: #666666;
      margin-right: 25px;
    }

    .content-buy-tag {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0px 15px;
      border-radius: 4px;
      box-sizing: border-box;
      height: 17px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
    }
    .tagfir {
      background: #fff2e6;
      border: 1px solid #ffe5cc;
      color: #ff7d00;
    }
    .tagsec {
      background: #e8f8fe;
      border: 1px solid #d0f2fd;
      color: #14bcf5;
    }
    .tagthir {
      background: #ffeeee;
      border: 1px solid #ffdddf;
      color: #fe5461;
    }
  }
}
</style>
