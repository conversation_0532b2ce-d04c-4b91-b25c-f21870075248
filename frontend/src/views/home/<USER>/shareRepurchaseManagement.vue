<!--回购管理-->
<template>
  <div class="container">
    <div class="title-con">
      <div class="title-line"></div>
      <div class="title-name">股份回购管理</div>
      <div class="title-more" @click="jumpMore()">
        <div class="title-more-text">更多</div>
        <div class="title-more-img">
          <img src="@/assets/images/homePage/arrowRight.png" alt="更多图标" />
        </div>
      </div>
    </div>
    <div class="repurchase" v-if="noData == false">
      <div class="repurchase-plan">
        <div class="repurchase-tit">
          <div class="repurchase-img">
            <img src="@/assets/images/homePage/diamond.png" alt="" />
          </div>
          <div class="repurchase-titext" :title="'回购计划 (' + planCaseName + ')'">回购计划 ({{planCaseName}})</div>
        </div>
        <div class="repurchase-bgc">
          <div class="bgc-con">
            <div class="bgc-purcenum">
              <div class="bgc-purcenum-valcon">
                <div class="bgc-purcenum-val"  v-if="repShareLowWan && repShareHighWan" :title="numberFilterStrZero(repShareLowWan) + '~' + numberFilterStrZero(repShareHighWan)">{{numberFilterStrZero(repShareLowWan)}} ~ {{numberFilterStrZero(repShareHighWan)}}</div>
                <div class="bgc-purcenum-val"  v-if="!repShareLowWan && repShareHighWan" :title="'不超过' + numberFilterStrZero(repShareHighWan)">不超过{{numberFilterStrZero(repShareHighWan)}}</div>
                <div class="bgc-purcenum-val"  v-if="repShareLowWan && !repShareHighWan" :title="'不低于' + numberFilterStrZero(repShareLowWan)">不低于{{numberFilterStrZero(repShareLowWan)}}</div>
                <div class="bgc-purcenum-val"  v-if="!repShareLowWan && !repShareHighWan" >--</div>
                <div class="bgc-purcenum-unit">(万股)</div>
              </div>
              <div class="bgc-purcenum-charac">拟回购股份数量</div>
            </div>
            <div class="bgc-purcenum">
              <div class="bgc-purcenum-valcon">
                <div class="bgc-purcenum-val" v-if="priceRangeLowWan && priceRangeHighWan" :title="numberFilterStrZero(priceRangeLowWan) + '~' + numberFilterStrZero(priceRangeHighWan)">{{ numberFilterStrZero(priceRangeLowWan) }} ~ {{ numberFilterStrZero(priceRangeHighWan )}}</div>
                <div class="bgc-purcenum-val" v-if="!priceRangeLowWan && priceRangeHighWan" :title="'不超过' + numberFilterStrZero(priceRangeHighWan)">不超过{{ numberFilterStrZero(priceRangeHighWan )}}</div>
                <div class="bgc-purcenum-val" v-if="priceRangeLowWan && !priceRangeHighWan" :title="'不低于' + numberFilterStrZero(priceRangeLowWan)">不低于{{ numberFilterStrZero(priceRangeLowWan) }} </div>
                <div class="bgc-purcenum-val" v-if="!priceRangeLowWan && !priceRangeHighWan" >--</div>
                <div class="bgc-purcenum-unit">(元/股)</div>
              </div>
              <div class="bgc-purcenum-charac">回购价格</div>
            </div>
            <div class="bgc-purcenum">
              <div class="bgc-purcenum-valcon">
                <div class="bgc-purcenum-val" v-if="repPriceLowWan && repPriceHighWan" :title="numberFilterStrZero(repPriceLowWan) + '~' + numberFilterStrZero(repPriceHighWan)">{{numberFilterStrZero(repPriceLowWan)}} ~ {{numberFilterStrZero(repPriceHighWan)}}</div>
                <div class="bgc-purcenum-val" v-if="!repPriceLowWan && repPriceHighWan" :title="'不超过' + numberFilterStrZero(repPriceHighWan)">不超过{{numberFilterStrZero(repPriceHighWan)}}</div>
                <div class="bgc-purcenum-val" v-if="repPriceLowWan && !repPriceHighWan" :title="'不低于' + numberFilterStrZero(repPriceLowWan)">不低于{{numberFilterStrZero(repPriceLowWan)}}</div>
                <div class="bgc-purcenum-val" v-if="!repPriceLowWan && !repPriceHighWan">--</div>
                <div class="bgc-purcenum-unit">(万元)</div>
              </div>
              <div class="bgc-purcenum-charac">拟回购金额</div>
            </div>
          </div>
        </div>
      </div>
      <div class="repurchase-progress">
        <div class="repurchase-tit repurchase-distance">
          <div class="repurchase-img">
            <img src="@/assets/images/homePage/diamond.png" alt="" />
          </div>
          <div class="repurchase-titext">回购进程</div>
        </div>
        <div class="content">
          <div class="content-detail">
            <el-skeleton
              style="width: 100%"
              :rows="6"
              :loading="loading"
              animated
            >
              <div class="detail">
                <div
                  class="detail-block"
                  v-for="(item, index) in detailList"
                  :key="index"
                  :class="[indexBlock(index)]"
                >
                  <div class="block-title">
                    <div class="block-title-img">
                      <img :src="item.titicon" alt="" />
                    </div>
                    <div class="block-title-text">{{ item.title }}</div>
                  </div>
                  <div class="block-data">
                    <div class="block-data-num" :title="numberFilterStrZero(item.num)">
                      {{ numberFilterStrZero(item.num) }}
                    </div>
                    <div class="block-data-unit">{{ item.unit }}</div>
                  </div>
                </div>
              </div>
            </el-skeleton>
          </div>
          <div class="content-progress">
            <div class="share-progress">
              <div class="progresstit">股份进度：</div>
              <div class="progressline">
                <el-tooltip placement="top" effect="light">
                  <div slot="content">
                    已回购股份：{{
                      numberFilterStrNull(sumShare)
                    }}万股<br /><br />
                    <span v-if="this.repShareLow"
                      >距回购下限：{{
                        numberFilterStrNull(shareLowRatio)
                      }}%<br /><br
                    /></span>
                    距回购上限：{{ numberFilterStrNull(shareHighRatio) }}%
                  </div>
                  <el-progress
                    :percentage="this.shareProgress>100?100:this.shareProgress"
                    :stroke-width="12"
                    color="#CF1A1C"
                    :show-text="false"
                  ></el-progress
                ></el-tooltip>
<!--                <el-tooltip placement="top" effect="light">-->
<!--                  <div slot="content">{{ shareProgress >100 ?100 + '%':shareProgress +'%'}}</div>-->

<!--                  <div v-if="shareProgress>0" class="yuan" :style="{'left':'calc('+shareProgress+'% - 17px)'}"></div>-->
<!--                </el-tooltip>-->
<!--                <div class="progressline-middle"></div>-->
                <div class="sharelower">
                  <div class="share-num">{{ numberFilterStrNull(repShareLowWan) }}万股</div>
                  <div class="share-desc">回购股份下限</div>
                </div>
                <div class="shareupper">
                  <div class="share-num">{{ numberFilterStrNull(repShareHighWan) }}万股</div>
                  <div class="share-desc">回购股份上限</div>
                </div>
              </div>
            </div>
            <div class="time-progress">
              <div class="progresstit">时间进度：</div>
              <div class="progressline">
                <el-tooltip placement="top" effect="light">
                  <div slot="content">
                    当前日期： {{ nowDate }}<br /><br />
                    距回购开始：{{ repStartDateNum }}个交易日<br /><br />
                    距回购结束：{{ repEndDateNum }}个交易日
                  </div>
                  <el-progress
                      :percentage="this.timeRatio>100?100:this.timeRatio"
                    :stroke-width="12"
                    color="#CF1A1C"
                      :show-text="false"
                  ></el-progress
                ></el-tooltip>
                <el-tooltip placement="top" effect="light">
                  <div slot="content">{{ timeRatio >100 ?100 + '%':timeRatio +'%'}}</div>

                  <div v-if="timeRatio>0" class="yuan" :style="{'left':'calc('+timeRatio+'% - 17px)'}"></div>
                </el-tooltip>
                <div class="sharelower timelower">
                  <div class="share-num">{{ startDate }}</div>
                  <div class="share-desc">回购开始日期</div>
                </div>
                <div class="shareupper timeupper">
                  <div class="share-num">{{ endDate }}</div>
                  <div class="share-desc">回购结束日期</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content" v-if="noData == true" style="text-align: center">
      <div style="margin: 0 auto">
        <img src="@/assets/images/noData.png" style="width: 141px" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRepManageInfo } from "@/api/block-api";
import common from "@/mixins/common.js";
import { _getOpenedSystems,_getRoleListGroupBySystem } from "@/api/common-api";
import {showMessage} from "stock-vue-plugin/lib/utils/message";
export default {
  name: "shareRepurchaseManagement",
  props: ["companyCode", "token"],
  mixins: [common],
  data () {
    return {
      systemSign: 'HGGL',
      loading: false,
      noData: true,
      detailList: [
        {
          title: "已回购股份数量",
          num: "",
          unit: "万股",
          titicon: require("@/assets/images/homePage/share_1.png")
        },
        {
          title: "占总股本",
          num: "",
          unit: "%",
          titicon: require("@/assets/images/homePage/share_2.png")
        },
        {
          title: "回购均价",
          num: "",
          unit: "元",
          titicon: require("@/assets/images/homePage/share_3.png")
        },
        {
          title: "已回购金额",
          num: "",
          unit: "万元",
          titicon: require("@/assets/images/homePage/share_4.png")
        },
        {
          title: "最高回购单价",
          num: "",
          unit: "元",
          titicon: require("@/assets/images/homePage/share_5.png")
        },
        {
          title: "最低回购单价",
          num: "",
          unit: "元",
          titicon: require("@/assets/images/homePage/share_6.png")
        }
      ],
      repShareHigh: "",
      repShareLow: "",
      endDate: "",
      nowDate: "",
      startDate: "",
      sumShare: "",
      shareLowRatio: "",
      shareHighRatio: "",
      shareProgress: "",
      repStartDateNum: "",
      repEndDateNum: "",
      timeRatio: "",
      repPriceLowWan: "",
      repPriceHighWan: "",
      priceRangeLowWan: "",
      priceRangeHighWan: "",
      repShareLowWan: "",
      repShareHighWan: "",
      planId: "",
      planCaseName: "",
      userInfo: {
        key:'',
        userName:'',
        id:'',
        childrenUserName:'',
        userType:'',
      },
    };
  },
  methods: {
    getRepManageInfo () {
      let param = {
        selCompanyCode: this.companyCode
      };
      getRepManageInfo(param).then((res) => {
        if (res.data.result.success) {
          this.noData = false;
          this.detailList.find((item) => item.title === "已回购股份数量").num =
              res.data.result.result.sumShare;
          this.detailList.find((item) => item.title === "占总股本").num =
              res.data.result.result.repShareTotalRatio;
          this.detailList.find((item) => item.title === "回购均价").num =
              res.data.result.result.avgPrice;
          this.detailList.find((item) => item.title === "已回购金额").num =
              res.data.result.result.sumMoney;
          this.detailList.find((item) => item.title === "最高回购单价").num =
              res.data.result.result.maxPrice;
          this.detailList.find((item) => item.title === "最低回购单价").num =
              res.data.result.result.minPrice;
          this.planId = res.data.result.result.planId;
          this.planCaseName = res.data.result.result.caseName;
          this.repShareHigh = res.data.result.result.repShareHigh;
          this.repShareLow = res.data.result.result.repShareLow;
          this.endDate = res.data.result.result.endDate;
          this.nowDate = res.data.result.result.nowDate;
          this.startDate = res.data.result.result.startDate;
          this.sumShare = res.data.result.result.sumShare;
          this.shareLowRatio = res.data.result.result.shareLowRatio;
          this.repStartDateNum = res.data.result.result.repStartDateNum;
          this.repEndDateNum = res.data.result.result.repEndDateNum;
          this.timeRatio = res.data.result.result.timeRatio;
          this.repPriceLowWan = res.data.result.result.repPriceLowWan;
          this.repPriceHighWan = res.data.result.result.repPriceHighWan;
          this.priceRangeLowWan = res.data.result.result.priceRangeLowWan;
          this.priceRangeHighWan = res.data.result.result.priceRangeHighWan;
          this.repShareLowWan = res.data.result.result.repShareLowWan;
          this.repShareHighWan = res.data.result.result.repShareHighWan;
          // this.shareHighRatio = res.data.result.result.priceHighRatio;
          // this.shareProgress = this.shareHighRatio === 0 ? this.shareHighRatio.toFixed(2) : (this.shareHighRatio >= 100 ? '100' : (100 - this.shareHighRatio).toFixed(2));
          if (this.repShareHighWan == null || this.repShareHighWan == undefined || this.repShareHighWan == "") {
            this.repShareHighWan = this.repShareLowWan * 2;
          }
          if (this.sumShare == null || this.sumShare == undefined || this.sumShare == "") {
            this.sumShare = 0.00;
          }
          this.shareHighRatio = 100 - (100 * this.sumShare / this.repShareHighWan).toFixed(2);
          this.shareProgress = (100 - this.shareHighRatio).toFixed(2);
        }
      });
    },
    indexBlock (i) {
      let result = "";
      this.detailList.map((item, index) => {
        let classFir = 3 * index - 3;
        let classSec = 3 * index - 2;
        let classThir = 3 * index - 1;
        if (i === classFir) {
          result = "detail-blockzero";
        } else if (i === classSec) {
          result = "detail-blockfir";
        } else if (i === classThir) {
          result = "detail-blocksec";
        }
      });
      return result;
    },
    numberFilterStrZero (value) {
      if (!value) return '0.00'
      if (value === '') return '0.00'
      let str = parseFloat(value).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
      return str;
    },
    jumpMore () {
      var myArray = [];
      if (this.userInfo.userName === 'admin') {
        myArray = ['HGGL','TDGF','GQJL'];
      }
      _getRoleListGroupBySystem({ id: this.userInfo.id, userType: this.userInfo.userType }).then(res => {
        if (res.data.success) {
          let roles = res.data.result.roles;
          let checkedRoleIds = res.data.result.checkedRoleIds;
          checkedRoleIds.forEach(item => {
            roles.forEach(role => {
              if (role.children != undefined) {
                role.children.forEach(child => {
                  if (child.id === item) {
                    myArray.push(child.flag);
                  }
                })
              }
            })
          })
          if (myArray.indexOf("HGGL") === -1){
            showMessage('warning', "您没有操作该菜单的权限！");
          }else{
            this.getOpenedSystems();
          }
        } else {
          showMessage('warning', res.data.errorMsg)
        }
      })
    },
    getOpenedSystems(){
      _getOpenedSystems({ menuName: this.systemSign }).then(res => {
        if (res.data.success) {
          if (res.data.result != null && res.data.result.length > 0) {
            console.log("00----", res.data.result[0].path + "&planId=" + this.planId + "&planCaseName=" + this.planCaseName + "&startDate=" + this.startDate )
            window.open(res.data.result[0].path + "&planId=" + this.planId + "&planCaseName=" + this.planCaseName + "&startDate=" + this.startDate );
          }
        }
      })
    }
  },
  mounted () {
    let date = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
    this.userInfo = date;
    this.getRepManageInfo();
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding-top: 16px;
  padding-bottom: 16px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  .title-con {
    height: 25px;
    position: relative;
    .title-line {
      position: absolute;
      left: 20px;
      top: 3.5px;
      width: 3px;
      height: 18px;
      background: #E03D3E;
    }
    .title-name {
      position: absolute;
      left: 30px;
      height: 25px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 600;
      font-size: 18px;
      line-height: 25px;
      color: #333333;
    }
    .title-more {
      position: absolute;
      right: 17px;
      width: 44px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-more-text {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 25px;
        height: 25px;
        color: #CF1A1C;
      }
      .title-more-img {
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 12px;
          height: 12px;
        }
      }
    }
    .title-more:hover {
      cursor:pointer;
    }
  }
  .repurchase-plan {
    margin-top: 16px;
    padding: 0 20px;
  }
  .repurchase-tit {
    display: flex;
    align-items: center;
    .repurchase-img {
      height: 8px;
      width: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .repurchase-titext {
      margin-left: 8px;
      height: 20px;
      color: #000000;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      line-height: normal;
      letter-spacing: 0px;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .repurchase-distance{
    padding: 20px 20px 0 20px;
  }
  .repurchase-bgc {
    background: url("../../../assets/images/homePage/interTranc.png") no-repeat;
    background-size: 100% 100%;
    background-color:  rgba(255, 224, 225, 0.1);
    height: 100px;
    width: 1200px;
    margin-top: 12px;
    display: flex;
    align-items: center;
    .bgc-con {
      width: 1075px;
      margin: auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .bgc-purcenum {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .bgc-purcenum-valcon {
      display: flex;
      align-items: baseline;
      margin-bottom: 8px;
    }
    .bgc-purcenum-charac {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #666666;
    }
    .bgc-purcenum-val {
      height: 34px;
      max-width: 280px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 500;
      font-size: 24px;
      line-height: 34px;
      color: #333333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .bgc-purcenum-unit {
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      margin-left: 10px;
    }
  }
  .content {
    margin-top: 19px;
    display: flex;
  }
  .content-detail {
    width: calc(100% - 520px);
    padding-left: 20px;
    padding-bottom: 24px;
    box-sizing: border-box;
    min-height: 185px;
    .detail {
      border-right: 1px solid #e4e4e4;
      min-height: 185px;
    }
    .detail-block {
      width: 204.8px;
      height: 92.5px;
      border-right: 1px dashed #eeeeee;
      border-top: 1px dashed #eeeeee;
      display: inline-block;
      padding-left: 56px;
      padding-top: 6.5px;
      box-sizing: border-box;
    }
    .detail-block:nth-child(3n) {
      border-right: none;
    }
    .detail-block:nth-child(-n + 3) {
      border-top: none;
      padding-top: 0;
    }
    .detail-blockzero {
      padding-left: 7px;
    }
    .detail-blockfir {
      padding-left: 48px;
      width: 238px;
    }
    .detail-blocksec {
      padding-left: 56px;
      width: 225.9px;
    }
    .block-title {
      display: flex;
      align-items: center;
      margin-top: 10px;
      margin-bottom: 8px;
      .block-title-img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        display: flex;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .block-title-text {
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
      }
    }
    .block-data {
      display: flex;
      align-items: flex-end;
      .block-data-num {
        height: 30px;
        max-width: 150px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 500;
        font-size: 24px;
        line-height: 30px;
        color: #333333;
        margin-right: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .block-data-unit {
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }
  .content-progress {
    width: 520px;
    padding-left: 30px;
    margin-top: -4px;
    box-sizing: border-box;
    .share-progress {
      margin-bottom: 60px;
    }
    .progresstit {
      margin-bottom: 7px;
      height: 20px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }
    .progressline {
      width: 460px;
      position: relative;
      .yuan{
        background: #3F528D;
        width: 18px;
        height: 18px;
        position: absolute;
        border: 2px solid #FFFFFF;
        box-shadow: -2px 0px 4px 0px #00000033;
        border-radius: 50%;
        top: -5px;
        cursor:pointer;
      }
      .mousetit {
        position: absolute;
        top: -106px;
        left: calc(100% - 341px);
        border: 1px solid #eeeeee;
        box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
        border-radius: 2px;
        background: #fff;
        padding: 4px 12px;
        display: none;
      }
      .mousetit-squ {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        height: 20px;
        color: #333333;
        margin: 8px 0;
      }
      .progressline-middle {
        height: 18px;
        width: 6px;
        border-radius: 4px;
        background-color: #3F528D;
        position: absolute;
        left: calc(100% - 230px);
        top: -3px;
      }
      .share-num,
      .share-desc {
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: #666666;
      }
      .share-desc {
        font-size: 12px;
      }
      .shareupper {
        position: absolute;
        top: 25px;
        right: 0;
        .share-num,
        .share-desc {
          text-align: right;
        }
      }
      .sharelower {
        position: absolute;
        top: 25px;
        left: calc(100% - 460px);
        .share-num,
        .share-desc {
          text-align: left;
        }
      }
      .timelower {
        left: 0;
      }
      .timeupper {
        right: 0;
      }
    }
    /deep/ .el-progress-bar__outer {
      //border-radius: 0 !important;
      background-color: #FEF1F1!important;
    }
    /deep/ .el-progress-bar__inner:hover {
      cursor:pointer;
    }
  }
  /deep/ .el-progress-bar__innerText {
    color: #0a0101;
  }
}
</style>
