<template>
  <div class="homedetail-container" v-loading="loading">
    <div class="homedetail-bgc"></div>
    <div class="homedetail-con" >
      <div class="con-title">
        <el-button v-if="!comFlag" class="back-btn" @click="toBack" round type="primary">返回</el-button>
        <div class="con-left">
          <div class="con-titimg">
            <img v-if="logoUrl" :src="logoUrl" alt="公司头像" />
            <img
              v-else
              class="default-image"
              src='@/assets/images/default-company.png'
              alt="默认图片"
            />
          </div>
          <div class="con-titslogin">
            <div class="block-comname">
              {{ numberFilterStrNull(saCompanyDto.zhName) }}
            </div>
            <div class="block-cotag">
              <div class="cotag-sign cotag-signsec" v-for="(item, index) in comStock" :key="index">
                <span v-if="item.stockType!=null">{{ item.stockType }} | </span>
                <span v-if="item.zhSortName!= null">{{ item.zhSortName }}  </span>
                <span v-if="item.companyCode!= null">{{ item.companyCode }} | </span>
                <span v-if="item.liststate!= null">{{ item.liststate }} </span>
              </div>
              <div class="cotag-sign cotag-signfir" v-if="comIndustry.industryCSRCStr">{{ numberFilterStrNull(comIndustry.industryCSRCStr) }}</div>
              <div class="cotag-sign cotag-signfir" v-if="comIndustry.industrySWStr">{{ numberFilterStrNull(comIndustry.industrySWStr) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="con-message">
        <div class="con-message-block">
          <div class="message-block-pro" v-if="stockInfo" v-loading="selectStock">
            <div class="profitnum">
              <div class="profitnum-snmallnum">
                <div :class="stockInfo.upanddown && stockInfo.upanddown.indexOf('-') !== -1?'stock-green':'stock-red'">
                  <span style="font-size: 42px;font-weight: 600">{{ stockInfo.shares }}</span>
                </div>
                <div style="font-size: 16px;line-height: 42px; display: flex; align-items: center;"
                     :class="stockInfo.upanddown && stockInfo.upanddown.indexOf('-') !== -1?'stock-green':'stock-red'">
                  <img v-if="stockInfo.upanddown && stockInfo.upanddown.indexOf('-') !== -1" src="@/assets/images/stock-green.png" style="height: 16px;margin-right:10px">
                  <img v-else-if="stockInfo.upanddown && stockInfo.upanddown.indexOf('-') === -1" src="@/assets/images/stock-red.png" style="height: 16px;margin-right:10px">
                  <span v-if="stockInfo.upanddown">{{ stockInfo.upanddown }}&nbsp;&nbsp;{{stockInfo.upanddownrate + "%" }}</span>
                  <span v-else>--&nbsp;&nbsp;--</span>
                </div>
              </div>
            </div>
            <div class="profitspec" >
              <div class="profitspec-title">总市值：</div>
              <div class="profitspec-num">{{ numberFilterStrZero(stockInfo.marketTotal) }}</div>
            </div>
            <div class="profitspec" >
              <div class="profitspec-title">流通市值：</div>
              <div class="profitspec-num">{{ numberFilterStrZero(stockInfo.marketCirculate) }}</div>
            </div>
            <div class="profitspec" >
              <div class="profitspec-title">总股本：</div>
              <div class="profitspec-num">{{ numberFilterStrZero(Number(isNaN(stockInfo.totalStockNum)?'0.00':stockInfo.totalStockNum).toLocaleString()) }}股</div>
            </div>
            <div class="profitspec" >
              <div class="profitspec-title">股东人数：</div>
              <div class="profitspec-num">{{ numberFilterStrZero1(stockInfo.shareNum) }}</div>
            </div>
            <div class="profitspec">
              <div class="profitspec-title">最近更新：</div>
              <div class="profitspec-num"> {{ stockInfo.endDate }}，5分钟/次</div>
            </div>

          </div>
          <div class="message-block-info" :class="{'message-block-info-not':!stockInfo}">
            <div class="block-info-relati">
              <div class="info-legal">
                <div class="legal-detail">
                  <div class="legaltit">法人：</div>
                  <div class="legalname">{{ saCompanyDto.corporate }}</div>
                </div>
                <div class="legal-detail">
                  <div class="legaltit">统一社会信用代码：</div>
                  <div class="legalname">{{ saCompanyDto.taxNo }}</div>
                </div>
              </div>
              <div class="info-legal">
                <div class="legal-detail">
                  <div class="legaltit">电话：</div>
                  <div class="legalname">{{ saCompanyDto.phone }}</div>
                </div>
                <div class="legal-detail">
                  <div class="legaltit">官网：</div>
                  <div class="legalname website redColor" @click="jumpWebsite(saCompanyDto.companyWebsite)">
                    {{ saCompanyDto.companyWebsite }}
                  </div>
                </div>
              </div>
              <div class="info-legal">
                <div class="legal-detail">
                  <div class="legaltit">邮箱：</div>
                  <div class="legalname">{{ saCompanyDto.companyMail }}</div>
                </div>
                <div class="legal-detail">
                  <div class="legaltit">地址：</div>
                  <div class="legalname legaltitaddress" title="">
                    {{ saCompanyDto.registerAddress }}
                  </div>
                </div>
              </div>
            </div>
            <div class="echartsDiv" v-if="saCompanyDto && (saCompanyDto.marketStockType === '05' || saCompanyDto.marketStockType === '99')">
              <div class="echartTip">注:本页面行情信息非即时行情，仅供参考。</div>
            </div>
            <div class="echartsDiv" v-else>
              <div class="echartTitle" style="font-weight: bold">股价走势图</div>
              <div class="echarts" id="recent-price-trend"></div>
              <div class="echartTip">注:本页面行情信息非即时行情，仅供参考。</div>
            </div>
          </div>
        </div>
      </div>
      <div class="con-platform-slogin">
        <div class="component-con" v-if="systemState.includes('回购管理')">
          <share :companyCode="companyCode" :token="token"></share>
        </div>
        <div class="component-con" v-if="systemState.includes('股权激励')">
          <equity :companyCode="companyCode" :token="token"></equity>
        </div>
        <div class="component-con" v-if="systemState.includes('合规交易')">
          <compliance :companyCode="companyCode" :token="token"></compliance>
        </div>
<!--        <div class="component-con" v-if="systemState.includes('SJWJ')">-->
<!--          <chanceDiscover :companyCode="companyCode" :token="token"></chanceDiscover>-->
<!--        </div>-->
      </div>
    </div>
  </div>
</template>
<script>
import Compliance from "./components/complianceTransactionManagement.vue";
import Equity from "./components/equityIncentiveManagement.vue";
import Share from "./components/shareRepurchaseManagement.vue";
import ChanceDiscover from "./components/chanceDiscoverManagement.vue";
import echarts from 'echarts';
import { _queryCompanyInfo, _querySharesInfo } from "@/api/listed-company-api";
import store from '@/store'

export default {
  name: "homePageDetail",
  components: {
    Compliance,
    Equity,
    Share,
    ChanceDiscover
  },
  props: {
    comFlag: {
      type: Boolean
    },
    companyCode: {
      type: String
    }
  },
  data () {
    return {
      stockTimer: null,
      stockInfo: '',
      selectStock: false,
      // 刷新旋转动画
      showAnimate: false,
      loading: true,
      // 控制展开收起
      needShowExpande: false,
      isExpande: false,
      needShowExpandeMore: false,
      isExpandMore: false,
      token: store.getters.token,
      sharesMessageMap: {
        zsz: "",
        agszbhxs: "",
        pe9: "",
        pb8: "",
        totalshares: "",
        shareNum: "",
        new: ""
      },
      upAndDownInfo: {
        upanddown: 0.00,
        upanddownrate: 0.00
      },
      upAndDownStatus: '0',
      saCompanyDto: [],
      logoUrl: '',
      comStock: [],
      comIndustry: [],
      dateList: [], // 时间
      newList: [], // 收盘价
      chgList: [], // 涨跌幅
      systemState: [] // 系统开通状态
    };
  },
  methods: {
    expandeControl () {
      let that = this;
      this.$nextTick(() => {
        const lineHeight = 20;
        const lineHeightMore = 28;
        if (that.$refs.expande.offsetHeight > lineHeight) {
          that.isExpande = true;
          that.needShowExpande = true;
        } else {
          that.isExpande = false;
          that.needShowExpande = false;
        }

        if (that.$refs.expandmore.offsetHeight > lineHeightMore * 3) {
          that.isExpandMore = false;
          that.needShowExpandeMore = true;
        } else {
          that.isExpandMore = true;
          that.needShowExpandeMore = false;
        }
      });
    },
    expandClick () {
      this.isExpande = !this.isExpande;
    },
    // 跳转官网
    jumpWebsite (url) {
      console.log(url)
      window.open("http://" + url);
    },
    // 为空显示零
    numberFilterStrZero (value) {
      if (!value) return '0.00'
      if (value == '') return '0.00'
      return value;
    },
    // 为空显示零不带小数点
    numberFilterStrZero1 (value) {
      if (!value) return '0'
      if (value == '') return '0'
      return value;
    },
    numberFilterStrNull (value) {
      if (!value) return '--'
      if (value === '') return '--'
      return value;
    },
    stockRightEchart () {
      let this_ = this;
      this_.stockRightPie = echarts.init(document.getElementById('recent-price-trend'));
      let option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            return `${params[0].name}` + '<br/>' + "收盘价：" + params[0].data + "元/股" +
                '<br/>' + "涨跌幅：" + this.chgList[params[0].dataIndex] + "%";
          },
          backgroundColor: "#fff",
          color: "#8EBFFA",
          borderWidth: "0.5",
          borderColor: "#fff",
          textStyle: {
            color: "#333",
            fontSize:14
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              type: 'solid'// 设置鼠标悬浮实线
            },
            itemStyle: {
              // 设置线条上点的颜色（和图例的颜色）
              normal: {
                color: "#8EBFFA"
              }
            }
          },
          extraCssText: 'width:150px;height:60px;'
        },
        xAxis: [
          {
            data: this.dateList,
            boundaryGap: false,
            splitLine: {
              show: true,
              lineStyle: {
                type: 'solid',
                color: '#EEEEEE'
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#666666" // 最左侧Y轴颜色
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            // name: '单位(元/股)',
            type: 'value',
            axisLine: {
              show: false,
              lineStyle: {
                color: "#666666" // 最左侧Y轴颜色
              }
            },
            axisTick: {
              show: false
            },
            splitNumber: 3.00,
            // 坐标轴刻度标签的相关设置
            axisLabel: {
              formatter: function (value) {
                return value.toFixed(2)
              }
            }
          }
        ],
        grid: {
          bottom: '15%',
            top: '10%',
          left: '60',
        },
        series: [
          {
            type: 'line',
            showSymbol: false,
            data: this.newList,
            lineStyle: {
              normal: {
                color: '#CE1A1C'
              }
            },
            emphasis: { // 高亮样式
              itemStyle: {
                color: '#CE1A1C',
                borderColor: '#CE1A1C'
              }
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [// 渐变颜色
                  {
                    offset: 0,
                    color: 'rgba(204, 0, 3, 0.26)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(204, 0, 3, 0.00)'
                  }
                ],
                global: false
              }
            }
          }
        ]
      }
      this_.stockRightPie.setOption(option);
    },
    stockInterval () {
      this.stockTimer = window.setInterval(() => {
        this.selectStock = true
        _querySharesInfo({
          companyCode: this.companyCode
        }).then((res) => {
          if (res.data.result && res.data.result[0]) {
            if (res.data.result[0].shares && res.data.result[0].shares !== '未上市' && res.data.result[0].shares !== '0.00') {
              this.stockInfo = res.data.result[0]
              if (this.stockInfo.endDate) {
                if (this.stockInfo.endDate > '15:00') {
                  this.stockInfo.endDate = '15:00'
                  this.selectStock = false
                } else if (!this.stockTimer) {
                  this.selectStock = false
                  this.stockInterval()
                }
              } else {
                this.selectStock = false
              }
            } else {
              // clearInterval(this.stockTimer)
              // this.stockTimer = null
              this.selectStock = false
            }
          }
          this.selectStock = false
        });
      }, 300000)
    },
    // 获取公司信息
    queryCompanyInfo () {
      this.selectStock = true
      var param = {
        companyCode: this.companyCode
      }
      _queryCompanyInfo(param).then((res) => {
        if (res.data) {
          if (res.data.sharesMessageMap) {
            this.sharesMessageMap = res.data.sharesMessageMap
          }
          if (res.data.saCompanyDto) {
            this.saCompanyDto = res.data.saCompanyDto
            if (this.saCompanyDto.systemState !== undefined && this.saCompanyDto.systemState !== null) {
              this.systemState = this.saCompanyDto.systemState.split(",");
            }
          }
          if (res.data.comIndustry) {
            this.comIndustry = res.data.comIndustry
          }
          if (res.data.comStock) {
            this.comStock = res.data.comStock
          }
          if (res.data.echartsList) {
            let _this = this;
            _this.dateList = [];
            _this.newList = [];
            _this.chgList = [];
            _this.echartDataList = res.data.echartsList;// 30个交易日折线图数据
            _this.echartDataList = _this.echartDataList.sort((a, b) => new Date(a.tradeDate).getTime() - new Date(b.tradeDate).getTime()); // 时间升序
            for (let i = 0; i < _this.echartDataList.length; i++) {
              _this.dateList.push(_this.echartDataList[i].tradeDate);
              _this.newList.push(_this.echartDataList[i].new);
              _this.chgList.push(_this.echartDataList[i].chg);
            }
            this.$nextTick(() => {
              _this.stockRightEchart();
            })
          }
          // 展开收起
          this.expandeControl();
          if (res.data.logoUrl) {
            this.logoUrl = 'https://oss.valueonline.cn/' + res.data.logoUrl
          }
        }
        this.loading = false;
      })
      _querySharesInfo(param).then((res) => {
        if (res.data.result && res.data.result[0]) {
          if (res.data.result[0].shares && res.data.result[0].shares !== '未上市' && res.data.result[0].shares !== '0.00') {
            this.stockInfo = res.data.result[0]
            if (this.stockInfo.endDate) {
              if (this.stockInfo.endDate > '15:00') {
                this.stockInfo.endDate = '15:00'
                this.selectStock = false
              } else if (!this.stockTimer) {
                this.selectStock = false
                this.stockInterval()
              }
            } else {
              this.selectStock = false
            }
          } else {
            this.selectStock = false
          }
        }
        this.selectStock = false
      })
    },
    toBack () {
      this.$emit('toBlock');
    }
  },
  mounted () {
    this.queryCompanyInfo();
  }
};
</script>
<style lang="scss" scoped>
@import "./homePageDetail.scss";
.echartTitle{
  width: 84px;
  height: 20px;

  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  /* identical to box height */

  /* 文字/#333333 */

  color: #666666;

  /* Inside auto layout */

  flex: none;
  order: 0;
  flex-grow: 0;
}
.echartTip {
  color: red;
  font-size: 14px;
}
.echartsDiv{
  width: 848px;
  left: 343px;
  top: 169px;
}
.echarts{
  //margin: -40px 0px 0px -68px;
  width: 848px;
  height: 173px;
  margin-bottom: 5px;
}
.redColor{
  color: #CF1A1C!important;
}
.stock-red{
  color: red;
}
.stock-green{
  color: #009900
}
.back-btn {
  position: absolute;
  //bottom: 16.6px;
  //left: 48vw;
  right:13px;
  top: 15px;
  z-index: 2;
}
</style>
