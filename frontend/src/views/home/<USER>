@mixin img-cover {
  // 另图片铺满容器
  width: 100%;
  height: 100%;
}



.homedetail-container {
  background-color: #F7F9FC;
  padding-bottom: 100px;
  #particles-js {
    width: 100%;
    height: 333px;
    //position: absolute;
    background: linear-gradient(#0158C2, #0158C2,#F7F9FC);
    //background-position: center, top;
    //background-repeat: no-repeat, no-repeat;
    background-size: 100% 100%, 100% 100%;
  }
  .homedetail-bgc{
    width: 100%;
    height: 360px;
    background: url("../../assets/images/homedetail-bgc.png") no-repeat;
    background-size: 100% 100%, 100% 100%;
  }
  .homedetail-con {
    margin-top: -360px;
    padding-top: 40px;
    .con-title {
      position: relative;
      width: 1240px;
      margin: auto;
      margin-bottom: 28px;
      display: flex;
      justify-content: space-between;
    }
    .con-left {
      display: flex;
      position: relative;
      .con-titimg {
        width: 100px;
        height: 100px;
        background: #ffffff;
        border-radius: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 100%;
          height:100%;
        }
      }
      .default-image{
        border-radius: 100%;
      }
      .con-titslogin {
        margin-left: 14px;
        padding-top: 10px;
      }
      .block-comname {
        margin-bottom: 14px;
        height: 23px;
        font-family: "Arial";
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 23px;
        color: #000;
      }
      .block-cotag {
        display: flex;
        flex-wrap: wrap;
        max-width: 780px;
      }
      .cotag-sign {
        height: 22px;
        line-height: 12px;
        box-sizing: border-box;
        border-radius: 4px;
        padding: 2.5px 8px;
        margin-right: 8px;
        margin-bottom: 6px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
      }
      .cotag-signfir {
        color: #14bcf5;
        background: #e8f8fe;
        border: 1px solid #d0f2fd;
      }
      .cotag-signsec {
        //background: #fff2e6;
        //border: 1px solid #ffe5cc;
        //color: #ff7d00;
        color: #CF1A1C;
        border-radius: 4px;
        border: 1px solid #D9ECFF;
        background: #FFF9F9;
      }
    }
    .con-right {
      display: flex;
      align-items: end;
      .con-year,
      .con-hour,
      .con-refre {
        height: 16px;
        font-family: "Arial";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #ffffff;
      }
      .con-year {
        margin-right: 10px;
      }
      .con-hour {
        margin-right: 4px;
      }
      .con-refre {
        margin-right: 8px;
      }
      .con-refreimg {
        height: 16px;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          height: 14px;
        }
      }
      .con-refreimg:hover{
        cursor: pointer;
      }
      .rotate360{
        // 无限循环
        animation: rotate360 1s ease-out 0s infinite;
      }
      @keyframes rotate360 {
        100% {
          transform: rotate(360deg);
        }
      }
    }
    .con-message {
      width: 1240px;
      position: relative;
      // height: 343px;
      margin: auto;
      background: #ffffff;
      border-radius: 4px;
      padding: 20px;
      box-sizing: border-box;
    }
    .con-message-block {
      box-sizing: border-box;
      background: #fafcff;
      display: flex;
    }
    .message-block-pro {
      width: 291px;
      border-right: 1px solid #ebeef2;
      padding: 24px 0 24px 29px;
      box-sizing: border-box;
      .profitnum {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        margin-left: -1px; //去首字母letter-space
      }
      .profitnum-bignum {
        margin-right: 20px;
        height: 38px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 38px;
        line-height: 38px;
        color: #fe5461;
      }
      .profitnum-snmallnum {
        align-items: center;
        .trendimg {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          display: flex;
          img {
            @include img-cover;
          }
        }
        .trendparse,
        .trendfloat {
          height: 20px;
          font-family: "PingFang SC";
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #fe5461;
        }
        .trendparse {
          margin-right: 10px;
        }
      }
      .profitspec {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }
      .profitspec-title,
      .profitspec-num {
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
      }
      .profitspec-title {
        width: 100px;
      }
      .profitrefresh {
        display: flex;
        align-items: center;
        padding-top: 8px;
      }
      .profitrefresh-year,
      .profitrefresh-hour,
      .profitrefresh-refre {
        height: 17px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        color: #999999;
      }
      .profitrefresh-year {
        margin-right: 4px;
      }
      .profitrefresh-refre {
        margin-right: 8px;
      }
      .profitrefresh-refreimg {
        width: 14px;
        height: 14px;
        display: flex;
        img {
          @include img-cover;
        }
      }
      .profitrefresh-refreimg:hover{
        cursor: pointer;
      }
    }
    .message-block-info {
      width: calc(100% - 291px);
      padding: 24px 29px 16px 32px;
      box-sizing: border-box;
      .block-info-relati {
        padding-bottom: 20px;
      }
      .legaltitaddress {
        max-width: 486px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .info-legal {
        margin-bottom: 8px;
        display: flex;
        // align-items: center;
      }
      .legal-detail {
        min-width: 312px;
        display: flex;
        //align-items: baseline;
        align-items: center;
        .contral-openorshowcon{
          display: flex;
          align-items: flex-end;
        }
        .legaltit{
          margin-right: 6px;
        }
        .legaltit,
        .legalname,
        .legalmore {
          height: 20px;
          font-family: "PingFang SC";
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #666666;
        }
        .legalmore:hover {
          cursor: pointer;
        }
        .legalname {
          color: #333333;
        }
        .legal-control-con{
          height: 76px;
          overflow: hidden;
        }
        .legalname-control{
          height: auto;
        }
        .expandmorecss {
          height: auto;
        }
        .legalmore,
        .website {
          color: #1f7ff4;
          cursor:pointer;
        }
        .website:hover{
          cursor:pointer;
        }
        .legalmore {
          margin-left: 10px;
        }
        .control-change {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          height: 20px;
        }
        .control-changeyear {
          margin-right: 10px;
        }
      }
      .openchange {
        display: flex;
        justify-content: flex-end;
        margin-top: -28px;
      }
      .block-info-introduction {
        display: flex;
        align-items: baseline;
        .legaltit {
          height: 20px;
          font-family: "PingFang SC";
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #666666;
        }
        .legal-introduction {
          max-width: 748px;
          height: auto;
        }
        .legaltit-produc {
          display: flex;
          align-items: flex-end;
        }
      }
      .introductiondetail {
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1; /*设置显示行数，此处为2行*/
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
      .open {
        color: #999999 !important;
        margin-left: 7px;
        display: flex;
        align-items: center;
      }
      .open:hover {
        cursor:pointer
      }
      .openimg {
        width: 12px;
        height: 12px;
        margin-left: 4px;
        margin-top: 1px;
      }
      .block-info-once {
        margin-top: 20px;
      }
    }
    .message-block-info-not {
      width:100%
    }
    .con-platform-slogin {
      width: 1240px;
      margin: auto;
      margin-top: 20px;
      .component-con{
        padding-bottom: 20px;
        box-sizing: border-box;
      }
    }
  }
}
