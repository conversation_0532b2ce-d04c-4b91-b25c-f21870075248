@mixin img-cover {
  // 另图片铺满容器
  width: 100%;
  height: 100%;
}
.home-container {
  background: #F7F8FA;
  .home-top {
    width: 100%;
    height: 236px;
    background: url("../../assets/images/homePage/hometop.png") no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 35px 0;
    .home-top-textfir {
      //width: 336px;
      //height: 59px;
      margin: auto;
      margin-bottom: 8px;
      color: #333;
      text-align: center;
      font-size: 42px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    .home-top-textsec {
      //width: 138px;
      //height: 25px;
      margin: auto;
      opacity: 0.8;
      color: #333;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .home-top-select {
      margin-top: 32px;
      text-align: center;
      // padding: 0 458px;
      .btn-grounp {
        display: flex;
        align-items: center;
        .btn-grounp-select {
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: "PingFang SC";
          color: #ffffff;
          margin-right: 12px;
          border-radius: 46px;
          background: #CF1A1C;
          width: 84px;
          height: 42px;
          font-size: 20px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;

        }
        .btn-grounp-reset {
          font-family: "PingFang SC";
          color: #555;
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 120%; /* 16.8px */
        }
        .btn-grounp-select:hover,.btn-grounp-reset:hover{
          cursor: pointer;
        }
      }
      .select-arrow {
        position: relative;
      }
      .select-arrow-ic {
        position: absolute;
        top: 17px;
        right: 10px;
        width: 14px;
        height: 14px;
        img {
          @include img-cover;
          float: left;
          transition: transform 0.3s;
          transform: rotateZ(0);
        }
      }
      .rotateArrow {
        transition: transform 0.3s;
        transform: rotateZ(-180deg) !important;
        cursor: pointer;
      }
    }
  }
  .home-content {
    width: 1300px;
    margin: auto;
    padding-top: 32px;
    .home-content-total {
      height: 22px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      color: #666666;
      padding-left: 30px;
    }
    .home-content-modular {
      min-height: 640px;
      padding-top: 16px;
      box-sizing: border-box;
    }
    .home-block {
      width: 423px;
      height: 112px;
      display: inline-block;
      border: 1px solid var(--e-8-e-8-e-8, #E8E8E8);
      background: #FFF;
      box-sizing: border-box;
      margin-right: 15px;
      margin-bottom: 16px;
      padding: 18px 12px;
      box-sizing: border-box;
    }
    .home-block:nth-child(3n) {
      margin-right: 0;
    }
    .home-block-con {
      display: flex;
      height: 100%;
    }
    .block-left {
      width: 60px;
      height: 100%;
      .block-titimg {
        width: 48px;
        height: 48px;
        margin: 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          @include img-cover;
        }
      }
      // .default-image{
      //   border-radius: 100%;
      //   width: 65px !important;
      //   height: 65px !important;
      // }
    }

    .block-right {
      width: calc(100% - 88px);
      height:100%;
      box-sizing: border-box;
      // .block-code {
      //   display: flex;
      //   margin-bottom: 4px;
      .block-coname {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        margin-bottom: 8px;
        box-sizing: border-box;
        height: 22px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 22px;
        color: #333333;

      }
      .block-cotag {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        height: 18px;
      }
      .cotag-sign {
        height: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        border-radius: 4px;
        padding: 0.5px 6px;
        margin-right: 4px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
      }
      .cotag-style:nth-child(3n+1){
        background: #FFF2E6;
        border: 1px solid #FFE5CC;
        color: #FF7D00;
      }
      .cotag-style:nth-child(3n+2){
        background: #E8EDFE;
        border: 1px solid #E8EEFE;
        color: #425BAB;
       }
      .cotag-style:nth-child(3n+3){
        color: #FE5461;
        background: #FFEEEE;
        border: 1px solid #FFDDDF;
       }
      .cotag-signfir {
        background: #E8EDFE;
        border: 1px solid #E8EEFE;
        color: #425BAB;
      }
      .cotag-signsec {
        background: #FFF2E6;
        border: 1px solid #FFE5CC;
        color: #FF7D00;
      }
      .cotag-signthir {
        color: #FE5461;
        background: #FFEEEE;
        border: 1px solid #FFDDDF;
      }
      .cotag-sign:nth-child(1) {
        margin-right: 4px;
      }
      // }
      .block-companyoraddress {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
      }
      .block-name {
        margin-right: 10px;
        height: 20px;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }
      .block-address {
        display: flex;
        align-items: center;
        .block-adimg {
          width: 14px;
          height: 14px;
          margin-right: 2px;
          display: flex;
          img {
            @include img-cover;
          }
        }
        .block-adname {
          height: 17px;
          font-family: "PingFang SC";
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 17px;
          color: #999999;
          max-width: 75px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
        }
      }
    }
  }
  .home-pagina {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0 60px 0;
  }
}
