<template>
  <div class="home-container" id="equityHomePage">
    <div class="home-top">
      <div class="home-top-textfir">上市公司及股东综合金融服务</div>
      <div class="home-top-textsec">利他共赢&nbsp;&nbsp;&nbsp;益他共荣</div>
      <div class="home-top-select">
        <el-form :inline="true" :model="formInline">
          <el-form-item>
            <el-input
              v-model="formInline.companyCode"
              placeholder="公司代码/简称"
            ></el-input>
          </el-form-item>
          <el-form-item class="select-arrow">
            <el-area-selector
                placeholder="地区"
                description="地区"
                :width="1050"
                :top="5"
                :lock-screen="true"
                :max-display="4"
                :data-depth="3"
                :data-key="'value'"
                :data-label="'label'"
                :data-value="'id'"
                :data-initial="'initial'"
                :data-options="areaSelDataList"
                @confirm-event="selectCancelEventDomestic"
                @cancel-event="selectCancelEventDomestic"
                ref="areasSelectShow"
                id="areasSelectShow1"
              >
            </el-area-selector>
          </el-form-item>
          <el-form-item class="select-arrow">
            <el-tree-multi-selector
                  ref="selectIndustryAll"
                  placeholder="行业"
                  description="行业"
                  :titles="['证监会行业','申万行业']"
                  :width="1100"
                  :top="5"
                  :lock-screen="true"
                  :data-depth="[2,3]"
                  :data-key="'labelValue'"
                  :data-label="'labelName'"
                  :data-value="'labelValue'"
                  :data-options="optionData.industryMulti"
                  @confirm-event="selectConfirmEventMulti"
                  @cancel-event="selectConfirmEventMulti"
                  @clear-event="selectConfirmEventMulti">
                </el-tree-multi-selector>
          </el-form-item>
          <el-form-item>
            <el-select-multiple ref="companyNatureStrRef" showSelectAll class="container-el" v-model="formInline.businessType" confirm  placeholder="公司性质">
              <el-select-multiple-option
                  v-for="(item,index) in orgFormSelDataList"
                  :key="index"
                  :label="item.labelName"
                  :value="item.labelValue">
              </el-select-multiple-option>
            </el-select-multiple>
          </el-form-item>
          <el-form-item>
            <div class="btn-grounp">
              <div class="btn-grounp-select" @click="onSubmit">查询</div>
              <div class="btn-grounp-reset" @click="resetformInline">重置</div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="home-content">
      <div class="home-content-total">共 {{ companyTotal }} 家公司</div>
      <div class="home-content-modular">
        <div
          class="home-block"
          v-for="(item, index) in companyList"
          :key="index"
          @click="toDetail(item.companyCode)"
          style="cursor:pointer"
        >
          <div class="home-block-con">
            <div class="block-left">
              <div class="block-titimg">
                <img v-if="item.logoUrl" :src="'https://oss.valueonline.cn/'+ item.logoUrl" alt="icon" />
                <img
                  v-else style="width: 100%;height: 100%"
                  src='@/assets/images/default-company.png'
                  alt="默认图片"
                />
              </div>
            </div>
            <div class="block-right">
              <div class="block-coname">{{ item.zhSortName }}&nbsp;&nbsp;{{ item.companyCode }}</div>
              <div class="block-cotag">
                <div class="cotag-sign cotag-signfir cotag-style"
                     v-for="(i, idx) in item.tagList" :key="idx">
                  {{ i }}
                </div>
              </div>
              <div class="block-companyoraddress">
                <div class="block-name" :title="item.zhName">
                  {{ item.zhName }}
                </div>
                <div class="block-address">
                  <div class="block-adimg">
                    <img
                      src="@/assets/images/homePage/homeAdressIcon.png"
                      alt="adress"
                    />
                  </div>
                  <div class="block-adname" :title="item.address">{{item.city}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-pagina" v-show="companyTotal > 0">
      <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :current-page="formInline.startRow"
          :currentPage="pageNum"
          :page-size="formInline.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="companyTotal">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { selectData, getCompanyList } from "@/api/home-api";
import common from "@/mixins/common.js";

export default {
  name: "homePage",
  mixins: [common],
  data () {
    return {

      // 下拉图标旋转开关
      ifRotate: false,
      companyCode: '',
      ifRotateSec: false,
      orgFormSelDataList: [],
      // 地区数据
      areaSelDataList: [],
      // 行业数据
      optionData: {},
      pageNum: 0,
      formInline: {
        companyCode: "",
        region: [],
        logoUrl: "",
        industryCSRCStr: [],
        industrySWStr: [],
        businessType: [],
        pageSize: 18,
        startRow: 0
      },
      businessTypeList: [],
      // 公司总数
      companyTotal: 0,
      companyList: []
    };
  },
  mounted () {
    // 获取初始数据
    this.getSelectData();
    // 查询公司信息
    this.getCompanyList()
  },
  methods: {
    getCompanyList () {
      let region = this.formInline.region;
      if (region != null && region.length !== 0 && region !== "") {
        region = region.split(",");
      } else {
        region = []
      }
      let industryCSRCStr = this.formInline.industryCSRCStr;
      if (industryCSRCStr.length !== 0) {
        industryCSRCStr = industryCSRCStr.split(",");
      }
      let industrySWStr = this.formInline.industrySWStr;
      if (industrySWStr.length !== 0) {
        industrySWStr = industrySWStr.split(",");
      }
      let param = {
        companyCode: this.companyCode,
        zhName: this.formInline.companyCode,
        city: region,
        industryCSRCStr: industryCSRCStr,
        industrySWStr: industrySWStr,
        orgform: this.formInline.businessType,
        pageSize: this.formInline.pageSize,
        startRow: this.formInline.startRow
      }

      getCompanyList(param).then(res => {
        let _this = this;
        _this.companyList = res.data.tableData;
        _this.companyList.forEach(function (item) {
          if (_this.getValue(item.systemState) !== '') {
            item.tagList = item.systemState.split(",");
          }
        });
        this.companyTotal = res.data.total;
        if (res.data.tableData.length === 0) {
          param.startRow = 0;
          getCompanyList(param).then(res => {
            let _this = this;
            _this.companyList = res.data.tableData;
            _this.companyList.forEach(function (item) {
              if (_this.getValue(item.systemState) !== '') {
                item.tagList = item.systemState.split(",");
              }
            });
            this.companyTotal = res.data.total;
          })
        }
      })
    },
    getSelectData () {
      selectData().then(res => {
        this.industryCrscList = res.data.result.industryCrscList;
        this.orgFormSelDataList = res.data.result.companyNatureList;
        this.optionData.industryMulti = [res.data.result.industryCrscList, res.data.result.industrySWList];
        this.areaSelDataList = res.data.result.areaSelDataList
      })
    },
    // 处理地区参数
    selectCancelEventDomestic (result) {
      this.formInline.region = this.changeStr(result, 'area');
    },
    // 处理行业参数
    selectConfirmEventMulti (result) {
      // 数组里面两个模块title
      if (result[0].length > 0) {
        this.formInline.industryCSRCStr = this.changeStr(result[0], 'industry');
      } else {
        this.formInline.industryCSRCStr = []
      }
      if (result[1].length > 0) {
        this.formInline.industrySWStr = this.changeStr(result[1], 'industry');
      } else {
        this.formInline.industrySWStr = []
      }
    },
    // 将参数转换成字符串
    changeStr (result, keyword) {
      let arr = '';
      if (result.length > 0) {
        result.forEach((iteam, index) => {
          if (keyword === 'industry') {
            if (index === result.length - 1) {
              arr += iteam.labelValue;
            } else {
              arr += iteam.labelValue + ",";
            }
          } else if (keyword === 'area') {
            if (index === result.length - 1) {
              arr += iteam.value;
            } else {
              arr += iteam.value + ",";
            }
          } else {
            if (index === result.length - 1) {
              arr += iteam;
            } else {
              arr += iteam + ",";
            }
          }
        })
      }
      return arr;
    },
    // 查询
    onSubmit () {
      this.pageNum = 0
      this.formInline.startRow = 0
      this.getCompanyList()
    },
    // 重置
    resetformInline () {
      this.formInline = {
        companyCode: "",
        region: [],
        industryCSRCStr: [],
        industrySWStr: [],
        businessType: []
      };
      this.$refs.areasSelectShow.clear();
      this.$refs.selectIndustryAll.clear();
      this.$refs.companyNatureStrRef.clear()
      // 模拟重新调用查询页面接口
      location.reload();
    },
    // 分页
    handleCurrentChange (val) {
      this.pageNum = val;
      // 设置当前页码
      this.formInline.startRow = (val - 1) * this.formInline.pageSize;
      // 重新获取数据
      this.getCompanyList();
    },
    handleSizeChange (val) {
      this.formInline.pageSize = val; // 设置显示数
      this.formInline.startRow = (this.pageNum - 1) * val; // 设置当前页码
      this.getCompanyList(); // 重新获取数据
    },

    // 跳转详情
    toDetail (companyCode) {
      this.$emit('toDetail', companyCode);
    }
  }
};
</script>
<style lang="scss" scoped>
@import "./homePage.scss";
</style>
<style lang="scss">
#equityHomePage {
  .el-button{
    line-height: 2;
  }
  .home-top .el-form--inline .el-form-item {
    margin-right: 8px;
  }
  .home-top .el-input--medium .el-input__inner {
    height: 42px;
    width: 240px;
    font-size: 16px;
    border-radius: 35px;

  }
  .home-top .select-arrow .el-input--medium .el-input__inner {
    height: 42px;
    width: 189px;
    border-radius:35px 0 0 35px;

  }
  .select-arrow{
    .el-input--medium .el-input__number {
      line-height: 42px!important;
    }
  }
  .home-top .select-area .el-input--medium .el-input__inner {
    width: 120px;
    //border-radius: 35px;

  }
  .home-top .el-input-group.is-readonly {
    border: none!important;
  }
  .home-top .select-induc .el-input--medium .el-input__inner {
    border-radius: 3px !important;
  }
  .home-top .el-input__inner::-webkit-input-placeholder {
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #c1c1c1;
  }
  .home-top {
    .el-input-group__append,.el-input-group__prepend {
      border-radius:0 35px 35px  0;
    }
    .el-input-group__append button.el-button:hover, .el-input-group__prepend button.el-button:hover{
      border-radius:0 35px 35px  0;
    }
    .el-input-group__append .el-button{
      padding: 5px 16px;
    }
  }
}
</style>
