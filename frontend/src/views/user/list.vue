<template>
    <div class="user-list">
        <div class="main-header">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item>用户管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="m-c">
            <el-card class="box-card">
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-input placeholder="ID" v-model="query.condition.id"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input placeholder="用户名" v-model="query.condition.userName"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input placeholder="邮箱" v-model="query.condition.mail"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input placeholder="手机号码" v-model="query.condition.telephone"></el-input>
                    </el-col>
                </el-row>
                <el-row :gutter="10" class="m-t-10">
                    <el-col :span="6" >
                        <el-input placeholder="角色名称" v-model="query.condition.roleName"></el-input>
                    </el-col>
                    <el-col :span="18" class="text-right">
                        <el-button @click="search" type="primary" size="small">查询</el-button>
                        <el-button @click="clearClick" size="small">清空条件</el-button>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="box-card m-t-20">
                <el-row>
                    <el-col :span="12">
                        用户列表
                    </el-col>
                    <el-col :span="12" class="text-right">
                        <el-button @click="openAddUserModal(null)" type="primary">新增用户</el-button>
                        <el-button @click="batchDelete">批量删除</el-button>
                    </el-col>
                </el-row>
                <el-table
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        class="m-t-20 full">
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="ID"
                            prop="id">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="用户名"
                            prop="userName">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="姓名"
                            prop="realName">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="手机号码"
                            prop="telephone"
                            width="120">
                        <template slot-scope="scope">
                            <span v-if="scope.row.telephone">{{scope.row.telephone}}</span>
                            <span class="empty-text" v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="邮箱"
                            prop="mail">
                        <template slot-scope="scope">
                            <span v-if="scope.row.mail">{{scope.row.mail}}</span>
                            <span class="empty-text" v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="角色"
                            prop="realName">
                        <template slot-scope="scope">
                            <span v-if="scope.row.roleName">{{scope.row.roleName}}</span>
                            <span class="empty-text" v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="加入时间"
                            prop="createTime"
                            width="160">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="操作"
                            width="100px">
                        <template slot-scope="scope">
                          <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="openAddUserModal(scope.row)"></i>
                          <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteClick(scope.row.id,scope.$index)"></i>
                          <i class="iconfont ic-user-yes custom-icon-button" title="启用用户"
                             v-if="scope.row && scope.row.status === '1'" @click="disabledClick(scope.row)"></i>
                          <i class="iconfont ic-user-no custom-icon-button" title="禁用用户" v-else @click="disabledClick(scope.row)"></i>
                          <i class="iconfont ic-refresh custom-icon-button" title="重置密码" @click="resetPassword(scope.row.id)"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="m-t-10 text-right">
                    <pagination
                      :pageSize="query.pageSize"
                      :total="total"
                      @current-change="handleCurrentChange"
                      @size-change="handleSizeChange">
                        :currentPage="pageNum"
                    </pagination>
                </div>
            </el-card>
        </div>
        <add-user :isShow="showModal" :updateData="currentRow" @addUserModalClose="addUserModalClose"></add-user>
    </div>
</template>
<script>
import { showMessage, showConfirm } from 'stock-vue-plugin/lib/utils/message'
import addUser from '../../components/Modal/addUser'
import { _queryUserList, _deleteUser, _modifyUser, _resetPassWord } from "../../api/user-api";
import Pagination from '../../components/Pagination/pagination';
export default {
  components: { addUser, Pagination },
  data () {
    return {
      // 选中的数据
      multipleSelection: [],
      // 新增用户弹出框是否打开
      showModal: false,
      pageNum: 1,
      query: {
        condition: {
          // id
          id: '',
          // 用户名
          userName: '',
          // 邮箱
          mail: '',
          // 角色
          roleName: '',
          // 电话
          telephone: ""
        },
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 20
      },
      // 当前编辑行
      currentRow: {},
      // 数据总数
      total: 10,
      // table 列表数据
      tableData: []
    }
  },
  mounted () {
    // 获取用户列表数据
    this.getUserData();
  },
  methods: {
    /* 获取用户列表数据
      * */
    getUserData () {
      // todo get list
      _queryUserList(this.query).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.data;
          this.total = res.data.result.total;
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 查询
      * */
    search () {
      this.getUserData();
    },
    /* 切换页码
      * val：当前的页码
      * */
    handleCurrentChange (val) {
      this.pageNum = val;
      // 设置当前页码
      this.query.startRow = (val - 1) * this.query.pageSize;
      // 重新获取数据
      this.getUserData();
    },
    /* 切换每页显示多少条数据
      * val：每页显示得数量
      * */
    handleSizeChange (val) { // 切换一页显示多少条数据
      this.query.pageSize = val; // 设置显示数
      this.query.startRow = (this.pageNum - 1) * val; // 设置当前页码
      this.getUserData(); // 重新获取数据
    },
    /* 清空条件
      * */
    clearClick () {
      Object.assign(this.query, this.$options.data().query); // 重置数据
      this.getUserData(); // 重新获取数据
    },
    /* 批量删除
                    * */
    batchDelete () {
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage('info', this.$messageText['deleteSelectNone']);
        return;
      }
      let index = -1;
      // 当前只选中了一条数据，查找当前数据的索引
      if (this.multipleSelection.length === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === this.multipleSelection[0].id) {
            index = i;
          }
        }
      }
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', { idList: this.multipleSelection, index: index })
    },
    /* table checkbox 选中事件
                    * val:选中得数据
                    * */
    handleSelectionChange (val) {
      this.multipleSelection = val.map((i) => { return i.id });
    },
    /* 删除单条角色数据
                    * id:要删除数据的id
                    * index:要删除数据的索引
                    * */
    deleteClick (id, index) {
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', { idList: [id], index: index });
    },
    /* 删除单条角色数据请求
                   * */
    deleteClickData (param) {
      // 删除角色
      _deleteUser({ idList: param.idList.join(',') }).then(res => {
        if (res.data.success) {
          // 只删除一条数据并且索引大于-1
          if (param.idList.length === 1 && param.index >= 0) {
            // 从列表中删除数据
            this.tableData.splice(param.index, 1);
          } else {
            // 重新获取数据
            this.getUserData();
          }
          // 提示删除成功消息
          showMessage('info', this.$messageText['deleteSuccess']);
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 关闭新建用户弹出框
              * flag：是取消还是确定操作
              * */
    addUserModalClose (flag) {
      // 关闭弹出框
      this.showModal = false;
      // 确定操作
      if (flag) {
        // 刷新列表
        this.getUserData();
      }
    },
    /* 编辑角色
    * item:要编辑的数据
    * */
    openAddUserModal (item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showModal = true;
    },
    /* disabledClick
      *id:要禁用的用户的id
      * index：要禁用用户的索引
       *  */
    disabledClick (item) {
      let title = 'disabledTitle', content = 'disabledContent';
      if (item.status && item.status === '1') {
        title = 'enDisabledTitle';
        content = 'enDisabledContent';
      }
      if (item) { showConfirm(this.$messageText[content], this.$messageText[title], this, 'disabledUser', { item: item }); }
    },
    /* 重置密码
      * id:要重置密码的数据
      * */
    resetPassword (id) {
      showConfirm(this.$messageText['resetPasswordContent'], this.$messageText['resetPasswordTitle'], this, 'resetUserPassword', { id: id });
    },
    /* 重置用户密码
      * param:要重置的用户数据
      * */
    resetUserPassword (param) {
      _resetPassWord({ id: param.id }).then(res => {
        if (res.data.success) {
          showMessage('info', this.$messageText['resetPasswordSuccess']);
        } else {
          showMessage('error', res.data.errorMsg)
        }
      })
    },
    /* disabledUser
     *id:要禁用的用户的id
     * index：要禁用用户的索引
      *  */
    disabledUser (item) {
      item.item.status = item.item.status && item.item.status === '1' ? "0" : "1";
      let message = item.item.status && item.item.status === '1' ? "disabledSuccess" : "enDisabledSuccess";
      _modifyUser(item.item).then(res => {
        if (res.data.success) {
          // 提示删除成功消息
          showMessage('info', this.$messageText[message]);
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
    .user-list{
        .user-list__avatar{
            width:50px;
            height:50px;
        }
    }
</style>
