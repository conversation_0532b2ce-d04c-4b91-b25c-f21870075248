<template>
  <div class="add-user">
    <el-dialog
      width="600px"
      :before-close="handleClose"
      :title="formInline.id?'编辑用户':'新增用户'"
      v-model="showing"
      append-to-body
      modal>
      <div>
        <el-form :model="formInline" label-width="110px" :rules="rules" ref="editForm">
          <el-row>
            <el-col :span="24">
              <el-form-item label="用户名" prop="userName">
                <el-input v-model="formInline.userName" placeholder="用户名" :maxLength="64"></el-input>
              </el-form-item>
              <el-form-item label="姓名" prop="realName">
                <el-input v-model="formInline.realName" placeholder="姓名" :maxLength="64"></el-input>
              </el-form-item>
              <el-form-item label="手机" prop="telephone">
                <el-input v-model="formInline.telephone" placeholder="手机"></el-input>
              </el-form-item>
              <el-form-item label="邮箱" prop="mail">
                <el-input v-model="formInline.mail" placeholder="邮箱"></el-input>
              </el-form-item>
              <el-form-item label="角色">
                <el-select v-model="formInline.roleIdList" multiple placeholder="角色" clearable class="full">
                  <el-option :label="item.codeName" :value="item.codeValue" v-for="(item,index) in roleList" :key="'role' + index"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </div>
      <div slot="footer" class="text-center">
        <el-button @click="closeModal(false)">取消</el-button>
        <el-button type="primary" @click="sureClick">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { _validatePhone, _validateEmail } from '@/utils/validate'
import store from '@/store'
import { _getRoleDownList, _createUser, _queryUserById, _modifyUser, _userNameCheck } from "@/api/user-api";
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data () {
    // 电话号码验证
    const validatePhone = function (rule, value, callback) {
      if (value != null && value !== '' && !_validatePhone(value)) {
        callback(new Error('请输入正确手机号'))
        return
      }
      callback()
    }
    // 电子邮件验证
    const validateEmail = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateEmail(value)) {
        callback(new Error('请输入正确邮箱'))
        return
      }
      callback()
    }
    const userNameCheck = (rule, value, callback) => {
      if (!value || value.length === 0) {
        return callback(new Error("请输入用户名"));
      } else {
        _userNameCheck({ id: this.formInline.id, userName: value }).then(res => {
          if (res.data.success) {
            if (!res.data.result) {
              callback();
            } else {
              return callback(new Error("用户名已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        realName: "", // 姓名
        // 用户名
        userName: "",
        // 电话
        telephone: "",
        // 邮箱
        mail: "",
        // 角色
        roleIdList: []
      },
      // 允许上传的文件类型列表
      acceptList: [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".JPG", ".JPEG", ".PBG", ".GIF", ".BMP"],
      // 表单验证规则
      rules: {
        realName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { validator: userNameCheck, trigger: 'blur' }
        ],
        telephone: [
          { validator: validatePhone, trigger: 'blur' }
        ],
        mail: [
          { validator: validateEmail, trigger: 'blur' }
        ]
      },
      // 角色列表
      roleList: []
    }
  },
  computed: {
    // 设置上传头像的请求头
    setHeaders () {
      let param = {
        Authorization: store.getters.token,
        'X-Tenant-Info': store.getters.info
      }
      return param;
    }
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose () {
      this.closeModal(false);
    },
    /* 保存角色
              * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          if (this.formInline.id) {
            _modifyUser(this.formInline).then(res => {
              // 成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示消息
                this.showGlobalMessage('info', this.$messageText['updateSuccess']);
              } else {
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          } else {
            _createUser(this.formInline).then(res => {
              // 成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示消息
                this.showGlobalMessage('info', this.$messageText['saveSuccess']);
              } else {
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal (flag) {
      this.$emit('addUserModalClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
       *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    /* 上传文件
      * */
    uploadFile (file) {
      // 获取上传的文件类型
      let type = file.file.type.split('/')[1];
      // 判断选择的文件是不是图片
      if (this.acceptList.indexOf('.' + type) === -1) {
        // 提示错误信息
        this.showGlobalMessage('error', this.$messageText['fileTypeError']);
        return;
      }
      // 获取选择的文件的大小
      let size = file.file.size / 1024 / 1024;
      // 文件大小限制在20M
      if (size >= 20) {
        // 提示错误信息
        this.showGlobalMessage('error', this.$messageText['fileSizeError']);
        return;
      }
      // todo upload image
      console.log('todo upload image');
    },
    /* 获取角色列表数据
    * */
    getRoleData () {
      _getRoleDownList().then(res => {
        if (res.data.success) {
          this.roleList = res.data.result;
          if (this.formInline.id) {
            // 获取用户详细信息
            this.queryUserById();
          }
        } else {
          this.showGlobalMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 根据id获取用户详细信息
      * */
    queryUserById () {
      _queryUserById({ id: this.formInline.id }).then(res => {
        if (res.data.success) {
          this.formInline = res.data.result;
          this.formInline.status = ''
        } else {
          this.showGlobalMessage('error', res.data.errorMsg);
        }
      })
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData) {
          Object.assign(this.formInline, this.updateData);
        }
        // 获取角色列表
        this.getRoleData();
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss">
  .add-user{
    .add-user__upload{
      .el-upload{
        border: 1px dashed $--el-border-color-lighter;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        height:178px;
        display:flex;
        align-items: center;
        width: 178px;
        justify-content: center;
        color:$--el-color-text-placeholder;
        .avatar-uploader-icon {
          font-size: 28px;
          color: $--el-color-text-placeholder;
        }
      }
    }
    .add-user__img{
      width:178px;
      height:178px;
    }
  }
</style>
