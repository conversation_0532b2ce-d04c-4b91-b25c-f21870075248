<template>
  <home-page-detail v-if="orgType > 3 || detailFlag"
                    :company-code="companyCode"
                    :com-flag="comFlag"
                    @toBlock="toBlock"></home-page-detail>
  <home-page v-else-if="orgType > 0" @toDetail="toDetail"></home-page>
  <div v-else></div>
</template>

<script>
import store from "@/store";
import common from "@/mixins/common";
import HomePage from "@/views/home/<USER>";
import HomePageDetail from "@/views/home/<USER>";

export default {
  name: "index",
  components: { HomePageDetail, HomePage },
  mixins: [common],
  data () {
    return {
      orgType: -1,
      companyCode: "",
      detailFlag: false,
      comFlag: true
    }
  },
  created () {
    let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
    if (!isNaN(data.orgType)) {
      this.orgType = Number(data.orgType)
      this.comFlag = this.orgType > 3;
    }
    this.detailFlag = false;
    this.companyCode = store.state.app.info.companyCode;
  },
  methods: {
    toDetail (companyCode) {
      this.detailFlag = true;
      this.companyCode = companyCode;
    },
    toBlock () {
      this.detailFlag = false;
      this.companyCode = store.state.app.info.companyCode;
    }
  }
}
</script>

<style scoped lang="scss">

</style>
