.login-content {
  height: 100vh;

  background: #FFF;


.login-img{
  background-color: #f1f2f7;
  display: flex;
  align-items: center;
  .IMG{
    max-width:100%;
    max-height:100%;
    margin: 0 auto;
    object-fit: contain;
  }
}
  .tits {
    color: var(--333333, #333);
    font-family: "Source Han Sans CN";
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    a {
      color: $--color-white;
    }
  }

  .hr {
    padding-left: 0px;
    padding-right: 1px;
    font-size: 20px;
    color: #736b6b;
  }

  .login-header {
    position: fixed;
    top: 24px;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 52px;
    //background: rgba(255, 255, 255, 1);
    padding-top: 8px;
    padding-bottom: 10px;

    .imgBox {
      padding-left: 20px;

      &.pd {
        //padding:7px 0;
      }
    }

    .fixed-footer {
      //position: absolute;
      //bottom: 0;
      //left: 0;
      //width: 100%;
      //height: 1px;
      //background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.3));
    }
  }

  .login-body-box {
    border-radius: 10px;
    width: 290px;
    //height: 430px;
    background: rgba(255, 255, 255, 0.9);

    .hygl{
      color: var(--333333, #333);
      font-family: "Source Han Sans CN";
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: 20px;
    }
    .logo_self {
      height: 120px;
      margin-left: 20px;
    }
  }

  .sendBut {
    padding: 11px 10px;
  }

  .sendBut:hover {
    color: #fff;
  }
}
