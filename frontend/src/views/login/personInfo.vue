<template>
    <div class="person-info">
        <div class="main-header">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item>个人中心</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="m-c">
            <el-card class="box-card">
                <el-row :gutter="50">
                    <el-col :span="14">
                        <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
                            <el-form-item label="角色" prop="name">
                                <span>{{formInline.realName}}</span>
                            </el-form-item>
                            <el-form-item label="邮箱" prop="mail">
                                <el-input placeholder="邮箱" v-model="formInline.mail"></el-input>
                            </el-form-item>
                            <el-form-item label="姓名">
                                <el-input placeholder="用户名" v-model="formInline.realName"></el-input>
                            </el-form-item>
                            <el-form-item label="电话" prop="telephone">
                                <el-input placeholder="电话" v-model="formInline.telephone"></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button @click="updateUser" type="primary">更新用户信息</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="6">
                        <img alt="avatar" class="person-info__avatar"
                        src="https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png">
                        <el-upload
                                :on-preview="handlePreview"
                                :on-remove="handleRemove"
                                action="https://jsonplaceholder.typicode.com/posts/"
                                class="upload-demo"
                                list-type="picture">
                            <el-button class="person-info__button m-t-20" size="small" type="primary">上传头像</el-button>
                        </el-upload>
                    </el-col>
                </el-row>
            </el-card>
        </div>
    </div>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import { _queryUserById, _modifyUserCenter } from "@/api/manage-api";
import { _validatePhone, _validateEmail } from '@/utils/validate'
import store from '@/store'
export default {
  data () {
    // 电话号码验证
    const validatePhone = function (rule, value, callback) {
      if (value != null && value !== '' && !_validatePhone(value)) {
        callback(new Error('请输入正确手机号'))
        return
      }
      callback()
    }
    // 电子邮件验证
    const validateEmail = function (rule, value, callback) {
      if (value != null && value !== '' && !_validateEmail(value)) {
        callback(new Error('请输入正确邮箱'))
        return
      }
      callback()
    }
    return {
      formInline: {
        // 电子邮件
        mail: "",
        // 用户名
        realName: "",
        // 电话号码
        telephone: ""
      },
      // 表单验证规则
      rules: {
        telephone: [
          { validator: validatePhone, trigger: 'blur' }
        ],
        mail: [
          { validator: validateEmail, trigger: 'blur' }
        ]
      }
    }
  },
  mounted () {
    // 获取菜单列表
    this.queryUserById();
  },
  methods: {

    /* 获取菜单列表数据
              * */
    queryUserById () {
      _queryUserById({ id: store.state.app.info.id }).then(res => {
        if (res.data.success) {
          this.formInline = res.data.result;
        }
      })
    },
    /* 编辑用户
    * */
    updateUser () {
      // 打开编辑角色弹出框
      _modifyUserCenter(this.formInline).then(res => {
        if (res.data.success) {
          showMessage('info', this.$messageText['updateSuccess'])
          store.commit('SET_INFO', Object.assign({}, this.formInline));
          this.setInfo(this.formInline);
        } else {
          showMessage('error', res.data.errorMsg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
    .person-info{
        .person-info__avatar{
            width:200px;
            height:200px;
        }
        .person-info__button{
            margin-left:50px;
        }
    }
</style>
