<style lang="scss">
@import '../../../src/styles/page-title.scss';
</style>
<template>
  <div class="container" style="padding: 20px 20px 0 10px">
    <div class="page-title" style="padding-left: 10px">
      <i></i>
      <span>法规检索</span>
    </div>
    <iframe id="lawSearchFrame1" :src="url" style="width: 102%;height: 830px;" frameborder="no"
            scrolling="yes"></iframe>
  </div>
</template>

<script>
import { getTokenAndTenantInfo } from "@/api/regulation-api.js";

export default {
  name: "regulationSearch",
  data () {
    return {
      url: ''
    }
  },
  mounted () {
  },
  created () {
    let token, ydBaseUrl;
    let param = {};
    getTokenAndTenantInfo(param).then(res => {
      token = res.data.result.token;
      ydBaseUrl = res.data.result.ydBaseUrl + "/ui/laws/laws/lawsCommon";
      this.url = ydBaseUrl +
          '?access_token=' + token +
          "&lawsApplyModule=1" +
          "&hasLawsApplyModule=1,4,7,15,2,3,9,18,11,6" +
          "&tenant_info=417SOVzTq-Dn1SHP0e4KyXSfkaQECi9zCQo64PhIDEqTCTFXRUfjLS-LeqZtqJL0Mn7vYXt7X5DryEDG44PDCLI_cm3Tsn6g__cTZuv6E21ma8c7zcRDMzfgXuH4kbeeQymzcyCaFIFTUEhPJiVdNcjFl87L6goNwGxVb4zCAsSk5VQ0U1b0m63iSGgfDC545aq4XStYr-4bOIp0tRcdmVK9TKM3rlv9SdAjAuIg_sOUI_7Hl1INHdIzTKdCZn_LVZCvwBiLzUU7MkE1eLF9t2BTUg6jHqdFGgmJNMhs6Z10gAvnRP7AqNcQD-QmSDE1-qDLBzNzT1_YMmgfvNVL6w%3D%3D&"
    })
  },
  methods: {}
}
</script>
