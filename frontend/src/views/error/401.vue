<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
    @import "./style.scss";
</style>
<!-- Template -->
<template>
    <div class="view-container container text-center">
        <div class="error-container">
            <div class="error-code">
                <span>4</span>
                <span><span class="screen-reader-text">0</span></span>
                <span>1</span>
            </div>
            <div class="error-message">
                抱歉，您需要登录后访问。
            </div>
            <back-button></back-button>
        </div>
    </div>
</template>

<!-- Script -->
<script>
import backButton from '@/views/error/back';
const _ERROR_401 = {
  name: 'ERROR_401',
  components: { backButton }
};
export default _ERROR_401;
</script>
