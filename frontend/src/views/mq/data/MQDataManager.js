import {getMessageQueue, getQueryInfo} from "@/api/mqManage/MQManage-api";

/**
 * 消息队列管理页面数据管理类
 */
export class DataManager {
   constructor(title, index) {
       this.title = title
       this.currentPage = index
       this.tableData = []
       this.total = 0
       this.searchData = []
       this.tableColumn =[]
    }

    /**
     * 获取表格数据
     * @param searchCondition
     */
    getTableData(searchCondition) {
        getMessageQueue(searchCondition).then(res => {
            this.setTableData(res)
        })
    }

    setTableData(res) {
        console.log(res.data.result)
        this.tableData = res.data.result.messages
        this.total = res.data.result.total
    }

    search(dto) {
       this.searchData.find(item => !item.isSelect).search(dto, this)
    }
    clear(dto) {
        this.searchData.forEach(item => item.model = '')
        getMessageQueue(dto).then(res => {
            this.setTableData(res)
        })
    }

    handlePagination(dto) {
        getMessageQueue(dto).then(res => {
            this.setTableData(res)
        })
    }

    /**
     * 防止多次请求后端
     * @returns {DataManager}
     */
    getRequestData() {
       return this
    }


}

class MessageQueueManager extends DataManager {
    constructor() {
        super('消息管理', 'mq');
        this.tableColumn = [
                // new TableColumn('目标服务','sysName',false,100),
                // new TableColumn('业务类型','businessType',false,150),
                // new TableColumn('操作类型', 'operationType', false, 100),
                // new TableColumn('消息类型', 'codeName', false, 100),
                new TableColumn('发送时间', 'createTime', false, 200),
                new TableColumn('发送内容','info',false, 500),
                new TableColumn('错误信息', 'errorMessage', false,300),
        ]
    }

    getRequestData() {
        getQueryInfo().then(res => {
            this.searchData = [
                new SystemCodeSearchCondition('目标服务', true, res.data.result),
                // new MessageType("消息类型", true, res.data.result),
                new StatusSearchCondition('状态', true),
                new BusinessTypeSearchCondition('业务类型', true, res.data.result),
                new OperationTypeSearchCondition('操作类型', true, res.data.result),
                new CustomSearchCondition('查找发送内容', false, res.data.result)
            ]
        })
        return this
    }
}

class ReportMessage extends DataManager {
    constructor() {
        super('子系统上报消息管理页面', 'reportMessage');

    }

}


/**
 * 不同页面工厂
 * @param currentPage
 * @returns {DataManager}
 * @constructor
 */
export function DataManagerFactory(currentPage) {
    let factory = [
        new MessageQueueManager(),
        new ReportMessage()
    ]
    return factory.find(item => item.currentPage === currentPage).getRequestData()
}

/**
 * dto
 */
export class MQDto{
    constructor() {
        this.id = ''
        this.sysCode = ''
        this.info = ''
        this.isSuccess = ''
        this.messageType = ''
        this.errorMessage = ''
        this.businessType = ''
        this.operationType = ''
        this.createTitme = ''
        this.updateTime = ''
        this.startRow = 1
        this.limit = 10
    }
}

/**
 * 搜索条件
 */
class SearchCondition {
    constructor(title, isSelect) {
        this.title = title
        this.isSelect = isSelect
        this.data = []
        this.model = ''
    }
    search(dto, table) {
        dto.startRow = 1
        dto.limit = 10
        getMessageQueue(dto).then(res => {
            table.setTableData(res)
        })
    }

}

/**
 * 目标微服——搜索条件
 */
class SystemCodeSearchCondition extends SearchCondition{
    constructor(title, isSelect, queryInfo) {
        super(title, isSelect);
        this.data = queryInfo.sysCode
        this.data.forEach(item => {
            item.value = item.sysCode
            delete item.sysCode
            item.label = item.sysName
            delete item.sysName
        })
    }

    search(dto, table) {
        dto.sysCode = this.model
        super.search(dto, table);
    }
}

/**
 * 状态条件搜索
 */
class StatusSearchCondition extends SearchCondition {
    constructor(title, isSelect) {
        super(title, isSelect);
        this.data = [
            {
                label:'失败',
                value:'0'
            },
            {
                label:'成功',
                value:'1'
            },
            {
                label:'已发送',
                value:'2'
            },
            {
                label:'已接收',
                value:'3'
            }
        ]
    }

    search(dto, table) {
        dto.isSuccess = this.model
        super.search(dto, table)
    }
}

/**
 * 业务类型搜索
 */
class BusinessTypeSearchCondition extends SearchCondition {
    constructor(title, isSelect, queryInfo) {
        super(title, isSelect);
        queryInfo.businessType.forEach(item => this.data.push({label:item.label, value: item.value}))
    }
    search(dto, table) {
        dto.businessType = this.model
        super.search(dto, table);
    }
}

/**
 * 操作类型搜索
 */
class OperationTypeSearchCondition extends SearchCondition {
    constructor(title, isSelect, queryInfo) {
        super(title, isSelect);
        queryInfo.operationType.forEach(item => this.data.push({label:item.label, value: item.value}))
    }
    search(dto, table) {
        dto.operationType = this.model
        super.search(dto, table);
    }
}

/**
 * 根据用户输入内容查找消息
 */
class CustomSearchCondition extends SearchCondition {
    constructor(title, isSelect) {
        super(title, isSelect);
    }

    search(dto, table) {
        dto.info = this.model
        super.search(dto, table);
    }

}

/**
 * 消息类型搜索
 */
class MessageType extends SearchCondition {
    constructor(title, isSelect, queryInfo) {
        super(title, isSelect);
        queryInfo.messageType.forEach(item => this.data.push({label:item.codeName, value: item.codeValue}))
    }

    search(dto, table) {
        dto.messageType = this.model
        super.search(dto, table);
    }
}

/**
 * 表格列映射
 */

class TableColumn {
    constructor(label, prop, isSortable, width) {
        this.prop = prop
        this.label = label
        this.isSortable = isSortable
        this.width = width
    }

}
