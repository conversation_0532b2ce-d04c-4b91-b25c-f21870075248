<template>
  <el-dialog
      :before-close="handleClose"
      title="按钮管理"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="70%">
    <div>
<!--      <div slot="footer">-->
<!--        <el-button round type="primary" size="small" style="margin-bottom: 20px;" @click="buttonManager">新增按钮</el-button>-->
<!--      </div>-->
      <el-table :data="tableData"
                row-key="id"
                fit
                default-expand-all
                style="width: 100%;margin-bottom: 20px;color: #000"
                highlight-current-row
                :font-size="14"
                header-row-class-name	="custom-header-class"
                >
        <el-table-column label="模块名称" prop="moduleName" align="center" min-width="10%" show-overflow-tooltip/>
        <el-table-column label="模块中文名称" prop="zhName" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.zhName">{{ scope.row.zhName }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="按钮名称" prop="buttonName" align="center" min-width="15%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.buttonName">{{ scope.row.buttonName }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="按钮状态" prop="status" align="center" min-width="20%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ showButtonStatus(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" align="center" min-width="15%" show-overflow-tooltip/>
        <el-table-column label="更新人" prop="updateUser" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.updateUser">{{ scope.row.updateUser }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
<!--            <i class="iconfont icon-ic-modify custom-icon-button" title="修改状态" @click="buttonManager(scope.row)"/>-->
            <i class="iconfont ic-sync1 custom-icon-button" title="修改状态" @click="setButtonStatus(scope.row)"/>
<!--            <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteButtonInfo(scope.row)"/>-->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
    </div>
<!--    <edit-button-info title="新增按钮"-->
<!--                      :isShow="showEditButton"-->
<!--                      :updateData="buttonInfo"-->
<!--                      @editButtonClose="editButtonClose"-->
<!--                      width="30%"/>-->
  </el-dialog>

</template>

<script>
import {getButtonInfo,updateButtonStatus,deleteButton,insertButton} from '@/api/mqManage/MQManage-api';
import editButtonInfo from "../../views/mq/editButton.vue";
export default {
  name: 'buttonManager',
  components: {editButtonInfo,},
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑需要的字段
      formInline: {},
      // table 列表数据
      tableData: [
        {
          id:'0',
          moduleName:'testModule',
          zhName:'测试模块',
          buttonName:'按钮名称',
          status:'1',
          updateTime:'2020-01-01 00:00:00',
          updateUser:'admin',
        }
      ],
      showEditButton:false,
      buttonInfo:{
        id:'',
        moduleName:'',
        buttonName:'',
        status:'',
        updateTime:'',
        updateUser:'',
      },
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('buttonManagerClose', flag);
      this.showing = false;
      this.resetForm();
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    getInfo() {
      getButtonInfo().then(res=>{
        if (res.data.success && res.data.result !== null) {
          this.tableData = res.data.result;
        }
      })
    },
    /**
     * 更改按钮状态
     * @param val
     */
    setButtonStatus(val){
      this.$confirm('是否修改按钮状态？', '确认修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete-confirm-button',
        cancelButtonClass: 'delete-cancel-button',
        customClass: 'delete-confirm-custom'
      }).then(()=>{
        updateButtonStatus(val).then(res=>{
          if(res.data.success){
            this.$message({
              message: '修改成功',
              type: 'info'
            });
            this.getInfo()
          }else{
            this.$message({
              message: res.data.msg,
              type: 'error'
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消修改状态'
        });
      });

    },
    /**
     * 删除按钮
     * @param val
     */
    deleteButtonInfo(val){
      this.$confirm('是否删除按钮状态？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete-confirm-button',
        cancelButtonClass: 'delete-cancel-button',
        customClass: 'delete-confirm-custom'
      }).then(()=>{
        deleteButton(val).then(res=>{
          if(res.data.success){
            this.$message({
              message: '删除成功',
              type: 'info'
            });
            this.getInfo()
          }else{
            this.$message({
              message: res.data.msg,
              type: 'error'
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消修改状态'
        });
      });
    },
    /**
     * 显示按钮状态
     * @param val
     * @returns {string}
     */
    showButtonStatus(val){
      if(val === "1"){
        return '显示'
      }else if(val === "0"){
        return '隐藏'
      }else{
        return '--'
      }
    },
    /**
     * 打开按钮管理窗口
     */
    buttonManager(val) {
      this.buttonInfo = val;
      this.showEditButton = true;
    },
    /**
     * 关闭按钮窗口
     */
    editButtonClose(){
      this.getInfo();
      console.log("关闭成功")
      this.showEditButton = false;
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getInfo()
        this.showing = this.isShow;
      } else {
        this.resetForm();
      }
    }
  }

}
</script>

<style scoped lang="scss">
.content {
  padding: 20px;

  .mq-index-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    width: 1300px;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;

    .content-body {
      margin-top: 20px;

      .content-body-search {
        display: flex;
        flex-wrap: wrap;

        .content-body-search-input {
          width: 350px;
        }

        .content-body-search-button {
          display: flex;
        }
      }

      .content-body-table {
      }

    }

    .is-plain {
      background: #F4ECEC;
      color: #CF1A1C;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      border: none;

    }

    .custom-header-class {

      th {
        color: #777 !important;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background: #F6F7F9;
        padding: 10px 0px !important;

      }
    }
  }

  .content-header {
    position: relative;
    margin: 0;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    color: $--el-color-text-primary;
    line-height: 22px;

    span {
      //算上边线本身4px一共12px
      padding-left: 12px;
    }

    //平行四边形边线
    i {
      position: absolute;
      top: 2px;
      display: inline-block;
      transform: skewY(-15deg);
      height: 18px;
      width: 4px;
      background-color: $--el-color-primary;
    }
  }
}
</style>
<style lang="scss">
.row-form {
  margin-bottom: 10px;

  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}
</style>
