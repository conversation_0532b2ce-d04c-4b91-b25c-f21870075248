<template>
  <el-dialog
      :before-close="handleClose"
      title="联系人管理"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      modal
      modal-append-to-body
      width="70%">
    <div>
      <div slot="footer">
        <el-button round type="primary" size="small" style="margin-bottom: 20px;" @click="maintainContact">新增联系人</el-button>
      </div>
      <el-table :data="tableData"
                row-key="id"
                fit
                default-expand-all
                style="width: 100%;margin-bottom: 20px;color: #000"
                :font-size="14"
                header-row-class-name	="custom-header-class"
                >
        <el-table-column label="联系人姓名" prop="contactName" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.contactName">{{ scope.row.contactName }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="联系人邮箱" prop="contactEmail" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.contactEmail">{{ scope.row.contactEmail }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateUser" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.updateUser">{{ scope.row.updateUser }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" align="center" min-width="15%" show-overflow-tooltip/>
        <el-table-column label="操作" align="center" min-width="10%" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="iconfont icon-ic-modify custom-icon-button" title="修改状态" @click="setContactInfo(scope.row)"/>
<!--            <i class="iconfont ic-sync1 custom-icon-button" title="修改状态" @click="setButtonStatus(scope.row)"/>-->
            <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteContactInfo(scope.row)"/>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
    </div>
<!--    <edit-button-info title="新增按钮"-->
<!--                      :isShow="showEditButton"-->
<!--                      :updateData="buttonInfo"-->
<!--                      @editButtonClose="editButtonClose"-->
<!--                      width="30%"/>-->
    <edit-m-q
        :isShow="showModal"
        :updateData="contactInfo"
        :title="title"
        @editMQClose="editMQClose"
        width="30%"/>
  </el-dialog>

</template>

<script>
import {getButtonInfo,updateButtonStatus,deleteButton,insertButton} from '@/api/mqManage/MQManage-api';
import editButtonInfo from "../../views/mq/editButton.vue";
import editMQ from "../../views/mq/editMQ.vue";
import {maintainContact,getContactInfo,getContactList,deleteContact} from '@/api/mqManage/MQManage-api';
import EditMQ from "./editMQ.vue";
export default {
  name: 'contactList',
  components: {EditMQ, editButtonInfo,},
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      title:'新增联系人',
      // 是否显示弹出框
      showing: false,
      // 编辑需要的字段
      formInline: {},
      // table 列表数据
      tableData: [
        {
          id:'0',
          contactName:'testModule',
          contactEmail:'测试模块',
          updateTime:'2020-01-01 00:00:00',
          updateUser:'admin',
        }
      ],
      showEditButton:false,
      // 修改联系人信息
      contactInfo:{
        id:'0',
        contactName:'testModule',
        contactEmail:'测试模块',
        updateTime:'2020-01-01 00:00:00',
        updateUser:'admin',
      },
      // 联系人维护窗口是否弹出
      showModal: false,
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('contactListClose', flag);
      this.showing = false;
      this.resetForm();
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    getInfo() {
      getContactList().then(res=>{
        if (res.data.success && res.data.result !== null) {
          this.tableData = res.data.result.contactList;
        }
      })
    },
    /**
     * 更改按钮状态
     * @param val
     */
    setButtonStatus(val){
      this.$confirm('是否修改联系人信息？', '确认修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete-confirm-button',
        cancelButtonClass: 'delete-cancel-button',
        customClass: 'delete-confirm-custom'
      }).then(()=>{
        updateButtonStatus(val).then(res=>{
          if(res.data.success){
            this.$message({
              message: '修改成功',
              type: 'info'
            });
            this.getInfo()
          }else{
            this.$message({
              message: res.data.msg,
              type: 'error'
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消修改状态'
        });
      });

    },
    /**
     * 删除按钮
     * @param val
     */
    deleteContactInfo(val){
      this.$confirm('是否删除联系人？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'delete-confirm-button',
        cancelButtonClass: 'delete-cancel-button',
        customClass: 'delete-confirm-custom'
      }).then(()=>{
        deleteContact(val).then(res=>{
          if(res.data.success){
            this.$message({
              message: '删除成功',
              type: 'info'
            });
            this.getInfo()
          }else{
            this.$message({
              message: res.data.msg,
              type: 'error'
            });
          }
        })
      })
    },
    /**
     * 显示按钮状态
     * @param val
     * @returns {string}
     */
    showButtonStatus(val){
      if(val === "1"){
        return '显示'
      }else if(val === "0"){
        return '隐藏'
      }else{
        return '--'
      }
    },
    /**
     * 打开按钮管理窗口
     */
    setContactInfo(val) {
      this.title = '修改联系人';
      this.contactInfo = val;
      this.showModal = true;
    },
    /**
     * 关闭按钮窗口
     */
    editButtonClose(){
      this.getInfo();
      this.showEditButton = false;
    },
    /**
     * 打开联系人维护窗口
     */
    maintainContact() {
      this.title = '新增联系人';
      this.showModal = true;
    },
    /**
     * 关闭联系人维护窗口
     */
    editMQClose(){
      this.showModal = false;
      this.contactInfo = JSON.parse(JSON.stringify(this.$options.data().contactInfo))
      this.getInfo();
    },
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getInfo()
        this.showing = this.isShow;
      } else {
        this.resetForm();
      }
    }
  }

}
</script>

<style scoped lang="scss">
.content {
  padding: 20px;

  .mq-index-div {
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    width: 1300px;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;

    .content-body {
      margin-top: 20px;

      .content-body-search {
        display: flex;
        flex-wrap: wrap;

        .content-body-search-input {
          width: 350px;
        }

        .content-body-search-button {
          display: flex;
        }
      }

      .content-body-table {
      }

    }

    .is-plain {
      background: #F4ECEC;
      color: #CF1A1C;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      border: none;

    }

    .custom-header-class {

      th {
        color: #777 !important;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background: #F6F7F9;
        padding: 10px 0px !important;

      }
    }
  }

  .content-header {
    position: relative;
    margin: 0;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    color: $--el-color-text-primary;
    line-height: 22px;

    span {
      //算上边线本身4px一共12px
      padding-left: 12px;
    }

    //平行四边形边线
    i {
      position: absolute;
      top: 2px;
      display: inline-block;
      transform: skewY(-15deg);
      height: 18px;
      width: 4px;
      background-color: $--el-color-primary;
    }
  }
}
</style>
<style lang="scss">
.row-form {
  margin-bottom: 10px;

  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}
</style>
