<template>
  <el-dialog
      :title="title"
      :before-close="handleClose"
      :visible.sync="showing"
      v-if="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="600px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-row>
          <el-col :span="24">
            <el-form-item label="姓名" prop="contactName">
              <el-input :maxLength="20" placeholder="请输入姓名" v-model.trim="formInline.contactName"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="contactEmail">
              <el-input :maxLength="64" placeholder="请输入邮箱" v-model.trim="formInline.contactEmail"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>

import {_validateEmail} from '@/utils/validate'
import {maintainContact, getContactInfo} from '@/api/mqManage/MQManage-api';

export default {
  name: 'editMQ',
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    },
    // 标题
    title: {
      type: String
    }
  },
  data() {
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 姓名
        contactName: "",
        // 邮箱
        contactEmail: "",
      },
      // 表单验证规则
      rules: {
        contactName: [
          {required: true, message: '请输入姓名', trigger: 'blur'}
        ],
        contactEmail: [
          {required: true, message: '请输入邮箱', trigger: 'blur'},
          { required: true,
            validator: (rule, value, callback) => {
              if (value != null && value !== '') {
                if (!_validateEmail(value)) {
                  callback(new Error('请输入正确邮箱号'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change']
          }
        ],
      }
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 保存人员信息* */
    sureClick() { // 点击确定按钮
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          maintainContact(this.formInline).then(res => {
            if (res.data.result) {
              this.$message({
                message: '保存成功',
                type: 'info'
              });
              this.closeModal(true);
            } else {
              this.$message({
                message: res.data.msg,
                type: 'error'
              });
            }
          });
        } else {
          // 当校验失败时，可以给用户一个提示
          this.$message({
            message: '请完善所填写的信息',
            type: 'warning'
          });
        }
      });
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('editMQClose', flag);
      this.showing = false;
      this.resetForm();
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    getInfo() {
      if (this.updateData.id !== undefined && this.updateData.id !== null) {
        getContactInfo(this.updateData).then(res => {
          if (res.data.success && res.data.result.contact !== null) {
            this.formInline = res.data.result.contact;
          }
        })
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getInfo()
        this.showing = this.isShow;
      } else {
        this.resetForm();
      }
    }
  }

}
</script>

<style scoped lang="scss">

</style>