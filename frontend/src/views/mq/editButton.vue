<template>
  <el-dialog
      :before-close="handleClose"
      :title="formInline.id?'编辑按钮':'新增按钮'"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="600px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-row>
          <el-col :span="24">
            <el-form-item label="模块名称" prop="moduleName">
              <el-input :maxLength="20" placeholder="请输入模块名称" v-model.trim="formInline.moduleName"></el-input>
            </el-form-item>
            <el-form-item label="模块中文名称" prop="zhName">
              <el-input :maxLength="20" placeholder="请输入模块中文名称" v-model.trim="formInline.zhName"></el-input>
            </el-form-item>
            <el-form-item label="按钮名称" prop="buttonName">
              <el-input :maxLength="64" placeholder="请输入按钮名称" v-model.trim="formInline.buttonName"></el-input>
            </el-form-item>
            <el-form-item label="按钮状态" prop="status">
              <template>
                <el-radio v-model.trim="formInline.status" label="1">显示</el-radio>
                <el-radio v-model.trim="formInline.status" label="0">隐藏</el-radio>
              </template>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {insertButton, selectButtonInfo} from '../../api/mqManage/MQManage-api'

export default {
  name: 'editButton',
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑信息
    updateData: {
      type: Object
    }
  },
  data() {
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 模块名称
        moduleName: "",
        // 模块中文名称
        zhName: "",
        // 按钮名称
        buttonName: "",
        // 按钮状态
        status: "1",
        // 更新人
        updateUser: "",
        // 更新时间
        updateTime: "",
      },
      // 表单验证规则
      rules: {
        moduleName: [
          {required: true, message: '请输入模块名', trigger: 'blur'}
        ],
        zhName: [
          {required: true, message: '请输入模块中文名称', trigger: 'blur'}
        ],
        buttonName: [
          {required: true, message: '请输入按钮名称', trigger: 'blur'}
        ],
      }
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 保存人员信息* */
    sureClick() { // 点击确定按钮
      insertButton(this.formInline).then(res => {
        console.log(res)
        if (res.data.result) {
          this.$message({
            message: '操作成功',
            type: 'info'
          });
          this.$emit('editButtonClose', true);
        } else {
          this.$message({
            message: res.data.msg,
            type: 'error'
          });
        }
      })
      this.closeModal(false);
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('editButtonClose', flag);
      this.showing = false;
      this.resetForm();
    },
    /* 清空表单数据
       *  */
    resetForm() {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    },
    getInfo() {
      if (this.updateData != null) {
        selectButtonInfo(this.updateData).then(res => {
          if (res.data.success && res.data.result !== null) {
            this.formInline = res.data.result;
          }
        })
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getInfo()
        this.showing = this.isShow;
      } else {
        this.resetForm();
      }
    }
  }

}
</script>

<style scoped lang="scss">

</style>