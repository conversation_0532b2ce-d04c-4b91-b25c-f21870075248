<template>
  <div class="content">
    <div class="mq-index-div">
      <!--    标题-->
      <div class="content-header">
        <i></i>
        <span>{{ dataManager.title }}</span>
      </div>
      <!--    搜索条件-->
      <div class="content-body">
        <div class="content-body-search">
          <el-row :gutter="10" class="row-form">

              <el-col :span="24 / (dataManager.searchData.length + 1)" style="margin-bottom: 10px"
                      v-for="(item, index) in dataManager.searchData" :key="index">
                <el-select  v-if="item.isSelect" :placeholder="item.title" clearable v-model="item.model"
                            @change="selectChange(item)" >
                  <el-option
                      :key="key"
                      v-for="(option, key) in item.data"
                      :label="option.label"
                      :value="option.value"
                      v-bury-click="dataAnalysis.setValue('消息队列管理',item.title, 'select')"
                  />
                </el-select>
                <el-input v-else class="content-body-search-input" :placeholder="item.title"
                          v-model.trim="item.model" @keyup.enter.native="selectChange(item)"
                          v-bury-input="dataAnalysis.setValue('消息队列管理',item.title,'input')"
                          style="margin-bottom: 10px"/>
              </el-col>

            <el-col :span="24">
              <div class="content-body-search-button">
                <el-button type="primary" @click="search" round>查询</el-button>
                <el-button @click="clear" round>重置</el-button>
                <el-button @click="contactList" round>联系人维护</el-button>
                <el-button @click="buttonManager" round>按钮管理</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <!--表格-->
        <div class="content-body-table">
          <el-table :data="dataManager.tableData" header-row-class-name	="custom-header-class">
            <el-table-column type="index" width="40" align="center"/>
            <el-table-column label="目标服务" width="100" prop="sysName" show-overflow-tooltip align="center"/>
            <el-table-column label="业务类型" width="150" prop="businessType" show-overflow-tooltip align="center">
              <template v-slot="scope">
                <span>{{ businessTypeConvert(scope.row.businessType) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作类型" width="100" prop="operationType" show-overflow-tooltip align="center">
              <template v-slot="scope">
                <span>{{ operationTypeConvert(scope.row.operationType) }}</span>
              </template>
            </el-table-column>
            <el-table-column v-for="(column, index) in dataManager.tableColumn" :key="index"
                             :label="column.label" :prop="column.prop" :sortable="column.isSortable"
                             :width="column.width" align="center" show-overflow-tooltip/>
            <el-table-column label="状态" width="80" prop="isSuccess" show-overflow-tooltip>
              <template v-slot="scope">
                <el-tag :type="status[scope.row.isSuccess].type">{{status[scope.row.isSuccess].msg}}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100" show-overflow-tooltip>
              <template v-slot="scope">
                <span v-if="scope.row.isSuccess === '0' && scope.row.operationType !== 'delete'" @click="resend(scope.row)" class="resend">重新发送</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="footer">
<!--        <papers ref="paper" @searchTable="handlePagination" :total="dataManager.total" :size="mqDto.limit"/>-->
      </div>
      <el-pagination
          style="text-align: center;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="mqDto.startRow"
          :page-sizes="[10, 20, 50, 200]"
          :page-size="mqDto.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="dataManager.total">
      </el-pagination>
      <edit-m-q
          :isShow="showModal"
          @editMQClose="editMQClose"
          width="30%"/>
      <button-manager
          :isShow="showButton"
          @buttonManagerClose="buttonManagerClose"
          width="30%"/>
      <contact-list
          :isShow="showContact"
          @contactListClose="contactListClose"
          width="30%"/>
    </div>
  </div>
</template>

<script>
import {DataManager, DataManagerFactory, MQDto} from './data/MQDataManager'
// import papers from "@/components/Pagination/papers.vue";
import {resendMessage} from "@/api/mqManage/MQManage-api";
import {DataAnalysis} from "@/utils/BurialPoint";
import editMQ from "../../views/mq/editMQ.vue";
import editPerson from "../person/editPersonOut.vue";
import ButtonManager from "../../views/mq/buttonManager.vue";
import ContactList from "../../views/mq/contactList.vue";

export default {
  components: {ButtonManager, editPerson, editMQ,ContactList},
  name: 'MessageQueue',
  computed: {
    DataAnalysis(menu, tag) {
      let data = new DataAnalysis()
      data.menu = menu
      data.tag = tag
      return data
    }
  },
  // components: {papers},
  data() {
    return {
      dataManager: new DataManager(),
      mqDto: new MQDto(),
      dataAnalysis: new DataAnalysis(),
      status: {
        '0': {msg:'失败', type:'danger'},
        '1': {msg:'成功', type:'success'},
        '2': {msg:'已发送', type:'info'},
        '3': {msg:'已接收', type:'success'}
      },
      // 联系人维护窗口是否弹出
      showModal: false,
      // 按钮管理窗口是否弹出
      showButton:false,
      // 联系人管理窗口是否弹出
      showContact:false,
    }
  },
  created() {
    this.dataManager = DataManagerFactory('mq')
    this.dataManager.getTableData(this.mqDto)
  },
  methods: {
    businessTypeConvert(value) {
      let businessTypeName = '- -'
      if (value === 'company') {
        businessTypeName = '公司'
      } else if (value === 'logout') {
        businessTypeName = '退出'
      } else if (value === 'role') {
        businessTypeName = '角色'
      } else if (value === 'user') {
        businessTypeName = '用户'
      } else if (value === 'permission') {
        businessTypeName = '用户授权'
      } else if (value === 'org') {
        businessTypeName = '组织架构'
      } else if (value === 'log') {
        businessTypeName = '日志'
      } else if (value === 'timedTasks') {
        businessTypeName = '定时任务'
      }
      return businessTypeName
    },
    operationTypeConvert(value) {
      let operationTypeName = '- -'
      if (value === 'insert') {
        operationTypeName = '新增'
      } else if (value === 'lock') {
        operationTypeName = '锁定'
      } else if (value === 'delete') {
        operationTypeName = '删除'
      } else if (value === 'update') {
        operationTypeName = '更新'
      } else if (value === 'setuser') {
        operationTypeName = '设置用户(回购专用)'
      } else if (value === 'merge') {
        operationTypeName = '外部用户合并'
      } else if (value === 'outUser') {
        operationTypeName = '合规外部用户导入'
      }
      return operationTypeName
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.mqDto.limit = val
      this.mqDto.startRow = 1
      this.dataManager.handlePagination(this.mqDto)
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.mqDto.startRow = val
      this.dataManager.handlePagination(this.mqDto)
    },
    /**
     * 查询
     */
    search() {
      this.dataManager.search(this.mqDto)
    },
    /**
     * 重置
     */
    clear() {
      this.mqDto = new MQDto()
      this.dataManager.clear(this.mqDto)
    },
    /**
     * 选择器事件，输入框事件
     * @param val
     */
    selectChange(val) {
      val.search(this.mqDto, this.dataManager)
    },
    /**
     * 重新发送消息
     * @param row
     */
    resend(row) {
      resendMessage(row).then(res => {
        if (res.data.result) {
          row.isSuccess = '2'
          this.$message.info("重新发送成功！")
        } else {
          this.$message.error("发送失败！")
        }
      })
    },
    /**
     * 打开联系人维护窗口
     */
    maintainContact() {
      this.showModal = true;
    },
    /**
     * 关闭联系人维护窗口
     */
    editMQClose(){
      this.showModal = false;
    },
    /**
     * 打开按钮管理窗口
     */
    buttonManager() {
      this.showButton = true;
    },
    /**
     * 关闭按钮窗口
     */
    buttonManagerClose(){
      this.showButton = false;
    },
    /**
     * 打开联系人管理窗口
     */
    contactList() {
      this.showContact = true;
    },
    /**
     * 关闭联系人窗口
     */
    contactListClose(){
      this.showContact = false;
    }
  }
}


</script>

<style scoped lang="scss">
.content {
  padding: 20px;

  .mq-index-div{
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    width: 1300px;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;

    .content-body {
      margin-top: 20px;

      .content-body-search {
        display: flex;
        flex-wrap: wrap;

        .content-body-search-input {
          width: 350px;
        }

        .content-body-search-button {
          display: flex;
        }
      }

      .content-body-table {
        .resend {
          color: #D6001D;
        }
        .resend:hover {
          cursor: pointer;
        }
      }

    }
    .is-plain{
      background: #F4ECEC;
      color: #CF1A1C;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      border: none;

    }

    .custom-header-class{

      th{
        color: #777!important;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background: #F6F7F9;
        padding: 10px 0px !important;

      }
    }
  }

  .content-header {
    position: relative;
    margin: 0;
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    color: $--el-color-text-primary;
    line-height: 22px;

    span {
      //算上边线本身4px一共12px
      padding-left: 12px;
    }

    //平行四边形边线
    i {
      position: absolute;
      top: 2px;
      display: inline-block;
      transform: skewY(-15deg);
      height: 18px;
      width: 4px;
      background-color: $--el-color-primary;
    }
  }
}
</style>
<style lang="scss">
.row-form{
  margin-bottom: 10px;
  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}
.el-tooltip__popper {
  max-width: 98%;
}
</style>
