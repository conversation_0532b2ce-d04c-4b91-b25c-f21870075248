<template>
    <el-dialog
            width="500px"
            :before-close="handleClose"
            :title="formInline.id?'编辑角色':'新增角色'"
            v-model="showing"
            append-to-body
            modal>
        <div>
            <el-form :model="formInline" label-width="110px" :rules="rules" ref="editForm">
                <el-form-item label="角色名" prop="roleName">
                    <el-input v-model="formInline.roleName" placeholder="角色名" :maxlength="50"></el-input>
                </el-form-item>
                <el-form-item label="具体描述">
                    <el-input v-model="formInline.roleDes" placeholder="具体描述" type="textarea" :maxlength="256"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="text-center">
            <el-button @click="closeModal(false)">取消</el-button>
            <el-button type="primary" @click="sureClick">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { _createRole, _modifyRole, _roleNameCheck } from "@/api/role-api";
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  emits:['addRoleModalClose'],
  data () {
    const roleNameCheck = (rule, value, callback) => {
      if (!value || value.length === 0) {
        return callback(new Error("请输入角色名"));
      } else {
        _roleNameCheck({ id: this.formInline.id, roleName: value }).then(res => {
          if (res.data.success) {
            if (res.data.result) {
              callback();
            } else {
              return callback(new Error("角色名已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 角色名
        roleName: '',
        // 具体描述
        roleDes: ''
      },
      // 表单验证规则
      rules: {
        roleName: [
          { required: true, message: '请输入角色名', trigger: 'blur' },
          { validator: roleNameCheck, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
      * */
    handleClose () {
      this.closeModal(false)
    },
    /* 关闭弹出框
       * flag:是取消还是保存
      * */
    closeModal (flag) {
      this.$emit('addRoleModalClose', flag);
      this.showing = false;
    },
    /* 保存角色
      * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          // 编辑角色
          if (this.formInline.id) {
            _modifyRole(this.formInline).then(res => {
              if (res.data.success) {
                this.showGlobalMessage('info', this.$messageText['updateSuccess']);
                this.closeModal(true);
              } else {
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          } else {
            _createRole(this.formInline).then(res => {
              if (res.data.success) {
                this.showGlobalMessage('info', this.$messageText['saveSuccess']);
                this.closeModal(true);
              } else {
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 清空表单数据
     *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData) {
          Object.assign(this.formInline, this.updateData);
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
