<template>
    <el-dialog
            :before-close="handleClose"
            :title="'设置权限-'+this.formInline.roleName"
            :visible.sync="showing"
            append-to-body
            class="add-person"
            modal
            modal-append-to-body
            :close-on-click-modal="false"
            width="30%">
        <div style="height: 416px">
          <el-tree
              ref="permissionTree"
              :data="list"
              show-checkbox
              check-on-click-node
              :expand-on-click-node="false"
              check-strictly
              node-key="id"
              :props="{ children: 'children', label: 'name' }"
              :default-checked-keys="checkedRouterIds"
              @check-change="checkChange"
              default-expand-all>
          </el-tree>
<!--            <el-checkbox label="全选" v-model="selectAll" @change="selectAllCheckBox()"></el-checkbox>-->
<!--            <el-form :model="this.formInline" :rules="rules" label-width="110px" ref="editForm">-->
<!--                <el-form-item prop="routerIdList">-->
<!--                    <el-row>-->
<!--                        <el-checkbox-group v-model="formInline.routerIdList">-->
<!--                            <el-col :span="4">-->
<!--                            <el-checkbox-->
<!--                                    @change="parentCheckboxChange(item)"-->
<!--                                    v-for="(item,index) in list"-->
<!--                                    :label="item.id"-->
<!--                                    :key="index"-->
<!--                                    v-model="item.hasPermission"-->
<!--                                    :checked="item.hasPermission"-->
<!--                                    :disabled="item.routerDes === '首页'"-->
<!--                                    style="margin-right: 10px"-->
<!--                            >{{item.routerDes}}-->
<!--                                <el-checkbox-->
<!--                                        @change="childCheckboxChange(subItem)"-->
<!--                                        v-for="(subItem,index) in item.routerList"-->
<!--                                        :label="subItem.id"-->
<!--                                        :key="index"-->
<!--                                        v-model="subItem.hasPermission"-->
<!--                                        :checked="subItem.hasPermission"-->
<!--                                        style="margin-right: 10px"-->
<!--                                >{{subItem.routerDes}}-->
<!--                                </el-checkbox>-->
<!--                            </el-checkbox>-->
<!--                            </el-col>-->
<!--                        </el-checkbox-group>-->
<!--                    </el-row>-->
<!--                </el-form-item>-->
<!--            </el-form>-->
        </div>
        <div slot="footer">
            <el-button @click="checkedAll" round>全选</el-button>
            <el-button @click="closeModal(false)" round>取消</el-button>
            <el-button @click="sureClick" type="primary" round>保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import {
  _queryRouterList, _setPermission
} from "@/api/person-api";

Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    },
    list: Array
  },
  data () {
    return {
      checkedRouterIds: [],
      // selectAll: false,
      // 是否显示弹出框
      showing: false,
      // 设置用户需要的表单字段
      formInline: {
        // 用户id
        id: '',
        // 路由id列表
        routerIdList: [],
        // 用户名字
        roleName: '',
        // 用户id
        roleId: ''
      },
      // 表单验证规则
      rules: {}
    }
  },
  computed: {},
  methods: {
    checkChange (data, checked, indeterminate) {
      if (checked && data.level === 1) {
        this.$refs.permissionTree.setChecked(data.parentId, true)
      } else if (!checked && data.level === 0) {
        data.children.forEach(item => {
          this.$refs.permissionTree.setChecked(item.id, false)
        })
      }
    },
    // parentCheckboxChange (item) {
    //   // 父联动
    //   var flag = false;
    //   for (var i = 0; i < this.formInline.routerIdList.length; i++) {
    //     if (this.formInline.routerIdList[i] === item.id) {
    //       flag = true;
    //       break;
    //     }
    //   }
    //
    //   if (!flag) {
    //     var subList = item.routerList;
    //     for (var j = 0; j < subList.length; j++) {
    //       this.formInline.routerIdList.remove(subList[j].id)
    //     }
    //   }
    // },
    // childCheckboxChange (item) {
    //   // 子联动
    //   var flag = false;
    //   for (var i = 0; i < this.formInline.routerIdList.length; i++) {
    //     if (this.formInline.routerIdList[i] === item.id) {
    //       flag = true;
    //       break;
    //     }
    //   }
    //
    //   if (flag) {
    //     if (item.pRouterId != null) {
    //       this.formInline.routerIdList.push(item.pRouterId)
    //     }
    //     // 去重
    //     this.formInline.routerIdList = Array.from(new Set(this.formInline.routerIdList))
    //   }
    // },
    /* 关闭弹出框
              * */
    handleClose () {
      this.closeModal(false);
    },
    /* 保存权限* */
    sureClick () { // 点击确定按钮
      this.formInline.routerIdList = this.$refs.permissionTree.getCheckedKeys();
      _setPermission(this.formInline).then(res => {
        if (res.data.success) {
          showMessage("info", "权限设置成功");
          this.closeModal(true);
        } else {
          showMessage("error", res.data.errorMsg)
        }
      })
    },

    closeModal (flag) {
      this.$emit('setPermissionClose', flag);
      this.showing = false;
    },
    checkedAll() {
      const allNodeId = this.getAllNodeId(this.list);
      this.$refs.permissionTree.setCheckedKeys(allNodeId);
    },
    getAllNodeId(data) {
      let idList = [];
      data.forEach(item => {
        idList.push(item.id);
        if (item.children && item.children.length > 0) {
          idList = idList.concat(this.getAllNodeId(item.children));
        }
      });
      return idList;
    },
    // resetForm () {
    //   // 重置表单
    //   this.$refs['editForm'].resetFields();
    //   // 重置数据
    //   this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    // },
    /** 选中所有的checkbox **/
    // selectAllCheckBox () {
    //   if (!this.selectAll) {
    //     let indexRouterId = ''
    //     for (let i = 0; i < this.list.length; i++) {
    //       if (this.list[i].routerDes === '首页') {
    //         indexRouterId = this.list[i].id
    //       }
    //     }
    //     // 插入首页
    //     this.formInline.routerIdList = [indexRouterId]
    //   } else {
    //     let set = new Set();
    //     for (var i = 0; i < this.list.length; i++) {
    //       set.add(this.list[i].id);
    //       var subList = this.list[i].routerList;
    //       for (var j = 0; j < subList.length; j++) {
    //         set.add(subList[j].id);
    //       }
    //     }
    //     this.formInline.routerIdList = Array.from(set)
    //   }
    // }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData != null) {
          this.formInline.id = this.updateData.id;
          this.formInline.roleId = this.updateData.id;
          this.formInline.roleName = this.updateData.roleName;
          _queryRouterList(this.formInline).then(res => {
            if (res.data.success) {
              this.list = res.data.result.routers
              this.list.forEach(item => {
                if ("首页" === item.name) {
                  item.disabled = true
                }
              })
              this.checkedRouterIds = res.data.result.checkedRouterIds
            } else {
              showMessage("error", res.data.errorMsg)
            }
          })
          // _queryRouterList(this.formInline).then(res => {
          //   if (res.data.success) {
          //     this.list = res.data.result
          //     this.selectAll = true;
          //     // 检查是否全选
          //     for (let i = 0; i < this.list.length; i++) {
          //       if (!this.list[i].hasPermission) {
          //         this.selectAll = false;
          //         break;
          //       }
          //     }
          //   } else {
          //     showMessage("error", res.data.errorMsg)
          //   }
          // })
        }
      } else {
        this.$refs.permissionTree.setCheckedKeys([]);
        // this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss">
    .add-user {
    }

    .aa > > > .el-form-item__content {
        margin-left: 0px !important;
    }
</style>
