<template>
  <div class="outer-role">
    <div class="outer-role-div" style="">
      <div class="page-title">
        <i></i>
        <span>接入系统角色</span>
      </div>
      <el-row class="m-c">
        <el-tabs v-model="activeTab" @tab-click="handleClick">
          <el-tab-pane v-for="(item, index) in tabList" :key="index" :name="item.sysCode" :label="item.sysName"></el-tab-pane>
        </el-tabs>
        <el-row gutter="15" class="row-form-list">
          <el-col :span="6">
            <el-input placeholder="角色名" v-model="query.roleName" size="small"></el-input>
          </el-col>
          <el-col :span="6">
            <el-input placeholder="描述" v-model="query.roleDesc" size="small"></el-input>
          </el-col>
          <el-col :span="6" class="text-left">
            <el-button @click="search" type="primary" size="small" round>查询</el-button>
            <el-button @click="clearClick" size="small" round>清空条件</el-button>
          </el-col>
          <el-col :span="6">
            <!--导入角色--- 临时-->
            <el-upload
                style="display: inline-block"
                :http-request="customUpload"
                :multiple="false"
                :show-file-list="false"
                :before-upload="beforeUpload">
              <el-button v-if="importSystemRole" round type="primary" size="small" style="margin-left: 10px;">导入角色</el-button>
            </el-upload>
          </el-col>
        </el-row>
  <!--      <el-row class="m-c">-->
  <!--        <el-col :span="12">-->
  <!--          <el-link :underline="false" type="primary" @click="editRole(null)">-->
  <!--            <i class="iconfont icon-gonggaoicon_tianjia"></i>-->
  <!--            <span class="btn">新增</span>-->
  <!--          </el-link>-->
  <!--        </el-col>-->
  <!--      </el-row>-->
        <el-row class="m-c">
          <el-table
              :data="tableData"
              class="full"
              style="width: 100%;margin-bottom: 20px;color: #000"
              :font-size="14"
              header-row-class-name	="custom-header-class-list"
          >
<!--            <el-table-column-->
<!--                type="selection"-->
<!--                width="55">-->
<!--            </el-table-column>-->
            <el-table-column
                align="center"
                label="角色名"
                prop="roleName">
            </el-table-column>
            <el-table-column
                align="center"
                label="具体描述"
                prop="roleDesc">
              <template slot-scope="scope">
                <span v-if="scope.row['roleDesc']">{{scope.row['roleDesc']}}</span>
                <span class="empty-text" v-else>--</span>
              </template>
            </el-table-column>
  <!--          <el-table-column-->
  <!--              align="center"-->
  <!--              label="操作">-->
  <!--            <template slot-scope="scope">-->
  <!--              <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="editRole(scope.row)"></i>-->
  <!--              <i class="iconfont ic-set-person custom-icon-button" title="设置角色权限"-->
  <!--                 v-if="scope.row.roleName != null && needAuth" @click="setRoleAuth(scope.row)"></i>-->
  <!--              <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteRole(scope.row.id)"></i>-->
  <!--            </template>-->
  <!--          </el-table-column>-->
          </el-table>
<!--          <papers ref="paper" @searchTable="paperSearch" :total="total" :size="10"></papers>-->
          <el-pagination
              style="text-align: center;"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="query.startRow"
              :page-sizes="[10, 20, 50, 200]"
              :page-size="query.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
          </el-pagination>
        </el-row>
      </el-row>
      <el-dialog
          v-if="showModal"
          :visible.sync="showModal"
          :title="getValue(modalForm['id']) === '' ?'新增' :'编辑'"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          draggable="true"
          width="30%">
        <el-row gutter="10">
          <el-col span="24">
            <el-input v-model="modalForm['roleName']" prefix-text="角色名" placeholder="请输入角色名"></el-input>
          </el-col>
        </el-row>
        <el-row gutter="10" class="m-c">
          <el-col span="24">
            <el-input v-model="modalForm['roleDesc']" prefix-text="角色描述" placeholder="请输入角色描述"></el-input>
          </el-col>
        </el-row>
        <span slot="footer">
          <el-button @click="close" size="small">取 消</el-button>
          <el-button type="primary" @click="confirm" size="small">确 定</el-button>
        </span>
      </el-dialog>
    </div>
</div>
</template>

<script>
import common from "@/mixins/common";
import papers from "@/components/Pagination/papers.vue";
import { _getOpenedSystems } from "@/api/common-api";
import { _cuRoleInfo, _delRoleInfo, _getRoleList } from "@/api/role-outer-api";
import {  _importRoleInfo } from "@/api/system/infoSync-api";
import {selectButtonByName} from "../../../api/mqManage/MQManage-api";

export default {
  components: { papers },
  mixins: [common],
  data () {
    return {
      importSystemRole:false,//控制导入角色按钮是否显示
      tabList: [],
      needAuth: false,
      activeTab: 'default',
      query: {
        roleName: '',
        roleDesc: '',
        // 页码
        startRow: 1,
        // 每页显示多少条数据
        pageSize: 10
      },
      tableData: [],
      total: 0,
      showModal: false,
      modalForm: {}
    }
  },
  mounted () {
    this.getTabList();
    this.checkButtonStatus();//获取导入角色按钮是否显示
  },
  methods: {
    customUpload (fileObj) {
      // this.importSystemRole = false;
      // 在这里编写自定义的上传逻辑
      let data = new FormData();
      data.append('files', fileObj.file);
      _importRoleInfo(data).then(res => {
        if (res.data.success) {
          this.$message.info("成功导入" + res.data.result + "条数据");
          window.location.reload();
        } else {
          // this.importSystemRole = true;
          this.$message.error("导入失败" + res.data.errorMsg);
        }
      })
      this.checkButtonStatus()
    },
    beforeUpload (file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const isXls = file.type === 'application/vnd.ms-excel';
      if (!isXlsx && !isXls) {
        this.$message.error('只能上传xls、xlsx文件');
        return false; // 取消上传
      }
      return true; // 立即上传文件
    },
    getTabList () {
      _getOpenedSystems({}).then(res => {
        if (res.data.success) {
          if (res.data.result.length > 0) {
            this.tabList = [];
            res.data.result.forEach(item => {
              let tab = {
                sysCode: item['menuName'], sysName: item['menuDes']
              }
              this.tabList.push(tab);
            })
            if (this.activeTab === 'default') {
              this.activeTab = this.tabList[0].sysCode;
            }
            this.search();
          }
        }
      })
    },
    handleClick () {
      this.query = {
        roleName: '',
        roleDesc: '',
        // 页码
        startRow: 1,
        // 每页显示多少条数据
        pageSize: 10
      }
      this.search();
    },
    search () {
      let query = { ...this.query };
      // query.startRow = 0;
      // query.pageSize = this.$refs.paper.submitData.pageSize;
      query.sysCode = this.activeTab;
      _getRoleList(query).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.tableData;
          this.total = res.data.result.total;
          this.needAuth = res.data.result.needAuth;
          if (this.tableData.length === 0) {
            query.startRow = 1;
            _getRoleList(query).then(res => {
              this.tableData = res.data.result.tableData;
              this.total = res.data.result.total;
              this.needAuth = res.data.result.needAuth;
            })
          }
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 1
      this.search();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.search();
    },
    // paperSearch () {
    //   let form = this.$refs.paper.submitData;
    //   this.query.startRow = form.startRow === undefined ? 0 : form.startRow;
    //   this.query.pageSize = form.pageSize;
    //   this.search();
    // },
    clearClick () {
      // let form = this.$refs.paper.submitData;
      this.query = {
        roleName: '',
        roleDesc: '',
        startRow: 1,
        pageSize: 10
      };
      this.search();
    },
    editRole (row) {
      this.showModal = true;
      this.modalForm = { ...row };
    },
    deleteRole (id) {
      let param = {
        id: id,
        sysCode: this.activeTab
      }
      _delRoleInfo(param).then(res => {
        if (res.data.success) {
          this.$message.info("删除成功");
          this.search();
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    close () {
      this.showModal = false;
      this.modalForm = {};
    },
    confirm () {
      let param = { ...this.modalForm };
      param.sysCode = this.activeTab;
      _cuRoleInfo(param).then(res => {
        if (res.data.success) {
          this.$message.info("保存成功");
          this.search();
          this.close();
        } else {
          this.$message.error(res.data.errorMsg);
        }
      })
    },
    checkButtonStatus() {
      selectButtonByName({moduleName:'role',zhName:'接入系统角色',buttonName:'导入角色'}).then(res=>{
        if (res.data.success && res.data.result !== null){
          if(res.data.result.status === '1'){
            this.importSystemRole = true;
          }else{
            this.importSystemRole = false;
          }
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
@import '../../../styles/page-title.scss';
.outer-role {
  padding: 20px;
  .outer-role-div{
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
}
</style>
<style lang="scss">
.row-form-list{
  .el-input__inner {
    border-radius: 35px;
    background: #F6F7F9;
    border: 1px solid #F6F7F9;
    height: 32px;
    line-height: 32px;

  }
}
.custom-header-class-list{

  th{
    color: #777!important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    background: #F6F7F9;
    padding: 10px 0px !important;

  }
}
</style>
