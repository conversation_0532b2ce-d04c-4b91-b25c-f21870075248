<template>
    <div class="user-list">
      <div class="role-list-div" style="">
        <div class="main-header">
            <i></i>
            <span>平台角色信息</span>
        </div>
        <div class="m-c">
            <el-card class="box-card box-card-search" shadow="never" :body-style="{ border: none }">
                <el-row :gutter="10" class="row-form-list">
                    <el-col :span="6">
                        <el-input placeholder="角色名" v-model="query.roleName" size="small"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input placeholder="描述" v-model="query.roleDes" size="small"></el-input>
                    </el-col>
                    <el-col :span="6" class="text-left">
                        <el-button @click="search" type="primary" size="small" round>查询</el-button>
                        <el-button @click="clearClick" size="small" round>清空条件</el-button>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-col :span="12" class="text-left">
                    <el-link
                            :underline="false"
                            type="primary"
                            @click="openAddRoleModal(null)"
                    >
                        <i class="iconfont icon-gonggaoicon_tianjia" style="color: #D6001D;"></i>
                        <span class="btn">新增</span>
                    </el-link>
                    <el-link :underline="false" type="primary" @click="batchDelete">
                        <i class="iconfont ic-trash" style="color: #D6001D;"></i>
                        <span class="btn">批量删除</span>
                    </el-link>
                    <el-link :underline="false" type="primary" @click="exportRole">
                        <i class="iconfont ic-import" style="color: #D6001D;"></i>
                        <span class="btn">导出</span>
                    </el-link>
                </el-col>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-table
                        :data="tableData"
                        @selection-change="handleSelectionChange"
                        class="m-t-20 full"
                        style="width: 100%;margin-bottom: 20px;color: #000"
                        :font-size="14"
                        header-row-class-name	="custom-header-class-list"
                >
                    <el-table-column
                            type="selection"
                            width="55">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="角色名"
                            prop="roleName">
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="具体描述"
                            prop="roleDes">
                        <template slot-scope="scope">
                            <span v-if="scope.row.roleDes">{{scope.row.roleDes}}</span>
                            <span class="empty-text" v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            align="center"
                            label="操作"
                    >
                        <template slot-scope="scope">
                          <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="openAddRoleModal(scope.row)"></i>
                          <i class="iconfont ic-set-person custom-icon-button" title="设置角色权限"
                             v-if="scope.row.roleName != null" @click="setPermissionModal(scope.row)"></i>
                          <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteClick(scope.row.id,scope.$index)"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    style="text-align: center;"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="query.startRow"
                    :page-sizes="[10, 20, 50, 200]"
                    :page-size="query.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total">
                </el-pagination>
            </el-card>
        </div>
        <add-role :isShow="showModal" :updateData="currentRow" @addRoleModalClose="addRoleModalClose"></add-role>
        <set-permission
                :isShow="showSetPermissionModal"
                :updateData="currentRow"
                @setPermissionClose="setPermissionClose"
        >
        </set-permission>
      </div>
    </div>
</template>
<script>
import { showMessage, showConfirm } from 'stock-vue-plugin/lib/utils/message'
import addRole from '../../components/Modal/addRole'
import { _queryRoleList, _deleteRole, exportRole } from "@/api/manage-api";
import SetPermission from "../../views/role/setPermission";

export default {
  components: { addRole, SetPermission },
  data () {
    return {
      showSetPermissionModal: false,
      // 选中的数据
      multipleSelection: [],
      // 是否打开新增角色弹出框
      showModal: false,
      // 是否打开授权弹出框
      showMenuList: false,
      // 授权菜单列表
      authorityDownList: [],
      // 查询需要的数据
      query: {
        // 角色名称
        roleName: "",
        // 描述
        roleDes: "",
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 10
      },
      // 选中的当前行
      currentRow: {},
      // 当前选中行id
      currentId: "",
      // 数据总数
      total: 0,
      // 列表数据
      tableData: []
    }
  },
  mounted () {
    // 获取角色列表数据
    this.getRoleData();
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.query.pageSize = val
      this.query.startRow = 0
      this.getRoleList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.query.startRow = val
      this.getRoleList();
    },
    exportRole () {
      exportRole(this.query);
    },
    // paperSearch () {
    //   let form = this.$refs.paper.submitData;
    //   this.query.startRow = form.startRow === undefined ? 0 : form.startRow;
    //   this.query.pageSize = form.pageSize;
    //   this.getRoleList();
    // },
    setPermissionModal (item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showSetPermissionModal = true;
    },
    /** 关闭权限设置页面 **/
    setPermissionClose () {
      this.showSetPermissionModal = false;
    },
    /* 获取角色列表数据
              * */
    getRoleData () {
      // let form = this.$refs.paper.submitData;
      // this.query.startRow = 0;
      // this.query.pageSize = form.pageSize;
      this.getRoleList();
    },
    /* 获取角色列表数据
              * */
    getRoleList () {
      _queryRoleList(this.query).then(res => {
        if (res.status === 200) {
          this.tableData = res.data.tableData;
          this.total = res.data.total;
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 查询
             * */
    search () {
      this.getRoleData();
    },
    clearClick () {
      // 重置数据
      Object.assign(this.query, this.$options.data().query);
      // 重新获取数据
      this.getRoleData();
    },
    /* 批量删除
                    * */
    batchDelete () {
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage('info', this.$messageText['deleteSelectNone']);
        return;
      }
      let index = -1;
      // 当前只选中了一条数据，查找当前数据的索引
      if (this.multipleSelection.length === 1) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id === this.multipleSelection[0].id) {
            index = i;
          }
        }
      }
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: this.multipleSelection,
        index: index
      })
    },
    /* table checkbox 选中事件
                    * val:选中得数据
                    * */
    handleSelectionChange (val) {
      this.multipleSelection = val.map((i) => {
        return i.id
      });
    },
    /* 删除单条角色数据
                    * id:要删除数据的id
                    * index:要删除数据的索引
                    * */
    deleteClick (id, index) {
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', {
        idList: [id],
        index: index
      })
    },
    /* 删除角色数据请求
                   * */
    deleteClickData (param) {
      // 删除角色
      _deleteRole({ idList: param.idList.join(',') }).then(res => {
        if (res.data.result) {
          // 只删除一条数据并且索引大于-1
          if (param.idList.length === 1 && param.index >= 0) {
            // 从列表中删除数据
            this.tableData.splice(param.index, 1);
            // 重新获取数据
            this.getRoleData();
          } else {
            // 重新获取数据
            this.getRoleData();
          }
          // 提示删除成功消息
          showMessage('info', this.$messageText['deleteSuccess']);
        } else {
          showMessage('error', "已有用户使用该角色，不可删除!");
        }
        this.getRoleData();
      })
    },
    /* 关闭新建角色弹出框
              * flag：是取消还是确定操作
              * */
    addRoleModalClose (flag) {
      // 关闭弹出框
      this.showModal = false;
      // 确定操作
      if (flag) {
        // 重新获取数据
        this.getRoleData();
      }
    },
    /* 编辑角色
              * id:要编辑的数据id
              * */
    openAddRoleModal (item) {
      // 设置当前编辑行
      this.currentRow = item;
      // 打开编辑角色弹出框
      this.showModal = true;
    },
    /* 关闭授权弹出框
              * flag:关闭类型
              * */
    menuListModalClose (flag) {
      // 确定操作
      if (flag) {
        // 重新获取数据
        this.getRoleData();
      }
      this.showMenuList = false;
    }
  }
}
</script>
<style lang="scss" scoped>
    .user-list {
        padding: 20px;
        .user-list__avatar {
            width: 50px;
            height: 50px;
        }
      .role-list-div{
        width: 1300px;
        margin: auto;
        border-radius: 8px;
        background: #FFF;
        padding: 12px 20px 32px 20px;
        box-sizing: border-box;
      }
    }

    .main-header {
        position: relative;
        margin: 0;
        // padding-left: 8px;
        // border-left: 4px solid #999999;
        font-family: PingFang SC;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        color: $--el-color-text-primary;
        line-height: 22px;
        span {
            //算上边线本身4px一共12px
            padding-left: 12px;
        }
        //平行四边形边线
        i {
            position: absolute;
            top: 2px;
            display: inline-block;
            transform: skewY(-15deg);
            height: 18px;
            width: 4px;
            background-color: $--el-color-primary;
        }
    }

    a.btn,
    span.btn {
        color: #333333;
        i {
            font-size: 16px;
        }
        .greenColor {
            color: #0fa958 !important;
        }
    }

    .el-card {
        border: none;
    }

    /deep/ .el-card__body,
    .el-main {
        padding: 0;
    }

    .box-card-btns {
        a {
            margin-right: 13px;
            font-family: PingFang SC;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            i {
                color: #1990fe;
            }
            span {
                margin-left: 0;
            }
        }
    }

    .box-card-search {
        .box-card-search-btns {
            & > :first-child {
                margin-left: 5px;
            }
        }
    }

    .el-table-name {
        display: inline-block;
        width: 40%;
        text-align: left;
    }

    .el-table-name-icon {
        width: 60%;
        text-align: right;
        .male {
            color: #76c3ff;
        }
        .female {
            color: #ff74b2;
        }
    }
</style>
<style lang="scss">
    .el-card__body, .el-main {
        padding: 0;
        text-align: left;
        line-height: 20px;
    }
    .row-form-list{
      .el-input__inner {
        border-radius: 35px;
        background: #F6F7F9;
        border: 1px solid #F6F7F9;
        height: 32px;
        line-height: 32px;

      }
    }
    .custom-header-class-list{

      th{
        color: #777!important;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        background: #F6F7F9;
        padding: 10px 0px !important;

      }
    }

</style>
