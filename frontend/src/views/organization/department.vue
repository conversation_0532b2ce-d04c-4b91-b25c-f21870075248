<!--部门管理-->
<template>
  <el-container class="department" style="padding-top: 8px; min-height: 200px;box-sizing: border-box">
    <el-aside width="320px" style="padding: 0 16px;height: calc(100vh - 77px);">
      <el-tree-search
          :data="treeList"
          :props-config="defaultProps"
          ref="tree"
          node-key="id"
          :hide-search="true"
          label-name="机构部门"
          :label-count="treeSize"
          @node-click="handleNodeClick"
      ></el-tree-search>
    </el-aside>
    <el-main style="padding: 0 10px;box-sizing: border-box">
      <!--      <div style="background-color: #fff;padding: 20px;display: flex;align-items: center;justify-content: space-between">-->
      <el-tabs type="border-card" v-model="activeName" @tab-click="changeTab" class="border-card">
        <el-tab-pane label="基本信息" name="0">
          <div style="min-height: 500px;">
            <div style="display: flex;">
              <div style="display: flex;align-items: center;margin-right: 20px;color: #333;font-size: 14px;font-weight: 400;" >
                <span>操作模式</span>
              </div>
              <el-radio-group v-model="type" @change="radioChange">
                <el-radio-button  type="primary" plain label="0">编辑部门</el-radio-button>
                <el-radio-button  v-show="currentNode.orgType != '1'" type="primary" plain label="1">添加同级部门</el-radio-button>
                <el-radio-button type="primary" plain label="2">添加子部门</el-radio-button>
                <el-radio-button  v-show="currentNode.orgType != '1'" type="primary" plain label="3">上移</el-radio-button>
                <el-radio-button  v-show="currentNode.orgType != '1'" type="primary" plain label="4">下移</el-radio-button>
                <el-radio-button  v-show="currentNode.orgType != '1'" type="primary" plain label="5">调整分类</el-radio-button>
                <el-radio-button  v-show="currentNode.orgType != '1' " type="primary" plain label="6">删除机构</el-radio-button>
              </el-radio-group>

              <!--导入组织机构按钮 临时-->
              <el-upload
                  :http-request="customUpload"
                  :multiple="false"
                  :show-file-list="false"
                  :before-upload="beforeUpload">
                <el-button v-if="importOrganizationStatus" round type="primary" size="small" style="margin-left: 10px;">导入机构部门</el-button>
              </el-upload>
            </div>
            <div style="display: flex;margin-top: 20px">
              <div style="display: flex;align-items: center;margin-right: 20px;color: #333;font-size: 14px;font-weight: 400;" >
                <span>机构代码</span>
              </div>
              <el-input v-model="paramNode.orgCode" :maxLength="32" size="small" style="width: 200px"></el-input>
            </div>
            <div style="display: flex;margin-top: 20px">
              <div style="display: flex;align-items: center;margin-right: 20px;color: #333;font-size: 14px;font-weight: 400;" >
                <span>机构名称</span>
              </div>
              <el-input v-model="paramNode.orgName" :maxLength="32" size="small" style="width: 200px"></el-input>
            </div>
            <div style="display: flex;margin-top: 20px" v-show="type != '1' && type != '3' && type != '4' && type != '6'">
              <div style="display: flex;align-items: center;margin-right: 20px;color: #333;font-size: 14px;font-weight: 400;" >
                <span>机构类别</span>
              </div>
              <el-radio v-show="currentNode.orgType == '1' && type == '0'"  v-model="paramNode.orgType" label="1">总部</el-radio>
              <el-radio v-show="!(currentNode.orgType == '1' && type == '0')" v-model="paramNode.orgType" label="2">分公司</el-radio>
              <el-radio  v-show="!(currentNode.orgType == '1' && type == '0')" v-model="paramNode.orgType" label="3">营业部</el-radio>
            </div>
            <div style="display: flex;margin-top: 20px" v-show=" ( paramNode.orgType == '2')">
              <el-checkbox @change="changeThreshold" v-model="paramNode.checked" >是否允许营业部互相查看</el-checkbox>
            </div>
            <div style="display: flex;margin-top: 30px;margin-left: 85px">
              <el-button @click="save" round type="primary">保存</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!--      </div>-->
    </el-main>
    <el-dialog :close-on-click-modal="false"
               title="提示"
               :visible.sync="personExistShow"
               width="30%">
      <span>当前部门或子部门内有人员存在,是否确认删除?</span>
      <div style="margin-top: 40px;display: flex;justify-content: center">
        <el-button @click="personExistShow = false" size="small">取 消</el-button>
        <el-button style="margin-left: 20px" type="primary" @click="deleteDepartment" size="small">确 定</el-button>
      </div>

    </el-dialog>
    <el-dialog
        :close-on-click-modal="false"
        v-if="adjustShow"
        title="调整分类"
        :visible.sync="adjustShow"
        @close="closeAdjustDialog"
        width="70%">
      <div style="background-color: rgb(247,247,247)">
        <div style="min-height: 500px;display: flex;">
          <el-tree-search
              :width="300"
              :expand-on-click-node="true"
              :data="treeList"
              :props-config="defaultProps"
              :hide-search="true"
              ref="adjustTree"
              node-key="id"
              label-name="机构部门"
              :label-count="treeSize"
              @node-click="handleNodeClick"
              @mostParent-click="reset"
              :loaded-data="treeList.length > 0 ? true : false"
          >
          </el-tree-search>
          <div style="width: 100%;margin-top:40px;margin-left: 20px;margin-right:10px;background-color: #fff;padding:20px 20px;height: 200px">
            <div  >
              <span style="font-weight: 700;margin-right: 40px">调整节点名称:</span>
              <span style="color: #0a67fb">{{adjustNode.orgName}}</span>
            </div>
            <div style="margin-top: 20px">
              <span style="font-weight: 700;margin-right: 40px">选中节点名称:</span>
              <span>{{currentNode.orgName}}</span>
            </div>
            <el-button style="margin-top: 20px;" @click="adjust(0)" type="primary" size="small" plain >移动到子节点</el-button>
          </div>

        </div>

      </div>

    </el-dialog>
  </el-container>

</template>

<script>
import {
  _addBrotherNode,
  _addChildNode,
  _adJustNode,
  _checkPerson,
  _deleteNode,
  _editNode,
  _nodeMoveDown,
  _nodeMoveUp,
  _queryOrgTree
} from '@/api/depart-api'
import { _importDeptInfo } from "@/api/system/infoSync-api";
import {selectButtonByName} from "../../api/mqManage/MQManage-api";

export default {
  name: "department",
  data () {
    return {
      importOrganizationStatus:false,// 控制导入机构部门按钮隐藏和显示
      adjustOldDom: {},
      oldDom: {},
      checkAll: "",
      activeName: "0",
      treeShow: true,
      treeList: [],
      treeSize: 0,
      expandKeys: [],
      defaultProps: { // 左侧树配置
        children: 'children',
        label: 'label'
      },
      paramNode: {
        orgName: "",
        orgType: "",
        orgCode: "",
        checked: false,
        thresholdFlag: ""
      },
      type: "0",
      currentNode: {},
      adjustNode: {},
      personExistShow: false,
      adjustShow: false,
      searchDto: {},
      classificationOption: [
        {
          value: "1",
          label: "有"
        },
        {
          value: "0",
          label: "没有"
        }
      ],
      classifyTableData: []
    }
  },
  created () {
    this.reset()
    this.checkButtonStatus();// 判断导入机构部门按钮是否显示
  },
  mounted () {
    // todo 组件问题,待解决,暂时让他消失
    this.$refs.tree.searchInputFlg = false
  },
  methods: {
    customUpload (fileObj) {
      // this.importOrganizationStatus = false;//控制隐藏导入机构部门按钮
      // 在这里编写自定义的上传逻辑
      let data = new FormData();
      data.append('files', fileObj.file);
      _importDeptInfo(data).then(res => {
        if (res.data.success) {
          this.$message.info("成功导入" + res.data.result + "条数据");
          window.location.reload();
        } else {
          // this.importOrganizationStatus = true;//控制隐藏导入机构部门按钮
          this.$message.error("导入失败" + res.data.errorMsg);
        }
      })
      this.checkButtonStatus()
    },
    beforeUpload (file) {
      const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const isXls = file.type === 'application/vnd.ms-excel';
      if (!isXlsx && !isXls) {
        this.$message.error('只能上传xls、xlsx文件');
        return false; // 取消上传
      }
      return true; // 立即上传文件
    },
    changeThreshold (data) {
      this.paramNode.checked = data
      if (data) {
        this.paramNode.thresholdFlag = '1'
      } else {
        this.paramNode.thresholdFlag = '0';
      }
    },
    checkChildren (currentNodeId, adjustNodeId) {
      let adjustNode = this.$refs.adjustTree.$refs.treeSearch.getNode(adjustNodeId);
      if (adjustNode.data.children && adjustNode.data.children.length > 0) {
        for (const child of adjustNode.data.children) {
          if (child.id == currentNodeId) {
            return true;
          }
          if (this.checkChildren(currentNodeId, child.id)) {
            return true;
          }
        }
      }
      return false;
    },
    adjust (type) {
      if (this.currentNode.id == null && this.type == '0') {
        this.$message("请先选择节点")
        return
      } else {
        if (this.currentNode.id == this.adjustNode.id) {
          this.$message("不能自己移动到自己以及子部门")
          return
        }
        if (this.checkChildren(this.currentNode.id, this.adjustNode.id)) {
          this.$message("不能自己移动到自己以及子部门")
          return
        }
        if (this.adjustNode.orgType <= this.currentNode.orgType) { // 检查是否能够被添加
          this.$message(this.getNameByOrgType(this.adjustNode.orgType) + "不能移动到" + this.getNameByOrgType(this.currentNode.orgType))
          return;
        }
      }
      let param = {
        fromNodeId: this.adjustNode.id, // 被移动的节点id
        toNodeId: this.currentNode.id, // 接收的节点id
        type: type
      }
      _adJustNode(param).then(data => {
        this.adjustShow = false;
        window.location.reload();
        // this.treeList = []
        // this.reset()
      })
    },
    radioChange () {
      if (this.type == 5) {
        if (this.currentNode.id == null) {
          this.$message("请先选择部门节点,再进行操作")
          this.type = 0
          return;
        }
        this.adjustShow = true;
        this.adjustNode = this.currentNode;
        this.$nextTick(() => {
          this.$refs.adjustTree.searchInputFlg = false
        })
        setTimeout(() => {
          this.$refs.adjustTree.$refs.treeSearch.setCurrentKey(this.adjustNode.id)
          this.$refs.adjustTree.$refs.treeSearch.defaultExpandedKeys = [this.treeList[0].id]
        }, 300)
      }
    },
    closeAdjustDialog () {
      this.type = 0
      this.reset()
      this.handleNodeClick(this.adjustNode)
    },
    reset () {
      this.paramNode = { orgName: "" }
      this.currentNode = {}
      this.type = 0
      let _this = this;
      _queryOrgTree().then(data => {
        let result = data.data.result
        _this.treeList = result.tree
        _this.treeSize = result.treeSize
        this.$nextTick(() => {
          this.$refs.tree.$refs.treeSearch.defaultExpandedKeys = [_this.treeList[0].id]
        })

      })
    },
    getNameByOrgType (type) {
      if (type == '1') {
        return "总部"
      } else if (type == '2') {
        return "分公司"
      } else if (type == '3') {
        return "营业部"
      }
    },
    checkOrgCode (code) {
      return /^[0-9a-zA-Z]+$/.test(code)
    },
    save () {
      let _this = this
      if (this.currentNode.id == null) {
        this.$message("请先选择部门节点,再进行操作")
        return;
      } else if (this.paramNode.orgName == '' || this.paramNode.orgCode == '') {
        this.$message("机构代码和机构名称不能为空!")
        return;
      }

      if (this.type == 0) {
        if (this.currentNode.children && this.currentNode.children.length > 0) { // 有子节点
          if (this.paramNode.orgType != this.currentNode.orgType) { // 营业部
            this.$message("当前" + this.getNameByOrgType(this.currentNode.orgType) + "下面有子部门,不能修改成" + this.getNameByOrgType(this.paramNode.orgType))
            return;
          }
        }
        let parentNode = this.$refs.tree.$refs.treeSearch.getNode(this.currentNode.pOrgId);
        if (parentNode) {
          if (parentNode.data.orgType == '2' && this.paramNode.orgType == '2' && this.currentNode.orgType != '2') {
            this.$message("当前营业部上级存在分公司,不能修改成分公司!")
            return;
          }
        }
        if (this.paramNode.orgType == '3') {
          this.paramNode.thresholdFlag = '';
          this.paramNode.checked = false;
        }
        if (!this.checkOrgCode(this.paramNode.orgCode)) {
          this.$message("公司代码不符合规范,只能输入字母和数字")
          return
        }
        _editNode(this.paramNode).then(data => {
          if (!data.data.success) {
            _this.$message(data.data.errorMsg);
            return;
          }
          this.currentNode.orgName = this.paramNode.orgName
          this.currentNode.label = this.paramNode.orgName
          this.currentNode.orgType = this.paramNode.orgType
          this.currentNode.orgCode = this.paramNode.orgCode
          this.currentNode.thresholdFlag = this.paramNode.thresholdFlag
          this.$message("保存成功")
        })
      } else if (this.type == 1) {
        if (!this.checkOrgCode(this.paramNode.orgCode)) {
          this.$message("公司代码不符合规范,只能输入字母和数字")
          return
        }
        _addBrotherNode(this.paramNode).then(data => {
          if (!data.data.success) {
            _this.$message(data.data.errorMsg);
            return;
          }
          if (this.currentNode.level == '0') {
            setTimeout(() => {
              this.$refs.tree.$refs.treeSearch.append(data.data.result, this.currentNode.parent)
            }, 500)
          } else {
            setTimeout(() => {
              this.$refs.tree.$refs.treeSearch.append(data.data.result, this.paramNode.pOrgId)
            }, 500)
          }
          this.treeSize = this.treeSize + 1
          let children = this.currentNode.children;
          this.currentNode.children = []
          this.paramNode = JSON.parse(JSON.stringify(this.currentNode))
          this.currentNode.children = children
          this.$message("添加成功")
        })
      } else if (this.type == 2) {
        if (!this.paramNode.orgType || this.paramNode.orgType == '1') {
          this.$message("请选择机构类别")
          return
        } else if (this.paramNode.orgType <= this.currentNode.orgType) { // 检查是否能够被添加
          this.$message(this.getNameByOrgType(this.currentNode.orgType) + "下面不能添加" + this.getNameByOrgType(this.paramNode.orgType))
          return;
        }
        if (!this.checkOrgCode(this.paramNode.orgCode)) {
          this.$message("公司代码不符合规范,只能输入字母和数字")
          return
        }
        _addChildNode(this.paramNode).then(data => {
          if (!data.data.success) {
            _this.$message(data.data.errorMsg);
            return;
          }
          setTimeout(() => {
            this.$refs.tree.$refs.treeSearch.append(data.data.result, this.paramNode.id)
          }, 200)

          this.treeSize = this.treeSize + 1
          let children = this.currentNode.children;
          this.currentNode.children = []
          this.paramNode = JSON.parse(JSON.stringify(this.currentNode))
          this.currentNode.children = children
          this.$message("添加成功")
        })
      } else if (this.type == 3) {
        _nodeMoveUp(this.currentNode).then(data => {
          if (!data.data.success) {
            _this.$message(data.data.errorMsg);
            return;
          }
          let list = data.data.result
          let preNode = this.$refs.tree.$refs.treeSearch.getNode(list[1].id);
          list[1].children = preNode.data.children
          let node = this.$refs.tree.$refs.treeSearch.getNode(list[0].id);
          list[0].children = node.data.children
          this.$refs.tree.$refs.treeSearch.remove(node)
          this.$refs.tree.$refs.treeSearch.insertBefore(list[0], preNode)
          this.$refs.tree.$refs.treeSearch.remove(preNode)
          this.$refs.tree.$refs.treeSearch.insertAfter(list[1], list[0].id)
          this.handleNodeClick(list[0]);
          this.$message("上移成功")
        })
      } else if (this.type == 4) {
        _nodeMoveDown(this.currentNode).then(data => {
          if (!data.data.success) {
            _this.$message(data.data.errorMsg);
            return;
          }

          let list = data.data.result
          let preNode = this.$refs.tree.$refs.treeSearch.getNode(list[0].id);
          list[0].children = preNode.data.children
          let node = this.$refs.tree.$refs.treeSearch.getNode(list[1].id);
          list[1].children = node.data.children
          this.$refs.tree.$refs.treeSearch.remove(preNode)
          this.$refs.tree.$refs.treeSearch.insertAfter(list[0], node)
          this.$refs.tree.$refs.treeSearch.remove(node)
          this.$refs.tree.$refs.treeSearch.insertBefore(list[1], list[0].id)
          this.handleNodeClick(list[0]);
          this.$message("下移成功")
        })
      } else if (this.type == 6) {
        if (this.currentNode.children && this.currentNode.children.length > 0) {
          this.$message("当前节点下面有子部门,不能够进行删除操作!")
          return;
        }
        let nodeList = []
        // this.setLeafDepartmentList(this.currentNode,nodeList);//设置当前部门向下所有结点,包括路径,id
        // let ids = nodeList.map(data=>data.id).join(",");
        let ids = this.currentNode.id;
        _checkPerson({ ids: ids }).then(data => {
          if (data.data.result) {
            this.$message("当前部门或子部门内有人员存在,无法删除")
            // this.personExistShow = true
          } else {
            this.deleteDepartment(nodeList.length)
          }
        })
      }
    },
    deleteDepartment (size) {
      _deleteNode({ id: this.paramNode.id }).then(data => {
        let node = this.$refs.tree.$refs.treeSearch.getNode(this.paramNode.id);
        this.$refs.tree.$refs.treeSearch.remove(node)
        this.currentNode = {}
        this.paramNode = { orgName: "" }
        this.treeSize = this.treeSize - 1
        this.$message("删除成功")
      })
    },
    handleNodeClick (node) {
      if (node.orgType == '4') {
        return;
      }
      if (this.currentNode.orgType == '1') {
        this.type = '0'
      }
      this.currentNode = node;
      this.paramNode = JSON.parse(JSON.stringify(node))
      if (typeof this.paramNode.thresholdFlag === 'object') {
        this.paramNode.thresholdFlag = ''
      }
      if (this.paramNode && this.paramNode.thresholdFlag == '1') {
        this.paramNode.checked = true;
      } else {
        this.paramNode.checked = false;
      }
      this.paramNode.children = []
    },
    changeTab (data) {
      if (data.index == '1') {
        if (this.currentNode.id) {
          this.searchDto.departmentId = this.currentNode.id
        }
      }
    },
    setParent (id, flag) {
      let data = this.getDataFromTree(id, this.classifyTableData);
      if (data != null) {
        data.havePermission = flag
        if (data.parentId != '0') {
          this.setParent(data.parentId);
        } else {
          let data = this.getDataFromTree(id, this.classifyTableData);
          data.havePermission = flag
        }
      }
    },
    getDataFromTree (id, list) {
      for (const data of list) {
        if (id == data.id) {
          return data
        }
        if (data.children && data.children.length > 0) {
          for (const child of data.children) {
            let result = this.getDataFromTree(id, data.children);
            if (result) {
              return result
            }
          }
        }
      }
      return null
    },
    setChild (row, flag) {
      if (row.children && row.children.length > 0) {
        for (const child of row.children) {
          child.havePermission = flag
          this.setChild(child, flag)
        }
      }
    },
    checkAllSelect (list) {
      for (const item of list) {
        if (item.havePermission != '1') {
          return false;
        }
        if (item.children && item.children.length > 0) {
          if (!this.checkAllSelect(item.children)) {
            return false;
          }
        }
      }
      return true;
    },
    tableSelectChange (row) {
      if (row.havePermission == '1') {
        this.setParent(row.parentId, '1')
        this.setChild(row, "1")
        if (this.checkAllSelect(this.classifyTableData)) {
          this.checkAll = true;
        }
      } else {
        this.setChild(row, "0")
        this.checkAll = false;
      }
    },
    tableSelectChangeAll () {
      if (!this.currentNode.id) {
        if (this.checkAll == '0' || this.checkAll == '') {
          this.checkAll = '1'
        } else {
          this.checkAll = '0'
        }
        this.$message("请先选择部门")
        return
      }
      for (const tableData of this.classifyTableData) {
        tableData.havePermission = this.checkAll
        this.setChild(tableData, this.checkAll)
      }
    },
    saveClassification () {
      _saveClassification(this.classifyTableData).then(data => {
        this.$message("保存成功")
      })
    },
    // 获取按钮显隐状态
    checkButtonStatus() {
      selectButtonByName({moduleName:'organization',zhName:'组织架构',buttonName:'导入机构部门'}).then(res=>{
        if(res.data.result.status === '1'){
          this.importOrganizationStatus = true;
        }else{
          this.importOrganizationStatus = false;
        }
      })
    },
  }
}
</script>

<style lang="scss">
.department >>> .el-tree-search__content {
  width:unset !important;
}
.department{
  .el-collapse-item__wrap{
    border-top: 1px solid #EBEEF5;
    border-bottom: none;

  }
  .border-card{
    border: 1px solid #EEE;
    min-height: calc(100vh - 77px);
    border-bottom: none;
    .el-tabs__content{
      padding: 20px;
    }
    .el-tabs__header {
      background-color: #F9F9F9;
    }
  }
  .el-tree-search__header_item i{
    color: #D50212!important;

  }
  .el-tree-search__header_operation .is-single {
    color: #D50212!important;
    border-color: #D50212!important;
  }
  .el-tree-search__header_operation .is-single:hover {
    color: #D50212!important;
    border-color: #D50212!important;

  }
  .el-tree-search__content{
    .el-tree-node__label{
      span{
        color: #D50212!important;
      }
    }
  }
}
</style>
