<template>
    <el-dialog
            width="700px"
            :before-close="handleClose"
            :title="formInline.id?'编辑路由':'新增路由'"
            v-model="showing"
            append-to-body
            modal>
        <div>
            <el-form :model="formInline" label-width="90px" :rules="rules" ref="editForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="上级路由" v-if="formInline.parentRouter">
                            <span>{{formInline.parentRouter}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="路由名称" prop="menuName">
                            <el-input v-model="formInline.routerName" placeholder="路由名称" :maxLength="100"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="路由path" prop="path">
                            <el-input v-model="formInline.path" placeholder="路由path" :maxLength="256"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="meta title" prop="metaData.title">
                            <el-input v-model="formInline.metaData.title" placeholder="meta title" :maxLength="240"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="图标">
                            <el-input v-model="formInline.routerIcon" placeholder="图标" :maxLength="256"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="显示为菜单">
                            <el-switch
                                    v-model="isMenu">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="菜单名称" v-if="isMenu" prop="routerDes">
                            <el-input v-model="formInline.routerDes" placeholder="菜单名称" :maxLength="256"></el-input>
                        </el-form-item>
                        <el-form-item label="路由描述" v-else>
                            <el-input v-model="formInline.routerDes" placeholder="路由描述" :maxLength="256"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="设置资源">
                            <el-select v-model="formInline.resourceIdList" confirm class="full" multiple
                            ref="resourceList">
                                <el-option
                                        v-for="(item,index) in resourceList"
                                        :key="index"
                                        :label="item.codeName"
                                        :value="item.codeValue">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div slot="footer" class="text-center">
            <el-button @click="closeModal(false)">取消</el-button>
            <el-button type="primary" @click="sureClick">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { _createMenu, _getResourceDownList, _queryMenuById, _modifyMenu, _menuNameCheck } from "@/api/router-api";
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  emits:['addRouterModalClose'],
  data () {
    /* 检查路由名称是否重复
      * */
    const menuNameCheck = (rule, value, callback) => {
      // 没有输入内容，提示错误信息
      if (!value || value.length === 0) {
        return callback(new Error("请输入路由名称"));
      } else {
        // 检验路由名称是否重复
        _menuNameCheck({ id: this.formInline.id, routerName: value }).then(res => {
          if (res.data.success) {
            if (res.data.result) {
              callback();
            } else {
              return callback(new Error("路由名称已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 资源列表
      resourceList: [],
      // 编辑菜单需要的字段
      formInline: {
        // 父路由id
        pRouterId: "",
        // 路由名
        routerName: '',
        // 图标
        routerIcon: '',
        // path
        path: "",
        // 资源
        resourceIdList: [],
        // 类型
        routerType: "",
        // meta title
        metaData: {
          title: ""
        },
        // 路由描述
        routerDes: ""
      },
      // 是否是菜单
      isMenu: true,
      // 表单验证规则
      rules: {
        // 路由名
        routerName: [
          { required: true, message: '请输入路由名称', trigger: 'blur' },
          { validator: menuNameCheck, trigger: 'blur' }
        ],
        // 路径
        path: [
          { required: true, message: '请输入path', trigger: 'blur' }
        ],
        // meta title
        'metaData.title': [
          { required: true, message: '请输入meta title', trigger: 'blur' }
        ],
        // 路由描述
        routerDes: [
          { required: true, message: '请输入路由描述', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
    * */
    handleClose () {
      this.closeModal(false)
    },
    /* 保存角色
              * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          // 更改路由类型
          this.formInline.routerType = this.isMenu ? '1' : '2';
          // 编辑路由
          if (this.formInline.id) {
            _modifyMenu(this.formInline).then(res => {
              // 编辑成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示信息
                this.showGlobalMessage('info', this.$messageText['updateSuccess']);
              } else {
                // 提示错误信息
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          } else {
            // 新建路由
            _createMenu(this.formInline).then(res => {
              // 新建成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示消息
                this.showGlobalMessage('info', this.$messageText['saveSuccess']);
              } else {
                // 提示错误信息
                this.showGlobalMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 取消操作
     * flag:是取消还是保存
    * */
    closeModal (flag) {
      this.$emit('addRouterModalClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
    *  */
    resetForm () {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
      this.isMenu = true;
      // 清空下拉数据
      this.$refs.resourceList.clear();
    },
    /* 获取路由下拉列表数据
      * */
    getRouterData () {
      _getResourceDownList().then(res => {
        // 获取成功
        if (res.data.success) {
          // 设置下拉列表的数据
          this.resourceList = res.data.result;
          // 当前是编辑路由
          if (this.formInline.id) {
            // 根据当前路由的id获取路由的详细信息
            _queryMenuById({ id: this.formInline.id }).then(res => {
              // 获取成功
              if (res.data.success) {
                // 设置显示的数据
                this.formInline = res.data.result;
                // console.log(this.$refs.resourceList[0])
                // this.$refs.resourceList.set(this.formInline.resourceIdList);
                // 设置菜单类别
                this.isMenu = this.formInline.routerType === '1';
              } else {
                // 提示错误信息
                this.showGlobalMessage('error', res.data.errorMsg)
              }
            })
          }
        }
      })
    }
  },
  watch: {
    isShow (val) {
      // 打开弹出框
      if (val) {
        // 显示弹出框
        this.showing = this.isShow;
        // 更新路由
        if (this.updateData) {
          // 设置数据
          Object.assign(this.formInline, this.updateData);
        }
        // 获取路由详细数据
        this.getRouterData();
      } else {
        // 重置表单
        this.resetForm();
      }
    }
  }

}
</script>
