<template>
    <div class="user-list">
        <div class="main-header">
            <i></i>
            <span>路由信息</span>
        </div>
        <div class="m-c">
            <el-card class="box-card box-card-search"
                     shadow="never"
                     :body-style="{ border: none }">
                <el-row :gutter="10">
                    <el-col :span="6">
                        <el-input placeholder="路由名称" v-model="query.routerName" size="small"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-input placeholder="路由path" v-model="query.path" size="small"></el-input>
                    </el-col>
                    <el-col :span="6">
                        <el-select @change="getMenuData" class="full" clearable size="small"
                                   placeholder="类型" v-model="query.routerType">
                            <el-option label="菜单" value="1"></el-option>
                            <el-option label="按钮" value="2"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6" class="text-left box-card-search-btns">
                        <el-button @click="search" type="primary" size="small">查询</el-button>
                        <el-button @click="clearClick" size="small">清空条件</el-button>
                    </el-col>
                </el-row>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-col :span="12" class="text-left">
                    <el-link :underline="false" type="primary" @click="openAddMenuModal(null)">
                        <i class="iconfont icon-gonggaoicon_tianjia"></i>
                        <span class="btn">新增</span>
                    </el-link>
                    <el-link :underline="false" type="primary" @click="batchDelete">
                        <i class="iconfont ic-trash"></i>
                        <span class="btn">批量删除</span>
                    </el-link>
                </el-col>
            </el-card>
            <el-card class="box-card m-t-20 box-card-btns" shadow="never">
                <el-row>
                    <el-col :span="6">
                        路由列表
                    </el-col>
                    <!--<el-col :span="18" class="text-right">-->
                    <!--<el-button @click="openAddMenuModal(null)" type="primary">新增路由</el-button>-->
                    <!--<el-button @click="batchDelete">批量删除</el-button>-->
                    <!--<el-button type="primary" @click="showMenuList = true">排序</el-button>-->
                    <!--</el-col>-->
                </el-row>
                <el-table
                        :data="tableData"
                        @select="select"
                        @select-all="selectAll"
                        @selection-change="handleSelectionChange"
                        class="m-t-20 full"
                        ref="multipleTable"
                        row-key="id">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column align="center" label="ID" prop="id"></el-table-column>
                    <el-table-column align="center" label="上级路由" prop="pRouterId">
                        <template slot-scope="scope">
                            <span v-if="scope.row.pRouterId">{{scope.row.pRouterId}}</span>
                            <span class="empty-text" v-else>--</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="left" label="路由名称" prop="routerName"></el-table-column>
                    <el-table-column align="left" label="路由描述" prop="routerDes"></el-table-column>
                    <el-table-column align="left" label="path" prop="path"></el-table-column>
                    <el-table-column align="center" label="类型" prop="routerType">
                        <template slot-scope="scope">
                            <span v-if="scope.row.routerType == '0'">一级菜单</span>
                            <span v-if="scope.row.routerType == '1'">二级菜单</span>
                            <span v-if="scope.row.routerType == '2'">按钮</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作">
                        <template slot-scope="scope">
                          <i class="iconfont ic-edit custom-icon-button" title="编辑" @click="openAddMenuModal(scope.row)"></i>
                          <i class="iconfont ic-trash custom-icon-button" title="删除" @click="deleteClick(scope.row.id,scope.$index)"></i>
                          <i class="iconfont ic-plus custom-icon-button" title="添加子路由" @click="addChildMenu(scope.row)"></i>
                        </template>
                    </el-table-column>
                </el-table>
                <papers ref="paper" @searchTable="paperSearch" :total="total" :size="10"></papers>
            </el-card>
        </div>
        <add-router :isShow="showModal" :updateData="currentRow" @addRouterModalClose="addRouterModalClose"></add-router>
        <menu-list :isShow="showMenuList" @menuListModalClose="menuListModalClose" type="sort"></menu-list>
    </div>
</template>
<script>
import { showMessage, showConfirm } from 'stock-vue-plugin/lib/utils/message'
import addRouter from '../../components/Modal/addRouter'
import menuList from '../../components/Modal/menuList'
import { _queryRouterList, _deleteMenu } from "@/api/manage-api";
import papers from "@/components/Pagination/papers.vue";

export default {
  components: { addRouter, menuList, papers },
  data () {
    return {
      // 选中的数据
      multipleSelection: [],
      // 是否打开新增菜单弹出框
      showModal: false,
      // 是否打开授权弹出框
      showMenuList: false,
      // 是否打开选择资源弹出框
      showSelectResource: false,
      // 查询需要的参数
      query: {
        // 菜单名称
        routerName: "",
        // 授权标识
        resourceId: "",
        // url
        path: "",
        // 资源名称
        resourceNameSearch: "",
        // 类型
        routerType: "",
        // 页码
        startRow: 0,
        // 每页显示多少条数据
        pageSize: 10
      },
      // 当前编辑行数据
      currentRow: {},
      // table 列表数据
      tableData: []
    }
  },
  mounted () {
    // 获取菜单列表
    this.getMenuData();
  },
  methods: {
    paperSearch () {
      let form = this.$refs.paper.submitData;
      this.query.startRow = form.startRow;
      this.query.pageSize = form.pageSize;
      this.getMenuList();
    },
    search () {
      this.getMenuData();
    },
    clearClick () {
      Object.assign(this.query, this.$options.data().query); // 重置数据
      this.getMenuData(); // 重新获取数据
    },

    getMenuData () {
      let form = this.$refs.paper.submitData;
      this.query.startRow = 0;
      this.query.pageSize = form.pageSize;
      this.getMenuList();
    },

    getMenuList () {
      _queryRouterList(this.query).then(res => {
        if (res.status == '200') {
          this.tableData = res.data.tableData;
          this.total = res.data.total;
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },

    batchDelete () {
      // 没有选中数据的时候，要提示消息
      if (this.multipleSelection.length === 0) {
        // 提示需要选中数据
        showMessage('info', this.$messageText['deleteSelectNone']);
        return;
      }
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', { idList: this.multipleSelection })
    },

    handleSelectionChange (val) {
      this.multipleSelection = val.map((i) => {
        return i.id
      });
    },
    getChildRouterId (data, list) {
      data.map((i) => {
        list.push(i);
        if (i.childRouter && i.childRouter.length > 0) {
          this.getChildRouterId(i.childRouter, list)
        }
      })
      return list;
    },
    deleteClick (id, index) {
      showConfirm(this.$messageText['deleteContent'], this.$messageText['deleteTitle'], this, 'deleteClickData', { idList: [id] });
    },
    deleteClickData (param) {
      _deleteMenu({ idList: param.idList.join(',') }).then(res => {
        if (res.data.success) {
          // 重新获取数据
          this.getMenuData();
          // 提示删除成功消息
          showMessage('info', this.$messageText['deleteSuccess']);
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    openAddMenuModal (item) {
      this.currentRow = item;
      // 打开编辑菜单弹出框
      this.showModal = true;
    },
    /* 添加子菜单
              * item:当前行的数据
              * */
    addChildMenu (item) {
      // 设置参数
      let param = {
        parentMenu: item.menuName,
        pRouterId: item.id
      }
      // 打开新建菜单弹出框
      this.openAddMenuModal(param);
    },
    /* 关闭新建用户弹出框
                              * flag：是取消还是确定操作
                              * */
    addRouterModalClose (flag) {
      // 关闭弹出框
      this.showModal = false;
      // 确定操作
      if (flag) {
        this.getMenuList();
      }
    },
    /* 关闭菜单列表弹出框
              * */
    menuListModalClose (flag) {
      this.showMenuList = false;
      if (flag) {
        this.getMenuData();
      }
    },
    /* 手动勾选数据行的checkbox
              * selection：当前选中的数据
              * row当前切换状态的行
              * */
    select (selection, row) {
      // 只对有子节点的行响应
      if (row.childRouter) {
        // 当前行选中
        if (!row.isChecked) {
          // 遍历所有子节点
          this.changeCheckedStatus(row.childRouter, true)
          // 当前行isChecked标志元素切换为true
          row.isChecked = true;
        } else { // 当前行未选中
          // 遍历子节点
          this.changeCheckedStatus(row.childRouter, false);
          // 当前行isChecked标志元素切换为false
          row.isChecked = false;
        }
      }
    },
    changeCheckedStatus (data, flag) {
      // 遍历所有子节点
      data.map((item) => {
        // 切换该子节点选中状态
        this.$refs.multipleTable.toggleRowSelection(item, flag);
        // 切换该子节点选中状态
        item.isChecked = flag;
        if (item.childRouter && item.childRouter.length > 0) {
          this.changeCheckedStatus(item.childRouter, flag)
        }
      });
    },
    /* 表头全选
              * */
    selectAll () {
      // 使用$ref获取注册的子组件信息，用data获取所有行，并用map函数遍历行
      this.$refs.multipleTable.data.map((items) => {
        if (items.childRouter) {
          // 若遍历出来的行未选中
          if (!items.isChecked) {
            // 行变为选中状态
            this.$refs.multipleTable.toggleRowSelection(items, true);
            // 更新标志参数
            items.isChecked = true;
            // 遍历子节点并改变状态与标志参数
            this.changeCheckedStatus(items.childRouter, true);
            // items.childRouter.map((item) => {
            //   this.$refs.multipleTable.toggleRowSelection(item, true);
            //   item.isChecked = true;
            // });
          } else { // 选中状态同理
            this.$refs.multipleTable.toggleRowSelection(items, false);
            items.isChecked = false;
            this.changeCheckedStatus(items.childRouter, false);
            // items.childRouter.map((item) => {
            //   this.$refs.multipleTable.toggleRowSelection(item, false);
            //   item.isChecked = false;
            // });
          }
        } else {
          // 遍历出来的行未选中
          if (!items.isChecked) {
            // 设置为选中
            items.isChecked = true;
          } else { // 遍历出来的行选中了
            // 设置为未选中
            items.isChecked = false;
          }
        }
      });
      // console.log(this.orgs)
    }
  }
}
</script>
<style lang="scss" scoped>
    .user-list {
        padding: 20px;
        .user-list__avatar {
            width: 50px;
            height: 50px;
        }
    }

    .main-header {
        position: relative;
        margin: 0;
        // padding-left: 8px;
        // border-left: 4px solid #999999;
        font-family: PingFang SC;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        color: $--el-color-text-primary;
        line-height: 22px;
        span {
            //算上边线本身4px一共12px
            padding-left: 12px;
        }
        //平行四边形边线
        i {
            position: absolute;
            top: 2px;
            display: inline-block;
            transform: skewY(-15deg);
            height: 18px;
            width: 4px;
            background-color: $--el-color-primary;
        }
    }

    a.btn,
    span.btn {
        color: #333333;
        i {
            font-size: 16px;
        }
        .greenColor {
            color: #0fa958 !important;
        }
    }

    .el-card {
        border: none;
    }

    /deep/ .el-card__body,
    .el-main {
        padding: 0;
    }

    .box-card-btns {
        a {
            margin-right: 13px;
            font-family: PingFang SC;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            i {
                color: #1990fe;
            }
            span {
                margin-left: 0;
            }
        }
    }

    .box-card-search {
        .box-card-search-btns {
            & > :first-child {
                margin-left: 5px;
            }
        }
    }

    .el-table-name {
        display: inline-block;
        width: 40%;
        text-align: left;
    }

    .el-table-name-icon {
        width: 60%;
        text-align: right;
        .male {
            color: #76c3ff;
        }
        .female {
            color: #ff74b2;
        }
    }
</style>
<style lang="scss">
    .el-card__body, .el-main {
        padding: 0;
        text-align: left;
        line-height: 20px;
    }

</style>
