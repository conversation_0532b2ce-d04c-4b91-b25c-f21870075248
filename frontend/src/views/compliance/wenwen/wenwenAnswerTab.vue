<template>
  <div class="wenwenAnswerList">
    <div v-if="!answerListObject.clearChartFlag && !answerLoading">
      <chart-clear-index :prop="prop" @getChat="getChat"></chart-clear-index>
    </div>
    <div v-else>
      <div class="calcCla"  :style="answerLoading ?'display: none' : { filter: `blur(${0}px)` }">
        <wenwen-answer-list-box :tabRadioVal="tabRadioVal" :answerListObject="answerListObject" :clearChartFlag="answerListObject.clearChartFlag" :answerLoading="answerLoading"
                         :radioLabelMap="radioLabelMap"  ref="AnswerListBox"></wenwen-answer-list-box>
      </div>
    </div>
  </div>
</template>
<script>
import common from '@/mixins/common.js'
import wenwenAnswerListBox from "@/views/compliance/wenwen/wenwenAnswerListBox.vue";
import ChartClearIndex from "@/views/compliance/wenwen/chartClearIndex.vue";
import {mapGetters} from "vuex";

export default {
  mixins: [common],
  components: { ChartClearIndex, wenwenAnswerListBox },
  props: {
    answerListObject: Object,
    answerLoading: Boolean
  },
  data () {
    return {
      tabRadioVal: null,
      baseRadioLabelMap: {
        lawsList: [
          { label: '00', list: 'lawInfoList', name: '法律法规', components: () => import('@/views/compliance/questionList/lawsRegulations.vue')},
          { label: '06', list: 'zhengQiuLawsList', name: '征求', components: () => import('@/views/compliance/questionList/solicit.vue')}
        ],
        qaList: [
          { label: '01', list: 'answerInfoList', name: '合规问答', components: () => import('@/views/compliance/questionList/answer.vue')}
        ],
        violateList: [
          { label: '02', list: 'violateInfoList', name: '违规案例', components: () => import('@/views/compliance/questionList/violate.vue')}
        ]
      },
      prop:{
        // 描述
        describe: "合规数据大模型加持的智能合规助手",
        // 名字
        name: "合规问答"
      }
    }
  },
  computed: {
    ...mapGetters(['homeSending']),
    radioLabelMap() {
      if (!this.answerListObject?.reportInfoList) {
        return this.baseRadioLabelMap;
      }

      return {
        ...this.baseRadioLabelMap,
      }
    }
  },
  watch: {
    answerListObject: {
      handler (newVal) {
        if (newVal && newVal.dataOrder) {
          const dataOrder = String(newVal.dataOrder);
          try {
            this.tabRadioVal = dataOrder.split(',')[0];
            if (this.tabRadioVal === 'lawsList') {
              this.tabRadioVal = '00'
            } else if (this.tabRadioVal === 'qaList') {
              this.tabRadioVal = '01'
            } else if (this.tabRadioVal === 'violateList') {
              this.tabRadioVal = '02'
            }
          } catch (e) {
            this.tabRadioVal = '00';
          }
        } else {
          this.tabRadioVal = '00';
        }
      }
    },
  },
  methods: {
    getChat (name) {
      this.$emit("isCompanyName", name);
    },
    getValue (val) {
      if (val === null || val === 'null' || val === undefined || val === 'undefined') {
        val = ''
      }
      return val
    },
    // 点击单条数据选择知识库-------end
    // 切换默认展示法律法规tab
    clickTop () {
      this.$refs.AnswerListBox.handleClick(this.tabRadioVal);
    }
  }
};
</script>
<style lang="scss">
.wenwenAnswerList {
  position: relative;
  height: calc(100vh - 61px);
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 100%;

  .calcCla, .wenwenCla {
    position: absolute;
    background-color: #ffffff;
    width: 100%;
    height: 100vh;
    z-index: 1;
  }
  .calcCla {
    background-color: #ffffff;
    transition: background-color 1s, filter 1s; /* 添加背景色和模糊效果的过渡效果 */
  }
  .calcCla.hide {
    background-color: transparent;
  }
  .answerTitleCla{
    height: 67px;
    opacity: 0.8;
    border-bottom: 1px solid #EAEFF4;
    display: flex;
    align-items: center;
    //background: linear-gradient(180deg, #FFF0F0 5.7%, #FFFFFF 100%);
    background-image: url("../../../assets/images/wenwen/image-bg-1.png");
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: right top;

    .aiLogoCla {
      width: 34px;
      height: 34px;
      background-image: url("../../../assets/images/wenwen/aiLogo.png");
      background-repeat: no-repeat;
      background-size: 100%;
      margin-left: 24px;
    }

    .aiTextCla {
      color: #111111;
      font-size: 18px;
      line-height: 25.2px;
      margin-left: 12px;
    }
  }
  .introduce-class {
    position: absolute;
    right: 16px;
    top: 20px;
    z-index: 1;

    .introduce-title-class {
      background: #F5F7FA;
      border-radius: 3px;
      font-size: 14px !important;
      width: 122px;
      height: 30px;
      text-align: center;
      line-height: 28px;
      -webkit-touch-callout: none; /*系统默认菜单被禁用*/
      -webkit-user-select: none; /*webkit浏览器*/
      -khtml-user-select: none; /*早期浏览器*/
      -moz-user-select: none; /*火狐*/
      -ms-user-select: none; /*IE10*/
      user-select: none;
    }

    .introduce-title-class-up {
      color: #666666 !important;
    }

    .introduce-title-class-down {
      color: #14BCF5 !important;
    }

    .introduce-title-class:hover {
      cursor: pointer;
      color: #14BCF5 !important;
    }

    .introduce-type-class {
      z-index: 99;
      width: 127px;
      border-radius: 3px !important;
      position: absolute;
      background-color: #fff;
      font-size: 14px;
      color: #666;
      border: 1px solid #EBEBEB;
    }

    .manual-class {
      padding: 8px 20px;
    }

    .manual-class:hover {
      cursor: pointer;
      background: #F5F7FB;
    }

    .demonstration-class {
      padding: 8px 20px;
    }

    .demonstration-class:hover {
      cursor: pointer;
      background: #F5F7FB;
    }

    .iconfont:hover {
      color: #666
    }

    .iconfont222 {
      float: left;
      margin-top: 1px;
      margin-right: 2px;
    }
  }

  .right-box-content {
    //height: calc(100vh - 70px - 60px - 62px);
    width: 100%;
    overflow: hidden;
  }

  .fixed-header {
    position: fixed;
    top: 62px;
    height: 70px;
    width: 100%;
    z-index: 999; /* 可以根据实际情况调整 z-index 值 */
    background-color: #FFFFFF;
  }

  .fixed—radio {
    margin-top: 16px;
  }

  .el-radio-button.is-gap .el-radio-button__inner {
    padding-left: 6px;
    padding-right: 6px;
    border: 1px solid rgba(51, 51, 51, 0.2);
    width: 100px;
    border-radius: 3px;
    height: 30px;
  }

  .zhengqiuLabel{
    margin-left: -11px;
    .el-radio-button__inner{
      width: 80px !important;
    }
  }

  .content-style {
    color: #777777;
    white-space: pre-line;
    font-size: 14px;
    line-height: 24px;
    blockquote{
      margin: 0;
    }
    p {
      margin: 0;
      padding: 0;
    }
  }

  .content-style_v {
    color: #222;
    white-space: pre-line;
    font-size: 13px;
    line-height: 24px;
  }

  .question_css {
    position: absolute;
    width: 15px;
    height: 16px;
  }

  .answer_css {
    width: 15px;
    position: absolute;
    height: 16px;
  }

  .answer_content p a {
    color: #145ccd !important;
  }

  .more-style {
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    color: #1990FE;
    margin: 0 0px 20px 22px;
  }

  .more-style:hover {
    color: #14BCF5;
  }

  .laws-box {
    margin-bottom: 6px;
    margin-left: 10px;
    padding: 0px 10px 10px 10px;
    border-radius: 10px;

    img {
      width: 100%;
    }

    .title-style {
      color: #222;
      font-size: 16px;
      font-weight: 500;
      display: inline-block;
      overflow: hidden;
      cursor: pointer;
    }

    .title-style:hover {
      color: #14BCF5;
    }

    .imgLawzhengqiu {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: pointer;
      margin-left: 3px;
    }

    .imgLawzhengqiugao {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: default;
      margin-left: 3px;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;
    }
  }

  .question-box {
    margin-bottom: 14px;
    padding: 0 10px 10px 10px;
    border-radius: 10px;
    margin-left: 10px;

    img {
      height: 90px;
      cursor: pointer;
    }

    .title:hover{
      color: #14BCF5;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;
    }
  }

  .quest-list-title {
    margin-left: 22px;
  }

  .quest-list-title .descriptionPosition:not(:last-child) {
    border-right: 1px solid;
  }

  .descriptionPosition {
    margin-left: 9px;
    font-size: 14px;
    line-height: 20px;
    color: #999999;
    padding-right: 10px;
  }

  .content_violate {
    margin-left: 3px;
    font-size: 13px;
    line-height: 20px;
    padding-right: 10px;
    display: inline-block;
    margin-left: 3px;
    color: #999;
  }

  .content_violate_title {
    margin-left: 3px;
    font-size: 13px;
    line-height: 20px;
    padding-right: 10px;
    display: inline-block;
    margin-left: 3px;
    color: #222;
  }

  .content_report {
    margin-left: 3px;
    font-size: 13px;
    line-height: 20px;
    padding-right: 10px;
    display: inline-block;
    margin-left: 3px;
    color: #222222;
  }

  .el-radio-button.is-gap .el-radio-button__inner {
    padding-left: 6px;
    padding-right: 6px;
    border: 1px solid #3333;
    width: 100px;
    border-radius: 3px;
  }

  .qatitle {
    margin-top: 12px;
    font-size: 18px;
    color: #333333;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
  }

  .qdtitle_a {
    margin-left: 5px;
    font-size: 14px;
    color: #999999;
    font-weight: 400;
  }

  .qatitle_border {
    border-bottom: 1px solid #EEE;
    margin-top: 12px;
  }

  .zhengqiu_title {
    margin-top: 12px;
    font-size: 16px;
    color: #333333;
    font-style: normal;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .zhengqiu_title_span {
    margin-left: 5px;
    font-size: 12px;
    color: #666666;
    font-weight: 400;
  }

  .yange_css {
    border: 1px solid #14BCF5;
    background-color: #14BCF5;
    border-radius: 4px;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 2px;
  }

  table {
    border-collapse: collapse;
    width: 100%;
  }

  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  th {
    background-color: #f2f2f2;
  }

  table {
    width: 100%;
    border: 0;
  }

  td {
    border: 0;
  }

  th, td {
    width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
  }

  .tableCss {
    width: 100%;
    overflow: auto;
    margin-top: 10px;
    font-size: 12px;
  }

  .nodata_css {
    justify-content: center;
    align-items: center;
    width: 100%;
    display: flex;
  }

  .el-no-data img {
    width: 50%;
  }

  .answerListBox {
    margin-bottom: 45px;
    padding: 0 16px;
  }

  .contentBox {
    overflow: scroll;
    position: relative;
    height: calc(100vh - 133px - 44px);
  }
  .el-radio-button.is-gap .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    z-index: 1;
  }

  .laws-tag {
    display: inline-block;
  }

  .slide-enter-active, .slide-leave-active {
    transition: transform 1s;
  }

  .slide-enter, .slide-leave-to {
    transform: translateX(100%);
  }
}
</style>
