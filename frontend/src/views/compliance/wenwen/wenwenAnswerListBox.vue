<template>
  <div class="answerListBoxCla">
    <div class="answerTitleCla" :style="answerLoading?'visibility: hidden':''">
      <div class="aiLogoCla" @click="a()"></div>
      <div class="aiTextCla">{{ "合规问答" }}</div>
    </div>
    <div class="answerListBox">
      <div class="fixed-header">
        <el-radio-group v-model="tabRadioVals" class="fixed—radio" size="medium" @change="handleClick">
          <template v-if="!!answerListObject['dataOrder']">
            <template v-for="answerList in answerListObject['dataOrder'].split(',')">
              <template v-for="item in radioLabelMap[answerList]">
                <el-radio-button v-if="!!answerListObject[item.list] && answerListObject[item.list].length >0" gap :label="item.label">
                  {{ item.name }}&nbsp;{{ answerListObject[item.list].length }}
                </el-radio-button>
              </template>
            </template>
          </template>
        </el-radio-group>
      </div>
      <!-- @scroll="handleScroll" -->
      <div id="contentBox" class="contentBox">
        <template v-if="!!answerListObject['dataOrder']">
          <template v-for="answerList in answerListObject['dataOrder'].split(',')">
            <template v-for="item in radioLabelMap[answerList]">
              <!-- @handleScroll="handleScroll" -->
              <component
                v-if="!!answerListObject[item.list]"
                :is="item.components"
                :dataList="answerListObject[item.list]"
              ></component>
            </template>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from "vuex";

export default {
  props: {
    tabRadioVal: String,
    answerListObject: Object,
    radioLabelMap: Object,
    answerLoading: Boolean
  },
  watch: {
    'answerListObject.dataOrder': {
      handler  (newvel, oldvel) {
        if (this.answerListObject.dataOrder && this.answerListObject.dataOrder.split(',').length > 1) {
          this.SHOW_MORE_THEN(true)
        } else {
          this.SHOW_MORE_THEN(false)
        }
      },
      deep: true,
      immediate: true
    },
    tabRadioVal: {
      handler  (newvel, oldvel) {
        this.handleClick(newvel)
      },
      deep: true,
      immediate: true
    }
  },
  data () {
    return {
      tabRadioVals: this.tabRadioVal,
      uploadData: {},
      fileList: [],
      myHeader: { Authorization: this.$store.state.app.token, 'X-Tenant-Info': this.$store.state.app.info },
      timer: '',
      atBottom: false
    }
  },

  methods: {
    ...mapMutations(['SHOW_MORE_THEN']),
    handleClick (tab) {
      this.tabRadioVals = tab
      const a = document.getElementsByClassName('contentBox')[0]
      const element = document.getElementById('infoList' + tab)
      if (element !== null) {
        const offset = element.offsetTop
        a.scrollTo({
          top: offset,
          behavior: 'smooth' // 可选，平滑滚动
        })
      }
    },
    handleScroll (event) {
      const scrollTop = event.target.scrollTop
      const infoListParent = document.getElementById('contentBox')
      // 获取所有小div
      const boxes = document.querySelectorAll('.right-box-content');
      if (!boxes.length) return;
      // 计算最后一个小div的位置
      const lastBoxOffsetTop = boxes[boxes.length - 1].offsetTop;
      const lastBoxHeight = boxes[boxes.length - 1].offsetHeight;
      const parentHeight = infoListParent.offsetHeight;
      // 判断是否到底部
      const isBottom = infoListParent.scrollTop >= lastBoxOffsetTop + lastBoxHeight - parentHeight - 2;
      if (isBottom) {
        if (!this.atBottom) {
          this.atBottom = true;
        }
        // 到底部时不切换tabRadioVals，直接return
        return;
      } else {
        if (this.atBottom) {
          // 离开底部，恢复切换逻辑
          this.atBottom = false;
        }
      }
      // 以下为原有逻辑
      const infoList02 = document.getElementById('infoList02')
      if (infoList02) {
        const offsetTop = infoList02.offsetTop - 50
        const height = infoList02.offsetHeight
        // 如果滚动位置超过了最后一个小div的位置，则将滚动位置设置为最后一个小div的位置
        if (scrollTop >= offsetTop && scrollTop <= offsetTop + height || infoListParent.scrollTop > lastBoxOffsetTop - infoListParent.offsetHeight + boxes[boxes.length - 1].offsetHeight) {
          this.tabRadioVals = '02'
        }
      }
      const infoList00 = document.getElementById('infoList00')
      if (infoList00) {
        const offsetTop00 = infoList00.offsetTop - 50
        const height00 = infoList00.offsetHeight
        if (scrollTop >= offsetTop00 && scrollTop <= offsetTop00 + height00 || infoListParent.scrollTop > lastBoxOffsetTop - infoListParent.offsetHeight + boxes[boxes.length - 1].offsetHeight) {
          this.tabRadioVals = '00'
        }
      }
      const infoList01 = document.getElementById('infoList01')
      if (infoList01) {
        const offsetTop01 = infoList01.offsetTop - 50
        const height01 = infoList01.offsetHeight
        if (scrollTop >= offsetTop01 && scrollTop <= offsetTop01 + height01 || infoListParent.scrollTop > lastBoxOffsetTop - infoListParent.offsetHeight + boxes[boxes.length - 1].offsetHeight) {
          this.tabRadioVals = '01'
        }
      }
      const infoList06 = document.getElementById('infoList06')
      if (infoList06) {
        const offsetTop06 = infoList06.offsetTop - 50
        const height06 = infoList06.offsetHeight
        if (scrollTop >= offsetTop06 && scrollTop <= offsetTop06 + height06 || infoListParent.scrollTop > lastBoxOffsetTop - infoListParent.offsetHeight + boxes[boxes.length - 1].offsetHeight) {
          this.tabRadioVals = '06'
        }
      }
      // console.log("滚动位置",this.tabRadioVals);
    }
  },
  mounted () {
    this.$nextTick(() => {
      let _this = this;
      const container = document.querySelector(".contentBox");
      if (container) {
        container.addEventListener("scroll", _this.handleScroll);
      }
    })
  },
  beforeDestroy () {
    let _this = this;
    const container = document.querySelector(".contentBox");
    if (container) {
      container.removeEventListener("scroll", _this.handleScroll);
    }
  }
}
</script>
<style scoped lang="scss">
.answerListBoxCla {
  height: calc(100vh - 61px - 60px - 24px);
  .fixed-header {
    position: sticky;
    top: 0px;
    background-color: white;
    height: 70px;
    width: calc(100% - 10px);
    z-index: 999; /* 可以根据实际情况调整 z-index 值 */
  }

  .fixed—radio {
    margin-top: 18px;
  }
  .el-radio-button:first-child:last-child .el-radio-button__inner {
    border-radius: 3px;
    &.focus{
      border-color: #CF1A1C;
    }
  }

  .el-radio-button.is-gap .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    background-color: #fff;
    border: 1px solid #CF1A1C;
    color: #CF1A1C;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
  :deep .el-radio-button.is-gap .el-radio-button__inner :hover{
    color: #CF1A1C;
  }
  :deep(.el-radio-button__inner) {
    padding-left: 6px;
    padding-right: 6px;
    border: 1px solid rgba(51, 51, 51, 0.2);
    width: 100px;
    border-radius: 3px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .zhengqiuLabel {
    margin-left: -11px;

    .el-radio-button__inner {
      width: 80px !important;
    }
  }

  .answerTitleCla{
    height: 67px;
    border-bottom: 1px solid #EAEFF4;
    display: flex;
    align-items: center;
    //background: linear-gradient(180deg, #FFF0F0 5.7%, #FFFFFF 100%);
    //background: linear-gradient(180deg, #D6EBFF 5.73%, rgba(242, 250, 255, 0) 100%), url("../../../assets/images/wenwen/titleBackground.png");
    background-image: url("../../../assets/images/wenwen/image-bg-1.png");
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: right top;

    .aiLogoCla {
      width: 34px;
      height: 34px;
      background-image: url("../../../assets/images/wenwen/aiLogo.png");
      background-repeat: no-repeat;
      background-size: 100%;
      margin-left: 24px;
    }

    .aiTextCla {
      color: #111111;
      font-size: 18px;
      line-height: 25.2px;
      margin-left: 12px;
    }
    .answerSwitch{
      margin-left: auto;
      height: 62px;
      display: flex;
      align-items: center;
      width: 178px;
      border-radius: 50px 0px 0px 50px;
      padding: 14px 16px 14px 30px;
      gap: 12px;
      background: linear-gradient(90deg, #FFFFFF 14.34%, rgba(255, 255, 255, 0.3) 100%);
      box-shadow: -2px 0px 2px 0px rgba(121, 128, 148, 0.3);
      cursor: pointer;
      font-size: 16px;
      color: #111111;
    }
  }

  .answerListBox {
    padding: 0 0 0 24px;
    height: calc(100vh - 60px - 60px)!important;
  }

  .contentBox {
    overflow: scroll;
    position: relative;
    height: calc(100vh - 46px - 40px - 62px - 60px);
    background-image: url("../../../assets/images/wenwen/documentBackground.png");
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: right bottom;
  }

  .el-radio-button.is-gap .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    z-index: 1;
  }
}
</style>
