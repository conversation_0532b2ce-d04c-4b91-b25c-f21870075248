<template>
  <div class="disclaimers-new">
    <el-dialog :visible.sync="showDialog"
               custom-class="aiDisclaimers" width="730px" v-if="showDialog"
               :close-on-click-modal="false"  :close-on-press-escape="false" :append-to-body="true"
    >
      <div class="temp-div">
        <img style="position: absolute;right: 0;bottom: 0;pointer-events: none" :src="dbUrl" width="276px" alt="">
        <div class="text-center" style="font-size:18px;font-weight:600;margin-bottom: 24px;color: #333333;">
          合规问答免责声明
        </div>
        <div style="max-height:440px;overflow-y: auto;">
          <p class="disclaimers-content disclaimers-content__indent">
            &nbsp; &nbsp; &nbsp; &nbsp;中信建投证券股份有限公司（以下简称“中信建投”）提供的智能合规问答助手是利用人工智能技术结合法律法规、违规案例数据行程的智能问答功能。
          </p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;使用本功能前，使用方应当认真阅读、充分理解《合规问答免责声明》（以下简称“本声明”）中各项条款，包括免除或者限制中信建投责任的免责条款及对使用方的权利限制条款，审慎阅读并选择接受或不接受本声明，使用方任何的使用行为均被视为已完整阅读本声明并对本声明全部内容的认可与接受。</p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;本功能根据现有的人工智能技术和条件所能达到的现状而生成。因现有技术限制，本平台及其服务内容可能存在瑕疵或错漏，并不能保证在任何情况下都能正常执行或达到使用方所期望的结果。
          </p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;使用方在使用本平台及服务时，应当具备完全的民事权利能力及民事行为能力并能独立承担责任，同时需对人工智能生成的内容具有独立判断及甄别的的能力；使用方在使用本平台及服务时应当遵守《中华人民共和国国家安全法》《中华人民共和国网络安全法》《中华人民共和国数据安全法》《中华人民共和国个人信息保护法》《生成式人工智能服务管理暂行办法》等任何中华人民共和国现行有效的法律法规及政策文件的要求；使用方的任何输入及输出行为及结果责任均由使用方自负，与中信建投无关；使用方的任何输入及输出行为及结果不得侵犯任何主体的肖像权、名誉权、荣誉权、姓名权、隐私权、个人信息等人身权益以及财产权益，不得处理任何涉及暴力、血腥、恐怖、淫秽、色情、赌博或教唆犯罪、传授犯罪方法，不得处理违反国家法律法规及政策文件、涉及国家秘密、商业秘密、保密信息的内容；不得侵犯任何主体的知识产权；不得从事不正当竞争的行为以及其他违背社会公序良俗的内容；不得将使用方的实际使用行为、结果与中信建投及本功能进行误导性或混淆性的关联；否则因此导致的任何损失及/或法律责任，均由使用方自负。
          </p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;本平台的所有权及知识产权（包括但不限于著作权、商标权、专利权以及其申请权利、技术和专有技术、商业秘密、其他无形权利等）始终归中信建投所有，不因使用方的使用及其他行为而产生变动。中信建投提供的本功能仅供使用方参考和学习之用，中信建投对本平台展示、输出（包括在本平台中调用第三方AI模型生成和完善的任何内容）的任何内容的真实性、准确性、完整性、时效性及适用性等不作任何保证，因本平台及其输入、输出的任何内容始终不代表中信建投的任何立场及观点，不构成中信建投任何肯定性许可与承诺，不构成任何投资、决策、商业及非商业指导和建议，亦不构成任何正式的法律意见和建议，使用方的任何行为与决策均应基于其自身的独立思考与理性判断。
          </p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;使用方使用本平台的服务时，由于不可抗力、政策原因、计算机病毒、黑客攻击、兼容性、网络信号不稳定、互联网通信的固有缺陷、第三方软件或平台限制、使用方设备故障、操作失误等原因导致的任何使用异常及使用方因此受到的任何损失，中信建投不承担任何责任。
          </p>
          <p class="disclaimers-content">
            &nbsp; &nbsp; &nbsp; &nbsp;使用方知悉并认可，中信建投保留未来基于中华人民共和国法律法规、司法或行政命令以及中信建投内部政策情况变化而随时修订本声明的权利。中信建投修订本声明内容的，将向使用方及时发出通知，届时请使用方及时查看。如使用方在本协议变更之后继续使用本平台的，视为已经认真阅读、充分理解并且接受变更之后的协议。
          </p>
          <p>&nbsp; &nbsp; &nbsp; &nbsp;</p>
          <p class="disclaimers-content" style="text-align:right">
            中信建投证券股份有限公司&nbsp; &nbsp; &nbsp; &nbsp;
          </p>
          <p class="disclaimers-content" style="text-align:right">
            2025年07月&nbsp; &nbsp; &nbsp; &nbsp;
          </p>
        </div>
        <div class="" slot="footer" style="text-align:center;margin-top: 24px;">
          <el-button size="small" type="primary" @click="handleCancel"
                     class="relief-modal-button">我已知晓并同意遵守
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {_setChatLoginRecord} from "@/api/Records";

export default {
  props: {
    showDialog: Boolean,
    firstFlag: Boolean
  },
  name: "disclaimersNew",
  data () {
    return {
      showing: false,
      second: 5,
      timer: null,
      dbUrl: require("../../../assets/images/image-db.png")
    }
  },
  created () {
    this.readSecond()
  },
  methods: {
    handleCancel () {
      _setChatLoginRecord().then()
      this.$emit('close');
    },
    readSecond () {
      if (this.second === 5) {
        this.timer = setInterval(() => {
          this.second--;
          if (this.second === 0) {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    }
  },
  beforeDestroy () {
    clearInterval(this.timer);
  },
  watch: {
  }
}
</script>
<style scoped lang="scss">
  ::v-deep .el-dialog {
    border-radius: 16px !important;
  }
  .el-button.el-button--primary{
    border-radius: 3px !important;
  }
  ::v-deep .el-dialog__header {
    display: none !important;
  }
  ::v-deep  .el-dialog__body {
    padding: 0 !important;
    max-height: 640px !important;
    background: linear-gradient(180deg, #F4FAFF 0%, #FFFFFF 100%);
    border-radius: 16px !important;
  }
  .temp-div{
    background: linear-gradient(180deg, #FFF0F0 0%, #FFFFFF 54.48%);
    padding: 24px;
    line-height: 23px;
  }
  .disclaimers-new{
    .relief-modal{
      border-radius: 20px;
      background: linear-gradient(180deg, #F5F0FF 0%, #FFF 54.48%),url(../../../assets/images/wenwen/relief.svg);
      background-blend-mode: color-burn;
      background-repeat: no-repeat;
      background-position: right bottom;
      .relief-modal-title{
        color: #222;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        margin-bottom: 24px;
      }
      .disclaimers-content{
        color: var(--666666, #666);
        font-size: 14px;
        margin-bottom:10px;
        line-height: 23px;
        text-align: justify;
        &.disclaimers-content__indent{
          text-indent: 30px;
        }
      }
      .el-dialog__body {
        padding: 24px 24px 24px;
        color: #666;
        overflow-y: auto;
      }
      .el-dialog__footer {
        padding: 10px 20px 24px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
      }
      .el-dialog__header {
        padding: 0 0;
      }
      .back_clear_img{
        width: 400px !important;
        position: absolute;
        bottom: 0;
        right: 0;
      }
      .relief-modal-button{
        &.is-disabled{
          background: rgba(241, 242, 244, 1);
          border-color:rgba(241, 242, 244, 1);
          color: rgba(51, 51, 51, 1);
        }
      }
      .relief-modal-mask{
        position:absolute;
        top:0;
        left:0;
        width:100%;
        height:100%;
        z-index:1;
        cursor: not-allowed;
      }
    }
  }
</style>
