<template>
  <div class="wenwenHomeIndex">
    <div class="demo-split-class">
      <el-split v-model="split" ref="split" :max="getLeftMinSize" :min="getRightMinSize">
          <template slot="left">
            <div class="wenwen-outer_frame_left">
              <el-watermark content="中信建投证券股份有限公司版权所有" :styles="{height: '1000px'}">
              <wenwen-session :answerLoading="answerLoading"
                              :currentChat="currentChat" :settingParam="settingParam"
                              @create-chat="createChat" @getWenCreateChat="getWenCreateChat" @editChatSure="editChatSure"
                              ref="questionWenWen" @wenClickData="wenClickData" @answerLoadingCla="answerLoadingCla"
                              @getKeywordNum="getKeywordNum" @wenStartChatLoading="wenStartChatLoading"></wenwen-session>
              </el-watermark>
            </div>
          </template>

        <template slot="right">
          <div v-if="answerLoading" class="wenwen-answerLoadingCla">
            <div class="wenwen-answerTitleCla">
              <div class="wenwen-aiLogoCla"></div>
              <div class="wenwen-aiTextCla">合规问答</div>
            </div>
            <div class="wenwen-backLoadBackCla">
              <div class="centerCla">
                <div class="backLoadImgCla"></div>
                <div style="text-align: center">合规问答正在回答中...</div>
              </div>
            </div>
          </div>
          <div v-else :class="[(answerListObject.clearChartFlag && answerLoading) ? 'frame_right_one':'frame_right_two']">
            <wenwen-answer-tab    @isCompanyName="isCompanyName"
                                  ref="wenPlatformAnswerList"
                                  :answerListObject="answerListObject"
                                  :answerLoading="answerLoading">
            </wenwen-answer-tab>
          </div>
        </template>
      </el-split>
    </div>
  </div>
</template>
<script>
import common from "@/mixins/common";
import clickoutside from 'stock-ui/src/utils/clickoutside';
import wenwenSession from "@/views/compliance/wenwen/wenwenSession.vue";
import wenwenAnswerTab from "@/views/compliance/wenwen/wenwenAnswerTab.vue";
import { _createChat } from "@/api/chat";

export default {
  components: { wenwenSession, wenwenAnswerTab },
  mixins: [common],
  directives: { clickoutside },
  props: {
    settingParam: Array
  },
  data () {
    return {
      split: 0.56,
      currentChat: '', // 当前会话id
      chatList: [], // 聊天会话列表
      typeTags: [
        { name: "法律法规", num: 0, type: 0, tagFlag: false },
        { name: "合规问答", num: 0, type: 1, tagFlag: false },
        { name: "违规案例", num: 0, type: 2, tagFlag: false },
      ],
      tagFlag: false,
      answerListObject: {
        answerInfoList: [],
        clearChartFlag: true, // 清空会话和打开新会话的时候展示
        lawInfoList: [],
        violateInfoList: [],
        zhengQiuLawsList: [],
      },
      answerLoading: false
    }
  },
  computed: {
    // 左侧最大宽度为屏幕宽度的 30%
    getLeftMinSize () {
      const number = document.body.scrollWidth * 0.3;
      return number + 'px'
    },
    // 右侧最大宽度为屏幕宽度的 60%
    getRightMinSize () {
      const number = document.body.scrollWidth * 0.4;
      return number + 'px'
    }
  },
  mounted () {
  },
  methods: {
    getKeywordNum (indexAll, indexNum) {
      this.$emit('getKeywordNum', indexAll, indexNum)
    },
    isCompanyName (name) { // 调子页面发送
      this.$refs.questionWenWen.fastStartChat(name);
    },
    wenStartChatLoading (data) {
      this.answerLoading = data
    },
    sendChatContent (name) {
      this.$refs.questionWenWen.sendChatContent(name);
    },
    // 创建新的对话chat_model = 2 专业模式chat_type聊天类型（暂存个3）
    async createChat (chatName, name, type) {
      this.answerListObject.clearChartFlag = true;
      this.getWenCreateChat(chatName, name, type);
    },
    // 创建聊天（针对刚开始进来没会话的）其余使用异步
    getWenCreateChat (chatName, name, type) {
      this.clearChartFlag = true
      _createChat({ questionChannel: '0', chatName: name }, true).then(res => {
        if (res.data.success) {
          this.currentChat = res.data.result.chatId // 聊天id
          this.$emit('getChatData','1')
          if (type === 1) { // 触发聊天(代表用户第一次进来，没有会话的时候)
            this.$refs.questionWenWen.sendChatContent(name, this.currentChat)
          }
          // })
        }
      })
    },
    editChatSure(name) {
      this.$emit('editChatSure', name)
    },
    wenClickData (data) {
      this.answerListObject = data
      this.answerListObject.clearChartFlag = (data.answerInfoList && data.answerInfoList.length > 0) || (data.lawInfoList && data.lawInfoList.length > 0)
        || (data.zhengQiuLawsList && data.zhengQiuLawsList.length > 0)|| (data.violateInfoList && data.violateInfoList.length > 0);
      if (data.dataOrder == null || data.dataOrder.length === 0){
        this.answerListObject.clearChartFlag = false
      }
    },
    answerLoadingCla (data) {
      this.answerLoading = data
    }
  },
  watch: {}
}
</script>
<style lang="scss">
.wenwenHomeIndex {
  width: 100%;
  height: calc(100vh - 61px)!important;
  overflow: hidden;
  position: relative;

  .el-split__trigger{
    z-index: 1000;
  }

  .el-split .el-split-trigger--vertical {
    height: 100vh;
    width: 3px;
    background: transparent;
    border-color: transparent;

  }
  .el-split .el-split-trigger--vertical:after{
  display: none;

  }

  .el-split .el-split-trigger--vertical:hover {
    width: 2px;
    background-color: #1990FE;
    border-color: #1990FE;
  }

  .el-split-trigger__bar-con.vertical {
    display: none;
  }

  .fade-enter-active, .fade-leave-active {
    transition: all 0.5s ease;
  }

  .fade-enter, .fade-leave-to {
    transform: translateY(100%);
  }

  .content {
    background-color: #eee;
    padding: 10px;
  }

  .chat-dong-right-header__set {
    width: 30px;
    height: 30px;
    border-radius: 22px;
    background: #E8F8FE;
    display: inline-block;
    line-height: 30px;
    cursor: pointer;
    text-align: center;
    margin-top: 3px;
    margin-left: 2px;

    .iconfont {
      color: #777777;
      font-size: 18px;
    }

    &:hover {
      background: #DAEAF2;

      .iconfont {
        color: #14BCF5;
      }
    }
  }

  .el-tag {
    background-color: transparent;
    font-size: 14px !important;
    line-height: 60px;
    border: none;
    color: #666666;
    height: 60px;
  }

  .top {
    padding: 0px 24px 0 24px;
    height: 40px;
    border-bottom: 1px solid #EEECED;
    border-radius: 10px 10px 0px 0px;
    color: #333333;
    line-height: 40px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    position: relative;
  }

  .tipBox {
    text-align: left;
    height: calc(100vh - 265px);
    position: absolute;
    width: calc(100% - 160px);
    bottom: 0px;
    border-radius: 10px 10px 0px 0px;
    border-top: 1px solid #E5E3E5;
    border-right: 1px solid #E5E3E5;
    border-left: 1px solid #E5E3E5;
    background: rgba(255, 255, 255, 0.80);
    backdrop-filter: blur(30px);
    margin-left: 80px;
  }

  .tipBox_left {
    float: left;
    width: 50%;
    border-right: 1px solid rgba(148, 141, 141, 0.2);
    color: rgba(68, 68, 68, 0.80);
    font-size: 12px;
    height: calc(100vh - 300px);
    overflow: scroll;
  }

  .tipBox_right {
    float: left;
    width: 50%;
    padding-left: 10px;
    height: calc(100vh - 300px);
    overflow: auto;
  }

  .wenwen-outer_frame_left {
    float: left;
    font-size: 14px;
    height: calc(100vh - 61px)!important;
    width: 100%;
    background-color: #fff;
    background-image: url('../../../assets/images/wenwen/background.svg');
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    background-position: center;
  }

  .wenwen-answerLoadingCla {
    font-size: 14px;
    position: absolute;
    right: 0;
    width: 100%;
    z-index: 2;
    height: calc(100% - 61px);
    background: rgba(252, 252, 252, 0.8);
    .wenwen-backLoadBackCla{
      height: calc(100vh - 62px - 60px);
      background-image: url("../../../assets/images/wenwen/documentBackground.png");
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: right bottom;
      background-color: white;
      .centerCla{
        margin: auto;
        .backLoadImgCla{
          background-image: url('../../../assets/images/wenwen/answerLoading.png'); /* 设置背景图片的 URL */
          background-repeat: no-repeat; /* 禁止背景图片重复 */
          background-position: center; /* 将背景图片定位到容器的中心 */
          height: 162px;
          width: 168px;
        }
      }
      display: flex;
      align-items: center;
    }

    .wenwen-answerTitleCla{
      height: 62px;
      border-bottom: 1px solid #EAEFF4;
      display: flex;
      align-items: center;
      //background: linear-gradient(180deg, #FFF0F0 5.7%, #FFFFFF 100%);
      //background: linear-gradient(180deg, #D6EBFF 5.73%, rgba(242, 250, 255, 0) 100%), url("../../../assets/images/wenwen/titleBackground.png");
      background-image: url("../../../assets/images/wenwen/image-bg-1.png");
      background-repeat: no-repeat;
      background-size: 100%;
      background-position: right top;
      background-color: white!important;
      .wenwen-aiLogoCla{
        width: 34px;
        height: 34px;
        background-image: url("../../../assets/images/wenwen/aiLogo.png");
        background-repeat: no-repeat;
        background-size: 100%;
        margin-left: 24px;
      }
      .wenwen-aiTextCla{
        color: #111111;
        font-size: 18px;
        line-height: 25.2px;
        margin-left: 12px;
      }
    }
  }

  .frame_right_one {
    font-size: 14px;
    float: left;
    height: 100%!important;
    width: 100%;
    background-color: #FFFFFFCC!important;
  }

  .frame_right_two {
    font-size: 14px;
    float: left;
    height: calc(100% - 61px)!important;
    width: 100%;
    background-color: #FFFFFFCC;
  }

  .wenwen-bottom_css_right_one {
    position: relative;
    display: flex;
    float: left;
    height: 44px;
    font-size: 14px;
    width: 100%;
    line-height: 20px;
    padding-left: 16px;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    border-top: 1px solid #EEE;
    z-index: 999;
    background-color: white;
  }

  .bottom_css_right_two {
    position: relative;
    display: flex;
    float: left;
    height: 44px;
    font-size: 14px;
    width: 800px;
    line-height: 20px;
    padding-left: 16px;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    background-color: #f7fafc;
  }

  .bottom_css_left_one {
    text-align: center;
    position: relative;
    bottom: 0px;
    float: left;
    color: #666;
    font-size: 14px;
    width: 100%;
    height: 40px;
    line-height: 36px;
  }

  .bottom_css_left_two {
    text-align: center;
    position: absolute;
    bottom: 0px;
    float: left;
    color: #666;
    font-size: 14px;
    width: 100%;
    height: 40px;
    line-height: 36px;
  }

  .lshh {
    margin: 0 auto;
    cursor: pointer;
    color: #333333;
    width: 112px;
    height: 34px;
  }

  .lshh:hover {
    background-color: rgba(51, 51, 51, 0.05);
    border-radius: 8px;
  }

  .el-input--medium .el-input__inner {
    height: 34px;
    line-height: 34px;
    border-radius: 90px;
    background: rgba(255, 255, 255, 0.60);
  }

  .el-input--medium .el-input__inner:hover {
    border: 1px solid var(--brand-414-bcf-5, #14BCF5);
    background: #FFFFFFE5;
  }

  .colCss {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }

  .chat-dong-chatList_item {
    color: #111111;
    width: 100%;
    font-size: 14px;
    margin-top: 6px;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    border-radius: 8px;

    .chat-dong-chatList_item-content {
      padding: 0 10px;
      display: flex;
      align-items: center;
    }

    &.active {
      box-shadow: 0px 1px 2px rgba(164, 172, 174, 0.5);
      background: #1990FE;
      color: #FFF;

      .chat-dong-chatList_icon {
        display: inline-block;
      }
    }

    &.active:hover {
      box-shadow: 0px 1px 2px rgba(164, 172, 174, 0.5);
      background: #1990FE;
      color: #FFF;

      .iconfont {
        color: #fff;
        font-size: 14px;
      }

      .chat-dong-chatList_icon {
        display: inline-block;
      }
    }

    &:hover {
      color: #222;
      background: #E8F8FE;

      .iconfont {
        color: #444;
        font-size: 14px;
      }

      .chat-dong-chatList_icon {
        display: inline-block;
      }
    }

    &.new {
      border: 1px solid #B2BDC0;
      border-radius: 6px;
      height: 38px;
      line-height: 38px;
      padding: 0 14px;

      &:hover {
        background: rgba(252, 254, 255, 0.95);
        color: #14bcf5;

        .iconfont {
          color: #444 !important;
        }
      }
    }

    .iconfont {
      color: #fff;
      font-size: 14px;
    }

    .chat-dong-chatList_icon {
      width: 24px;
      height: 24px;
      display: none;
      line-height: 24px;
      text-align: center;

      &:hover {
        background: rgba(199, 209, 216, 0.2);
        border-radius: 2px;
      }
    }

    .chat-dong-chatList_text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }

    .chat-dong-chatList__input {
      width: 200px;
      text-overflow: ellipsis;

      .el-input__inner {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .chat-dong-chatList-new {
    padding: 0 16px;
  }

  .chat-dong-chatList_title {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.80);
    height: 35px;
    line-height: 35px;
    text-indent: 10px;
    margin-top: 6px;
  }

  .chat-dong-chatList-content {
    overflow-y: auto;
    padding: 0 32px;
    overflow-x: hidden;
    padding-bottom: 5px;
  }

  .chat-dong-chatList__img {
    width: 18px;
    margin-right: 12px;
    vertical-align: sub;

    &.new {
      color: #333 !important;
      margin-right: 8px;
    }
  }

  .xuanze {
    font-family: PingFang SC;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    color: #666666;
    top: 12px;
    position: absolute;
  }

  .tag_title {
    width: 100%;
    padding-left: 35px;
    top: -8px;
    position: absolute;
    color: #666666;
    font-size: 14px;
  }

  .genju_button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    line-height: 30px;
    position: relative;
    border-radius: 0px;
    top: 0px;
    right: 0px;
  }

  .clear_tag {
    font-family: PingFang SC;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    color: #14BCF5;
    cursor: pointer
  }

  @media screen and (max-width: 1600px) { // 分辨率小于1600
    .tipBox {
      text-align: left;
      height: calc(100vh - 260px);
      position: absolute;
      width: calc(100% - 40px);
      bottom: 0px;
      border-radius: 10px 10px 0px 0px;
      border-top: 1px solid #E5E3E5;
      border-right: 1px solid #E5E3E5;
      border-left: 1px solid #E5E3E5;
      background: rgba(255, 255, 255, 0.80);
      backdrop-filter: blur(15px);
      margin-left: 20px;
    }

    .wenwen-answerLoadingCla {
      font-size: 14px;
      height: calc(100vh);
      position: absolute;
      right: 0;
      width: 100%;
      z-index: 9999;
      background: rgba(252, 252, 252, 0.8);
      .wenwen-backLoadBackCla{
        height: calc(100vh - 62px - 60px);
        .centerCla{
          margin: auto;
          .backLoadImgCla{
            background-image: url('../../../assets/images/wenwen/answerLoading.png'); /* 设置背景图片的 URL */
            background-repeat: no-repeat; /* 禁止背景图片重复 */
            background-position: center; /* 将背景图片定位到容器的中心 */
            height: 162px;
            width: 168px;
          }
        }
        display: flex;
        align-items: center;
      }

      .wenwen-answerTitleCla{
        height: 62px;
        border-bottom: 1px solid #EAEFF4;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #FFF0F0 0%, #FFFFFF 54.48%);
        //background: linear-gradient(180deg, #EAEFF4FF 5.73%, rgba(242, 250, 255, 0) 100%);
        background-repeat: no-repeat;
        background-position: right top;
        background-color: white!important;
        .wenwen-aiLogoCla{
          width: 34px;
          height: 34px;
          background-image: url("../../../assets/images/wenwen/aiLogo.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-left: 24px;
        }
        .wenwen-aiTextCla{
          color: #111111;
          font-size: 18px;
          line-height: 25.2px;
          margin-left: 12px;
        }
      }
    }

    .frame_right_one {
      font-size: 14px;
      float: left;
      height: 100%!important;
      width: 100%;
      background-color: #FFFFFFCC!important;
    }
    .frame_right_two {
      font-size: 14px;
      float: left;
      height: calc(100vh - 61px)!important;
      width: 100%;
      background-color: #FFFFFFCC;
    }
    .bottom_css_left_one {
      text-align: center;
      position: relative;
      bottom: 0px;
      float: left;
      color: #666;
      font-size: 14px;
      width: 100%;
      height: 40px;
      line-height: 36px;
    }
    .bottom_css_left_two {
      text-align: center;
      position: absolute;
      bottom: 0px;
      float: left;
      color: #666;
      font-size: 14px;
      width: 100%;
      height: 40px;
      line-height: 36px;
    }
    .wenwen-bottom_css_right_one {
      position: relative;
      display: flex;
      float: left;
      height: 44px;
      font-size: 14px;
      width: 100%;
      line-height: 20px;
      padding-left: 16px;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      z-index: 999;
      background-color: white;
    }
    .bottom_css_right_two {
      position: relative;
      display: flex;
      float: left;
      height: 44px;
      font-size: 14px;
      width: 50%;
      line-height: 20px;
      padding-left: 16px;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      background-color: #f7fafc;
    }
  }

  .recordEmpty {
    text-align: center;
    font-size: 14px;
    color: #999999;
  }

  .historyBox {
    padding: 10px 10px 10px 10px;
    width: 100%;
  }
}
</style>
