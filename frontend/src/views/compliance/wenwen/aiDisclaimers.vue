<template>
  <div class="dialog">
    <div class="dialog-top" :title="text">{{ text }}</div>
    <div class="dialog-bottom" :title="text2">{{ text2 }}</div>
  </div>
</template>
<script>
import DisclaimersNew from "./disclaimersNew";
export default {
  name: "aiDisclaimers",
  components: { DisclaimersNew },
  props: {
  },
  data () {
    return {
      showDialog: false,
      text:"本内容为AI生成，不代表开发者立场，请勿删除或修改本标记。本服务的输出合理性有限，请确认后进行使用。",
      text2:"注: 本服务涉及输入信息向公司外传输的功能，请勿输入国家秘密、商业秘密、工作秘密、上市公司内幕信息或非公开的个人隐私信息等内容。"
    }
  },
  methods: {
    open () {
      this.showDialog = true
    },
    close () {
      this.showDialog = false
    }
  }
}
</script>
<style scoped lang="scss">
.dialog {
  height: 80px;
  margin-top: 12px;
  padding:0 15px;
  background-color: #E8E8E8BD;
  border-radius: 8px;
  font-size:14px;
  text-align: center;
  //cursor: pointer;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  .Pop-ups {
    font-size: 12px;
    color: #95A8C0;
  }

  .Pop-ups:hover {
    color: rgba(25, 144, 254, 1);
    cursor: pointer;
  }
  .dialog-top{
    color:#666666;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin-top: 15px;
  }
  .dialog-bottom{
    color: #FE5461;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    margin-top:8px;
  }
}
</style>
