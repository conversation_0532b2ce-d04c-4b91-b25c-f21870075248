<template>
  <div class="questionSetting">
    <div class="top">
      <div class="selectbox">
        <div class="selectGpt">
          <span style="font-weight: 600;width: 64px;margin-right: 16px;height: 38px;font-size:14px;line-height: 22px;display: inline-block;padding: 8px 0">对话模型</span>
          <el-select ref="gptSelect" v-model="wenChatGpt" @change="chatGptClick" @visible-change="onSelectVisibleChange('wenChatGpt', $event)" class="select-setting">
            <el-option
              v-for="item in chatGptOptions"
              :key="item.modelId"
              :label="item.modelName"
              :value="item.modelId">
            </el-option>
          </el-select>
        </div>
        <div class="selectBelongsPlate">
          <span  style="font-weight: 600;width: 64px;margin-right: 16px;height: 38px;line-height: 22px;font-size:14px;display: inline-block;padding: 8px 0">板块设置</span>
          <el-select ref="plateSelect" v-model="wenBelongsPlate" placeholder="请选择" @change="chatBelongsPlateClick" @visible-change="onSelectVisibleChange('wenBelongsPlate', $event)" class="select-setting">
            <el-option
              v-for="item in belongsPlateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="divergent-thinking">
        <div class="check">
          <el-checkbox @change="onCheckboxChange" v-model="checkboxTwo" style="color: #111">
            连续对话模式，发消息会联想上述对话
          </el-checkbox>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {_getModelList} from "@/api/chat";
import common from "@/mixins/common";
import { mapGetters, mapMutations } from "vuex";

export default {
  mixins: [common],
  data () {
    return {
      chatGptOptions: [
      ],
      belongsPlateOptions: [
        { value: '', label: '所有板块' },
        { value: '04', label: '沪主板' },
        { value: '07', label: '科创板' },
        { value: '00', label: '深主板' },
        { value: '02', label: '创业板' },
        { value: '09', label: '北交所' }
      ],
    }
  },
  mounted () {
    this.getSetting();
    this.initData()
  },
  computed: {
    ...mapGetters(['wenChatGpt','checkboxTwo','wenBelongsPlate', 'homeSending'])
  },
  methods: {
    ...mapMutations(['SET_WEN_CHAT_GPT', 'SET_WEN_BELONGS_PLATE', 'SET_CHECKBOX_TWO', 'SET_WEN_CHAT_GPT_NAME']),
    chatGptClick(event) {
      this.SET_WEN_CHAT_GPT(event)
      const selectedOption = this.chatGptOptions.find(item => item.modelId === event);
      window.localStorage.setItem("wenChatGpt", event)
      window.localStorage.setItem("wenChatGptName", selectedOption.modelName)
      this.SET_WEN_CHAT_GPT_NAME(selectedOption.modelName)
    },
    chatBelongsPlateClick(event) {
      this.SET_WEN_BELONGS_PLATE(event)
      window.localStorage.setItem("wenBelongsPlate", event)
    },
    changeCheckBoxTwo(event) {
      this.SET_CHECKBOX_TWO(event)
      window.localStorage.setItem("checkboxTwo", event)

    },
    onSelectVisibleChange(type, visible) {
      if (visible && this.homeSending) {
        this.$message.info('正在回答中，请勿修改设置。');
        if (type === 'wenChatGpt') this.$refs.gptSelect && this.$refs.gptSelect.blur && this.$refs.gptSelect.blur();
        if (type === 'wenBelongsPlate') this.$refs.plateSelect && this.$refs.plateSelect.blur && this.$refs.plateSelect.blur();
      }
    },
    onCheckboxChange(val) {
      if (this.homeSending) {
        this.$message.info('正在回答中，请勿修改设置。');
        this.SET_CHECKBOX_TWO(!val);
        return;
      }
      this.changeCheckBoxTwo(val);
    },
    // 获取聊天设置
    getSetting() {
      _getModelList().then(res => {
        if (res.data.success) {
          this.chatGptOptions = res.data.result;
        }
      });
    },
    initData(){
      const wenChatGpt = window.localStorage.getItem("wenChatGpt");
      if (wenChatGpt != null){
        this.SET_WEN_CHAT_GPT(wenChatGpt)
      }

      const wenChatGptName = window.localStorage.getItem("wenChatGptName");
      if (wenChatGptName != null){
        this.SET_WEN_CHAT_GPT_NAME(wenChatGptName)
      }

      const wenBelongsPlate = window.localStorage.getItem("wenBelongsPlate");
      if (wenBelongsPlate != null){
        this.SET_WEN_BELONGS_PLATE(wenBelongsPlate)

      }

      const checkboxTwo = window.localStorage.getItem("checkboxTwo");
      if (checkboxTwo != null){
        this.SET_CHECKBOX_TWO(!!checkboxTwo)
      }
    }
  },
}
</script>
<style scoped lang="scss">
.questionSetting {
  height: calc(100vh - 39px - 46px - 80px - 5px);
  .chat-dong-chatList_icon {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;

    &:hover {
      background: rgba(199, 209, 216, 0.2);
      border-radius: 2px;
    }
  }
  :deep .divergent-thinking .el-checkbox__input.is-focus .el-checkbox__inner {
    border-color:#CF1A1C;
  }
  .top {
    margin: 24px 0;
    padding: 0 16px;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      align-self: stretch;
      margin-bottom: 12px;
      color: var(--333, #333);
    }

    .button-box {
      text-align: center;
      width: 100%;
      height: 43px;
      padding: 4px 4px;
      align-items: center;
      border-radius: 8px;
      margin-bottom: 24px;

      .el-radio-button {
        width: 33.3%;
      }

      .el-radio-group {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0px 1px 2px 0px rgba(164, 171, 174, 0.30);
        height: 43px;
      }

      .el-radio-button .el-radio-button__inner {
        width: 100%;
        border: none;
        border-radius: 3px;
        background: #F2FBFF;
        height: 43px;
        line-height: 30px;
        font-size: 14px;
      }

      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        background: linear-gradient(90deg, rgba(20, 188, 245, 1) 0%, rgba(124, 136, 243, 1) 100%), linear-gradient(232.88deg, rgba(50, 226, 237, 1) 5.35%, rgba(0, 94, 255, 1) 88.15%)!important;
        border-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
      }
    }

    .check {
      font-size: 14px;
      border-radius: 8px;

      .el-checkbox {
        padding: 8px 0;
      }
    }

    .selectbox {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
      align-items: flex-end;
      flex-direction: column;
      .selectGpt{
        width: 100%;
        .select-setting{
          width: 180px;
          height: 38px !important;
          margin-bottom: 12px;
        }
        :deep(.el-input__inner){
          height: 38px;
          border-radius: 3px;
        }
      }
      .selectBelongsPlate{
        width: 100%;
        .select-setting{
          width: 180px;
          height: 38px !important;
        }
        :deep(.el-input__inner){
          height: 38px;
          border-radius: 3px;
        }
      }
    }
  }

  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #111;
  }

  .first {
    padding: 8px;
    height: 35px;
    margin: 16px 0px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    background: #fff;
    cursor: pointer;

    .first_xz_name {
      font-size: 14px;
      color: #111;
    }
  }

  .first:hover {
    background: #EBF8FF;
    border-radius: 8px;
    box-shadow: 0px 1px 2px 0px rgba(131, 137, 140, 0.30);
  }

  .first_xz {
    padding: 8px;
    margin: 16px 0px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    background: #EBF8FF;
    box-shadow: 0px 1px 2px 0px rgba(131, 137, 140, 0.30);
    cursor: pointer;

    .first_xz_name {
      font-size: 14px;
      color: #111;
    }

    .first_xz_prompt {
      color: #858A8F;
      font-size: 12px;
      font-weight: 400;
      margin-top: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .setting-modal-item {
    border-top: 1px solid #EEEEEE;
    padding: 24px 16px;
    //height: calc(100vh - 372px);
    //overflow: auto;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    .setting-modal-item__title {
      font-size: 16px;
      color: var(--333, #333);

      .name_css {
        float: right;
        margin-right: 6px;
        cursor: pointer;
        font-size: 14px;
      }

      .name_css:hover {
        color: var(--brand-414-bcf-5, #14BCF5);
      }
    }

    .el-form {
      margin-top: 14px;
    }

    .el-form-item__label {
      font-weight: normal;
    }

    .setting-modal-item__select {
      width: 400px;
    }

    .setting-modal-item-model {
      background: #EFEFEF;
      border-radius: 6px;
      padding: 3px 2px;
      width: 268px;
      margin-top: 11px;

      .setting-modal-item-model__item {
        display: inline-block;
        height: 30px;
        text-align: center;
        width: 127px;
        line-height: 30px;
        color: #333;
        font-size: 14px;
        border-radius: 3px;
        cursor: pointer;

        &:last-child {
          margin-left: 10px;
        }

        &.active {
          background: #FFFFFF;
          color: #14bcf5;

          .iconfont {
            color: #14bcf5;
          }
        }
      }
    }

    .setting-model-item-slider {
      flex: 1;
      margin-left: 16px;

      .el-slider {
        margin-top: -8px;
        margin-bottom: 24px;

        .el-slider__stop {
          display: none;
        }

        .el-slider__marks-text {
          color: #666666;
          width: 28px;
        }
      }

      .el-slider__bar {
        background: linear-gradient(90deg, #14BCF5 9.21%, #207DFD 52.41%, #E64BFF 95.07%);
      }

      .el-slider__button {
        width: 24px;
        height: 24px;
        border-width: 1.5px;
        line-height: 24px;

        &:after {
          content: '';
          width: 12px;
          height: 12px;
          background: #14bcf5;
          display: inline-block;
          border-radius: 12px;
        }
      }

      &.first {
        .el-slider__marks-text:first-child {
          color: #14bcf5;
        }
      }

      &.second {
        .el-slider__marks-text:nth-child(2) {
          color: #14bcf5;
        }
      }

      &.third {
        .el-slider__marks-text:last-child {
          color: #14bcf5;
        }
      }
    }

    .setting-modal-item-range {
      display: flex;
      margin-top: 14px;
      color: #333;
    }

    .setting-model-item__input textarea {
      resize: none;
      padding: 4px 10px 3px 10px;
      font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    }
  }

  .setting-modal-tip {
    font-weight: normal;
    font-size: 12px;
    margin-left: 10px;
    color: #999;

    &:hover {
      color: #999;
    }
  }

  .setting-modal-body {
    padding: 20px 20px 0 20px;
  }

  .dialog-footer {
    height: 70px;
    line-height: 70px;
    border-top: 1px solid #e8e8e8;
    box-sizing: content-box;
  }

  .setting-modal-empty {
    .setting-modal-empty__text {
      color: #999;
      margin-top: 8px;
      margin-bottom: 30px;
    }
  }
}
</style>
