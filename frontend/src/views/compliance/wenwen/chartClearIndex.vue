<template>
  <div ref="chartClearIndex" class="chartClearIndex" :class="'bg2'">
    <div class="backgroundImage"></div>
    <div class="chart_titleA">
      <span>{{ prop.name }}</span>
    </div>
    <div class="chart_titleB">{{ prop.describe }}</div>
    <div class="contentB">
      <div class="contentC">
        <img src="../../../assets/images/wenwen/Frame.png" style="width: 26px;height: 26px;margin-right: 8px"/>
        <span>您或许想知道</span>
        <span class="refreshQuesitonCla" @click="getRandomQuestions"><i class="iconfont ic-refresh"
                                                                        style="color: rgba(207, 26, 28, 1)"> &nbsp;换一换</i></span>
      </div>
      <div class="otherQuestionCla">
        <div class="otherChildCl" v-if="questionRand.length > 0">
          <div class="contentDb" @click="getChat(item)" v-for="(item,index) in questionRand"
               :title="item">
            <div class="contentD">
              <span style="padding: 0 10px">{{ item }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import {_getAiAssistantStandardIssues} from "@/api/chat";

export default {
  props: {
    prop: {
      flag: Array,
      name: String,
      describe: String,
      illustrate: Array
    }
  },
  components: {},
  data() {
    return {
      questionRand: [],
      userShow: true,
      question: []
    }
  },
  computed: {},
  mounted() {
    this.getAiAssistantStandardIssues()
  },
  created() {
    if (window.innerHeight < 760) {
      this.userShow = false
    }
  },
  methods: {
    // 获取标问
    getAiAssistantStandardIssues() {
      _getAiAssistantStandardIssues().then(res => {
        if (res.data.success) {
          res.data.result.forEach((item) => {
            this.question.push(item)
          });
          this.getRandomQuestions()
        }
      });
    },
    getRandomQuestions() {
      if (this.question !== null && this.question !== []) {
        const numberOfQuestionsToShow = Math.min(8,this.question.length);
        const randomIndexes = [];
        while (randomIndexes.length < numberOfQuestionsToShow) {
          const randomIndex = Math.floor(Math.random() * this.question.length);
          if (!randomIndexes.includes(randomIndex)) {
            randomIndexes.push(randomIndex);
          }
        }
        this.questionRand = randomIndexes.map(index => this.question[index]);

      }
    },
    getChat(name) {
      this.$emit("getChat", name);
    }
  },
  watch: {
  }
}
</script>
<style scoped lang="scss">

.bg2 {
   background: url('../../../assets/images/wenwen/background-2.png') top right/529px 334px no-repeat, linear-gradient(185deg, rgba(250, 212, 212, 0.8), rgba(255, 255, 255, 0) 335px);
}

.chartClearIndex {
  //width: 100%;
  padding: 50px 32px 16px 32px;
  overflow: auto;
  position: relative;
  height: calc(100vh - 61px);
  background-color: white;
 .iconfont{
   font-size: 14px;
   font-weight: 400;
 }
  .useExplainCla {
    background: linear-gradient(180deg, #F1EFFF 0%, #FFFFFF 47.35%);

    border-radius: 10px;
    padding: 14px;
    position: absolute;
    left: 32px;
    right: 32px;
    bottom: 16px;
    margin-right: 60px;
  }

  .explainTitle {
    font-size: 14px;
    color: #292E33;
    margin-bottom: 8px;
  }

  .explainContent {
    font-size: 12px;
    line-height: 16.8px;
    color: #525C66;
    margin-bottom: 12px;
  }

  .chart_title {
  }

  .chart_contentA {
    display: flex;
    margin-top: 32px;
  }

  .chart_contentB {
    margin-left: 10px;
    width: 80%;
  }

  .casrt_contentB_con {
    font-size: 14px;
    color: #666666;
    margin-top: 8px;
  }

  .chart_titleA {
    color: #292E33;
    font-weight: 600;
    font-size: 36px;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
  }

  .chart_titleB {
    color: #333333;
    font-weight: 400;
    font-size: 16px;
    margin: 12px 0px;
  }

  .chart_content_title {
    display: flex;
    position: relative;
  }

  .table_css {
    color: #1b44e2;
    font-weight: 1000;
    margin-right: 10px;
  }

  .contentB {
    margin-top: 60px;
    width: 100%;
  }

  .contentC {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .refreshQuesitonCla {
    margin-left: 24px;
    cursor: pointer;
    width: 95px;
    height: 30px;
    padding: 6px 4px 6px 4px;
    border-radius: 4px;
    gap: 5px;
    font-size: 14px;
    display: flex;
    align-items: center;
  }

  .refreshQuesitonCla:hover {
    background: #F4F1F7;
  }

  .otherQuestionCla {
    width: 100%;
  }

  .otherChildCl {
    //margin: 0 6px;
    //min-width: 0;
    margin-left: -25px;
  }
  .contentDb {
    width: 45%;
    margin-left: 25px;
    display: inline-block;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    font-size: 14px;
    color: #333333;
    height: 50px;

  }
  .contentD {
    display: inline-block;
    border-radius: 10px;
    height: 40px;
    gap: 24px;
    background: linear-gradient(90deg,#FFF2F2 30.85%, #FFF8F1 100%);
    cursor: pointer;
    max-width: 100%;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    font-size: 14px;
    color: #333333;
    line-height: 40px;
    border: 1px solid transparent; /* 添加透明边框 */
  }

  .contentD:hover {
    background: linear-gradient(90deg, #FFC4C4 0%, #FFDDBF 100%);
    border: 1px solid rgba(255, 255, 255, 1);
    box-shadow: 0px 1px 4px 0px rgba(192, 214, 221, 0.4);
    color: var(--brand-414-bcf-5, #CF1A1CFF);

  }

  .back_clear_img {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 320px
  }

  .contentE {
    position: absolute;
    display: flex;
    bottom: 74px;
  }

  .content_ea {
    color: #333333;
    font-size: 14px;
    margin-left: 8px;
  }
}
</style>
