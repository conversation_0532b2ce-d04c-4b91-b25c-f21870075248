<template>
  <div class="historyIndex">
    <div class="bottom_css_left_two">
        <div class="tipBox_left" style="height: 100%">
          <div class="chat-dong-chatList_title" v-show="historyParam.chatListNum !== 0" v-if="historyParam.chatList.length > 0 && historyParam.chatList[0]['name'] ==='今天' && historyParam.chatList[0]['children'].length > 0">
            今天
          </div>
          <div v-if="historyParam.chatListNum===0" class="recordEmpty">
            历史记录为空
          </div>
          <div v-else>
            <div class="chat-dong-chatList">
              <div class="chat-dong-chatList-content" ref="chatList">
                <div v-for="(item,index) in historyParam.chatList" :key="'chatList'+ index">
                  <div class="chat-dong-chatList_title" v-if="item.name !=='今天' && item.children.length > 0">
                    {{item.name}}
                  </div>
                  <div  v-for="(_chat, _idx) in item.children"
                        :key="_chat.id">
                    <div class="chat-dong-chatList_item bhvlog"  :class="[_chat.id === historyParam.currentChat && 'active']"
                         @click="changeChat(_chat.id, _chat.aiType)">
                      <div v-if="_chat.edit"  v-clickoutside="handleClickOut" class="chat-dong-chatList_item-content">
                        <el-input size="small" class="chat-dong-chatList__input" v-model="_chat.chatName"
                                  :maxlength="30"
                                  :ref="'input' + _chat.id"></el-input>
                        <span class="chat-dong-chatList_icon" title="确定"
                              @click.stop="editChatSure(_chat)">
                      <i class="iconfont ic-check"></i>
                      </span>
                        <span class="chat-dong-chatList_icon" title="取消"
                              @click.stop="editChatCancel(_chat)">
                      <i class="iconfont ic-close"></i>
                      </span>
                      </div>
                      <div v-else class="chat-dong-chatList_item-content">
                        <span class="chat-dong-chatList_text"  :title="_chat.chatName" v-html="_chat.chatName"></span>
                        <span class="chat-dong-chatList_icon" title="编辑"
                              @click.stop="editChat(_chat)"> <i class="iconfont icon-ic-edit-n1"></i></span>
                        <span class="chat-dong-chatList_icon bhvlog" title="删除"
                              @click.stop="deleteChat(_chat)"
                        ><i class="iconfont ic-trash"></i></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script>
import {
  _delete,
  _editChatName,
  _getChatRecordList
} from "@/api/chat";
import common from "@/mixins/common";
import clickoutside from 'stock-ui/src/utils/clickoutside';
import {mapGetters, mapMutations} from "vuex";
export default {
  mixins: [common],
  directives: { clickoutside },
  data () {
    return {
      historyParam: {
        currentChat: '', // 当前会话id
        chatList: [], // 聊天会话列表
        chatListNum: 0,
        keyword: '', // 搜索关键词空格拼接字符串
        keywordAll: 0,
        keywordNum: 0,
        contentAllList: [] // 当前人所有历史会话
      },
      wenNullList: false,
      clickId: ''
    }
  },
  computed: {
    ...mapGetters(['homeSending']),
  },
  mounted () {
    this.getChatList(); // 查询当前用户对话框
  },
  methods: {
    ...mapMutations(['SET_WEN_NULL_LIST']),
    // 点击其他地方保存
    handleClickOut () {
      this.historyParam.chatList.map((i) => {
        i.children.map((j) => {
          if (j.edit) {
            this.$set(j, 'edit', false);
            this.editChatSure(j);
          }
        })
      });
    },
    // 获取聊天列表
    getChatList () {
      this.getChatData().then((data) => {
        this.historyParam.currentChat = data[0] ? data[0].id : null;
        this.$emit('wenClickChart', this.historyParam);
      });
    },
    // 获取列表列表数据
    async getChatData (val) {
      const res = await _getChatRecordList();
      if (res.data.success) {
        if (res.data.result.chatRecordListToday.length === 0 && res.data.result.chatRecordList7Days.length === 0 && res.data.result.chatRecordList30Days.length === 0 && res.data.result.chatRecordListOld.length === 0) {
          this.wenNullList = true;
          this.SET_WEN_NULL_LIST(this.wenNullList);
        } else {
          this.wenNullList = false;
          this.SET_WEN_NULL_LIST(this.wenNullList);
        }
        const chatList = [
          {
            name: '今天',
            children: res.data.result.chatRecordListToday
          },
          {
            name: '近7天',
            children: res.data.result.chatRecordList7Days
          },
          {
            name: '近30天',
            children: res.data.result.chatRecordList30Days
          },
          {
            name: '更早',
            children: res.data.result.chatRecordListOld
          }
        ];
        const result = res.data.result;
        const data = result.chatRecordListToday.concat(result.chatRecordList7Days).concat(result.chatRecordList30Days).concat(result.chatRecordListOld);
        if (data.length > 0) {
          if (this.clickId === '' || val === '1') {
            this.historyParam.currentChat = data[0].id
          } else {
            this.historyParam.currentChat = this.clickId;
          }
        }
        this.historyParam.chatList = chatList;
        this.historyParam.chatListNum = res.data.result.chatRecordListOld.length + res.data.result.chatRecordList30Days.length + res.data.result.chatRecordList7Days.length +
          res.data.result.chatRecordListToday.length
        return data;
      }
    },
    // 编辑聊天标题
    editChat (item) {
      const div = document.createElement('div');
      div.innerHTML = item.chatName;
      item.chatName = div.textContent || '';
      // 保留原来的聊天标题，取消编辑的时候恢复
      item.oldName = item.chatName;
      this.$set(item, 'edit', true);
      // 默认选中全部聊天标题
      this.$nextTick(() => {
        this.$refs['input' + item.id][0].$el.querySelector('input').select();
      });
    },
    // 删除某个聊天
    deleteChat (item) {
      if (this.homeSending) {
        this.$message.info('正在回答中，请勿删除对话。');
        return
      }
      this.$confirm('删除后不可恢复，请确认是否继续该操作', '删除所选会话？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _delete({ chatId: item.id }).then(res => {
          if (res.data.success) {
            this.getChatList(); // 查询当前用户对话框
            this.$message.success('删除成功');
          }
        });
      });
    },
    // 确认修改聊天标题
    editChatSure (item) {
      let auto = false
      if (!!!item.oldName){
        auto = true
      }
      if (this.getValue(this.trimStr(item.chatName)) === '') {
        item.chatName = item.oldName;
        this.$message.error('聊天标题不能为空');
        return
      }
      item.chatName = this.trimStr(item.chatName)
      _editChatName({ chatId: item.id, chatName: item.chatName.replace(/\r|\n|\\s/g, ""), auto: auto }).then(res => {
        if (res.data.success) {
          this.clickId = item.id;
          this.$set(item, 'edit', false);
          this.getChatData()
        }
      });
    },
    // 取消修改聊天标题
    editChatCancel (item) {
      this.$set(item, 'edit', false);
      item.chatName = item.oldName;
    },
    // 切换选中的聊天
    changeChat (val, type) {
      if (this.homeSending) {
        this.$message.info('正在回答中，请勿切换对话。');
        return
      }
      this.historyParam.currentChat = val;
      this.$emit('wenClickChart', this.historyParam);
    }
  }
}
</script>
<style scoped lang="scss">
.historyIndex{
  width: 92%;
  height: calc(100vh - 60px - 48.33px - 38px);
  overflow: hidden;
  position: relative;
  padding: 7px 16px;
  :deep .el-input__inner {
    border-radius: 8px !important;
  }
  .ssdw {
    padding: 10px;
    text-align: left;
    font-size: 14px;
    color: #111;
    .ssdw-icon-left {
      width: 90px;
      height: 25px;
      color: var(--666, #666);
      font-size: 12px;
      line-height: 25px;
      text-align: center;
      border-radius: 3px 0 0 3px;
      border: 1px solid #D9D9D9;
      display: inline-block;
      .el-icon-arrow-left{
        font-size: 14px;
        padding-right: 5px;
        cursor: pointer
      }
      .el-icon-arrow-left:hover{
        color: #1990FE;
      }
      .el-icon-arrow-right{
        font-size: 14px;
        padding-left: 5px;
        cursor: pointer
      }
      .el-icon-arrow-right:hover{
        color: #1990FE;
      }
    }

    .ssdw-icon-min {
      border-left: 1px solid #D9D9D9;
      border-right: 1px solid #D9D9D9;
      display: inline-block;
      padding: 0 2px;
      color: #666;
      min-width: 40px;
    }

  }
  .bottom_css_left_two{
    text-align: center;
    position: relative;
    bottom: 0px;
    float: left;
    color: #666;
    font-size: 14px;
    width: 100%;
    height: 100%;
    line-height: 36px;
  }
  .tipBox_left{
    font-size: 12px;
    //height:  calc(100vh - 537px);
    //min-height: 100px;
    overflow: auto;
  }
  .el-input--medium .el-input__inner{
    border-radius: 3px;
    border: 1px solid var(--e-4-e-4-e-4, #E4E4E4);
  }
  .tipBox_right{
    border-top: 1px solid #E8EAEB;
    padding-left: 10px;
    //height:400px;
    //min-height: 200px;
    //height: 400px;
    overflow: hidden;
  }
  .chat-dong-chatList_title{
    text-align: left;
    font-size: 12px;
    color: rgba(68, 68, 68, 0.80);
    height:35px;
    line-height: 35px;
    text-indent: 10px;
    margin-top:6px;
  }
  .chat-dong-chatList-new{
    padding:0 16px;
  }
  .chat-dong-chatList-content{
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom:5px;
  }
  .chat-dong-chatList_item{
    color:#111111;
    width:100%;
    font-size: 14px;
    margin-top:6px;
    height:40px;
    line-height: 40px;
    cursor: pointer;
    border-radius: 8px;
    .chat-dong-chatList_item-content{
      padding:0 10px;
      display:flex;
      align-items: center;
      text-align: left;
    }
    &.active{
      box-shadow: 0px 1px 2px rgba(164, 172, 174, 0.5);
      background: linear-gradient(90deg, #CF1A1C 0%, #FF8E8F 100%);
      color: #FFF;
      .chat-dong-chatList_icon{
        display:inline-block;
      }
      .chat-dong-chatList_icon{
        display: none;
      }
      &:hover{
        box-shadow: 0px 1px 2px rgba(164, 172, 174, 0.5);
        background: linear-gradient(90deg, #CF1A1C 0%, #FF8E8F 100%);
        color: #FFF;
        .chat-dong-chatList_icon{
          display:inline-block;
        }
        .iconfont {
          color: #FFF;
        }
      }
    }
    &:hover{
      background: #F4ECEC;
      color:#111111;
      .iconfont{
        color:#444;
        font-size:14px;
      }
      .chat-dong-chatList_icon{
        display:inline-block;
      }
    }
    &.new{
      border: 1px solid #B2BDC0;
      border-radius: 6px;
      height:38px;
      line-height: 38px;
      padding:0 14px;
      &:hover{
        background: rgba(252, 254, 255, 0.95);
        color:#CF1A1C;
        .iconfont{
          color:#444!important;
        }
      }
    }
    .iconfont{
      color:#fff;
      font-size:14px;
    }
    .chat-dong-chatList_icon{
      width:24px;
      height:24px;
      display:none;
      line-height: 24px;
      text-align: center;
      &:hover{
        background: rgba(199, 209, 216, 0.2);
        border-radius: 2px;
      }
    }
    .chat-dong-chatList_text{
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex:1;
    }
    .chat-dong-chatList__input{
      width:200px;
      text-overflow: ellipsis;
      .el-input__inner{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
