<template>
  <div class="left-box">
    <el-tabs type="card">
      <el-tab-pane>
        <span slot="label"><i class="el-icon-chat-dot-square"></i> 历史对话 </span>
        <wen-history-index ref="wenHistoryIndex" @wenClickChart="wenClickChart" @sendChatContent="sendChatContent" ></wen-history-index>
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label"><i class="el-icon-setting"></i> 工具箱</span>
        <wen-question-setting ref="wenQuestionSetting"></wen-question-setting>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import WenHistoryIndex from "@/views/compliance/wenwen/wenHistoryIndex.vue";
import WenQuestionSetting from "@/views/compliance/wenwen/wenQuestionSetting.vue";

export default {
  components: {
    WenQuestionSetting,
    WenHistoryIndex
  },
  data () {
    return {
      historyParam: {
        currentChat: '', // 当前会话id
        chatList: [], // 聊天会话列表
        chatListNum: 0,
        belongsPlate: '',
        companyCode: '',
        keywordAll: 0,
        keywordNum: 0,
      },
    }
  },
  methods: {
    // 点击历史对话 聊天
    wenClickChart (data) {
      this.historyParam = data;
      this.$emit('wenClickChart', this.historyParam);
    },
    getCreateChat (chatName, name, type, index) {
      if (index === 0) {
        this.$refs.wenwenHomeIndex.createChat(chatName, name, type);
      } else {
        this.$refs.wenwenHomeIndex.getCreateChat(chatName, name, type);
      }
    },
    sendChatContent (name) {
      this.$emit('sendChatContent', name);
    },
    getChatData (val) {
      this.$refs.wenHistoryIndex.getChatData(val);
    },
    editChatSure (name) {
      this.$refs.wenHistoryIndex.editChatSure(name);
    }
  }
}

</script>
<style scoped lang="scss">
.left-box {
  background-color:#FFFCFC99;
  height: 100%;
  :deep .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    border-bottom-color: #F4ECEC;
    background-color: #F4ECEC;
    color: #CF1A1CFF !important;
    font-size: 16px;
  }
  :deep .el-tabs--card>.el-tabs__header .el-tabs__item {
    background-color: #FFFFFF;
    color: #333333 !important;
    font-size: 16px;
  }
  :deep .el-tabs--card>.el-tabs__header .el-tabs__nav{
    border: 1px solid #ffffff;
  }
  :deep(.el-tabs--border-card) {
    height: calc(100% - 39px);
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  :deep(.el-tabs) {
    height: calc(100% - 39px);
  }

  :deep(.el-tabs__nav) {
    width: 100%;
  }

  :deep(.el-tabs__item) {
    width: 50%;
    text-align: center;
  }
}

:deep(.el-tabs--border-card>.el-tabs__content) {
  padding: 0;
  height: calc(100vh - 48px - 60px);
  background-color: white;

  .el-tab-pane {
    height: 100%;
  }
}
</style>
