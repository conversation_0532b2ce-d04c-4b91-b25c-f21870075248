<template>
  <div class="zgsTableManage-class">
    <div class="head-class">
      <el-form :model="param"  ref="ruleForm" class="demo-ruleForm" >
        <div style="display: flex">
          <div class="inputmodel">
            <el-form-item prop="questionContent">
              <el-input v-model="param.questionContent" class="inputWidth-class" @keyup.enter.native="search" placeholder="请输入关键字"></el-input>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="answerContent">
              <el-input v-model="param.answerContent" class="inputWidth-class" @keyup.enter.native="search" placeholder="请输入回复关键字"></el-input>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="userType" class="el-colone">
              <el-select v-model="param.userType" clearable class="inputWidth-class"  placeholder="用户类型">
                <el-option
                    v-for="(item,index) in tableConfigInfo.userTypeList"
                    :key="index"
                    :label="item.codeName"
                    :value="item.codeValue">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="channel">
              <el-select v-model="param.questionChannel" clearable class="inputWidth-class" placeholder="渠道">
                <el-option
                    v-for="(item,index) in tableConfigInfo.questionChannelList"
                    :key="index"
                    :label="item.codeName"
                    :value="item.codeValue">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="aiType">
              <el-select v-model="param.aiType" clearable class="inputWidth-class"  placeholder="回复模型">
                <el-option
                    v-for="(item,index) in tableConfigInfo.aiTypeList"
                    :key="index"
                    :label="item.codeName"
                    :value="item.codeName">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <div style="display: flex">
          <div class="inputmodel">
            <el-form-item prop="userId">
              <el-input v-model="param.realName" class="inputWidth-class" @keyup.enter.native="search" placeholder="姓名"></el-input>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="userName">
              <el-input v-model="param.userName" class="inputWidth-class" @keyup.enter.native="search" placeholder="用户名"></el-input>
            </el-form-item>
          </div>
          <div class="inputmodel">
            <el-form-item prop="questionTime">
              <el-date-picker
                  class="inputWidths-class"
                  v-model="questionTime"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange">
              </el-date-picker>
            </el-form-item>
          </div>
          <div class="button-group" >
              <el-button type="primary" class="query-btn" @click="dosearch()">查询</el-button>
              <el-button type="primary" plain="reset-btn" @click="clearForm">重置</el-button>
              <el-button type="primary" plain  @click="exportFieldList">导出列表</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <!--  数据表格-->
    <div class="body-class">
      <el-table :data="tableData" ref="multipleTable"
                @sort-change="handleSortChange"
                stripe style="width: 100%"
                @selection-change="tableSelectionChange">
        <el-table-column
            label="序号"
            align="center"
            type="index"
            width="60"
            fixed="left">
        </el-table-column>
        <el-table-column align="left" prop="questionContent" label="问题" width='150'  fixed="left">
          <template scope="scope">
          <span :title="scope.row.questionContent"
                :style="{display: '-webkit-box',
            '-webkit-box-orient': 'vertical',
            '-webkit-line-clamp': '2',}">
            {{ scope.row.questionContent ? scope.row.questionContent : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="answerContent" label="回复" width='300'  fixed="left">
          <template #default="{ row }">
            <el-tooltip v-if="!!row.answerContent && row.answerContent != '--'" :content="row.answerContent" placement="top">
      <span
          class="prompt-text"
          :style="{
          display: '-webkit-box',
          '-webkit-box-orient': 'vertical',
          '-webkit-line-clamp': '2',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }"
      >
        {{ row.answerContent ? row.answerContent : '--' }}
      </span>
            </el-tooltip>

      <span v-else
          class="prompt-text"
          :style="{
          display: '-webkit-box',
          '-webkit-box-orient': 'vertical',
          '-webkit-line-clamp': '2',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }"
      >
        {{ row.answerContent ? row.answerContent : '--' }}
      </span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="answerStatus" label="回复状态" width='100'  fixed="left">
          <template scope="scope">
          <span :style="{display: '-webkit-box',
            '-webkit-box-orient': 'vertical',
            '-webkit-line-clamp': '2',}">
            {{ scope.row.answerStatus ? scope.row.answerStatus : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="prompt" label="上下文" width='150'>
          <template #default="{ row }">
            <el-tooltip v-if="!!row.prompt && row.prompt != '--'" :content="row.prompt" placement="top">
      <span
          class="prompt-text"
          :style="{
          display: '-webkit-box',
          '-webkit-box-orient': 'vertical',
          '-webkit-line-clamp': '2',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }"
      >
        {{ row.prompt ? row.prompt : '--' }}
      </span>
            </el-tooltip>

      <span v-else
          class="prompt-text"
          :style="{
          display: '-webkit-box',
          '-webkit-box-orient': 'vertical',
          '-webkit-line-clamp': '2',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }"
      >
        {{ row.prompt ? row.prompt : '--' }}
      </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="channel" label="渠道" width="100">
          <template scope="scope">
            <span
                :title="scope.row.questionChannel">{{ scope.row.questionChannel ? scope.row.questionChannel : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="userType" label="用户类型" width="100">
          <template scope="scope">
            <span :title="scope.row.userType">{{ scope.row.userType ? scope.row.userType : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="userName" label="用户名" width="100">
          <template scope="scope">
            <span :title="scope.row.userName">{{ scope.row.userName ? scope.row.userName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="realName" label="姓名" width="100">
          <template scope="scope">
            <span :title="scope.row.realName">{{ scope.row.realName ? scope.row.realName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="id" label="会话ID" width="180">
          <template scope="scope">
            <span :title="scope.row.id">{{ scope.row.id ? scope.row.id : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="tokens" label="Tokens" width="100">
          <template scope="scope">
            <span :title="scope.row.tokens">{{ scope.row.tokens ? scope.row.tokens : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="aiType" label="回复模型" width="110">
          <template scope="scope">
            <span :title="scope.row.aiType">{{ scope.row.aiType ? scope.row.aiType : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="questionTime" label="提问时间" width="160" sortable="true">
        </el-table-column>
        <el-table-column align="center" prop="answerTime" label="响应时间" width="160" >
          <template scope="scope">
            <span :title="scope.row.answerTime">{{ scope.row.answerTime ? scope.row.answerTime : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="waitTime" label="回复用时" width="120" :formatter="formatWaitTime">
        </el-table-column>
        <el-table-column align="center" prop="feedbackType" label="赞、踩" width="100">
          <template scope="scope">
            <span :title="scope.row.feedbackType">{{ scope.row.feedbackType ? scope.row.feedbackType : '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-pagination
            style="display: inline-block; margin-top: 10px;float: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 50, 200]"
            :page-size="param.pageSize"
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableTotal">
        </el-pagination>
      </el-row>
    </div>
  </div>
</template>
<script>
import {exportFieldList, getInformation, getTableList} from "@/api/Records";
import {_getModelList} from "@/api/chat";

export default {
  name: 'viewRecords',
  data() {
    return {
      param: {
        pageSize: 10,
        startRow: 0,
        questionContent: '',
        answerContent: '',
        questionChannel: '',
        aiType: '',
        userId: '',
        userName: '',
        userType: '',
        sortOrder: '',
        sortField: '',
        realName:'',
        startTime: "",
        endTime: "",
      },
      currentPage: 1,
      tableConfigInfo: {
        aiTypeList: [],
        questionChannelList: [],
        userTypeList: [],
      },
      chatGptOptions:[],
      tableData: [],
      codeName: '',
      codeValue: '',
      tableTotal: 10,
      selectTable: [],
      selectedRows: [],
      tableTotals:0,
      questionTime:[]
    }
  },
  created() {
    this.search()
    this.getInformation()
  },
  mounted() {
  },
  methods: {
    dosearch(){
      this.param.startRow = 0
      this.currentPage = 1
      this.search()
    },
    search() {
      getTableList(this.param).then(res => {
        this.tableData = res.data.result.tableList
        for (const data of this.tableData) {
          if (data.questionChannel === 'AI agent'){
            data.userType = '内部'
            let name = data.userName
            data.userName = data.realName
            data.realName = name
          }
        }


        this.tableTotal = res.data.result.total
        this.tableTotals=this.tableData.length
      })
    },
    getInformation() {
      getInformation({}).then(res => {
        this.tableConfigInfo = res.data.result;
      })
    },
    formatWaitTime(row) {
      if (!row.waitTime || row.waitTime === -1) return '--';
      const minutes = Math.floor(row.waitTime / 60);
      const seconds = row.waitTime % 60;
      return minutes > 0
          ? `${minutes}分${seconds}秒`
          : `${seconds}秒`;
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.param.startRow = (val - 1) * this.param.pageSize;
      this.search()
    },
    handleSizeChange(val) {
      this.param.pageSize = val;
      this.search()
    },
    handleDateChange(val) {
      if (val && val.length === 2) {
        this.param.startTime = val[0]; // 开始时间
        this.param.endTime = val[1];   // 结束时间
      } else {
        this.param.startTime = "";
        this.param.endTime = "";
      }
    },
    clearForm() {
      this.param = {
        pageSize: 10,
        startRow: 0,
        questionContent: '',
        answerContent: '',
        questionChannel: '',
        aiType: '',
        userId: '',
        userName: '',
        userType: '',
        sortOrder: '',
        sortField: '',
        realName:'',
        startTime: '',
        endTime: ''
      }
      this.questionTime=[]
      this.currentPage = 1
      this.dosearch()
    },
    handleSortChange({prop, order}) {
      this.param.sortField = prop;
      this.param.sortOrder = order === 'ascending' ? 'asc' : 'desc';
      this.search() // 重新加载数据
    },
    exportFieldList() {
        if (this.tableData && this.tableData.length > 0) {
          exportFieldList(this.param)
        } else {
          this.$message.warning('当前表格没有数据可导出');
          return;
      }

    },
    tableSelectionChange(rows) {
      this.selectTable = rows;
      if(rows.length>0) {
        this.selectedRows = rows
        this.tableTotals = this.selectedRows.length
      }
    },
  },
}
</script>
<style scoped lang="scss">
.zgsTableManage-class {
  padding: 10px 50px;
  .head-class {
    height: 120px;
  }
  .el-date-editor .el-range-input .large{
    background-color: #F6F7F9 !important
  }
  .hubody-class {
      padding-top: 18px;
  }
  .inputWidth-class {
    width: 242px;

  }

  .inputmodel{
    margin-right: 12px;
  }
  .inputWidths-class{
    width: 350px;

  }
  ::v-deep .el-date-editor .el-range-input.large{
    background-color: #F6F7F9 !important
  }
  .input-container-class {
    display: flex;
    align-items: center;

    span {
      margin-right: 10px;
      min-width: 70px;
    }
  }
  ::v-deep .prompt-text {
    /* 可选：调整字体、颜色等基础样式 */
    font-size: 14px;
    color: #606266;
    line-height: 1.5;

    /* 确保截断效果 */
    word-break: break-word; /* 允许长单词换行 */
  }
  .el-table__body-wrapper {
    max-width: 600px; /* 按需设置合适的最大宽度 */
  }
.total-count{
  color: #333;
  font-size: 14px;
  font-family: Arial, sans-serif;
  letter-spacing: 4px;
}
  .button-group{
   position: absolute;
    right: 50px;
  }

  .el-select .el-input__inner {
    width: 100%;
    background-color: #F6F7F9; /* 浅灰色背景 */
    border: 1px solid #e0e0e0; /* 浅灰色边框 */
    color: #333;
  }
  .question-content-limit {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 限制为两行 */
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 3em; /* 大约两行文字的高度 */
  }

  /deep/ .el-input__inner {
    background-color: #F6F7F9 !important; /* 浅灰色背景 */
    border: none; /* 浅灰色边框 */
    color: #333 !important;
  }

  /deep/ .el-table__fixed-arrow {
    display: none !important;
  }
  //.button-group {
  //  display: flex;
  //  //gap: 12px; // 按钮间距
  //  justify-content: flex-end;
  //  align-items: center;
  //}

  .el-button {
    border-radius: 20px !important; // 图片中的圆角半径
    padding: 8px 20px;
  }
  .flex-container {
    display: flex;
    justify-content: space-between;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
    }
  }
</style>