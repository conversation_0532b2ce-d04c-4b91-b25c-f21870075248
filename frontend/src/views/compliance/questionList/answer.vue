<template>
<!--  合规问答-->
  <div v-if="dataList.length > 0" id="infoList01" class="right-box-content">
    <div class="qatitle">
      <img style="width: 25px;height: 25px;margin-right: 11px;" src="../../../assets/images/wenwen/hegui.png"> 合规问答<span class="qdtitle_a">关联结果共{{ dataList.length }}条</span>
    </div>
    <div class="qatitle_border" style="margin-bottom: 12px;"></div>
    <div v-for="(item, i) in dataList" :key="item['answerId']" style="position: relative">
      <div v-show="moreThen ? (!moreFlagQa) && i<3 || moreFlagQa : i<dataList.length">
        <div class="question-box">
          <div style="position: relative;display: flex;margin-top: 6px;margin-bottom: 6px;">
            <div class="question_css">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 9.7V5C20 3.9 19.1 3 18 3H2C0.9 3 0 3.9 0 5V21C0 22.1 0.9 23 2 23H18C19.1 23 20 22.1 20 21V16.7L26 13.2L20 9.7Z" fill="#FEE9E9"/>
                <path d="M4.69995 9.9001H5.59995V18.9001H4.69995V9.9001ZM5.59995 7.1001C6.19995 7.7001 7.09995 8.6001 7.49995 9.2001L6.79995 9.8001C6.39995 9.2001 5.49995 8.3001 4.89995 7.6001L5.59995 7.1001ZM8.59995 15.7001V16.5001H7.69995V10.9001H12.2V15.7001H8.59995ZM15.2999 17.5001C15.2999 18.1001 15.2 18.4001 14.8 18.6001C14.4 18.8001 13.7 18.8001 12.6 18.8001C12.6 18.5001 12.4 18.1001 12.3 17.9001H14.0999C14.2999 17.9001 14.4 17.8001 14.4 17.6001V8.6001H8.09995V7.7001H15.2L15.2999 17.5001ZM8.59995 11.8001V14.8001H11.3V11.8001H8.59995Z" fill="#CF1A1C"/>
              </svg>
            </div>
            <div class="title-style" style="display:inline-block;margin-left: 25px;cursor: pointer;font-weight: bold;font-size: 16px"
                 @click="turn(item['qId'])" v-html="item['questionContent']"></div>
          </div>
          <div class="quest-list-title">
            <i class="el-icon-tickets" style="color: #999999;font-size: 13px;"></i>
            <span class="descriptionPosition"
                  style="margin-left: 3px;font-size: 14px;line-height: 20px;">{{
                item['businessTypeName']
              }}</span>
            <span v-if="item['plateName']" class="descriptionPosition">{{ item['plateName'] }}</span>
            <span class="descriptionPosition">提问时间：{{ item['askTime'] }}</span>
          </div>
          <div style="position: relative;border-radius: 3px;background: #F9F9F9;margin-top: 12px;padding:12px 12px 12px 12px;margin-left: -12px;">
            <div style="display: flex;">
              <div class="answer_css">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 9.7V5C20 3.9 19.1 3 18 3H2C0.9 3 0 3.9 0 5V21C0 22.1 0.9 23 2 23H18C19.1 23 20 22.1 20 21V16.7L26 13.2L20 9.7Z" fill="#E0EAFA"/>
                  <path d="M12.513 9.186H11.603C11.356 9.628 11.057 10.018 10.771 10.33C10.589 10.187 10.212 9.927 9.97804 9.797C10.667 9.134 11.239 8.12 11.538 7.08L12.448 7.288C12.331 7.639 12.201 8.016 12.045 8.354H15.984V9.186H13.579C13.891 9.615 14.203 10.122 14.346 10.473L13.449 10.798C13.293 10.369 12.89 9.706 12.513 9.186ZM7.10504 9.173H6.42904C6.03904 9.823 5.59704 10.421 5.14204 10.876C4.97304 10.733 4.57004 10.46 4.34904 10.33C5.20704 9.537 5.98704 8.315 6.42904 7.08L7.35204 7.327C7.22204 7.665 7.06604 8.016 6.89704 8.341H10.186V9.173H8.09304C8.36604 9.641 8.61304 10.148 8.73004 10.525L7.87204 10.811C7.74204 10.382 7.43004 9.719 7.10504 9.173ZM13.137 15.764H7.52104V17.623H13.137V15.764ZM6.58504 19.001V14.919H14.086V18.936H13.137V18.481H7.52104V19.001H6.58504ZM8.24904 13.06H12.63C11.707 12.54 10.875 11.981 10.251 11.474C9.67904 12.059 9.00304 12.592 8.24904 13.06ZM10.134 10.2L11.044 10.564C10.966 10.681 10.875 10.785 10.784 10.889C12.019 11.838 14.346 13.021 16.387 13.619C16.153 13.814 15.867 14.204 15.711 14.464C14.814 14.165 13.891 13.736 13.02 13.281V13.879H7.69004V13.411C6.85804 13.879 5.94804 14.282 4.99904 14.646C4.86904 14.425 4.58304 14.035 4.38804 13.866C6.84504 13.021 9.05504 11.708 10.134 10.2Z" fill="#0052D9"/>
                </svg>
              </div>
              <div class="content-style answer_content" :id="'answerContent' + i" v-html="item['originalAnswerContent'] || item['questionContent']"></div>
              <template v-if="item.preview">
                <image-viewer :initial-index="imageIndex" v-if="item.showViewer" :on-close="closeViewer" :url-list="item.src"/>
              </template>
            </div>
            <div v-if="item.lawInfoItemArr.laws_item && item.lawInfoItemArr.laws_item.length > 0"
                 style="font-size: 14px;color:#CF1A1C;margin-left: 22px;cursor: pointer;margin-bottom: 10px;">
              <div style="float: left;color: #999999">参考法规：</div>
              <div class="law-turn" v-for="lawItem in item.lawInfoItemArr.laws_item" :key="lawItem['law_id']">
                <span @click="turnLawAll(lawItem['law_id'])">{{ lawItem['law_name'] }}</span>
                <span v-if="lawItem['law_status'] !== '0'">{{
                    item.lawInfoItemArr.law_status === '1' ? '(已失效)' : '(已被修改)'
                  }}</span>
                <span v-if="lawItem['law_all_flag'] === '1'" style="color:#CF1A1C"
                      @click="turnLawRead(lawItem['law_id'], item['qId'])">
                  (精读{{ lawItem['law_read_count'] }}条)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="moreThen && dataList.length > 3" class="more-style" @click="moreFlagQa = !moreFlagQa">
      {{ !moreFlagQa ? '查看更多' : '收起' }}
      <i :class="{'el-icon-arrow-down': !moreFlagQa, 'el-icon-arrow-up': moreFlagQa}"></i>
    </div>
  </div>
</template>
<script lang="js">
import ImageViewer from 'stock-ui/packages/image/src/image-viewer.vue'
import {mapGetters} from "vuex";
import {getTenantInfo} from "@/api/chat";

let prevOverflow = '';

export default {
  name: 'answer',
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters(['moreThen'])
  },
  components: {
    ImageViewer
  },
  data () {
    return {
      moreFlagQa: false,
      imageIndex: 0,
      token: '',
      tenant_info: ''
    }
  },
  mounted () {
    this.getTenantInfo();
    if (this.dataList != null && this.dataList.length > 0) {
      this.addImagePreView()
    }
  },
  methods: {
    turn (id) {
      const str = 'https://services.easy-board.com.cn/ui/compliance/complianceDetail?id=' + id + '&access_token=' + this.token + '&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(str)
    },
    turnLawAll (lawId) {
      const str = 'https://services.easy-board.com.cn/ui/laws/laws/lawsDetail?lawId=' + lawId + '&hasLawsApplyModule=1,4,16,6&lawsApplyModule=all&access_token=' + this.token + '&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(str)
    },
    turnLawRead (lawId, typeId) {
      const str = 'https://services.easy-board.com.cn/ui/laws/laws/lawClause?lawId=' + lawId + '&typeId=' + typeId + '&intensiveReading=1&type=4&access_token=' + this.token + '&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(str)
    },
    clickHandler (index) {
      // don't show viewer when preview is false
      if (!this.dataList[index].preview) {
        return;
      }
      // prevent body scroll
      prevOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      this.dataList[index].showViewer = true;
    },
    closeViewer () {
      document.body.style.overflow = prevOverflow;
      this.dataList.forEach((item) => {
        if (item.preview) {
          item.showViewer = false
        }
      })
    },
    getTenantInfo () {
      getTenantInfo().then(res => {
        this.token = res.data.result.token;
        this.tenant_info = res.data.result.tenant_info;
      });
    },
    addImagePreView () {
      this.dataList.forEach((item, dataListIndex) => {
        if (item.originalAnswerContent) {
          const imgReg = /<img.*?(?:>|\/>)/gi
          const srcReg = /src=['"]?([^'"]*)['"]?/i
          const arr = item.originalAnswerContent.match(imgReg)
          if (arr) {
            this.$set(this.dataList[dataListIndex], 'preview', false)
            this.$set(this.dataList[dataListIndex], 'showViewer', false)
            this.$set(this.dataList[dataListIndex], 'src', [])
            arr.forEach((imgItem, imgIndex) => {
              const src = imgItem.match(srcReg)
              if (src[1]) {
                this.dataList[dataListIndex].preview = true
                this.dataList[dataListIndex].src.push(src[1])
              }
            })
            this.$nextTick(() => {
              const contentElement = document.getElementById('answerContent' + dataListIndex)
              if (contentElement) {
                const imgElements = contentElement.querySelectorAll('img')
                imgElements.forEach((imgElement, imgIndex) => {
                  imgElement.style.cursor = 'pointer'
                  imgElement.addEventListener('click', (e) => {
                    e.stopPropagation()
                    this.imageIndex = imgIndex
                    this.clickHandler(dataListIndex)
                  })
                })
              }
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.right-box-content{
  height: auto;
  width: 100%;
  overflow: hidden;

  .qatitle {
    margin-top: 12px;
    font-size: 18px;
    color: #333333;
    font-style: normal;
    font-weight: 600;
    line-height: 22.4px;
    margin-bottom: 20px;
    display: flex;

    .qdtitle_a {
      margin-left: 12px;
      font-size: 14px;
      color: #999999;
      font-weight: 400;
    }
  }
  .qatitle_border {
    border-bottom: 1px solid #EEE;
    margin-top: 12px;
  }
  .question-box {
    margin-bottom: 14px;
    padding: 0px 10px 10px 10px;
    border-radius: 10px;
    margin-left: 10px;

    img {
      width: 40%;
    }

    .title:hover {
      color: #CF1A1C;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;
    }
    .question_css {
      position: absolute;
      width: 15px;
      height: 16px;
    }
    .title-style {
      color: #222;
      font-size: 16px;
      font-weight: bold;
      display: inline-block;
      overflow: hidden;
      cursor: pointer;
    }
    .quest-list-title {
      margin-left: 22px;
    }
    .quest-list-title .descriptionPosition:not(:last-child) {
      border-right: 1px solid;
    }

    .descriptionPosition {
      margin-left: 9px;
      font-size: 14px;
      line-height: 20px;
      color: #999999;
      padding-right: 10px;
    }

    .answer_css {
      width: 15px;
      position: absolute;
      height: 16px;
    }
  }
  .answer_content {
    color: #333333;
    font-size: 16px;
    display: inline-block;
    margin-left: 25px;
    margin-top: 0;
    :deep(p) {
      line-height: 22px;
      white-space: pre-wrap;
    }
    :deep(p a) {
      color: #CF1A1C;
    }

    :deep(img) {
      height: 90px!important;
      cursor: default!important;
    }
  }
  .more-style {
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    color: #CF1A1C;
    margin: 0px 0px 36px 22px;
  }

  .more-style:hover {
    color: #CF1A1C;
  }
}
</style>
