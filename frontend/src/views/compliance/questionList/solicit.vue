<template>
  <div v-if="dataList.length >0" id="infoList06" v-scroll="handleScroll" class="right-box-content">
    <div ref="myDiv" class="zhengqiu_title">
      <img style="width: 14px;height: 14px;margin-right: 8px;" src="../../../assets/images/wenwen/zhengqiu.png">征求情况 <span class="zhengqiu_title_span">关联结果共{{ dataList.length }}条</span>
    </div>
    <div v-for="(item,i) in dataList" :key="i" style="position: relative">
      <div v-if="moreThen ? (!moreFlagzhengQiu) && i<3 || moreFlagzhengQiu : i<dataList.length">
        <div class="laws-box" v-if="item['lawXmlList'] == null && item['lawsId'] != null">
          <div v-if="item['titleId']" style="position: relative">
            <div class="laws-name-row">
              <div class="title-style" @click="turnLaws(item['lawsId'],item['parentId'])">
                《{{ item['lawsName'] }}》
              </div>
              <div style="display: inline-block"
                   v-if="item['lawShowFlagNew'] === '1' || item['lawShowFlagNew'] === '4'">
                <span style="color: #E64BFF">(待生效)</span>
              </div>
              <div class="laws-tag" v-if="item.zhengQiuList != null && item.zhengQiuList.length > 0">
                <img alt="" v-for="(zhengQiu,ii) in item.zhengQiuList" :key="ii"
                     src="@/assets/images/wenwen/img-beizhengqiu.svg"
                     class="imgLawzhengqiu"
                     :title="zhengQiu['lawsName']"
                     @click="turnLaws(zhengQiu.opinionsLawId, '')"
                />
              </div>
              <div class="laws-tag" v-if="item.zhengQiuGaoFlag != null && item.zhengQiuGaoFlag === '1'">
                <img alt="" src="@/assets/images/wenwen/img-zhengqiugao.svg" class="imgLawzhengqiugao"/>
              </div>
            </div>
            <div style="display: flex;margin-left: 2px;margin-top: 4px;margin-bottom: 14px;font-size: 14px;color: #999999">
              <div>颁布日期：<span style="color: #444444">{{ item['published'] }}</span></div>
              <div style="margin-left: 24px;" class="hidden">发文单位：<span style="color: #444444;"
                                                                            :title="item['lawSourceName']">{{
                  item['lawSourceName']
                }}</span>
              </div>
              <div style="margin-left: 24px;">适用范围：<span style="color: #444444">{{
                  item['lawsScopesShowName']
                }}</span></div>
            </div>
            <div class="name-style" style="color: #222222;font-size: 16px; ">
              {{ item['chapterName'] }}&nbsp;{{ item['sectionName'] }}&nbsp;{{ item['itemName'] }}
              <span v-if="item['evolution_status'] === '1'" class="yange_css"
                    @click="yangeClick(item['lawsId'],item['parentId'])">沿革</span>
            </div>
            <div class="content-style" style="color: #444444" v-html="getNameStr(item.title,200)"></div>
          </div>
          <div v-else style="position: relative">
            <div class="title-style" @click="turnLaws(item['lawsId'],item['parentId'])">《{{
                item['lawsName']
              }}》
            </div>
            <div v-if="item['lawShowFlagNew'] === '1' || item['lawShowFlagNew'] === '4'">
              <span style="color: #E64BFF">(待生效)</span>
            </div>
            <div class="content-style" style="color: #999999">
              <div>颁布日期：<span style="color: #444444">{{ item['published'] }}</span></div>
              <div class="hidden">发文单位：<span style="color: #444444;"
                                                 :title="item['lawSourceName']">{{ item['lawSourceName'] }}</span>
              </div>
              <div>适用范围：<span style="color: #444444">{{ item['lawsScopesShowName'] }}</span></div>
            </div>
          </div>
        </div>
        <div class="laws-box" v-else>
          <div style="position: relative">
            <div class="laws-name-row">
              <div class="title-style" @click="turnLaws(item['lawsId'],item['lawXmlList'][0].id)">
                《{{ item['lawsName'] }}》
              </div>
              <div style="display: inline-block"
                   v-if="item['lawShowFlagNew'] === '1' || item['lawShowFlagNew'] === '4'">
                <span style="color: #E64BFF">(待生效)</span>
              </div>
              <div class="laws-tag">
                <img alt="" src="@/assets/images/wenwen/img-zhengqiugao.svg" class="imgLawzhengqiugao"/>
              </div>
            </div>
            <div style="display: flex;margin-left: 2px;margin-top: 4px;margin-bottom: 14px;font-size: 14px;color: #999999">
              <div>颁布日期：<span style="color: #444444">{{ item['published'] }}</span></div>
              <div style="margin-left: 24px;" class="hidden">发文单位：<span style="color: #444444;"
                                                                            :title="item['lawSource']">{{
                  item['lawSource']
                }}</span></div>
              <div style="margin-left: 24px;">适用范围：<span
                style="color: #444444">{{ item['lawsScopesShowName'] }}</span></div>
            </div>
            <div class="name-style" style="color: #222222;font-size: 16px ">{{
                item['lawXmlList'][0].title
              }}&nbsp;
            </div>
            <div v-if="item['lawXmlList'][0].content != null" class="content-style" style="color: #444444"
                 v-html="replaceNameStr(item['lawXmlList'][0].content,200)"></div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="moreThen && dataList.length > 3" class="more-style" @click="moreFlagzhengQiu = !moreFlagzhengQiu">
      {{ !moreFlagzhengQiu ? '查看更多' : '收起' }}
      <i :class="{'el-icon-arrow-down': !moreFlagzhengQiu, 'el-icon-arrow-up': moreFlagzhengQiu}"></i>
    </div>
  </div>
</template>

<script lang="js">
import Vue from "vue";
import {mapGetters} from "vuex";
import {getTenantInfo} from "@/api/chat";

Vue.directive('scroll', {
  inserted: function (el) {
    el.scroll()
  }
})
export default {
  name: 'solicit',
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    isShowZhengqiu: Boolean
  },
  data () {
    return {
      token: '',
      tenant_info: '',
      moreFlagzhengQiu: false
    }
  },
  computed: {
    ...mapGetters(['moreThen'])
  },
  mounted() {
    this.getTenantInfo();
  },
  methods: {
    turnLaws (lawsId, parentId) {
      const lawUrl = 'https://services.easy-board.com.cn/ui/laws/laws/lawsDetail?lawId=' + lawsId + '&lawItemId=' + parentId + '&access_token=' +
        this.token + '&hasLawsApplyModule=1,4,16,6&lawsApplyModule=all&&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(lawUrl)
    },
    yangeClick (lawsId, parentId) {
      const lawUrl = 'https://services.easy-board.com.cn/ui/laws/laws/lawsEvolutionTab/lawsEvolution?allModule=1,4,16,6&lawItemId=' + parentId + '&lawId=' + lawsId + '&access_token=' +
        this.token + '&lawsApplyModule=1&fromFlag=1,0,0,0&more=0&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(lawUrl)
    },
    getNameStr (str, num) {
      if (str.length > num) {
        return str.substring(0, num) + '...'
      } else {
        return str
      }
    },
    replaceNameStr (str, num) {
      str = str.replace(/<[^>]+>/g, '')
      if (str.length > num) {
        return str.substring(0, num) + '...'
      } else {
        return str
      }
    },
    handleScroll () {
      this.$emit('handleScroll')
    },
    getTenantInfo () {
      getTenantInfo().then(res => {
        this.token = res.data.result.token;
        this.tenant_info = res.data.result.tenant_info;
      });
    }
  }
}
</script>

<style scoped  lang="scss">
.right-box-content {
  height: auto;
  width: 100%;
  overflow: hidden;

  .zhengqiu_title {
    margin-top: 12px;
    color: #333333;
    font-style: normal;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    font-weight: bold;
  }

  .zhengqiu_title_span {
    margin-left: 8px;
    font-size: 12px;
    color: #666666;
    font-weight: 400;
  }

  .laws-box {
    margin-bottom: 6px;
    margin-left: 10px;
    padding: 0px 10px 10px 10px;
    border-radius: 10px;

    img {
      width: 100%;
    }

    .laws-name-row {
      display: inline-block;
      width: 100%;
    }

    .laws-tag {
      display: inline-block;
    }

    .imgLawzhengqiu {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: pointer;
      margin-left: 3px;
    }

    .imgLawzhengqiugao {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: default;
      margin-left: 3px;
    }

    .title-style {
      color: #222;
      font-size: 16px;
      font-weight: 500;
      display: inline-block;
      overflow: hidden;
      cursor: pointer;
    }

    .title-style:hover {
      color: #CF1A1C;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;

      .yange_css {
        border: 1px solid #CF1A1C;
        background-color: #CF1A1C;
        border-radius: 4px;
        color: #ffffff;
        font-size: 12px;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 2px;
      }
    }

    .content-style {
      color: #777777;
      white-space: pre-line;
      font-size: 14px;
      line-height: 24px;

    }

    .content-style_v {
      color: #222;
      white-space: pre-line;
      font-size: 13px;
      line-height: 24px;
    }

    .hidden {
      width: 200px;
      display: -webkit-box; //对象作为弹性伸缩盒子模型显示
      overflow: hidden; //溢出隐藏
      -webkit-box-orient: vertical; //设置伸缩盒子对象的子元素的排列方式
      -webkit-line-clamp: 1; //设置 块元素包含的文本行数
    }
  }

  .more-style {
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    color: #CF1A1C;
    margin: 0px 0px 36px 22px;
  }

  .more-style:hover {
    color: #CF1A1C;
  }
}
</style>
