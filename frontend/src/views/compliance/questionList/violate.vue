<template>
  <div v-if="dataList.length > 0" id="infoList02" v-scroll="handleScroll" class="right-box-content">
    <div class="qatitle">
      <img style="width: 25px;height: 25px;margin-right: 11px;" src="../../../assets/images/wenwen/weigui.png">违规案例<span class="qdtitle_a">关联结果共{{ dataList.length }}条</span>
    </div>
    <div class="qatitle_border" style="margin-bottom: 24px;"></div>
    <div v-for="(item,i) in dataList" :key="i" style="position: relative">
      <div v-if="moreThen ? (!moreFlagVi) && i<3 || moreFlagVi : i<dataList.length ">
        <div class="laws-box">
          <div style="position: relative;display: flex;margin-top: 6px;margin-bottom: 6px;">
            <div class="title-style" style="display:inline-block;margin-left: 3px;"
                 @click="violateDetail(item['violateId'])" v-html="item.title"></div>
          </div>
          <div style="position: relative;display: flex;">
                    <span v-if="getValue(item['companyCode']) !== ''" class="content_violate_title"
                          style="top:3px;position: relative;">{{ item['companyCode'] }}</span>
            <span class="content_violate_title" style="top:3px;position: relative;">{{ item['companyName'] }}</span>
            <span class="content_violate">公告时间：
                <span class="content-style_v">{{ item['eventTimeStr'] }}</span></span>
            <div class="content_violate"> 处理人：
              <span class="content-style_v">{{ getNameStr(item['punishOrg'], 15) }}</span>
            </div>
          </div>
          <div style="position: relative;display: flex;">
                    <span class="content_violate">处罚类型：
                      <span :title="item['punishTypeLabel']"
                            class="content-style_v">{{ getNameStr(item['punishTypeLabel'], 30) }}</span>
                    </span>
            <span class="content_violate">违规类型：
                      <span :title="item['violateTypeLabel']"
                            class="content-style_v">{{ getNameStr(item['violateTypeLabel'], 30) }}</span>
                    </span>
          </div>
          <div style="position: relative;display: flex;line-height: 25px;margin-top: 10px;">
            <div v-if="item['processInfo'] && item['processInfo']['backList'] && item['processInfo']['backList'].length >0">
              <div style="color: #222222">一、案例背景</div>
              <div style="color: #444444"
                   v-html="getNameStr(item['processInfo']['backList'][0].textContent, 300)"></div>
            </div>
            <div v-else>
              <div v-if="item['processInfo']  && item['processInfo']['caseList']">
                <div style="color: #222222">一、违规事项</div>
                <div style="color: #444444"
                     v-html="getNameStr(item['processInfo']['caseList'].textContent, 300)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="moreThen && dataList.length > 3" class="more-style"
         @click="moreFlagVi = !moreFlagVi">
      {{ !moreFlagVi ? '查看更多' : '收起' }}
      <i :class="{'el-icon-arrow-down': !moreFlagVi, 'el-icon-arrow-up': moreFlagVi}"></i>
    </div>
  </div>
</template>

<script lang="js">
import Vue from "vue";
import {mapGetters} from "vuex";
import {getTenantInfo} from "@/api/chat";
Vue.directive('scroll', {
  inserted: function (el) {
    el.scroll()
  }
})
export default {
  name: 'violate',
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapGetters(['moreThen'])
  },
  data () {
    return {
      token: '',
      tenant_info: '',
      moreFlagVi: false
    }
  },
  mounted() {
    this.getTenantInfo();
  },
  methods: {
    violateDetail (id) {
      const str = 'https://services.easy-board.com.cn/ui/violation/newViolation/newViolationDetail?id=' + id + '&queryKeyWords=&violateApplyModule=All&allModule=01,02,03,04,05,06,08,13,14&allModuleFlag=1&access_token=' + this.token + '&tenant_info=' + this.tenant_info + '&platform=fromzxjt'
      window.open(str)
    },
    handleScroll (e) {
      this.$emit('handleScroll', e)
    },
    getNameStr (str, num) {
      if (str && str.length > num) {
        return str.substring(0, num) + '...'
      } else {
        return str
      }
    },
    getValue (str) {
      if (str && str.length > 0) {
        return str + '：'
      } else {
        return ''
      }
    },
    getTenantInfo () {
      getTenantInfo().then(res => {
        this.token = res.data.result.token;
        this.tenant_info = res.data.result.tenant_info;
      });
    }
  }
}
</script>

<style scoped lang="scss">
.right-box-content {
  height: auto;
  width: 100%;
  overflow: hidden;

  .qatitle {
    margin-top: 12px;
    font-size: 18px;
    color: #333333;
    font-style: normal;
    font-weight: 600;
    line-height: 22.4px;
    margin-bottom: 20px;
    display: flex;

    .qdtitle_a {
      margin-left: 12px;
      font-size: 14px;
      color: #999999;
      font-weight: 400;
    }
  }

  .qatitle_border {
    border-bottom: 1px solid #EEE;
    margin-top: 12px;
  }

  .laws-box {
    margin-bottom: 6px;
    margin-left: 10px;
    padding: 0px 10px 10px 10px;
    border-radius: 10px;

    img {
      width: 100%;
    }

    .laws-name-row {
      display: inline-block;
      width: 100%;
    }

    .laws-tag {
      display: inline-block;
    }

    .imgLawzhengqiu {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: pointer;
      margin-left: 3px;
    }

    .imgLawzhengqiugao {
      height: 14px;
      width: 40px;
      margin-bottom: 3px;
      cursor: default;
      margin-left: 3px;
    }

    .title-style {
      color: #222;
      font-size: 16px;
      font-weight: bold;
      display: inline-block;
      overflow: hidden;
      cursor: pointer;
    }

    .title-style:hover {
      color: #CF1A1C;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;

      .yange_css {
        border: 1px solid #CF1A1C;
        background-color: #CF1A1C;
        border-radius: 4px;
        color: #ffffff;
        font-size: 12px;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 2px;
      }
    }

    .content-style {
      color: #777777;
      white-space: pre-line;
      font-size: 14px;
      margin-top: 10px;
      line-height: 24px;
    }

    .content-style_v {
      color: #222;
      white-space: pre-line;
      font-size: 13px;
      margin-top: 10px;
      line-height: 24px;
    }

    .hidden {
      width: 200px;
      display: -webkit-box; //对象作为弹性伸缩盒子模型显示
      overflow: hidden; //溢出隐藏
      -webkit-box-orient: vertical; //设置伸缩盒子对象的子元素的排列方式
      -webkit-line-clamp: 1; //设置 块元素包含的文本行数
    }

    .content_violate_title {
      margin-left: 3px;
      font-size: 13px;
      line-height: 20px;
      padding-right: 10px;
      display: inline-block;
      margin-left: 3px;
      color: #222;
    }

    .content_violate {
      margin-left: 3px;
      font-size: 13px;
      line-height: 20px;
      padding-right: 10px;
      display: inline-block;
      margin-left: 3px;
      color: #999;
    }
  }

  .question-box {
    margin-bottom: 14px;
    padding: 0px 10px 10px 10px;
    border-radius: 10px;
    margin-left: 10px;

    img {
      width: 40%;
    }

    .title:hover {
      color: #CF1A1C;
    }

    .name-style {
      margin-top: 10px;
      font-size: 16px;
    }
  }

  .more-style {
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    color: #CF1A1C;
    margin: 0px 0px 36px 22px;
  }

  .more-style:hover {
    color: #CF1A1C;
  }
}
</style>
