
export function _createPassword (str) {
  let code = '';
  code += getRandomLower();
  code += getRandomUpper();
  code += getRandomNumber();
  code += getRandomSymbol();
  for (var i = 0; i < 6; i++) {
    var type = getRandom(1, 4)
    switch (type) {
      case 1:
        code += getRandomNumber()// 数字
        break
      case 2:
        code += getRandomUpper()// 大写字母
        break
      case 3:
        code += getRandomLower()// 小写字母
        break
      case 4:
        code += getRandomSymbol()// 特殊字符
        break
    }
  }
  return code
}

function getRandom (min, max) {
  return Math.round(Math.random() * (max - min) + min)
}

// 随机小写
function getRandomLower () {
  return String.fromCharCode(Math.floor(Math.random() * 26) + 97);
}
// 随机大写
function getRandomUpper () {
  return String.fromCharCode(Math.floor(Math.random() * 26) + 65);
}
// 随机数字
function getRandomNumber () {
  return +String.fromCharCode(Math.floor(Math.random() * 10) + 48);
}
// 随机符号
function getRandomSymbol () {
  const symbols = '!@#$%^&*(){}[]=<>';
  return symbols[Math.floor(Math.random() * symbols.length)];
}
