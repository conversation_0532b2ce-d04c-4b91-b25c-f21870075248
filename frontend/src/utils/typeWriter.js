export class TypeWriterQueue {
    constructor(options = {}) {
        this.queue = [];
        this.isTyping = false;
        this.defaultOptions = {
            fun: (text) => {},
            speed: 20,
            delayBetween: 20,
            cursor: '|',
            cursorSpeed: 200,
            ...options
        };

        // 光标闪烁效果
        this.cursorInterval = null;
        this.showCursor = true;
    }

    // 添加到队列
    addToQueue(text, options = {}) {
        const mergedOptions = { ...this.defaultOptions, ...options };
        this.queue.push({ text, options: mergedOptions });

        if (!this.isTyping) {
            this.processQueue();
        }

        return this; // 支持链式调用
    }

    // 处理队列
    processQueue() {
        if (this.queue.length === 0) {
            this.isTyping = false;
            // this.stopCursor();
            return;
        }

        this.isTyping = true;
        const { text, options } = this.queue.shift();

        // 开始光标闪烁
        // this.startCursor(options);

        // 实际打字效果
        this.typeText(text, options).then(() => {
            // 打字完成后延迟一段时间再处理下一个
            setTimeout(() => {
                this.processQueue();
            }, options.delayBetween);
        });
    }

    // 打字效果实现
    typeText(text, options) {
        return new Promise((resolve) => {
            let i = 0;
            const typing = () => {
                if (i < text.length) {
                    options.fun(text.substring(i, i + 1));
                    // if (this.showCursor) {
                    //     options.element += options.cursor;
                    // }
                    i++;
                    setTimeout(typing, options.speed);
                } else {
                    resolve();
                }
            };
            typing();
        });
    }

    // 开始光标闪烁
    // startCursor(options) {
    //     this.stopCursor(); // 先停止已有的
    //     this.cursorInterval = setInterval(() => {
    //         this.showCursor = !this.showCursor;
    //         const currentText = options.element.replace(new RegExp(`${options.cursor}$`), '');
    //         options.element = currentText + (this.showCursor ? options.cursor : '');
    //     }, options.cursorSpeed);
    // }

    // 停止光标闪烁
    // stopCursor() {
    //     if (this.cursorInterval) {
    //         clearInterval(this.cursorInterval);
    //         this.cursorInterval = null;
    //     }
    // }

    // 清空队列
    clearQueue() {
        this.queue = [];
        this.isTyping = false;
        // this.stopCursor();
    }
}
