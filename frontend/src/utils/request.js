var showInvalidFlag = true; // 是否显示登录失效弹窗
module.exports =
    /******/ (function (modules) { // webpackBootstrap
    /******/ 	// The module cache
    /******/
    var installedModules = {};
    /******/
    /******/ 	// The require function
    /******/
    function __webpack_require__(moduleId) {
        /******/
        /******/ 		// Check if module is in cache
        /******/
        if (installedModules[moduleId]) {
            /******/
            return installedModules[moduleId].exports;
            /******/
        }
        /******/ 		// Create a new module (and put it into the cache)
        /******/
        var module = installedModules[moduleId] = {
            /******/            i: moduleId,
            /******/            l: false,
            /******/            exports: {}
            /******/
        };
        /******/
        /******/ 		// Execute the module function
        /******/
        modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
        /******/
        /******/ 		// Flag the module as loaded
        /******/
        module.l = true;
        /******/
        /******/ 		// Return the exports of the module
        /******/
        return module.exports;
        /******/
    }

    /******/
    /******/
    /******/ 	// expose the modules object (__webpack_modules__)
    /******/
    __webpack_require__.m = modules;
    /******/
    /******/ 	// expose the module cache
    /******/
    __webpack_require__.c = installedModules;
    /******/
    /******/ 	// define getter function for harmony exports
    /******/
    __webpack_require__.d = function (exports, name, getter) {
        /******/
        if (!__webpack_require__.o(exports, name)) {
            /******/
            Object.defineProperty(exports, name, {enumerable: true, get: getter});
            /******/
        }
        /******/
    };
    /******/
    /******/ 	// define __esModule on exports
    /******/
    __webpack_require__.r = function (exports) {
        /******/
        if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
            /******/
            Object.defineProperty(exports, Symbol.toStringTag, {value: 'Module'});
            /******/
        }
        /******/
        Object.defineProperty(exports, '__esModule', {value: true});
        /******/
    };
    /******/
    /******/ 	// create a fake namespace object
    /******/ 	// mode & 1: value is a module id, require it
    /******/ 	// mode & 2: merge all properties of value into the ns
    /******/ 	// mode & 4: return value when already ns object
    /******/ 	// mode & 8|1: behave like require
    /******/
    __webpack_require__.t = function (value, mode) {
        /******/
        if (mode & 1) value = __webpack_require__(value);
        /******/
        if (mode & 8) return value;
        /******/
        if ((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
        /******/
        var ns = Object.create(null);
        /******/
        __webpack_require__.r(ns);
        /******/
        Object.defineProperty(ns, 'default', {enumerable: true, value: value});
        /******/
        if (mode & 2 && typeof value !== 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {
            return value[key];
        }.bind(null, key));
        /******/
        return ns;
        /******/
    };
    /******/
    /******/ 	// getDefaultExport function for compatibility with non-harmony modules
    /******/
    __webpack_require__.n = function (module) {
        /******/
        var getter = module && module.__esModule
            /******/ ? function getDefault() {
                return module['default'];
            }
            /******/ : function getModuleExports() {
                return module;
            };
        /******/
        __webpack_require__.d(getter, 'a', getter);
        /******/
        return getter;
        /******/
    };
    /******/
    /******/ 	// Object.prototype.hasOwnProperty.call
    /******/
    __webpack_require__.o = function (object, property) {
        return Object.prototype.hasOwnProperty.call(object, property);
    };
    /******/
    /******/ 	// __webpack_public_path__
    /******/
    __webpack_require__.p = "/dist/";
    /******/
    /******/
    /******/ 	// Load entry module and return exports
    /******/
    return __webpack_require__(__webpack_require__.s = 39);
    /******/
})
    /************************************************************************/
    /******/ ([
        /* 0 */
        /***/ function (module, exports) {
            module.exports = require("vue");
            /***/
        },
        /* 1 */
        /***/ function (module, __webpack_exports__, __webpack_require__) {
            "use strict";
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "d", function () {
                return getToken;
            });
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "i", function () {
                return setToken;
            });
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "g", function () {
                return setInfo;
            });
            /* unused harmony export getInfo */
            /* unused harmony export removePermissions */
            /* unused harmony export removeToken */
            /* unused harmony export removeRefreshToken */
            /* unused harmony export getRefreshToken */
            /* unused harmony export setRefreshToken */
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "c", function () {
                return getRedirectIndexUrl;
            });
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "h", function () {
                return setRedirectIndexUrl;
            });
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "f", function () {
                return removeRedirectIndexUrl;
            });
            /* unused harmony export setCookieInfo */
            /* unused harmony export getCookieInfo */
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "b", function () {
                return clearAllCookie;
            });
            /* unused harmony export _microUUID */
            /* unused harmony export _microInject */
            /* unused harmony export _triggerMicroEvent */
            /* unused harmony export _isMicroEvent */
            /* unused harmony export _matchMicroEventType */
            /* unused harmony export _matchMicroGuid */
            /* unused harmony export _broadcastMicroModalOpen */
            /* unused harmony export _broadcastMicroModalClose */
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "e", function () {
                return iframeDoMessage;
            });
            /* unused harmony export sendUserBehaviorLog */
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "a", function () {
                return Decrypt;
            });
            /* unused harmony export commonModulePageLog */
            /* harmony import */
            var core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(11);
            /* harmony import */
            var core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_0___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_object_to_string__WEBPACK_IMPORTED_MODULE_0__);
            /* harmony import */
            var core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
            /* harmony import */
            var core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_1___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_1__);
            /* harmony import */
            var core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(12);
            /* harmony import */
            var core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_2___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_2__);
            /* harmony import */
            var core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(23);
            /* harmony import */
            var core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_3___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_string_replace__WEBPACK_IMPORTED_MODULE_3__);
            /* harmony import */
            var js_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(2);
            /* harmony import */
            var js_cookie__WEBPACK_IMPORTED_MODULE_4___default = /* #__PURE__ */__webpack_require__.n(js_cookie__WEBPACK_IMPORTED_MODULE_4__);
            /* harmony import */
            var _store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(3);

            var Authorization = 'access_token_platform';
            var refreshToken = 'refreshToken';
            var PermissionsKey = 'permissions';
            var Info = 'X-Tenant-Info';
            var redirectIndexUrl = 'redirectIndexUrl';

            function getToken() {
                var field = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Authorization;
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.get(field);
            }

            function setToken(token) {
                var field = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Authorization;
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.set(field, token);
            }

            function setInfo(data) {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.set(Info, data);
            }

            function getInfo() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.get(Info);
            }

            function removePermissions() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.remove(PermissionsKey);
            }

            function removeToken() {
                var field = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Authorization;
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.remove(field);
            }

            function removeRefreshToken() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.remove(refreshToken);
            }

            function getRefreshToken() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.get(refreshToken);
            }

            function setRefreshToken(token) {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.set(refreshToken, token);
            }

            function getRedirectIndexUrl() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.get(redirectIndexUrl);
            }

            function setRedirectIndexUrl(url) {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.set(redirectIndexUrl, url);
            }

            function removeRedirectIndexUrl() {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.remove(redirectIndexUrl);
            }

            /**
             * 设置cookie
             *
             * @param key
             *
             * @param value
             *
             */

            function setCookieInfo(key, value) {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.set(key, value);
            }

            /**
             * 获取cookie
             *
             * @param key
             *
             */

            function getCookieInfo(key) {
                return js_cookie__WEBPACK_IMPORTED_MODULE_4___default.a.get(key);
            }

            function clearAllCookie() {
                var field = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Authorization;
                removeToken(field);
                removeRefreshToken();
                removePermissions();
            }

            /**
             * 生成GUID(组件)
             * @returns {string}
             * @private
             */

            function _microUUID() {
                return ((1 + Math.random()) * 0x10000 | 0).toString(16).substring(1);
            }

            /**
             * 是否集成在易董IFRAME下
             * @returns {boolean}
             * @private
             */

            function _microInject() {
                var injected = window.frames.length !== parent.frames.length;

                if (injected) {
                    console.log('MICRO - > _microInject', 'Currently, micro-services have been integrated into the frame');
                } else {
                    console.log('MICRO - > _microInject', 'Currently, micro-services are not integrated into the frame');
                }

                return injected;
            }

            /**
             * 触发微服务事件
             * @param _func
             * @param _param
             * @private
             */

            function _triggerMicroEvent(_func, _param) {
                console.log('MICRO - > _triggerMicroEvent', _func, _param);
                parent.postMessage({
                    type: 'function',
                    func: _func,
                    param: _param
                }, '*');
            }

            /**
             * 是否为Micro Service中触发事件
             * @param e
             * @returns {boolean}
             * @private
             */

            function _isMicroEvent(e) {
                if (!e || !e.data || !e.data.event) {
                    return false;
                }

                if (e.data && e.data.event === 'micro_event') {
                    // 弹窗滚动事件
                    return true;
                }

                return false;
            }

            /**
             * 判断Micro 事件类型
             *
             * @param e
             * @param type  |_micro_dialogScroll|
             * @returns {boolean}
             * @private
             */

            function _matchMicroEventType(e, type) {
                if (!_isMicroEvent(e)) {
                    return false;
                }

                if (e.data.type === type && e.data.param && e.data.param.length === 1) {
                    // 弹窗滚动事件
                    return true;
                }

                return false;
            }

            /**
             * 匹配GUID(组件)
             * @param guid_stack
             * @param guid
             * @returns {boolean}
             * @private
             */

            function _matchMicroGuid(guid_stack, guid) {
                // console.log('MICRO - > _microGuidMatch > guid_stack ', guid_stack,
                // this.VO_UI._GUID);
                if (guid_stack && guid_stack.length > 0) {
                    for (var i = 0; i < guid_stack.length; i++) {
                        if (guid_stack[i] === guid) {
                            return true;
                        }
                    }
                }

                return false;
            }

            /**
             * 广播弹窗打开事件
             * @param _guid
             * @param _lock_screen
             */

            function _broadcastMicroModalOpen(_guid, _lock_screen, _modal) {
                // 通知父窗口 弹窗打开
                if (_microInject()) {
                    _triggerMicroEvent('_micro_dialogOpen', [_guid, _lock_screen, _modal]);
                }
            }

            /**
             * 广播弹窗关闭事件
             * @param _guid
             * @param _lock_screen
             */

            function _broadcastMicroModalClose(_guid) {
                // 通知父窗口 弹窗关闭
                if (_microInject()) {
                    _triggerMicroEvent('_micro_dialogClose', [_guid]);
                }
            }

            /**
             * iframe传值
             *
             * @param win <Dom>
             *        iframe.contentWindow or window.parent 对象
             * @param func <String>
             *        要执行的方法名
             * @param param <Array>
             *        传给方法的参数
             * */

            function iframeDoMessage(win, func, param) {
                win.postMessage({
                    type: 'function',
                    func: func,
                    param: param
                }, '*');
            }

            /**
             * 触发用户行为日志
             *
             * */

            function sendUserBehaviorLog(bhvText, tabName) {
                var logInfo = {}; // 从store中取值

                Object.assign(logInfo, _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo);

                if (!logInfo.isEmbed) {
                    // 从路由上获取moduleId
                    logInfo.moduleId = this.$route.meta.moduleId; // 从路由上获取pageId

                    logInfo.pageId = this.$route.meta.pageId;
                }

                var _logInfo = {
                    appVersion: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.appVersion,
                    bhvText: bhvText,
                    bhvType: '点击',
                    vender: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.vender,
                    moduleId: this.$route.meta.moduleId,
                    pageId: this.$route.meta.pageId,
                    platform: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.platform,
                    tabName: tabName
                };
                _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].commit('SET_LOG_INFO', logInfo);
                var domainName = this.pluginParams.domainName ? this.pluginParams.domainName : this.pluginParams.baseUrl;
                this.$axios.post(domainName + '/log/user/behavior', _logInfo, {
                    headers: {
                        Authorization: this.$store.state.app.token,
                        'X-Tenant-Info': this.$store.state.app.info
                    }
                });
            }

            function Decrypt(data) {
                var sizes = data.length % 13 + 1;
                var addr = data.substring(sizes, sizes + 32);
                var lawst = data.replace(addr, "");
                var key = ut.enc.Utf8.parse(addr.substring(0, 16));
                var iv = ut.enc.Utf8.parse(addr.substring(16, addr.length));
                var decrypt = ut.AES.decrypt(lawst, key, {
                    iv: iv,
                    mode: ut.mode.CBC,
                    padding: ut.pad.Pkcs7
                });
                var decryptedStr = decrypt.toString(ut.enc.Utf8);
                return JSON.parse(decryptedStr.toString());
            }

            function commonModulePageLog(bhvText, moduleId, pageId, tabName) {
                var _logInfo = {
                    appVersion: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.appVersion,
                    bhvText: bhvText,
                    bhvType: '点击',
                    vender: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.vender,
                    moduleId: moduleId,
                    pageId: pageId,
                    platform: _store__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"].state.app.logInfo.platform,
                    tabName: tabName
                };
                var domainName = this.pluginParams.domainName ? this.pluginParams.domainName : this.pluginParams.baseUrl;
                this.$axios.post(domainName + '/log/user/behavior', _logInfo, {
                    headers: {
                        Authorization: this.$store.state.app.token,
                        'X-Tenant-Info': this.$store.state.app.info
                    }
                });
            }

            /***/
        },
        /* 2 */
        /***/ function (module, exports) {
            module.exports = require("js-cookie");
            /***/
        },
        /* 3 */
        /***/ function (module, __webpack_exports__, __webpack_require__) {
            "use strict";

            // EXTERNAL MODULE: external "vue"
            var external_vue_ = __webpack_require__(0);
            var external_vue_default = /* #__PURE__ */__webpack_require__.n(external_vue_);

            // EXTERNAL MODULE: external "vuex"
            var external_vuex_ = __webpack_require__(10);
            var external_vuex_default = /* #__PURE__ */__webpack_require__.n(external_vuex_);

            // CONCATENATED MODULE: ./src/store/module/app.js
            var app = {
                state: {
                    logInfo: {},
                    vender: undefined,
                    productId: undefined
                },
                mutations: {
                    SET_LOG_INFO: function SET_LOG_INFO(state, info) {
                        state.logInfo = info;
                    },
                    SET_PRODUCTION_ID: function SET_PRODUCTION_ID(state, info) {
                        state.vender = info.vender;
                        state.productId = info.fromProductId;
                    }
                }
            };
            /* harmony default export */
            var module_app = (app);
            // EXTERNAL MODULE: external "core-js/modules/es.array.index-of"
            var es_array_index_of_ = __webpack_require__(8);

            // EXTERNAL MODULE: external "core-js/modules/es.array.slice"
            var es_array_slice_ = __webpack_require__(26);

            // EXTERNAL MODULE: external "core-js/modules/es.array.some"
            var es_array_some_ = __webpack_require__(27);

            // EXTERNAL MODULE: external "core-js/modules/es.array.splice"
            var es_array_splice_ = __webpack_require__(28);

            // EXTERNAL MODULE: external "core-js/modules/es.function.name"
            var es_function_name_ = __webpack_require__(7);

            // EXTERNAL MODULE: external "core-js/modules/es.object.to-string"
            var es_object_to_string_ = __webpack_require__(11);

            // EXTERNAL MODULE: external "core-js/modules/web.dom-collections.iterator"
            var web_dom_collections_iterator_ = __webpack_require__(21);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/arrayWithoutHoles"
            var arrayWithoutHoles_ = __webpack_require__(14);
            var arrayWithoutHoles_default = /* #__PURE__ */__webpack_require__.n(arrayWithoutHoles_);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/iterableToArray"
            var iterableToArray_ = __webpack_require__(15);
            var iterableToArray_default = /* #__PURE__ */__webpack_require__.n(iterableToArray_);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/unsupportedIterableToArray"
            var unsupportedIterableToArray_ = __webpack_require__(5);
            var unsupportedIterableToArray_default = /* #__PURE__ */__webpack_require__.n(unsupportedIterableToArray_);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/nonIterableSpread"
            var nonIterableSpread_ = __webpack_require__(16);
            var nonIterableSpread_default = /* #__PURE__ */__webpack_require__.n(nonIterableSpread_);

            // CONCATENATED MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js

            function _toConsumableArray(arr) {
                return arrayWithoutHoles_default()(arr) || iterableToArray_default()(arr) || unsupportedIterableToArray_default()(arr) || nonIterableSpread_default()();
            }

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/arrayWithHoles"
            var arrayWithHoles_ = __webpack_require__(17);
            var arrayWithHoles_default = /* #__PURE__ */__webpack_require__.n(arrayWithHoles_);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/iterableToArrayLimit"
            var iterableToArrayLimit_ = __webpack_require__(18);
            var iterableToArrayLimit_default = /* #__PURE__ */__webpack_require__.n(iterableToArrayLimit_);

            // EXTERNAL MODULE: external "@babel/runtime/helpers/esm/nonIterableRest"
            var nonIterableRest_ = __webpack_require__(19);
            var nonIterableRest_default = /* #__PURE__ */__webpack_require__.n(nonIterableRest_);

            // CONCATENATED MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js

            function _slicedToArray(arr, i) {
                return arrayWithHoles_default()(arr) || iterableToArrayLimit_default()(arr, i) || unsupportedIterableToArray_default()(arr, i) || nonIterableRest_default()();
            }

            // CONCATENATED MODULE: ./node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js

            function _createForOfIteratorHelper(o, allowArrayLike) {
                var it;

                if (typeof Symbol === "undefined" || o[Symbol.iterator] == null) {
                    if (Array.isArray(o) || (it = unsupportedIterableToArray_default()(o)) || allowArrayLike && o && typeof o.length === "number") {
                        if (it) o = it;
                        var i = 0;

                        var F = function F() {
                        };

                        return {
                            s: F,
                            n: function n() {
                                if (i >= o.length) {
                                    return {
                                        done: true
                                    };
                                }
                                return {
                                    done: false,
                                    value: o[i++]
                                };
                            },
                            e: function e(_e) {
                                throw _e;
                            },
                            f: F
                        };
                    }

                    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }

                var normalCompletion = true,
                    didErr = false,
                    err;
                return {
                    s: function s() {
                        it = o[Symbol.iterator]();
                    },
                    n: function n() {
                        var step = it.next();
                        normalCompletion = step.done;
                        return step;
                    },
                    e: function e(_e2) {
                        didErr = true;
                        err = _e2;
                    },
                    f: function f() {
                        try {
                            if (!normalCompletion && it["return"] != null) it["return"]();
                        } finally {
                            if (didErr) throw err;
                        }
                    }
                };
            }

            // CONCATENATED MODULE: ./src/store/module/tagsView.js

            var tagsView = {
                state: {
                    visitedViews: [],
                    cachedViews: []
                },
                mutations: {
                    ADD_VISITED_VIEWS: function ADD_VISITED_VIEWS(state, view) {
                        if (state.visitedViews.some(function (v) {
                            return v.path === view.path;
                        })) return;
                        state.visitedViews.push({
                            name: view.name,
                            path: view.path,
                            title: view.meta.title || 'no-name'
                        });

                        if (!view.meta.noCache) {
                            console.log(view);
                            state.cachedViews.push(view.name);
                        }
                    },
                    DEL_VISITED_VIEWS: function DEL_VISITED_VIEWS(state, view) {
                        var _iterator = _createForOfIteratorHelper(state.visitedViews.entries()),
                            _step;

                        try {
                            for (_iterator.s(); !(_step = _iterator.n()).done;) {
                                var _step$value = _slicedToArray(_step.value, 2),
                                    i = _step$value[0],
                                    v = _step$value[1];

                                if (v.path === view.path) {
                                    state.visitedViews.splice(i, 1);
                                    break;
                                }
                            }
                        } catch (err) {
                            _iterator.e(err);
                        } finally {
                            _iterator.f();
                        }

                        var _iterator2 = _createForOfIteratorHelper(state.cachedViews),
                            _step2;

                        try {
                            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
                                var _i = _step2.value;

                                if (_i === view.name) {
                                    var index = state.cachedViews.indexOf(_i);
                                    state.cachedViews.splice(index, 1);
                                    break;
                                }
                            }
                        } catch (err) {
                            _iterator2.e(err);
                        } finally {
                            _iterator2.f();
                        }
                    },
                    DEL_OTHERS_VIEWS: function DEL_OTHERS_VIEWS(state, view) {
                        var _iterator3 = _createForOfIteratorHelper(state.visitedViews.entries()),
                            _step3;

                        try {
                            for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
                                var _step3$value = _slicedToArray(_step3.value, 2),
                                    i = _step3$value[0],
                                    v = _step3$value[1];

                                if (v.path === view.path) {
                                    state.visitedViews = state.visitedViews.slice(i, i + 1);
                                    break;
                                }
                            }
                        } catch (err) {
                            _iterator3.e(err);
                        } finally {
                            _iterator3.f();
                        }

                        var _iterator4 = _createForOfIteratorHelper(state.cachedViews),
                            _step4;

                        try {
                            for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
                                var _i2 = _step4.value;

                                if (_i2 === view.name) {
                                    var index = state.cachedViews.indexOf(_i2);
                                    state.cachedViews = state.cachedViews.slice(index, _i2 + 1);
                                    break;
                                }
                            }
                        } catch (err) {
                            _iterator4.e(err);
                        } finally {
                            _iterator4.f();
                        }
                    },
                    DEL_ALL_VIEWS: function DEL_ALL_VIEWS(state) {
                        state.visitedViews = [];
                        state.cachedViews = [];
                    }
                },
                actions: {
                    addVisitedViews: function addVisitedViews(_ref, view) {
                        var commit = _ref.commit;
                        commit('ADD_VISITED_VIEWS', view);
                    },
                    delVisitedViews: function delVisitedViews(_ref2, view) {
                        var commit = _ref2.commit,
                            state = _ref2.state;
                        return new Promise(function (resolve) {
                            commit('DEL_VISITED_VIEWS', view);
                            resolve(_toConsumableArray(state.visitedViews));
                        });
                    },
                    delOthersViews: function delOthersViews(_ref3, view) {
                        var commit = _ref3.commit,
                            state = _ref3.state;
                        return new Promise(function (resolve) {
                            commit('DEL_OTHERS_VIEWS', view);
                            resolve(_toConsumableArray(state.visitedViews));
                        });
                    },
                    delAllViews: function delAllViews(_ref4) {
                        var commit = _ref4.commit,
                            state = _ref4.state;
                        return new Promise(function (resolve) {
                            commit('DEL_ALL_VIEWS');
                            resolve(_toConsumableArray(state.visitedViews));
                        });
                    }
                }
            };
            /* harmony default export */
            var module_tagsView = (tagsView);
            // CONCATENATED MODULE: ./src/store/getters.js
            var getters = {
                logInfo: function logInfo(state) {
                    return state.app.logInfo;
                },
                vender: function vender(state) {
                    return state.app.vender;
                },
                productId: function productId(state) {
                    return state.app.productId;
                },
                cachedViews: function cachedViews(state) {
                    return state.tagsView.cachedViews;
                },
                visitedViews: function visitedViews(state) {
                    return state.tagsView.visitedViews;
                }
            };
            /* harmony default export */
            var store_getters = (getters);
            // CONCATENATED MODULE: ./src/store/index.js

            external_vue_default.a.use(external_vuex_default.a);
            var store = new external_vuex_default.a.Store({
                modules: {
                    app: module_app,
                    tagsView: module_tagsView
                },
                getters: store_getters
            });
            /* harmony default export */
            var src_store = __webpack_exports__["a"] = (store);
            /***/
        },
        /* 4 */
        /***/ function (module) {
            module.exports = JSON.parse("{\"zh-CN\":{\"error\":{\"403\":\"没有访问权限\",\"405\":\"请求方法错误\",\"413\":\"请求数据大小超出限制\",\"415\":\"请求数据格式错误\",\"500\":\"服务器错误，请联系客服\",\"502\":\"服务器忙，请稍后再试\",\"504\":\"请求超时，请稍后再试\",\"busy\":\"系统忙，请重新操作\",\"param\":\"请求参数错误\",\"token\":\"token失效\",\"login\":\"登录失效，请重新登录\"},\"info\":{\"invalid\":\"您的账户登录状态已失效，请重新登录\",\"title\":\"信息\",\"sure\":\"确定\"}},\"en\":{\"error\":{\"403\":\"No access rights\",\"405\":\"Request method error\",\"413\":\"Request data size exceeds limit\",\"415\":\"Incorrect format of request data\",\"500\":\"Server error, please contact customer service\",\"502\":\"The server is busy, please try again later\",\"504\":\"Request timed out. Please try again later\",\"busy\":\"The system is busy, please operate again\",\"param\":\"Request parameter error\",\"token\":\"Token invalid\",\"login\":\"Login failed, please login again\"},\"info\":{\"invalid\":\"Your account login status has expired, please login again\",\"title\":\"Information\"}},\"tc\":{\"error\":{\"403\":\"沒有存取權限\",\"405\":\"請求方法錯誤\",\"413\":\"請求數據大小超出限制\",\"415\":\"請求數據格式錯誤\",\"500\":\"服務器錯誤，請聯系客服\",\"502\":\"服務器忙，請稍後再試\",\"504\":\"請求超時，請稍後再試\",\"busy\":\"系統忙，請重新操作\",\"param\":\"請求參數錯誤\",\"token\":\"token失效\",\"login\":\"登入失效，請重新登入\"},\"info\":{\"invalid\":\"您的帳戶登入狀態已失效，請重新登入\",\"title\":\"訊息\",\"sure\":\"確定\"}}}");
            /***/
        },
        /* 5 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/unsupportedIterableToArray");
            /***/
        },
        /* 6 */
        /***/ function (module, exports) {
            module.exports = require("stock-ui");
            /***/
        },
        /* 7 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.function.name");
            /***/
        },
        /* 8 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.array.index-of");
            /***/
        },
        /* 9 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.regexp.exec");
            /***/
        },
        /* 10 */
        /***/ function (module, exports) {
            module.exports = require("vuex");
            /***/
        },
        /* 11 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.object.to-string");
            /***/
        },
        /* 12 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.regexp.to-string");
            /***/
        },
        /* 13 */
        /***/ function (module, __webpack_exports__, __webpack_require__) {
            "use strict";
            /* harmony export (binding) */
            __webpack_require__.d(__webpack_exports__, "a", function () {
                return recursionGetData;
            });
            /* unused harmony export getUrlParam */
            /* harmony import */
            var core_js_modules_es_array_map__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(24);
            /* harmony import */
            var core_js_modules_es_array_map__WEBPACK_IMPORTED_MODULE_0___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_array_map__WEBPACK_IMPORTED_MODULE_0__);
            /* harmony import */
            var core_js_modules_es_regexp_constructor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29);
            /* harmony import */
            var core_js_modules_es_regexp_constructor__WEBPACK_IMPORTED_MODULE_1___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_regexp_constructor__WEBPACK_IMPORTED_MODULE_1__);
            /* harmony import */
            var core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
            /* harmony import */
            var core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_2___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_regexp_exec__WEBPACK_IMPORTED_MODULE_2__);
            /* harmony import */
            var core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(12);
            /* harmony import */
            var core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_3___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_regexp_to_string__WEBPACK_IMPORTED_MODULE_3__);
            /* harmony import */
            var core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(25);
            /* harmony import */
            var core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_string_match__WEBPACK_IMPORTED_MODULE_4__);
            /* harmony import */
            var core_js_modules_es_string_search__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(30);
            /* harmony import */
            var core_js_modules_es_string_search__WEBPACK_IMPORTED_MODULE_5___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_string_search__WEBPACK_IMPORTED_MODULE_5__);

            function recursionGetData(data, field, list) {
                data.map(function (i) {
                    list.push(i);

                    if (i[field] && i[field].length > 0) {
                        recursionGetData(i[field], field, list);
                    }
                });
                return list;
            }

            function getUrlParam(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); // 构造一个含有目标参数的正则表达式对象

                var r = window.location.search.substr(1).match(reg); // 匹配目标参数

                if (r !== null) return unescape(r[2]);
                return null; // 返回参数值
            }

            /***/
        },
        /* 14 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/arrayWithoutHoles");
            /***/
        },
        /* 15 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/iterableToArray");
            /***/
        },
        /* 16 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/nonIterableSpread");
            /***/
        },
        /* 17 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/arrayWithHoles");
            /***/
        },
        /* 18 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/iterableToArrayLimit");
            /***/
        },
        /* 19 */
        /***/ function (module, exports) {
            module.exports = require("@babel/runtime/helpers/esm/nonIterableRest");
            /***/
        },
        /* 20 */
        /***/ function (module, __webpack_exports__, __webpack_require__) {
            "use strict";

            // EXTERNAL MODULE: external "core-js/modules/es.array.index-of"
            var es_array_index_of_ = __webpack_require__(8);

            // EXTERNAL MODULE: external "core-js/modules/es.object.to-string"
            var es_object_to_string_ = __webpack_require__(11);

            // EXTERNAL MODULE: external "core-js/modules/es.string.iterator"
            var es_string_iterator_ = __webpack_require__(34);

            // EXTERNAL MODULE: external "core-js/modules/web.dom-collections.iterator"
            var web_dom_collections_iterator_ = __webpack_require__(21);

            // EXTERNAL MODULE: external "core-js/modules/web.url"
            var web_url_ = __webpack_require__(35);

            // EXTERNAL MODULE: external "vue"
            var external_vue_ = __webpack_require__(0);
            var external_vue_default = /* #__PURE__ */__webpack_require__.n(external_vue_);

            // EXTERNAL MODULE: external "axios"
            var external_axios_ = __webpack_require__(32);
            var external_axios_default = /* #__PURE__ */__webpack_require__.n(external_axios_);

            // EXTERNAL MODULE: ./src/utils/auth.js
            var auth = __webpack_require__(1);

            // EXTERNAL MODULE: external "stock-ui"
            var external_stock_ui_ = __webpack_require__(6);

            // EXTERNAL MODULE: external "lodash"
            var external_lodash_ = __webpack_require__(33);
            var external_lodash_default = /* #__PURE__ */__webpack_require__.n(external_lodash_);

            // EXTERNAL MODULE: ./src/utils/utils.js
            var utils = __webpack_require__(13);

            // CONCATENATED MODULE: ./src/utils/axiosHelperLoading.js

            var needLoadingRequestCount = 0;
            var loading;

            function startLoading() {
                Object(auth["e" /* iframeDoMessage */])(window.parent, 'addLoading', []);
            }

            function endLoading() {
                if (loading) {
                    loading.close();
                }

                Object(auth["e" /* iframeDoMessage */])(window.parent, 'removeLoading', []);
            }

            var tryCloseLoading = function tryCloseLoading() {
                if (needLoadingRequestCount === 0) {
                    endLoading();
                }
            };

            function showFullScreenLoading() {
                if (needLoadingRequestCount === 0) {
                    startLoading();
                }

                needLoadingRequestCount++;
            }

            function tryHideFullScreenLoading() {
                if (needLoadingRequestCount <= 0) return;
                needLoadingRequestCount--;

                if (needLoadingRequestCount === 0) {
                    external_lodash_default.a.debounce(tryCloseLoading, 100)();
                }
            }

            function capitalOperationLoading(lang) {
                var text = '';

                if (lang === 'zh-CN') {
                    text = '给我一点时间';
                } else if (lang === 'tc') {
                    text = '給我一點時間';
                } else {
                    text = 'Give me a little time';
                }

                if (needLoadingRequestCount === 0) {
                    loading = external_stock_ui_["Loading"].service({
                        // text: '加载中……',
                        // background: 'rgba(0, 0, 0, 0.7)'
                        text: text
                    });
                }

                needLoadingRequestCount++;
            }

            // CONCATENATED MODULE: ./src/utils/message.js

            function showMessage(type, message, btn, customClass, center, duration) {
                var _center = center || false;

                var _duration = duration || 1500;

                var injected = window.frames.length !== parent.frames.length; // 判断是否在易董中

                if (injected) {
                    // 在易董中
                    if (type === 'success') {
                        Object(auth["e" /* iframeDoMessage */])(window.parent, 'popMsg', [message]);
                    } else {
                        Object(auth["e" /* iframeDoMessage */])(window.parent.parent, 'popAlert', [message, btn]);
                    }
                } else {
                    // 不在易董中
                    Object(external_stock_ui_["Message"])({
                        type: type,
                        message: message,
                        duration: _duration,
                        showClose: btn,
                        customClass: customClass,
                        center: _center
                    });
                }
            }

            function showConfirm(ConfMsg, titleMsg, _this, confirmCallBack, paraCallBack, cancelCallBack, cancelParaCallBack) {
                var injected = window.frames.length !== parent.frames.length; // 判断是否在易董中

                if (injected) {
                    // 在易董中
                    Object(auth["e" /* iframeDoMessage */])(window.parent.parent, 'popConfirm', [ConfMsg, confirmCallBack, paraCallBack, cancelCallBack, cancelParaCallBack, titleMsg]);
                } else {
                    // 不在易董中
                    external_stock_ui_["MessageBox"].confirm(ConfMsg, titleMsg, {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                        callback: function callback(flag) {
                            if (flag === 'confirm') {
                                _this[confirmCallBack](paraCallBack);
                            } else {
                                if (cancelCallBack != undefined) {
                                    _this[cancelCallBack](cancelParaCallBack);
                                }
                            }
                        }
                    });
                }
            }

            // EXTERNAL MODULE: ./packages/request/src/lang.json
            var src_lang = __webpack_require__(4);

            // CONCATENATED MODULE: ./packages/request/src/index.js

            var Request = external_axios_default.a.create({
                baseURL: '',
                // 修改，引用env profile配置
                timeout: 60000 // request timeout

            });
            Request.interceptors.request.use(function (config) {
                var currentLang = external_vue_default.a.prototype.pluginParams.lang || 'zh-CN';

                if (config.responseType === 'blob') {
                    config.timeout = 180000;
                }

                if (external_vue_default.a.prototype.pluginParams.withCredentials) {
                    config.withCredentials = true;
                } // 设置基本路径

                config.baseURL = external_vue_default.a.prototype.pluginParams.baseUrl; // 设置header的Authorization

                config.headers['Authorization'] = external_vue_default.a.prototype.pluginParams.store.state.app.token; // 如果是微服，设置X-Tenant-Info

                if (external_vue_default.a.prototype.pluginParams.isMicroService) {
                    config.headers['X-Tenant-Info'] = external_vue_default.a.prototype.pluginParams.store.state.app.info;
                } // 传递了isContactUrl参数，需要在路径上添加额外的参数

                if (external_vue_default.a.prototype.pluginParams.isContactUrl) {
                    // 原来得路径上已经有了参数，新添加的参数用“&”连接
                    if (config.url.indexOf('?') >= 0) {
                        config.url = config.url + '&' + external_vue_default.a.prototype.pluginParams.store.state.app[external_vue_default.a.prototype.pluginParams.contactUrlField];
                    } else {
                        // 原来得路径上没有参数，新添加的参数用“？”链接
                        config.url = config.url + '?' + external_vue_default.a.prototype.pluginParams.store.state.app[external_vue_default.a.prototype.pluginParams.contactUrlField];
                    }
                } // 需要在headers上添加额外的信息

                if (external_vue_default.a.prototype.pluginParams.isExtraHeaders && external_vue_default.a.prototype.pluginParams.extraHeadersFields) {
                    for (var i = 0; i < external_vue_default.a.prototype.pluginParams.extraHeadersFields.length; i++) {
                        config.headers[external_vue_default.a.prototype.pluginParams.extraHeadersFields[i]] = external_vue_default.a.prototype.pluginParams.store.state.app[external_vue_default.a.prototype.pluginParams.extraHeadersFields[i]];
                    }
                }

                var injected = window.frames.length !== parent.frames.length; // 判断是否在iframe下

                if (!config.hiddenLoading && config.url !== '/log/collect') {
                    // 是否显示loading
                    if (injected) {
                        // 在iframe下
                        showFullScreenLoading();
                    } else {
                        // 不在iframe下
                        capitalOperationLoading(currentLang);
                    }
                }

                if (external_vue_default.a.prototype.pluginParams.store.state.app.parentCookieFlag) {
                    Object(auth["e" /* iframeDoMessage */])(window.parent, 'microServiceCallBack', [external_vue_default.a.prototype.pluginParams.store.state.app.parentCookie]);
                    external_vue_default.a.prototype.pluginParams.store.commit('SET_PARENT_COOKIE_FLAG', false);
                }

                return config;
            }, function (error) {
                Promise.reject(error);
            });
            Request.interceptors.response.use(function (response) {
                // 获取传递过来的登录路径
                var loginUrl = external_vue_default.a.prototype.pluginParams.loginUrl || '/user/login'; // 当前请求的路径是登录，登录成功之后设置token跟info

                var requestUrl = response.config.baseURL + response.config.url; // 获取传递过来的登录api

                var loginApi = external_vue_default.a.prototype.pluginParams.loginApi; // 判断当前的路径是否是登录

                var flag = requestUrl.indexOf(loginApi) >= 0; // 获取用户信息的api

                var userInfoApi = external_vue_default.a.prototype.pluginParams.userInfoApi || 'user/info'; // 获取登录之后跳转得页面路由

                var IndexUrl = external_vue_default.a.prototype.pluginParams.index || '/index';
                var currentLang = external_vue_default.a.prototype.pluginParams.lang || 'zh-CN';
                var tokenName = external_vue_default.a.prototype.pluginParams.tokenName || 'access_token'; // 如果当前的请求是登录

                if (loginApi && (response.config.url === loginApi || flag) && response.data.success) {
                    // 设置token
                    Object(auth["i" /* setToken */])(response.data.result.accessToken, tokenName);
                    external_vue_default.a.prototype.pluginParams.store.commit('SET_TOKEN', {
                        token: response.data.result.accessToken
                    });
                    Request({
                        url: userInfoApi,
                        method: 'post'
                    }).then(function (res) {
                        if (res.data.success) {
                            // 设置token
                            external_vue_default.a.prototype.pluginParams.store.commit('SET_TOKEN', {
                                token: external_vue_default.a.prototype.pluginParams.store.state.app.token,
                                info: res.data.result
                            }); // 触发菜单权限

                            external_vue_default.a.prototype.saveJurisdictionData(res.data.result); // 路由跳转

                            if (external_vue_default.a.prototype.pluginParams.isRedirectIndexUrl && Object(auth["c" /* getRedirectIndexUrl */])()) {
                                external_vue_default.a.prototype.pluginParams.router.push({
                                    path: Object(auth["c" /* getRedirectIndexUrl */])()
                                });
                                Object(auth["f" /* removeRedirectIndexUrl */])();
                            } else {
                                external_vue_default.a.prototype.pluginParams.router.push({
                                    path: IndexUrl
                                });
                            }
                        }
                    });
                } // 去除全局加载

                if (response.config.url !== '/log/collect') {
                    tryHideFullScreenLoading();
                }

                if (response.config.responseType == 'blob') {
                    if (!response.data) {
                        return;
                    }

                    if (response.data.type.toString() === 'application/json') {
                        // 说明下载的是undefined.txt文件，跳转到登录页面
                        external_vue_default.a.prototype.pluginParams.router.push({
                            path: loginUrl
                        });
                        return;
                    }

                    var blob = new Blob([response.data]);
                    var fileName = decodeURIComponent(response.headers["filename"]);

                    if ('download' in document.createElement('a')) {
                        // 非IE下载
                        var url = window.URL.createObjectURL(blob);
                        var link = document.createElement('a');
                        link.style.display = 'none';
                        link.href = url;
                        link.setAttribute('download', fileName);
                        link.setAttribute('target', '_top');
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link); // 下载完成移除元素

                        window.URL.revokeObjectURL(url); // 释放掉blob对象

                        return;
                    } else {
                        // IE10+下载
                        window.navigator.msSaveOrOpenBlob(blob, fileName);
                        return;
                    }
                }

                if (typeof response.data === 'string' && response.data.length > 0) {
                    response.data = Object(auth["a" /* Decrypt */])(response.data);
                }

                if (response.data.errorCode && (response.data.errorCode === 'error.00005' || response.data.errorCode === 'error.403' || response.data.errorCode === 'error.401')) {
                    // 通过缓存获取orgType, >=4 为上市公司用户（外部用户）, <4 为总部、营业部用户（内部用户）
                    let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
                    // 跳转到登录页面
                    if (showInvalidFlag) {
                        showInvalidFlag = false;
                        external_stock_ui_["MessageBox"].alert('您的账户登录状态已失效，请重新登录', '信息', {
                            confirmButtonText: '确定',
                            callback: function callback() {
                                // 内部用户跳转到cas登录地址
                                if (!isNaN(data.orgType) && Number(data.orgType) < 4) {
                                    let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
                                    let baseUrl =  process.env.VUE_APP_CASURL + "/logout?service=" + process.env.VUE_APP_CASURL + "/login?service=" + url
                                    window.open(baseUrl, "_self");
                                } else {
                                    // 外部用户跳转到平台登录页
                                    external_vue_default.a.prototype.pluginParams.router.push({
                                        path: loginUrl
                                    });
                                }
                            }
                        });
                    }

                    response.data = [];
                    return response;
                } else {
                    return response;
                }
            }, function (error) {
                // 去除全局加载
                tryHideFullScreenLoading();
                var currentLang = external_vue_default.a.prototype.pluginParams.lang || 'zh-CN';
                var tipError = false; // TODO Reservation processing error response

                if (error && error.response) {
                    switch (error.response.status) {
                        case 400:
                            // 请求参数不正确
                            // console.log(error);
                            error.message = src_lang[currentLang].error.param;
                            tipError = true;
                            break;

                        case 401:
                            if (external_vue_default.a.prototype.pluginParams.isMicroService) {
                                error.message = 'token失效';
                                tipError = true;
                            } else {
                                // 未在易董中，跳转到登录页面
                                if (external_vue_default.a.prototype.pluginParams.loginFunc) {
                                    external_vue_default.a.prototype[external_vue_default.a.prototype.pluginParams.loginFunc]();
                                } else {
                                    external_vue_default.a.prototype.pluginParams.router.push({
                                        path: external_vue_default.a.prototype.pluginParams.loginUrl
                                    });
                                }

                                error.message = '登录失效，请重新登录';
                                tipError = true;
                            }
                            return;

                        case 403:
                            // 区分易董非易董，弹出框，跳到403页
                            error.message = src_lang[currentLang].error['403'];
                            tipError = true;
                            break;

                        case 404:
                            tipError = false;
                            break;

                        case 405:
                            error.message = src_lang[currentLang].error['405'];
                            tipError = true;
                            break;

                        case 413:
                            error.message = src_lang[currentLang].error['413'];
                            tipError = true;
                            break;

                        case 414:
                            error.message = src_lang[currentLang].error.param;
                            tipError = true;
                            break;

                        case 415:
                            error.message = src_lang[currentLang].error['415'];
                            tipError = true;
                            break;

                        case 500:
                            error.message = src_lang[currentLang].error['500'];
                            tipError = true;
                            break;

                        case 502:
                            error.message = src_lang[currentLang].error['502'];
                            tipError = true;
                            break;

                        case 503:
                            error.message = src_lang[currentLang].error['502'];
                            tipError = true;
                            break;

                        case 504:
                            error.message = src_lang[currentLang].error['504'];
                            tipError = true;
                            break;

                        default:
                            error.message = error.message = error.response.data.errorCode + '：' + error.response.data.errorMsg; // console.log(error);

                            tipError = true;
                            break;
                    }
                }

                if (tipError) {
                    if (error.message && error.message !== '') {
                        showMessage('error', error.message);
                    }
                }

                return Promise.reject(error);
            });
            /* harmony default export */
            var src = __webpack_exports__["a"] = (Request);
            /***/
        },
        /* 21 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/web.dom-collections.iterator");
            /***/
        }, , /* 22 */
        /* 23 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.string.replace");
            /***/
        },
        /* 24 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.array.map");
            /***/
        },
        /* 25 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.string.match");
            /***/
        },
        /* 26 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.array.slice");
            /***/
        },
        /* 27 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.array.some");
            /***/
        },
        /* 28 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.array.splice");
            /***/
        },
        /* 29 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.regexp.constructor");
            /***/
        },
        /* 30 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.string.search");
            /***/
        }, , /* 31 */
        /* 32 */
        /***/ function (module, exports) {
            module.exports = require("axios");
            /***/
        },
        /* 33 */
        /***/ function (module, exports) {
            module.exports = require("lodash");
            /***/
        },
        /* 34 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/es.string.iterator");
            /***/
        },
        /* 35 */
        /***/ function (module, exports) {
            module.exports = require("core-js/modules/web.url");
            /***/
        }, , , /* 36 */, /* 37 */
        /* 38 */
        /* 39 */
        /***/ function (module, __webpack_exports__, __webpack_require__) {
            "use strict";
            __webpack_require__.r(__webpack_exports__);
            /* harmony import */
            var core_js_modules_es_function_name__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
            /* harmony import */
            var core_js_modules_es_function_name__WEBPACK_IMPORTED_MODULE_0___default = /* #__PURE__ */__webpack_require__.n(core_js_modules_es_function_name__WEBPACK_IMPORTED_MODULE_0__);
            /* harmony import */
            var _src_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(20);

            /* istanbul ignore next */

            _src_index__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].install = function (Vue) {
                Vue.component(_src_index__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].name, _src_index__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"]);
            };

            /* harmony default export */
            __webpack_exports__["default"] = (_src_index__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"]);
            /***/
        }
        /******/]);
