/**
 * Created by ji<PERSON>n<PERSON> on 16/11/18.
 */

export function isvalidUsername (str) {
  // const valid_map = ['admin', 'editor']
  // return valid_map.indexOf(str.trim()) >= 0
  return true
}

/* 合法uri */
export function validateURL (textval) {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlregex.test(textval)
}

/* 小写字母 */
export function validateLowerCase (str) {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/* 大写字母 */
export function validateUpperCase (str) {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/* 大小写字母 */
export function validateAlphabets (str) {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * validate email.
 *
 * @param email
 * @returns {boolean}
 */
export function _validateEmail (email) {
  const re = /^[^@\s]+@(?:[^@\s.]+)(?:\.[^@\s.]+)+$/
  return re.test(email)
}

/**
 * validate cellphone
 * @param cellphone
 * @returns {boolean}
 */
export function _validatePhone (cellphone) {
  const regex = /^(1[3456789][0-9]{9}|(\d{3,4}-)\d{7,8}(-\d{1,4})?)$/
  return regex.test(cellphone)
}

/**
 * validate excel
 * @param fileName
 * @returns {boolean}
 */
export function _validateExcel (fileName) {
  const regex = /(.xls|.xlsx)$/
  return regex.test(fileName.toLowerCase())
}

/**
 * validate word
 * @param fileName
 * @returns {boolean}
 */
export function _validateWord (fileName) {
  const regex = /(.doc|.docx)$/
  return regex.test(fileName.toLowerCase())
}

/**
 * validate integer
 * @param integer
 * @returns {boolean}
 */
export function _validateInteger (integer) {
  // 只能输入整数
  const regex = /^\+?(0|[1-9][0-9]*)$/
  return regex.test(integer)
}

/**
 * validate special
 * @param special
 * @returns {boolean}
 */
export function _validateSpecial (characters) {
  // 不能输入特殊字符
  const regex = /^[^\/\:\*\<\>\?\|\\\"]*$/
  return regex.test(characters)
}

/**
 * validate required
 * @param required
 * @returns {boolean}
 */

export function _validateRequired (value) {
  // 不能输入特殊字符
  const regex = /^\S/
  return regex.test(value)
}

export function _validateVerifyCode (value) {
  // 6位数字验证码
  const regex = /^\d{6}$/
  return regex.test(value)
}

/**
 * 检查密码 6-12位
 * @param value
 * @returns {boolean}
 * @private
 */
export function _validatePassword (value) {

  const num = /^.*[0-9]+.*/
  const low = /^.*[a-z]+.*/
  const up = /^.*[A-Z]+.*/
  const spe = /^.*[^a-zA-Z0-9]+.*/
  const passLength = value.length > 7 && value.length < 33
  // return num.test(value) && low.test(value)  && spe.test(value) && passLength
  return num.test(value) && low.test(value) && up.test(value) && spe.test(value) && passLength

  // 6-12位密码
  //const regex = /^.{6,12}$/
  //return regex.test(value)
}


export function validPassword(value) {
  const num = /^.*[0-9]+.*/
  const low = /^.*[a-z]+.*/
  const up = /^.*[A-Z]+.*/
  const spe = /^.*[^a-zA-Z0-9]+.*/
  const passLength = value.length > 5 && value.length < 21
  return num.test(value) && low.test(value) && up.test(value) && spe.test(value) && passLength
}

/**
 * 检查金额格式 decimal(38,18)
 * @param value
 * @returns {boolean}
 * @private
 */
export function _amountFormatCheck (value) {
  // 金额
  const regex = /^\d{1,20}([.]\d{1,18})$/
  return regex.test(value)
}

/**
 * validate idCardNo
 * @param idCardNo
 * @returns {boolean}
 */
export function _validateIdCardNo(idCardNo) {
  const regex = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return regex.test(idCardNo)
}

/**
 * 字符串和数字
 */
export function _validateCharNumber(charNumber) {
  const regex = /^[A-Za-z0-9]+$/
  return regex.test(charNumber)
}

/**
 * validate money
 * @param money
 * @returns {boolean}
 */
export function _validateMoney(money) {
  // 只能输入数字或小数
  // 小数点后8位
  const regex = /(^[0-9]{1,20}$)|(^[0-9]{1,20}[\.]{1}[0-9]{1,8}$)/
  return regex.test(money)
}
/**
 * validate name
 * @param name
 * @returns {boolean}
 * @private
 */
export function _validateName(name) {
  // 不能包含特殊字符  ^!\s*$%^&*()+|~=`{}[]:/;<>?,.@#'"
  // 可以包含 - _
  // 1 - 30 长度
  const regex = /^[^!\s*$%^&*()+|~=`{}\[\]:/;<>?,.@#'"\\]{1,30}$/
  return regex.test(name)
}

/**
 * validate name
 * @param name
 * @returns {boolean}
 * @private
 */
export function _validateName10(name) {
  // 不能包含特殊字符  ^!\s*$%^&*()+|~=`{}[]:/;<>?,.@#'"
  // 可以包含 - _
  // 1 - 10 长度
  const regex = /^[^!\s*$%^&*()+|~=`{}\[\]:/;<>?,.@#'"\\]{1,10}$/
  return regex.test(name)
}

export function _validateNumEnglish(integer) {
  // 只能输入数字和英文
  const regex = /^[0-9a-zA-Z]+$/
  return regex.test(integer)
}
/**
 * 密码不能包含三个连续字符
 * @param password
 * @private
 */
export function _validatePasswordsSameChar(password) {
  var reg = /(\w)\1\1/g;
  return !reg.test(password);
}
/**
 * 8-20位
 * @param password
 * @returns {boolean}
 * @private
 */
export function _validatePasswordsLength(password) {
  const passwordChar = password.split('')
  return !(passwordChar.length > 20 || passwordChar.length < 8);
}

/**
 *
 * @param password
 * @returns {boolean}
 * @private
 */
export function _validatePasswordsSpace(password) {
  return password.indexOf(" ") === -1;
}

/**
 * 密码不能包含用户名
 * @param password
 * @param userName
 * @private
 */
export function _validatePasswordsUserName(password , userName) {
  return password.indexOf(userName) === -1;
}
export function _validateTelephone(cellphone) {
  // 手机号和座机通用
  const regex = /^(1\d{10}|(\d{3,4}-)\d{7,8}(-\d{1,4})?)$/
  return regex.test(cellphone)
}

/**
 * validate username
 * @param username
 * @returns {boolean}
 * @private
 */
export function _validateUsername(username) {
  // 1 英文字母开头
  // 2 匹配包括 "_" "." 的任何单词字符
  // 3 3 - 18 位
  const regex = /^[\w.]{2,17}$/
  return regex.test(username)
}

export function _validatePasswords(password) {
// 密码必须包含大写英文字母(A - Z)、小写英文字母(a - z)、数字(0 - 9)、特殊符号(!$#%等)；
  var bigChar = 0
  var smallChar = 0
  var numChar = 0
  var specialChar = 0
  const regx1 = /[A-Z]$/
  const regx2 = /[a-z]$/
  const regx3 = /[0-9]$/
  const regx4 = /[`_~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]$/
  const passwordChar = password.split('')
  for (var i = 0; i < passwordChar.length; i++) {
    if (regx1.test(passwordChar[i])) {
      bigChar++
    }
    if (regx2.test(passwordChar[i])) {
      smallChar++
    }
    if (regx3.test(passwordChar[i])) {
      numChar++
    }
    if (regx4.test(passwordChar[i])) {
      specialChar++
    }
  }
  if (bigChar > 0 && smallChar > 0 && numChar > 0 && specialChar > 0) {
    return true
  } else {
    return false
  }
}
export function _validatePrice(price) {
  // 只能输入数字或小数
  // 小数点后四位
  const regex = /(^[0-9]{1,20}$)|(^[0-9]{1,20}[\.]{1}[0-9]{1,4}$)/
  return regex.test(price)

}
