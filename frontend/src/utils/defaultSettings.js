/**
 * 项目默认配置项
 * primaryColor - 默认主题色, 如果修改颜色不生效，请清理 localStorage
 * navTheme - sidebar theme ['dark', 'light'] 两种主题
 * colorWeak - 色盲模式
 * layout - 整体布局方式 ['sidemenu', 'topmenu'] 两种布局
 * fixedHeader - 固定 Header : boolean
 * fixSiderbar - 固定左侧菜单栏 ： boolean
 * autoHideHeader - 向下滚动时，隐藏 Header : boolean
 * contentWidth - 内容区布局： 流式 |  固定
 *
 * storageOptions: {} - Vue-ls 插件配置项 (localStorage/sessionStorage)
 *
 */
import local from 'store'
import store from '@/store'

let defaultSetting = {
  nav_theme: 'light', // theme for nav menu
  color: '#D6001D', // primary color of ant design
  layout: 'topmenu', // nav menu position: `sidemenu` or `topmenu`
  content_width: 'Fixed', // layout of content: `Fluid` or `Fixed`, only works when layout is topmenu
  fixed_header: true, // sticky header
  fixed_sidebar: false // sticky siderbar
}

console.log(local.get('defaultSetting'))
if (local.get('defaultSetting')) {
  updateStore();
} else {
  local.set('defaultSetting', defaultSetting)
  updateStore();
}

function updateStore () {
  let settings = local.get('defaultSetting');
  for (let i in settings) {
    store.commit([i], settings[i])
  }
}
