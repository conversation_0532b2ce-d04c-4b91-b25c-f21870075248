import {reportData} from '@/api/common-api'


export class DataAnalysis {
    constructor() {
        this.menu = ''
        this.tag = ''
        this.event = 'click'
        this.count = 0
    }

    setValue(...args) {
        let data = new DataAnalysis()
        Object.keys(data).forEach(
            (key, index) => data[key] = args[index] === undefined ? data[key] : args[index])
        return data
    }


}

export class Aspect {

    #filter = [
        'BUTTON',"LI"
    ]
    #isInvalid = true

    constructor(el) {
        this.el = el
    }
    before() {
        console.log(this.el)
        if (this.#filter.find(item => item === this.el.tagName) !== undefined) {
            this.#isInvalid = false
            if (this.el.tagName === 'LI') {
                sessionStorage.setItem('menu', this.el.innerText)
            }
        }
    }

    around() {

    }

    after() {
        if (!this.#isInvalid) {
            let data = new DataAnalysis()
            data.menu = sessionStorage.getItem('menu')
            data.tag = this.el.innerText
            this.send(data)
        }
    }

    send(data) {
        // 2023/11/30经讨论后暂时注释
        //reportData(data)
    }




}
