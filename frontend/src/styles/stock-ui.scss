/** el-button */
.el-button:focus, .el-button:hover {
  border-color: $--el-color-primary;
  color: $--el-color-primary;
}
.el-button.el-button--primary {
  background-color: $--el-color-primary;
  border-color: $--el-color-primary;
  color: $--color-white;
}
.el-button.el-button--primary:hover {
  background-color: $--el-color-primary-light-2;
  border-color: $--el-color-primary-light-2;
}
.el-button.el-button--primary.is-plain {
  background-color: $--color-white;
  border-color: $--el-color-primary;
  color: $--el-color-primary;
}
.el-button.el-button--primary.is-plain:hover {
  background-color: $--el-color-primary-light-2;
  border-color: $--el-color-primary-light-2;
  color: $--color-white;
}
.el-button.el-button--default:hover {
  border-color: $--el-color-primary;
  color: $--el-color-primary;
}
.el-button--large {
  font-size: 17px;
}

/** el-checkbox */
.el-checkbox__inner:hover {
  border-color: $--el-color-primary;
}
.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $--color-white;
  border-color: $--el-color-primary;
}
.el-checkbox.is-plain .el-checkbox__inner::after {
  border: 1px solid $--el-color-primary;
  border-left: 0;
  border-top: 0;
}
.el-checkbox__inner::after {
  border: 1px solid $--el-color-primary;
  border-left: 0;
  border-top: 0;
}

/** el-tabs */
.el-tabs__item.is-active, .el-tabs__item:hover {
  color: $--el-color-primary;
}
.el-tabs__active-bar{
  background-color: $--el-color-primary;
}
.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active{
  color: $--el-color-primary;
}
.el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: $--el-color-primary;
}

/** el-link */
.el-link.el-link--primary {
  border-color: $--el-color-primary;
  color: $--el-color-primary;
}
.el-link.el-link--primary:hover {
  color: $--el-color-primary-light-2;
}
.el-link--inner {
  border-bottom: 1px solid $--el-color-primary;
  padding-bottom: .2px;
}

/** el-progress */
.el-progress-bar__inner {
  //background-color: $--el-color-primary;
}

/** el-menu */
.el-menu-item.is-active {
  color: $--el-color-primary;
}

/** el-radio */
.el-radio__input.is-checked .el-radio__inner {
  border-color: $--el-color-primary;
}
.el-radio__inner:hover {
  border-color: $--el-color-primary;
}
.el-radio__inner::after {
  background-color: $--el-color-primary;
}
.el-radio.is-bordered.is-checked {
  border-color: $--el-color-primary;
}
.el-radio-button__inner:hover {
  color: $--el-color-primary;
}
.el-radio-button__orig-radio:checked+.el-radio-button__inner {
  background-color: $--el-color-primary;
  border-color: $--el-color-primary;
  box-shadow: -1px 0 0 0 $--el-color-primary;
}
.el-radio-button.is-gap .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  border: 1px solid $--el-color-primary;
  color: $--el-color-primary;
}
.el-radio-button.is-plain .el-radio-button__orig-radio:checked+.el-radio-button__inner {
  border-color: $--el-color-primary-light-7;
  color: $--el-color-primary;
  background-color: $--el-color-primary-light-9;
  box-shadow: -1px 0 0 0 $--el-color-primary-light-7;
}

/** el-loading */
.el-loading-spinner .path {
  stroke: $--el-color-primary;
}
.el-loading-spinner .el-loading-text {
  color: $--el-color-primary;
}

/** el-switch */
.el-switch.is-checked .el-switch__core {
  background-color: $--el-color-primary;
  border-color: $--el-color-primary;
}

/** el-input*/
.el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: $--el-color-primary;
}
.el-textarea__inner:focus {
  border-color: $--el-color-primary;
}

/** el-input-group */
.el-input-group.is-double-text.is-focus{
  border-color: $--el-color-primary;
  border-radius: 3px;
}
.el-input--medium .el-input__inner:hover{
  border-color: $--el-color-primary;
}
.el-input-group.is-readonly.is-focus, .el-input-group.is-readonly:hover {
  border-color: $--el-color-primary;
}
.el-input-group.is-readonly.is-focus .el-input-group__append button.el-button,
.el-input-group.is-readonly.is-focus .el-input-group__prepend button.el-button,
.el-input-group.is-readonly:hover .el-input-group__append button.el-button,
.el-input-group.is-readonly:hover .el-input-group__prepend button.el-button,
.el-input-group__append button.el-button:hover,
.el-input-group__prepend button.el-button:hover {
  color: $--el-color-primary;
}
.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: $--el-color-primary;
}
/** el-select */
.el-select .el-input.is-focus .el-input__inner{
  border-color: $--el-color-primary;
}
.el-select-region__item.active, .el-select-region__item:hover {
  color: $--el-color-primary;
}
.el-select-dropdown__item.selected{
  color: $--el-color-primary;
}
.el-select-multiple-dropdown .el-select-dropdown__item:not(.is-disabled):hover .el-checkbox__inner{
  border-color: $--el-color-primary;
}
/**el-date-table*/
.el-date-table td.end-date span, .el-date-table td.start-date span{
  background-color: $--el-color-primary;
  border: $--el-color-primary;
}
.el-date-table td.today span{
  border: 1px solid $--el-color-primary;
}

.el-input-number.is-controls-right .el-input-number__handler:hover+div .el-input__inner {
  border-color: $--el-color-primary;
}
.el-date-table td.current:not(.disabled) span{
  background-color: $--el-color-primary;
}

/** el-tree */
.el-tree-search .el-tree-node__label.blue-text {
  color: $--el-color-primary;
}
.el-tree-search__header_item {
  i:hover {
    color: $--el-color-primary;
  }
}
/** el-dialog-tree */
.el-dialog.tree-dialog .el-dialog__header .tree-header .tree-multi-title .tree-title.active {
  border-bottom: 2px solid $--el-color-primary;
  color: $--el-color-primary;
}
.el-dialog.tree-dialog .el-dialog__header .tree-header .tree-multi-title .tree-title:hover {
  color: $--el-color-primary;
}

/**el-main*/
.el-card__body, .el-main {
  padding: 0px!important;
}

/** el-date-picker */
.el-date-picker__header-label:hover {
  color: $--el-color-primary;
}
.el-picker-panel__icon-btn:hover {
  color: $--el-color-primary;
}
// el-year
.el-year-table td.current:not(.disabled) .cell {
  background: $--el-color-primary;
}
.el-year-table td.today .cell{
  color: $--el-color-primary;
}
// el-month
.el-month-table td.current:not(.disabled) .cell {
  background: $--el-color-primary;
}
.el-month-table td.today .cell {
  color: $--el-color-primary;
}
.el-month-table td .cell:hover {
  color: $--el-color-primary;
}
// el-date
.el-date-table td.current:not(.disabled) span {
  background-color: $--el-color-primary;
}
.el-date-table td.today span {
  border-color: $--el-color-primary;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  border-color: #CF1A1C!important;
  background-color: #F4ECEC;
  color: #CF1A1C;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  border-color: #CF1A1C;
  background-color: #F4ECEC;
  color: #CF1A1C;
}