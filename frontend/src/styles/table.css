/* chiweiming add */
.el-table__header thead th {
  background: #f2f2f2 !important;
  padding: 5px !important;
  border:0px;
  color: #525252 !important;
  font-size: 14px;
}

.el-table__header thead th .cell{
  line-height: 18px;
  font-weight: 500;
}

.el-table__header thead th:last-child{
  border-right: 1px solid #e4e4e4 !important;
}

.el-table__body tbody tr td{
  height: 40px !important;
  border-bottom: 1px solid #e4e4e4 !important;
  border-left: 0px solid #e4e4e4 !important;
}
.el-table__body tbody tr td:last-child{
  /*border-right: 1px solid #e4e4e4 !important;*/
}
.el-table__body, .el-table__footer, .el-table__header {
  background-color: #f2f2f2 !important;
  table-layout: fixed;
  border-collapse: separate;
}
.el-table__footer, .el-table__header{
  height: 40px !important;
}

.el-checkbox {
  color: #555;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-right: 0px;
}

.el-table__body .hover-row>td{
  background: rgba(27,146,238,0.05) !important;
}



