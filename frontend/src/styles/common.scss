.el-container {
  min-height: 100vh;
}

html,body{
  margin:0;
  padding:0;
}

body,div,ul,ol,dl,dt,dd,li,dl,h1,h2,h3,h4{
  outline: none;
}
img{
  vertical-align: middle;
}
a{
  text-decoration: none;
}

.main-header{
  background:$--color-white;
  margin:20px 20px 0 20px;
  // margin:-20px -20px 0 -20px;
  // padding:20px;
}

.m-c{
  margin-top:20px;
}

.p-l{
  float:left;
}

.p-r{
  float:right;
}

.m-t-10{
  margin-top:10px;
}

.m-t-20{
  margin-top:20px;
}

.full{
  width:100% !important;
}

.icon-style{
  color:$--el-color-info;
  cursor: pointer;
}

.display-inline-block{
  display:inline-block;
}

.text-center{
  text-align: center;
}

.no-padding-r {
  padding-right: 0!important;
}

.empty-text{
  color:$--el-color-info;
}

.title {
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  /* 灰/#333333 */
  color: #333333;
  padding: 14px 0 16px 0;
}
.title_left--border {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333333;
  padding-bottom: 20px;
  font-weight: bold;
  &:before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: rgba(153,153,153,.4);
    margin-right: 8px;
  }
}

.dialog-footer {
  display: block;
  padding: 10px 16px;
  background-color: white;
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.06);
  >.flex_flex_start {
    font-size: 14px;
    color: #333333;
    float: left;
    >.clear-con {
      color: $--el-color-primary;
      margin-left: 8px;
      &:hover {
        cursor: pointer;
      }
    }
  }
}

.footer {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
}

/* 居中对齐 */
.flex_center {
  display: flex;
  justify-content: center;
  align-items: center;
}
/* 两边对齐 */
.flex_space_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* 两侧的间隔相等 */
.flex_space_around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
/* 左对齐 */
.flex_flex_start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
/* 右对齐 */
.flex_flex_end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
/*文字靠右*/
.text-right{
  text-align:right;
}
/*按钮成功颜色*/
.successColor{
  color: #0FA958;
}

/** 自定义图标按钮 */
.custom-icon-button {
  cursor: pointer;
  margin: 5px;
  font-size: 17px;
  color: #686868;
  span {
    padding-left: 2px;
    font-size: 14px;
    font-weight: 300;
  }
}
.custom-icon-button:hover {
  color: #8e8e8e;
}