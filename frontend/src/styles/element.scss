.el-button.el-button--default:hover{
    background-color: $--el-color-primary !important;
    border-color: $--el-color-primary !important;
    color: $--color-white !important;
}
  .custom-icon-button:hover{
  color:$--el-color-primary;
}
.equ-btn-span:hover{
    color: $--el-color-primary;
    }
.el-select-region__item:hover{
    color: $--el-color-primary !important; 
}
.el-button.el-button--primary.is-plain:hover{
    background-color: $--el-color-primary !important;
    border-color: $--el-color-primary !important;
    color: $--color-white !important;
}
.el-dialog__headerbtn:hover .el-dialog__close {
    color:  $--el-color-primary !important;
}
.repPlanEdit .dialogClass .el-dialog__body{
    padding: 20px 20px 4px !important;
  }
  .repPlanEdit .dialogClass{
    width: 56% !important;
    margin-left: 20% !important;
  }
  @media screen and (max-width: 1440px){
    .repPlanEdit .dialogClass{
        width: 68% !important;
        margin-left: 16% !important;
      }
      .repPlanEdit .title_up_dowm{
        width: 42% !important;

      }
  }
    
  
  .el-dialog.tree-dialog .el-dialog__body .tree-option-box .tree-option-panel .tree-option-group .tree-item.hover .el-checkbox, .el-dialog.tree-dialog .el-dialog__body .tree-search-box .tree-option-panel .tree-option-group .tree-item:hover .el-checkbox{
    color:  $--el-color-primary !important;
    border-color: $--el-color-primary !important;
  }
  .el-dialog.tree-dialog .el-dialog__body .el-checkbox .is-indeterminate .el-checkbox__inner, .el-dialog.tree-dialog .el-dialog__body .el-checkbox.is-checked .el-checkbox__inner{
    background: $--el-color-primary !important;
    border-color: $--el-color-primary !important;
  }
  .el-dialog.tree-dialog .el-dialog__body .tree-box .tree-option-panel .tree-option-group .tree-item.hover .tree-option-group-icon, .el-dialog.tree-dialog .el-dialog__body .tree-box .tree-option-panel .tree-option-group .tree-item.hover .tree-option-group-right{
    color:  $--el-color-primary !important;
  }
.el-dialog.tree-dialog .el-dialog__body .tree-box .tree-option-panel .tree-option-group .tree-item.hover .el-checkbox .el-checkbox__inner, .el-dialog.tree-dialog .el-dialog__body .tree-option-box .tree-option-panel .tree-option-group .tree-item.hover .el-checkbox .el-checkbox__inner, .el-dialog.tree-dialog .el-dialog__body .tree-search-box .tree-option-panel .tree-option-group .tree-item:hover .el-checkbox .el-checkbox__inner{
    border-color: $--el-color-primary !important;
}
.el-dialog.tree-dialog .el-dialog__body .el-checkbox .is-indeterminate, .el-dialog.tree-dialog .el-dialog__body .el-checkbox.is-checked, .el-dialog.tree-dialog .el-dialog__body .el-checkbox:hover, .el-dialog.tree-dialog .el-dialog__body .tree-box .tree-option-panel .tree-option-group .tree-item.hover .el-checkbox{
    color:  $--el-color-primary !important; 
}
.el-dialog.tree-dialog .el-dialog__body .el-checkbox:hover .el-checkbox__inner{
    border-color: $--el-color-primary !important;
}
.el-dialog.tree-dialog .el-dialog__body .el-checkbox .el-checkbox__input.is-focus .el-checkbox__inner{
    border-color: $--el-color-primary !important;
}
.el-dialog.area-dialog .el-dialog__body .el-checkbox .is-indeterminate, .el-dialog.area-dialog .el-dialog__body .el-checkbox .is-indeterminate .el-checkbox__label, .el-dialog.area-dialog .el-dialog__body .el-checkbox.is-checked, .el-dialog.area-dialog .el-dialog__body .el-checkbox.is-checked .el-checkbox__label, .el-dialog.area-dialog .el-dialog__body .el-checkbox:hover{
    color:  $--el-color-primary !important; 
}
.el-dialog.area-dialog .el-dialog__body .el-checkbox .is-indeterminate .el-checkbox__inner, .el-dialog.area-dialog .el-dialog__body .el-checkbox.is-checked .el-checkbox__inner{
    background: $--el-color-primary !important;
    border-color: $--el-color-primary !important;
}
.el-dialog.area-dialog .el-dialog__body .el-checkbox:hover .el-checkbox__inner{
    border-color: $--el-color-primary !important;
}
.el-dialog.area-dialog .el-dialog__body .el-area-selector__option-box .el-area-selector__option-group .el-area-selector__item.hover>.el-checkbox{
    color:  $--el-color-primary !important; 
}
.el-dialog.area-dialog .el-dialog__body .el-area-selector__option-box .el-area-selector__option-group .el-area-selector__item.active>.el-checkbox .el-checkbox__inner, .el-dialog.area-dialog .el-dialog__body .el-area-selector__option-box .el-area-selector__option-group .el-area-selector__item.hover>.el-checkbox .el-checkbox__inner{
    border-color: $--el-color-primary !important;
}
.el-dialog.area-dialog .el-dialog__body .el-area-selector__option-box .el-area-selector__option-group .el-area-selector__item .el-area-selector__item-bar .active, .el-dialog.area-dialog .el-dialog__body .el-area-selector__option-box .el-area-selector__option-group .el-area-selector__item .el-area-selector__item-bar:hover{
    color:  $--el-color-primary !important; 
}
.el-dialog.area-dialog .el-dialog__footer .el-area-selector__result-box .el-area-selector__result-bar .el-area-selector__clear{
    color:  $--el-color-primary !important; 
}
.el-input__inner {
  border-radius: 35px;
}
.el-input-group__prepend {
  border-bottom-left-radius: 35px;
  border-top-left-radius: 35px;
}
.el-textarea__inner {
  border-radius: 10px;
}
.el-button {
  border-radius: 999px;
}