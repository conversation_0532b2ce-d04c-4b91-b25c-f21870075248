
//定义的颜色变量

/* Transition
-------------------------- */
$--all-transition: all .3s cubic-bezier(.645,.045,.355,1) !default;
$--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--fade-linear-transition: opacity 200ms linear !default;
$--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--border-transition-base: border-color .2s cubic-bezier(.645,.045,.355,1) !default;
$--color-transition-base: color .2s cubic-bezier(.645,.045,.355,1) !default;

/* Color
-------------------------- */
/// color|1|Brand Color|0
$--el-color-primary: #D50212 !default;// D6001D
/// color|1|Background Color|4
$--color-white: #FFFFFF !default;
/// color|1|Background Color|4
$--color-black: #000000 !default;
$--el-color-primary-light-1: mix($--color-white, $--el-color-primary, 10%) !default;
$--el-color-primary-light-2: mix($--color-white, $--el-color-primary, 20%) !default;
$--el-color-primary-light-3: mix($--color-white, $--el-color-primary, 30%) !default;
$--el-color-primary-light-4: mix($--color-white, $--el-color-primary, 40%) !default;
$--el-color-primary-light-5: mix($--color-white, $--el-color-primary, 50%) !default;
$--el-color-primary-light-6: mix($--color-white, $--el-color-primary, 60%) !default;
$--el-color-primary-light-7: mix($--color-white, $--el-color-primary, 70%) !default;
$--el-color-primary-light-8: mix($--color-white, $--el-color-primary, 80%) !default;
$--el-color-primary-light-9: mix($--color-white, $--el-color-primary, 90%) !default;
/// color|1|Functional Color|1
$--el-color-success: #14BCF5 !default;
/// color|1|Functional Color|1
$--el-color-warning: #FAAD14 !default;
/// color|1|Functional Color|1
$--el-color-danger: #FE5461 !default;
/// color|1|Functional Color|1
$--el-color-info: #999999 !default;

$--color-success-light: mix($--color-white, $--el-color-success, 80%) !default;
$--color-warning-light: mix($--color-white, $--el-color-warning, 80%) !default;
$--color-danger-light: mix($--color-white, $--el-color-danger, 80%) !default;
$--el-color-info-light: mix($--color-white, $--el-color-info, 80%) !default;

$--el-color-success-lighter: mix($--color-white, $--el-color-success, 90%) !default;
$--el-color-warning-lighter: mix($--color-white, $--el-color-warning, 90%) !default;
$--el-color-danger-lighter: mix($--color-white, $--el-color-danger, 90%) !default;
$--el-color-info-lighter: mix($--color-white, $--el-color-info, 90%) !default;
/// color|1|Font Color|2
$--el-color-text-primary: #333333 !default;
/// color|1|Font Color|2
$--el-color-text-regular: #666666 !default;
/// color|1|Font Color|2
$--el-color-text-secondary: #777777 !default;
/// color|1|Font Color|2
$--el-color-text-auxiliary: #999999 !default;
/// color|1|Font Color|2
$--el-color-text-placeholder: #C1C1C1 !default;
/// color|1|Border Color|3
$--el-border-color-base: #CACACA !default;
/// color|1|Border Color|3
$--el-border-color-light: #D7D7D7 !default;
/// color|1|Border Color|3
$--el-border-color-lighter: #E4E4E4 !default;
/// color|1|Border Color|3
$--el-border-color-extra-light: #EEEEEE !default;
// Background
/// color|1|Background Color|4
$--el-background-color-base: #F5F7FA !default;

/* Link
-------------------------- */
$--link-color: $--el-color-primary-light-2 !default;
$--el-link-hover-color: $--el-color-primary !default;

/* Border
-------------------------- */
$--border-width-base: 1px !default;
$--border-style-base: solid !default;
$--el-border-color-hover: $--el-color-text-placeholder !default;
$--el-border-base: $--border-width-base $--border-style-base $--el-border-color-base !default;
/// borderRadius|1|Radius|0
$--el-border-radius-base: 3px !default;
/// borderRadius|1|Radius|0
$--border-radius-small: 2px !default;
/// borderRadius|1|Radius|0
$--border-radius-circle: 100% !default;
/// borderRadius|1|Radius|0
$--border-radius-zero: 0 !default;

// Box-shadow
/// boxShadow|1|Shadow|1
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04) !default;
// boxShadow|1|Shadow|1
$--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12) !default;
/// boxShadow|1|Shadow|1
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !default;
/// boxShadow|1|Shadow|1
$--el-box-shadow-lighter: 0 3px 6px -4px rgba(0, 0, 0, 0.12) !default;

/* Fill
-------------------------- */
$--fill-base: $--color-white !default;

/* Typography
-------------------------- */
$--font-path: 'fonts' !default;
$--font-display: 'auto' !default;
/// fontSize|1|Font Size|0
$--font-size-extra-large: 20px !default;
/// fontSize|1|Font Size|0
$--font-size-large: 18px !default;
/// fontSize|1|Font Size|0
$--font-size-medium: 16px !default;
/// fontSize|1|Font Size|0
$--font-size-base: 14px !default;
/// fontSize|1|Font Size|0
$--font-size-small: 13px !default;
/// fontSize|1|Font Size|0
$--font-size-extra-small: 12px !default;
/// fontWeight|1|Font Weight|1
$--font-weight-primary: 500 !default;
/// fontWeight|1|Font Weight|1
$--font-weight-secondary: 100 !default;
/// fontLineHeight|1|Line Height|2
$--font-line-height-primary: 24px !default;
/// fontLineHeight|1|Line Height|2
$--font-line-height-secondary: 16px !default;
$--font-color-disabled-base: #bbb !default;
/* Size
-------------------------- */
$--size-base: 14px !default;

/* z-index
-------------------------- */
$--index-normal: 1 !default;
$--index-top: 1000 !default;
$--index-popper: 2000 !default;

/* Disable base
-------------------------- */
$--disabled-fill-base: $--el-background-color-base !default;
$--el-disabled-color-base: $--el-color-text-placeholder !default;
$--el-disabled-border-base: $--el-border-color-lighter !default;

/* Icon
-------------------------- */
$--icon-color: #666 !default;
$--icon-color-base: $--el-color-info !default;

/* Checkbox
-------------------------- */
/// fontSize||Font|1
$--checkbox-font-size: 14px !default;
/// fontWeight||Font|1
$--checkbox-font-weight: $--font-weight-primary !default;
/// color||Color|0
$--el-checkbox-font-color: $--el-color-text-regular !default;
$--checkbox-input-height: 14px !default;
$--checkbox-input-width: 14px !default;
/// borderRadius||Border|2
$--checkbox-border-radius: $--border-radius-small !default;
/// color||Color|0
$--checkbox-background-color: $--color-white !default;
$--el-checkbox-input-border: $--el-border-base !default;

/// color||Color|0
$--el-checkbox-disabled-border-color: $--el-border-color-lighter !default;
$--checkbox-disabled-input-fill: #edf2fc !default;
$--el-checkbox-disabled-icon-color: $--el-color-text-placeholder !default;

$--checkbox-disabled-checked-input-fill: $--el-border-color-extra-light !default;
$--el-checkbox-disabled-checked-input-border-color: $--el-border-color-lighter !default;
$--el-checkbox-disabled-checked-icon-color: mix($--color-white, $--el-color-info, 50%) !default;

/// color||Color|0
$--checkbox-checked-font-color: $--el-color-primary !default;
$--el-checkbox-checked-input-border-color: $--el-color-primary !default;
/// color||Color|0
$--el-checkbox-checked-background-color: $--el-color-primary !default;
$--checkbox-checked-icon-color: $--fill-base !default;

$--el-checkbox-input-border-color-hover: $--el-color-primary !default;
/// height||Other|4
$--el-checkbox-bordered-height: 38px !default;
/// padding||Spacing|3
$--el-checkbox-bordered-padding: 8px 20px 8px 10px !default;
/// padding||Spacing|3
$--el-checkbox-bordered-medium-padding: 7px 20px 7px 10px !default;
/// padding||Spacing|3
$--el-checkbox-bordered-small-padding: 6px 15px 6px 10px !default;
/// padding||Spacing|3
$--el-checkbox-bordered-mini-padding: 4px 15px 4px 10px !default;
$--checkbox-bordered-medium-input-height: 14px !default;
$--checkbox-bordered-medium-input-width: 14px !default;
/// height||Other|4
$--el-checkbox-bordered-medium-height: 34px !default;
$--el-checkbox-bordered-small-input-height: 14px !default;
$--el-checkbox-bordered-small-input-width: 14px !default;
/// height||Other|4
$--el-checkbox-bordered-small-height: 30px !default;
$--checkbox-bordered-mini-input-height: 12px !default;
$--checkbox-bordered-mini-input-width: 12px !default;
/// height||Other|4
$--el-checkbox-bordered-mini-height: 26px !default;

/// color||Color|0
$--el-checkbox-button-checked-background-color: $--el-color-primary !default;
/// color||Color|0
$--checkbox-button-checked-font-color: $--color-white !default;
/// color||Color|0
$--el-checkbox-button-checked-border-color: $--el-color-primary !default;



/* Radio
-------------------------- */
/// fontSize||Font|1
$--radio-font-size: $--font-size-base !default;
/// fontWeight||Font|1
$--radio-font-weight: $--font-weight-primary !default;
/// color||Color|0
$--el-radio-font-color: $--el-color-text-regular !default;
$--radio-input-height: 14px !default;
$--radio-input-width: 14px !default;
/// borderRadius||Border|2
$--radio-input-border-radius: $--border-radius-circle !default;
/// color||Color|0
$--radio-input-background-color: $--color-white !default;
$--el-radio-input-border: $--el-border-base !default;
/// color||Color|0
$--radio-input-border-color: $--el-border-color-base !default;
/// color||Color|0
$--radio-icon-color: $--color-white !default;

$--el-radio-disabled-input-border-color: $--el-disabled-border-base !default;
$--radio-disabled-input-fill: $--disabled-fill-base !default;
$--radio-disabled-icon-color: $--disabled-fill-base !default;

$--radio-disabled-checked-input-border-color: $--el-disabled-border-base !default;
$--radio-disabled-checked-input-fill: $--disabled-fill-base !default;
$--radio-disabled-checked-icon-color: $--el-color-text-placeholder !default;

/// color||Color|0
$--radio-checked-font-color: $--el-color-primary !default;
/// color||Color|0
$--el-radio-checked-input-border-color: $--el-color-primary !default;
/// color||Color|0
$--radio-checked-input-background-color: $--color-white !default;
/// color||Color|0
$--radio-checked-icon-color: $--el-color-primary !default;

$--el-radio-input-border-color-hover: $--el-color-primary !default;

$--el-radio-bordered-height: 38px !default;
$--el-radio-bordered-padding: 11px 20px 0 9px !default;
$--el-radio-bordered-medium-padding: 9px 20px 0 9px !default;
$--el-radio-bordered-small-padding: 7px 15px 0 9px !default;
$--el-radio-bordered-mini-padding: 5px 15px 0 9px !default;
$--radio-bordered-medium-input-height: 14px !default;
$--radio-bordered-medium-input-width: 14px !default;
$--el-radio-bordered-medium-height: 34px !default;
$--radio-bordered-small-input-height: 12px !default;
$--radio-bordered-small-input-width: 12px !default;
$--el-radio-bordered-small-height: 30px !default;
$--radio-bordered-mini-input-height: 12px !default;
$--radio-bordered-mini-input-width: 12px !default;
$--el-radio-bordered-mini-height: 26px !default;

/// fontSize||Font|1
$--radio-button-font-size: $--font-size-base !default;
/// color||Color|0
$--el-radio-button-checked-background-color: $--el-color-primary !default;
/// color||Color|0
$--radio-button-checked-font-color: $--color-white !default;
/// color||Color|0
$--el-radio-button-checked-border-color: $--el-color-primary !default;
$--radio-button-disabled-checked-fill: $--el-border-color-extra-light !default;

/* Select
-------------------------- */
$--el-select-border-color-hover: $--el-border-color-hover !default;
$--el-select-disabled-border: $--el-disabled-border-base !default;
/// fontSize||Font|1
$--select-font-size: $--font-size-base !default;
$--el-select-close-hover-color: $--el-color-text-auxiliary !default;

$--el-select-input-color: $--el-color-text-placeholder !default;
$--select-multiple-input-color: #666 !default;
/// color||Color|0
$--el-select-input-focus-border-color: $--el-color-primary !default;
/// fontSize||Font|1
$--select-input-font-size: 14px !default;

$--el-select-option-color: $--el-color-text-regular !default;
$--el-select-option-disabled-color: $--el-color-text-placeholder !default;
$--select-option-disabled-background: $--color-white !default;
/// height||Other|4
$--el-select-option-height: 32px !default;
$--select-option-hover-background: $--el-background-color-base !default;
/// color||Color|0
$--el-select-option-selected-font-color: $--el-color-primary !default;
$--select-option-selected-hover: $--el-background-color-base !default;

$--select-group-color: $--el-color-info !default;
$--select-group-height: 30px !default;
$--select-group-font-size: 12px !default;

$--select-dropdown-background: $--color-white !default;
$--el-select-dropdown-shadow: $--el-box-shadow-lighter !default;
$--select-dropdown-empty-color: #999 !default;
/// height||Other|4
$--select-dropdown-max-height: 274px !default;
$--el-select-dropdown-padding: 4px 0 !default;
$--select-dropdown-empty-padding: 10px 0 !default;
$--el-select-dropdown-border: solid 1px $--el-border-color-extra-light !default;

/* Alert
-------------------------- */
$--el-alert-padding: 6px 20px !default;
/// borderRadius||Border|2
$--el-alert-border-radius: $--el-border-radius-base !default;
/// fontSize||Font|1
$--el-alert-title-font-size: 12px !default;
/// fontSize||Font|1
$--alert-description-font-size: 12px !default;
/// fontSize||Font|1
$--alert-close-font-size: 12px !default;
/// fontSize||Font|1
$--alert-close-customed-font-size: 13px !default;

$--el-alert-success-color: $--el-color-success-lighter !default;
$--el-alert-info-color: $--el-color-info-lighter !default;
$--el-alert-warning-color: $--el-color-warning-lighter !default;
$--el-alert-danger-color: $--el-color-danger-lighter !default;

/// height||Other|4
$--alert-icon-size: 16px !default;
/// height||Other|4
$--alert-icon-large-size: 28px !default;

/* MessageBox
-------------------------- */
/// color||Color|0
$--el-messagebox-title-color: $--el-color-text-regular !default;
$--msgbox-width: 420px !default;
$--msgbox-border-radius: 4px !default;
/// fontSize||Font|1
$--el-messagebox-font-size: $--font-size-base !default;
/// fontSize||Font|1
$--messagebox-content-font-size: $--font-size-base !default;
/// color||Color|0
$--el-messagebox-content-color: $--el-color-text-auxiliary !default;
/// fontSize||Font|1
$--messagebox-error-font-size: 12px !default;
$--el-msgbox-padding-primary: 24px 24px 10px 24px !default;
/// color||Color|0
$--el-messagebox-success-color: $--el-color-success !default;
/// color||Color|0
$--el-messagebox-info-color: $--el-color-info !default;
/// color||Color|0
$--el-messagebox-warning-color: $--el-color-warning !default;
/// color||Color|0
$--el-messagebox-danger-color: $--el-color-danger !default;

/* Message
-------------------------- */
$--message-shadow: $--box-shadow-base !default;
$--message-min-width: 380px !default;
$--el-message-background-color: #edf2fc !default;
$--el-message-padding: 15px 16px !default;
/// color||Color|0
$--message-close-icon-color: $--el-color-text-placeholder !default;
/// height||Other|4
$--message-close-size: 16px !default;
/// color||Color|0
$--message-close-hover-color: $--el-color-text-secondary !default;

/// color||Color|0
$--el-message-success-font-color: $--el-color-success !default;
/// color||Color|0
$--el-message-info-font-color: $--el-color-info !default;
/// color||Color|0
$--el-message-warning-font-color: $--el-color-warning !default;
/// color||Color|0
$--el-message-danger-font-color: $--el-color-danger !default;

/* Notification
-------------------------- */
$--notification-width: 330px !default;
/// padding||Spacing|3
$--el-notification-padding: 24px !default;
$--notification-radius: 8px !default;
$--notification-shadow: $--box-shadow-light !default;
/// color||Color|0
$--el-notification-border-color: $--el-border-color-lighter !default;
$--notification-icon-size: 24px !default;
$--notification-close-font-size: $--message-close-size !default;
$--notification-group-margin-left: 13px !default;
$--notification-group-margin-right: 8px !default;
/// fontSize||Font|1
$--notification-content-font-size: $--font-size-base !default;
/// color||Color|0
$--el-notification-content-color: $--el-color-text-primary !default;
/// fontSize||Font|1
$--el-notification-title-font-size: 14px !default;
/// color||Color|0
$--el-notification-title-color: $--el-color-text-regular !default;

/// color||Color|0
$--el-notification-close-color: $--el-color-text-secondary !default;
/// color||Color|0
$--el-notification-close-hover-color: $--el-color-text-regular !default;

/// color||Color|0
$--el-notification-success-icon-color: $--el-color-success !default;
/// color||Color|0
$--el-notification-info-icon-color: $--el-color-info !default;
/// color||Color|0
$--el-notification-warning-icon-color: $--el-color-warning !default;
/// color||Color|0
$--el-notification-danger-icon-color: $--el-color-danger !default;

/* Input
-------------------------- */
$--input-font-size: $--font-size-base !default;
/// color||Color|0
$--el-input-font-color: $--el-color-text-primary !default;
/// height||Other|4
$--input-width: 140px !default;
/// height||Other|4
$--el-input-height: 38px !default;
$--el-input-border: $--border-width-base $--border-style-base $--el-border-color-lighter !default;
$--input-border-color: $--el-border-color-base !default;
/// borderRadius||Border|2
$--el-input-border-radius: $--el-border-radius-base !default;
$--input-border-color-hover: $--el-border-color-hover !default;
/// color||Color|0
$--input-background-color: $--color-white !default;
$--input-fill-disabled: $--disabled-fill-base !default;
$--input-color-disabled: $--font-color-disabled-base !default;
/// color||Color|0
$--el-input-icon-color: $--el-color-text-placeholder !default;
/// color||Color|0
$--el-input-placeholder-color: $--el-color-text-placeholder !default;
$--input-max-width: 314px !default;

$--el-input-hover-border: $--el-border-color-hover !default;
$--el-input-clear-hover-color: $--el-color-text-auxiliary !default;
$--el-input-clear-active-color: $--el-color-text-secondary !default;

$--el-input-focus-border: $--el-color-primary !default;
$--input-focus-fill: $--color-white !default;

$--input-disabled-fill: $--disabled-fill-base !default;
$--el-input-disabled-border: $--el-disabled-border-base !default;
$--el-input-disabled-color: $--el-disabled-color-base !default;
$--el-input-disabled-placeholder-color: $--el-color-text-placeholder !default;

/// fontSize||Font|1
$--el-input-medium-font-size: 14px !default;
/// height||Other|4
$--el-input-medium-height: 34px !default;
/// fontSize||Font|1
$--el-input-small-font-size: 14px !default;
/// height||Other|4
$--el-input-small-height: 30px !default;
/// fontSize||Font|1
$--el-input-mini-font-size: 14px !default;
/// height||Other|4
$--el-input-mini-height: 26px !default;

/* Cascader
-------------------------- */
/// color||Color|0
$--el-cascader-menu-font-color: $--el-color-text-regular !default;
/// color||Color|0
$--el-cascader-menu-selected-font-color: $--el-color-primary !default;
$--cascader-menu-fill: $--fill-base !default;
$--cascader-menu-font-size: $--font-size-base !default;
$--el-cascader-menu-radius: $--el-border-radius-base !default;
$--el-cascader-menu-border: solid 1px $--el-border-color-extra-light !default;
$--cascader-menu-shadow: $--box-shadow-light !default;
$--el-cascader-node-background-hover: $--el-background-color-base !default;
$--el-cascader-node-color-disabled:$--el-color-text-placeholder !default;
$--el-cascader-color-empty:$--el-color-text-placeholder !default;
$--el-cascader-tag-background: #f0f2f5;

/* Group
-------------------------- */
$--group-option-flex: 0 0 (1/5) * 100% !default;
$--group-option-offset-bottom: 12px !default;
$--group-option-fill-hover: rgba($--color-black, 0.06) !default;
$--group-title-color: $--color-black !default;
$--group-title-font-size: $--font-size-base !default;
$--group-title-width: 66px !default;

/* Tab
-------------------------- */
$--tab-font-size: $--font-size-base !default;
$--tab-border-line: 1px solid #e4e4e4 !default;
$--tab-header-color-active: $--el-color-text-secondary !default;
$--tab-header-color-hover: $--el-color-text-regular !default;
$--tab-header-color: $--el-color-text-regular !default;
$--tab-header-fill-active: rgba($--color-black, 0.06) !default;
$--tab-header-fill-hover: rgba($--color-black, 0.06) !default;
$--tab-vertical-header-width: 90px !default;
$--tab-vertical-header-count-color: $--color-white !default;
$--tab-vertical-header-count-fill: $--el-color-text-secondary !default;

/* Button
-------------------------- */
/// fontSize||Font|1
$--button-font-size: $--font-size-base !default;
/// fontWeight||Font|1
$--button-font-weight: $--font-weight-primary !default;
/// borderRadius||Border|2
$--el-button-border-radius: $--el-border-radius-base !default;
/// padding||Spacing|3
$--el-button-padding-vertical: 11px !default;
/// padding||Spacing|3
$--el-button-padding-horizontal: 18px !default;

/// fontSize||Font|1
$--button-medium-font-size: $--font-size-base !default;
/// borderRadius||Border|2
$--el-button-medium-border-radius: $--el-border-radius-base !default;
/// padding||Spacing|3
$--el-button-medium-padding-vertical: 9px !default;
/// padding||Spacing|3
$--el-button-medium-padding-horizontal: 16px !default;

/// fontSize||Font|1
$--el-button-small-font-size: 14px !default;
$--el-button-small-border-radius: #{$--el-border-radius-base - 1} !default;
/// padding||Spacing|3
$--el-button-small-padding-vertical: 7px !default;
/// padding||Spacing|3
$--el-button-small-padding-horizontal: 14px !default;
/// fontSize||Font|1
$--button-mini-font-size: 12px !default;
$--button-mini-border-radius: #{$--el-border-radius-base - 1} !default;
/// padding||Spacing|3
$--el-button-mini-padding-vertical: 6px !default;
/// padding||Spacing|3
$--el-button-mini-padding-horizontal: 12px !default;

/// color||Color|0
$--el-button-default-font-color: $--el-color-text-regular !default;
/// color||Color|0
$--el-button-default-background-color: $--color-white !default;
/// color||Color|0
$--el-button-default-border-color: $--el-border-color-base !default;

/// color||Color|0
$--el-button-disabled-font-color: $--el-color-text-placeholder !default;
/// color||Color|0
$--el-button-disabled-background-color: $--el-color-info-lighter !default;
/// color||Color|0
$--el-button-disabled-border-color: $--el-color-info-light !default;

/// color||Color|0
$--el-button-primary-border-color: $--el-color-primary !default;
/// color||Color|0
$--button-primary-font-color: $--color-white !default;
/// color||Color|0
$--el-button-primary-background-color: $--el-color-primary !default;
/// color||Color|0
$--button-success-border-color: $--el-color-success !default;
/// color||Color|0
$--button-success-font-color: $--color-white !default;
/// color||Color|0
$--button-success-background-color: $--el-color-success !default;
/// color||Color|0
$--el-button-warning-border-color: $--el-color-warning !default;
/// color||Color|0
$--button-warning-font-color: $--color-white !default;
/// color||Color|0
$--el-button-warning-background-color: $--el-color-warning !default;
/// color||Color|0
$--el-button-danger-border-color: $--el-color-danger !default;
/// color||Color|0
$--button-danger-font-color: $--color-white !default;
/// color||Color|0
$--el-button-danger-background-color: $--el-color-danger !default;
/// color||Color|0
$--el-button-info-border-color: $--el-color-info !default;
/// color||Color|0
$--button-info-font-color: $--color-white !default;
/// color||Color|0
$--el-button-info-background-color: $--el-color-info !default;

$--button-hover-tint-percent: 20% !default;
$--button-active-shade-percent: 10% !default;


/* cascader
-------------------------- */
$--cascader-height: 200px !default;

/* Switch
-------------------------- */
/// color||Color|0
$--el-switch-on-color: $--el-color-primary !default;
/// color||Color|0
$--el-switch-off-color: $--el-border-color-base !default;
/// fontSize||Font|1
$--switch-font-size: $--font-size-base !default;
$--switch-core-border-radius: 10px !default;
// height||Other|4 TODO: width 代码写死的40px 所以下面这三个属性都没意义
$--switch-width: 40px !default;
// height||Other|4
$--switch-height: 20px !default;
// height||Other|4
$--switch-button-size: 16px !default;

/* Dialog
-------------------------- */
$--dialog-background-color: $--color-white !default;
$--el-dialog-header-background-color: #F5F6F8 !default;
$--el-dialog-header-border-color: #EAE8EC !default;
$--dialog-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !default;
/// fontSize||Font|1
$--el-dialog-title-font-size: $--font-size-base !default;
/// fontSize||Font|1
$--el-dialog-content-font-size: 14px !default;
/// fontLineHeight||LineHeight|2
$--dialog-font-line-height: $--font-line-height-primary !default;
/// padding||Spacing|3
$--dialog-padding-primary: 20px !default;

/* Table
-------------------------- */
/// color||Color|0
$--el-table-border-color: $--el-border-color-extra-light !default;
$--el-table-border: 1px solid $--el-table-border-color !default;
/// color||Color|0
$--el-table-font-color: $--el-color-text-primary !default;
/// color||Color|0
$--el-table-header-font-color: $--el-color-text-primary !default;
/// color||Color|0
$--table-row-hover-background-color: $--el-background-color-base !default;
$--el-table-current-row-background-color: $--el-color-primary-light-9 !default;
/// color||Color|0
$--el-table-header-background-color: #f2f2f2 !default;
$--table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, .12) !default;


/* Pagination
-------------------------- */
/// fontSize||Font|1
$--pagination-font-size: 13px !default;
/// color||Color|0
$--pagination-background-color: $--color-white !default;
/// color||Color|0
$--pagination-font-color: $--el-color-text-primary !default;
$--pagination-border-radius: 3px !default;
/// color||Color|0
$--el-pagination-button-color: $--el-color-text-primary !default;
/// height||Other|4
$--el-pagination-button-width: 30px !default;
/// height||Other|4
$--el-pagination-button-height: 30px !default;
/// color||Color|0
$--el-pagination-button-disabled-color: $--el-color-text-placeholder !default;
/// color||Color|0
$--pagination-button-disabled-background-color: $--color-white !default;
/// color||Color|0
$--el-pagination-hover-color: $--el-color-primary !default;

/* Popup
-------------------------- */
/// color||Color|0
$--popup-modal-background-color: $--color-black !default;
/// opacity||Other|1
$--popup-modal-opacity: 0.5 !default;

/* Popover
-------------------------- */
/// color||Color|0
$--popover-background-color: $--color-white !default;
/// fontSize||Font|1
$--popover-font-size: $--font-size-base !default;
/// color||Color|0
$--el-popover-border-color: $--el-border-color-extra-light !default;
$--popover-arrow-size: 6px !default;
/// padding||Spacing|3
$--popover-padding: 12px !default;
$--popover-padding-large: 18px 20px !default;
/// fontSize||Font|1
$--el-popover-title-font-size: 14px !default;
/// color||Color|0
$--popover-title-font-color: $--el-color-text-primary !default;

/* Tooltip
-------------------------- */
/// color|1|Color|0
$--el-tooltip-fill: $--el-color-text-primary !default;
/// color|1|Color|0
$--tooltip-color: $--color-white !default;
/// fontSize||Font|1
$--tooltip-font-size: 12px !default;
/// color||Color|0
$--tooltip-border-color: $--el-color-text-primary !default;
$--tooltip-arrow-size: 6px !default;
/// padding||Spacing|3
$--tooltip-padding: 10px !default;

/* Tag
-------------------------- */
/// color||Color|0
$--el-tag-info-color: $--el-color-info !default;
/// color||Color|0
$--el-tag-primary-color: $--el-color-primary !default;
/// color||Color|0
$--el-tag-success-color: $--el-color-success !default;
/// color||Color|0
$--el-tag-warning-color: $--el-color-warning !default;
/// color||Color|0
$--el-tag-danger-color: $--el-color-danger !default;
/// fontSize||Font|1
$--tag-font-size: 12px !default;
$--tag-border-radius: 4px !default;
$--el-tag-padding: 0 8px !default;

/* Tree
-------------------------- */
/// color||Color|0
$--tree-node-hover-background-color: $--el-background-color-base !default;
/// color||Color|0
$--el-tree-font-color: $--el-color-text-primary !default;
/// color||Color|0
$--el-tree-expand-icon-color: $--el-color-text-placeholder !default;

/* Dropdown
-------------------------- */
$--el-dropdown-menu-box-shadow: $--el-box-shadow-lighter !default;
$--dropdown-menuItem-hover-fill: $--el-color-primary-light-9 !default;
$--dropdown-menuItem-hover-color: $--link-color !default;

/* Badge
-------------------------- */
/// color||Color|0
$--badge-background-color: $--el-color-danger !default;
$--badge-radius: 10px !default;
/// fontSize||Font|1
$--badge-font-size: 12px !default;
/// padding||Spacing|3
$--badge-padding: 6px !default;
/// height||Other|4
$--badge-size: 18px !default;

/* Card
--------------------------*/
/// color||Color|0
$--card-border-color: $--el-border-color-lighter !default;
$--card-border-radius: 4px !default;
/// padding||Spacing|3
$--card-padding: 20px !default;

/* Slider
--------------------------*/
/// color||Color|0
$--el-slider-main-background-color: $--el-color-primary !default;
/// color||Color|0
$--el-slider-runway-background-color: $--el-border-color-light !default;
$--slider-button-hover-color: mix($--el-color-primary, black, 97%) !default;
$--slider-stop-background-color: $--color-white !default;
$--el-slider-disable-color: $--el-color-text-placeholder !default;
$--slider-margin: 16px 0 !default;
$--slider-border-radius: 3px !default;
/// height|1|Other|4
$--slider-height: 6px !default;
/// height||Other|4
$--slider-button-size: 16px !default;
$--slider-button-wrapper-size: 36px !default;
$--slider-button-wrapper-offset: -15px !default;

/* Steps
--------------------------*/
$--steps-border-color: $--el-disabled-border-base !default;
$--steps-border-radius: 4px !default;
$--steps-padding: 20px !default;

/* Menu
--------------------------*/
/// fontSize||Font|1
$--menu-item-font-size: $--font-size-base !default;
/// color||Color|0
$--el-menu-item-font-color: $--el-color-text-primary !default;
/// color||Color|0
$--el-menu-background-color: #f7f7f7 !default;
$--el-menu-item-hover-fill: #e6f3f7 !default;

/* Rate
--------------------------*/
$--rate-height: 20px !default;
/// fontSize||Font|1
$--rate-font-size: $--font-size-base !default;
/// height||Other|3
$--rate-icon-size: 18px !default;
/// margin||Spacing|2
$--rate-icon-margin: 6px !default;
$--rate-icon-color: $--el-color-text-placeholder !default;

/* DatePicker
--------------------------*/
$--el-datepicker-font-color: $--el-color-text-regular !default;
/// color|1|Color|0
$--el-datepicker-off-font-color: $--el-color-text-placeholder !default;
/// color||Color|0
$--el-datepicker-header-font-color: $--el-color-text-regular !default;
$--el-datepicker-icon-color: $--el-color-text-primary !default;
$--el-datepicker-border-color: $--el-border-color-extra-light !default;
$--datepicker-inner-border-color: #e4e4e4 !default;
/// color||Color|0
$--el-datepicker-inrange-background-color: $--el-border-color-extra-light !default;
/// color||Color|0
$--el-datepicker-inrange-hover-background-color: $--el-border-color-extra-light !default;
/// color||Color|0
$--el-datepicker-active-color: $--el-color-primary !default;
/// color||Color|0
$--el-datepicker-hover-font-color: $--el-color-primary !default;
$--datepicker-cell-hover-color: #fff !default;
$--el-datepicker-current-next-month-color:#8adffa !default;

/* Loading
--------------------------*/
/// height||Other|4
$--loading-spinner-size: 42px !default;
/// height||Other|4
$--loading-fullscreen-spinner-size: 50px !default;

/* Scrollbar
--------------------------*/
$--scrollbar-background-color: rgba($--el-color-text-secondary, .3) !default;
$--scrollbar-hover-background-color: rgba($--el-color-text-secondary, .5) !default;

/* Carousel
--------------------------*/
/// fontSize||Font|1
$--carousel-arrow-font-size: 12px !default;
$--carousel-arrow-size: 36px !default;
$--carousel-arrow-background: rgba(31, 45, 61, 0.11) !default;
$--carousel-arrow-hover-background: rgba(31, 45, 61, 0.23) !default;
/// width||Other|4
$--carousel-indicator-width: 30px !default;
/// height||Other|4
$--carousel-indicator-height: 2px !default;
$--carousel-indicator-padding-horizontal: 4px !default;
$--carousel-indicator-padding-vertical: 12px !default;
$--carousel-indicator-out-color: $--el-border-color-hover !default;

/* Collapse
--------------------------*/
/// color||Color|0
$--collapse-border-color: $--el-border-color-lighter !default;
/// height||Other|4
$--el-collapse-header-height: 40px !default;
/// color||Color|0
$--el-collapse-header-background-color: $--el-color-info-lighter !default;
/// color||Color|0
$--el-collapse-header-font-color: $--el-color-text-primary !default;
/// fontSize||Font|1
$--el-collapse-header-font-size: 14px !default;
/// color||Color|0
$--collapse-content-background-color: $--color-white !default;
/// fontSize||Font|1
$--el-collapse-content-font-size: 14px !default;
/// color||Color|0
$--el-collapse-content-font-color: $--el-color-text-primary !default;
$--el-collapse-hover-bg:#e6e6e6 !default;
$--el-collapse-header-bg:#f4f4f4 !default;

/* Transfer
--------------------------*/
$--el-transfer-border-color: $--el-border-color-lighter !default;
$--el-transfer-border-radius: $--el-border-radius-base !default;
/// height||Other|4
$--transfer-panel-width: 200px !default;
/// height||Other|4
$--transfer-panel-header-height: 40px !default;
/// color||Color|0
$--el-transfer-panel-header-background-color: $--el-background-color-base !default;
/// height||Other|4
$--transfer-panel-footer-height: 40px !default;
/// height||Other|4
$--transfer-panel-body-height: 246px !default;
/// height||Other|4
$--transfer-item-height: 30px !default;
/// height||Other|4
$--transfer-filter-height: 32px !default;

/* Header
  --------------------------*/
$--header-padding: 0 20px !default;

/* Footer
--------------------------*/
$--footer-padding: 0 20px !default;

/* Main
--------------------------*/
$--main-padding: 20px !default;

/* Timeline
--------------------------*/
$--timeline-node-size-normal: 12px !default;
$--timeline-node-size-large: 14px !default;
$--timeline-node-color: $--el-border-color-light !default;

/* Backtop
--------------------------*/
/// color||Color|0
$--backtop-background-color: $--color-white !default;
/// color||Color|0
$--backtop-font-color: $--el-color-primary !default;
/// color||Color|0
$--backtop-hover-background-color: $--el-border-color-extra-light !default;

/* Link
--------------------------*/
/// fontSize||Font|1
$--link-font-size: $--font-size-base !default;
/// fontWeight||Font|1
$--link-font-weight: $--font-weight-primary !default;
/// color||Color|0
$--el-link-default-font-color: $--el-color-text-primary !default;
/// color||Color|0
$--el-link-default-active-color: $--el-color-text-regular !default;
/// color||Color|0
$--el-link-disabled-font-color: $--el-color-text-placeholder !default;
/// color||Color|0
$--el-link-primary-font-color: #1990FE !default;
/// color||Color|0
$--el-link-success-font-color: $--el-color-success !default;
/// color||Color|0
$--el-link-warning-font-color: $--el-color-warning !default;
/// color||Color|0
$--el-link-danger-font-color: $--el-color-danger !default;
/// color||Color|0
$--el-link-info-font-color: $--el-color-info !default;
/* Calendar
--------------------------*/
/// border||Other|4
$--calendar-border: $--el-table-border !default;
/// color||Other|4
$--calendar-selected-background-color: #F2F8FE !default;
$--calendar-cell-width: 85px !default;

/* Form
-------------------------- */
/// fontSize||Font|1
$--form-label-font-size: $--font-size-base !default;

/* Avatar
--------------------------*/
/// color||Color|0
$--avatar-font-color: #fff !default;
/// color||Color|0
$--avatar-background-color: #C0C4CC !default;
/// fontSize||Font Size|1
$--avatar-text-font-size: 14px !default;
/// fontSize||Font Size|1
$--avatar-icon-font-size: 18px !default;
/// borderRadius||Border|2
$--avatar-border-radius: $--el-border-radius-base !default;
/// size|1|Avatar Size|3
$--avatar-large-size: 40px !default;
/// size|1|Avatar Size|3
$--avatar-medium-size: 36px !default;
/// size|1|Avatar Size|3
$--avatar-small-size: 28px !default;

/* Break-point
--------------------------*/
$--sm: 768px !default;
$--md: 992px !default;
$--lg: 1200px !default;
$--xl: 1920px !default;

$--breakpoints: (
        'xs' : (max-width: $--sm - 1),
        'sm' : (min-width: $--sm),
        'md' : (min-width: $--md),
        'lg' : (min-width: $--lg),
        'xl' : (min-width: $--xl)
);

$--breakpoints-spec: (
        'xs-only' : (max-width: $--sm - 1),
        'sm-and-up' : (min-width: $--sm),
        'sm-only': "(min-width: #{$--sm}) and (max-width: #{$--md - 1})",
        'sm-and-down': (max-width: $--md - 1),
        'md-and-up' : (min-width: $--md),
        'md-only': "(min-width: #{$--md}) and (max-width: #{$--lg - 1})",
        'md-and-down': (max-width: $--lg - 1),
        'lg-and-up' : (min-width: $--lg),
        'lg-only': "(min-width: #{$--lg}) and (max-width: #{$--xl - 1})",
        'lg-and-down': (max-width: $--xl - 1),
        'xl-only' : (min-width: $--xl),
);


$--el-aside-dark:#001529 !default;
$--main-content-bg:#f0f2f5 !default;
$--logo-shadow:#e8e8e8 !default;
