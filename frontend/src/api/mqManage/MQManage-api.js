import request from '@/utils/request'

const BASE_URL = "/mqManager/"

/**
 * 获取已发送的消息
 * @param query
 * @returns {*|AxiosPromise}
 */
export function getMessageQueue(query) {
    return request({
        url: BASE_URL + 'getMessageQueue',
        method: 'post',
        data: query
    })
}

/**
 * 获取查询条件
 * @returns {*|AxiosPromise}
 */
export function getQueryInfo() {
    return request({
        url: BASE_URL + 'getQueryInfo',
        method: 'post'
    })
}

/**
 * 重新发送消息
 * @param query
 * @returns {*|AxiosPromise}
 */
export function resendMessage(query) {
    return request({
        url: BASE_URL + "resendMessage",
        method: 'post',
        data: query
    })
}

/**
 * 获取联系人
 * @param query
 * @returns {*}
 */
export function maintainContact(query) {
    return request({
        url: BASE_URL + "maintainContact",
        method: 'post',
        data: query
    })
}

/**
 * 获取联系人
 * @returns {*}
 */
export function getContactInfo(query){
    return request({
        url: BASE_URL + "getContactInfo",
        method: 'post',
        data: query
    })
}

/**
 * 获取联系人信息列表
 * @returns {*}
 */
export function getContactList(){
    return request({
        url: BASE_URL + "getContactList",
        method: 'post'
    })
}


/**
 * 删除联系人信息
 * @returns {*}
 */
export function deleteContact(query){
    return request({
        url: BASE_URL + "deleteContact",
        method: 'post',
        data: query
    })
}

/**
 * 获取按钮信息列表
 * @returns {*}
 */
export function getButtonInfo(){
    return request({
        url: BASE_URL + "getButtonInfo",
        method: 'post'
    })
}
/**
 * 获取按钮信息
 * @returns {*}
 */
export function selectButtonInfo(query){
    return request({
        url: BASE_URL + "selectButtonInfo",
        method: 'post',
        data: query
    })
}



/**
 * 获取按钮信息
 * @returns {*}
 */
export function selectButtonByName(query){
    return request({
        url: BASE_URL + "selectButtonByName",
        method: 'post',
        data: query
    })
}

/**
 * 新增按钮信息
 * @returns {*}
 */
export function insertButton(query){
    return request({
        url: BASE_URL + "insertButton",
        method: 'post',
        data: query
    })
}


/**
 * 修改按钮状态
 * @returns {*}
 */
export function updateButtonStatus(query){
    return request({
        url: BASE_URL + "updateButtonStatus",
        method: 'post',
        data: query
    })
}


/**
 * 删除按钮状态
 * @returns {*}
 */
export function deleteButton(query){
    return request({
        url: BASE_URL + "deleteButton",
        method: 'post',
        data: query
    })
}


