'use strict'

import request from '@/utils/request'

export function _queryResourceList (_param) {
  return request({
    url: '/resource/queryResourceList?length=' + _param.pageSize + '&start=' + _param.startRow,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _deleteResource (_param) {
  return request({
    url: '/resource/deleteResourceByIds',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _resourceNameCheck (_param) {
  return request({
    url: '/resource/resourceNameCheck',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _modifyResource (_param) {
  return request({
    url: '/resource/updateResource',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _createResource (_param) {
  return request({
    url: '/resource/createResource',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getResourceDownList (_param) {
  return request({
    url: '/resource/getResourceDownList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _deleteRole (_param) {
  return request({
    url: '/role/deleteRole',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _queryRoleList (_param) {
  return request({
    url: '/role/queryRoleList?length=' + _param.pageSize + '&start=' + (_param.startRow-1)*_param.pageSize,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _roleNameCheck (_param) {
  return request({
    url: '/role/roleNameCheck',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _modifyRole (_param) {
  return request({
    url: '/role/modifyRole',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _createRole (_param) {
  return request({
    url: '/role/createRole',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _modifyUserCenter () {
}

export function _queryUserById () {
}

export function _userNameCheck () {
}

export function _modifyUser () {
}

export function _createUser () {
}

export function _getRoleDownList () {
}

export function _setMenuSort (_param) {
  return request({
    url: '/router/setMenuSort',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getMenuSort (_param) {
  return request({
    url: '/router/getMenuSort',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getAuthorityCheckedList (_param) {
  return request({
    url: '/role/getAuthorityCheckedList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getAuthorityDownList (_param) {
  return request({
    url: '/role/getAuthorityDownList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _setAuthority (_param) {
  return request({
    url: '/role/setAuthority',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _deleteMenu (_param) {
  return request({
    url: '/router/deleteMenu',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _queryRouterList (_param) {
  return request({
    url: '/router/queryRouterList?length=' + _param.pageSize + '&start=' + _param.startRow,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _menuNameCheck (_param) {
  return request({
    url: '/router/menuNameCheck',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _modifyMenu (_param) {
  return request({
    url: '/router/modifyMenu',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _queryMenuById (_param) {
  return request({
    url: '/router/queryMenuById',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _createMenu (_param) {
  return request({
    url: '/router/createRouter',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getRoleDataAll () {
  return request({
    url: '/user/all?t=' + new Date().getTime(),
    method: 'get'
  })
}
export function exportRole (_param) {
  return request({
    url: '/role/exportRole',
    method: 'post',
    headers: { 'Content-Type': 'application/json;charset=UTF-8 ' },
    responseType: 'blob',
    data: _param
  })
}
