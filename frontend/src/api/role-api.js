'use strict'

import request from '@/utils/request'

// 创建角色
export function _createRole (_param) {
  return request({
    url: '/rm/role/create',
    method: 'post',
    data: _param
  })
}

// 编辑角色
export function _modifyRole (_param) {
  return request({
    url: '/rm/role/update',
    method: 'post',
    data: _param
  })
}

// 角色名不能重复
export function _roleNameCheck (_param) {
  return request({
    url: '/rm/role/roleNameCheck',
    method: 'post',
    params: _param,
    hiddenLoading: true
  })
}
