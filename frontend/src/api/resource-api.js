'use strict'

import request from '@/utils/request'

// 新增资源
export function _createResource (_param) {
  return request({
    url: '/rm/resource/create',
    method: 'post',
    data: _param
  })
}

// 编辑资源
export function _modifyResource (_param) {
  return request({
    url: '/rm/resource/modify',
    method: 'post',
    data: _param
  })
}

// 资源名不能重复
export function _resourceNameCheck (_param) {
  return request({
    url: '/rm/resource/resourceNameCheck',
    method: 'post',
    params: _param,
    hiddenLoading: true
  })
}
