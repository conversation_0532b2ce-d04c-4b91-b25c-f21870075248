'use strict'

import request from '@/utils/request'

// 获取公司信息 和走势图
export function _queryCompanyInfo (query) {
  return request({
    url: 'listedCompany/queryCompanyInfo',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}

// 获取动态股价
export function _querySharesInfo (query) {
  return request({
    url: 'listedCompany/querySharesInfo',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}
