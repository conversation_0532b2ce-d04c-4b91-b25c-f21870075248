'use strict'

import request from '@/utils/request'

// 股权激励信息
export function listAllStock (query) {
  return request({
    url: 'listedCompany/listAllStock',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}

// 回购管理信息
export function getRepManageInfo (query) {
  return request({
    url: 'repManage/getRepManageInfo',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}

// 合规交易管理
export function getCplTransInfo (query) {
  return request({
    url: 'brokerInfo/cplTrans/getCplTransInfo',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}
// 合规交易详情
export function getCplTransDetail (query) {
  return request({
    url: 'brokerInfo/cplTrans/getCplTransDetail',
    method: 'post',
    data: query,
    hiddenLoading: true
  })
}
