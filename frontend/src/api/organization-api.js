'use strict'

import request from '@/utils/request'

// 查询
export function _getOrgTree (_param) {
  return request({
    url: '/organization/query',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 交换节点
export function _exchangePoint (_param) {
  return request({
    url: '/organization/exchangePoint',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 编辑
export function _editPoint (_param) {
  return request({
    url: '/organization/editPoint',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 删除
export function _deletePoint (_param) {
  return request({
    url: '/organization/deletePoint',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getOrgName (_param) {
  return request({
    url: '/organization/getOrgName',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getOrgNameList (_param) {
  return request({
    url: '/organization/getOrgNameList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

