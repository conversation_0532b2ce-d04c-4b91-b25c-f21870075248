'use strict'

import request from '@/utils/request'

// 登录
export function _login (form) {
  let param = "username=" + form.username + "&password=" + form.password + "&telephone=" + form.telephone + "&verifyCode=" +
      form.verifyCode + "&loginType=" + form.loginType + "&captchaCode=" + form.captchaCode + "&captchaTextCode=" + form.captchaTextCode
  return request({
    url: '/login',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    data: param
  })
}

// cas认证登录
export function _submitTicketAndService(param0) {
  let param = "ticket=" + param0.ticket + "&service=" + param0.service
      + "&loginType=1"
  return request({
    url: '/login',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: param
  })
}


// 退出登录
export function _logout () {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取用户当前权限
export function _getCurrentAudith (param) {
  return request({
    url: '/user/getCurrentAudith',
    method: 'post',
    data: param
  })
}

// 手机号获取验证码
export function _getUserNameVerifyCode (param) {
  return request({
    url: '/user/getUserNameDynamicCode',
    method: 'post',
    data: "telephone=" + param.telephone
  })
}

// 手机号获取验证码
export function _getVerifyCode (param) {
  return request({
    url: '/user/getDynamicCode',
    method: 'post',
    params: param
  })
}
// 手机号获取验证码
export function _getDynamicCodeAndCheck (data) {
  return request({
    url: '/user/getDynamicCodeAndCheck',
    method: 'post',
    data
  })
}
/*
// 根据用户名获取验证码
export function _getDynamicCodeByUsername (param) {
  return request({
    url: '/user/getDynamicCodeByUsername',
    method: 'post',
    data: param
  })
}
*/


export function _changePassword (param) {
  return request({
    url: '/user/changePassword',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: param
  })
}

export function _captchaCodeOnChange (param) {
  return request({
    url: '/user/captchaCodeOnChange',
    method: 'post',
    data: param
  })
}

// export function logoutMq (param) {
//   return request({
//     url: '/user/logoutMq',
//     method: 'post',
//     data: param
//   })
// }

export function _getMailCaptcha (param) {
  return request({
    url: '/user/getMailCaptcha',
    method: 'post',
    data: param
  })
}

export function _saveNewPassword (param) {
  return request({
    url: '/user/saveNewPassword',
    method: 'post',
    data: param
  })
}

export function checkUser (param) {
  return request({
    url: '/user/checkUser',
    method: 'post',
    data: param
  })
}


/**
 * 获取子账号信息
 * @param _param
 * @returns {*}
 * @private
 */
export function _getChildrenInfo (_param) {
  return request({
    url: '/user/getChildrenInfo',
    method: 'post',
    data: _param
  })
}

export function _getRoleListGroupBySystem (_param) {
  return request({
    url: '/user/getRoleListGroupBySystem',
    method: 'post',
    data: _param
  })
}

