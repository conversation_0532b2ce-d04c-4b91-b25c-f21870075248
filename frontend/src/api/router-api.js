'use strict'

import request from 'stock-vue-plugin/lib/request'

// 创建路由
export function _createMenu (_param) {
  return request({
    url: '/rm/router/create',
    method: 'post',
    data: _param
  })
}

/* 资源管理
* */
// 编辑用户
export function _getResourceDownList () {
  return request({
    url: '/rm/resource/getResourceDownList',
    method: 'post'
  })
}

// 根据id获取路由详细数据
export function _queryMenuById (_param) {
  return request({
    url: '/rm/router/queryById',
    method: 'post',
    params: _param
  })
}

// 编辑路由
export function _modifyMenu (_param) {
  return request({
    url: '/rm/router/update',
    method: 'post',
    data: _param
  })
}

// 路由名称不能重复
export function _menuNameCheck (_param) {
  return request({
    url: '/rm/router/routerNameCheck',
    method: 'post',
    params: _param,
    hiddenLoading: true
  })
}
