'use strict'

import request from '@/utils/request'

// 获取人员列表
export function _getPersonList (_param) {
  return request({
    url: '/person/queryPersonList?length=' + _param.pageSize + '&start=' + (_param.startRow-1)*_param.pageSize,
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}
// 获取员工管理下拉
export function getStaffSelectData (_param) {
  return request({
    url: '/person/getStaffSelectData',
    method: 'post',
    data: _param
  })
}
// 获取机构列表
export function _getOrgList () {
  return request({
    url: '/person/queryOrgList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: '{}'
  })
}

// 获取机构列表
export function _getOrgListByType (_param) {
  return request({
    url: '/person/getOrgListByType',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _getOrgListOnlyThree () {
  return request({
    url: '/person/_getOrgListOnlyThree',
    method: 'post',
    headers: { 'Content-Type': 'application/json' }
  })
}

// 获取机构列表
export function _getIdentityList () {
  return request({
    url: '/person/queryIdentityTypeList',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: '{}'
  })
}

// 插入用户信息
export function _insertPersonInfo (_param) {
  return request({
    url: '/person/createPersonInfo',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 更新用户信息
export function _updatePersonInfo (_param) {
  return request({
    url: '/person/updatePersonInfo',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 通过查询用户信息
export function _queryPersonInfoById (_personId, _seriakey) {
  return request({
    url: '/person/queryPersonInfoById',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { id: _personId, seriakey: _seriakey }
  })
}

// 设置用户可用状态
export function _setAvailableStatus (_param) {
  return request({
    url: '/person/updateUserAvailableStatus',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { userName: _param.userName, userAvailableStatus: _param.userAvailableStatus }
  })
}

// 设置用户锁定状态
export function _setLockStatus (_param) {
  return request({
    url: '/person/updateUserLockStatus',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { userName: _param.userName, userLockStatus: _param.userLockStatus }
  })
}

// 删除用户和批量删除用户
export function _deleteChooseUsers (_param) {
  return request({
    url: '/person/deletePersonByIds',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { personIds: _param.idList, personIdSeriakeys: _param.SeriakeyList }
  })
}

// 是否存在手机号
// export function _hasTelephone (_param, personId) {
//   return request({
//     url: '/person/hasTelephone',
//     method: 'post',
//     headers: { 'Content-Type': 'application/json' },
//     data: { telephone: _param, id: personId }
//   })
// }

// 是否存在邮箱
// export function _hasMail (_param, personId) {
//   return request({
//     url: '/person/hasMail',
//     method: 'post',
//     headers: { 'Content-Type': 'application/json' },
//     data: { mail: _param, id: personId }
//   })
// }

/**
 * 检查用户名字，不可重复
 * @param _param
 * @returns {*|AxiosPromise}
 * @private
 */
export function _checkUserNameExist (_param) {
  return request({
    url: '/permission/checkUserName',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { userName: _param }
  })
}

export function _checkUserNameById (_param1, _param2) {
  return request({
    url: '/permission/checkUserNameById',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: { userName: _param1, id: _param2 }
  })
}

/**
 * 检查用户名字，不可重复
 * @param _param
 * @returns {*|AxiosPromise}
 * @private
 */
export function _setUser (_param) {
  return request({
    url: '/permission/setUser',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

/**
 * 查询路由列表
 * @param _param
 * @returns {*|AxiosPromise}
 * @private
 */
export function _queryRouterList (_param) {
  return request({
    url: '/permission/queryRouterListByUserId',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

/**
 * 设置用户权限
 * @param _param
 * @returns {*|AxiosPromise}
 * @private
 */
export function _setPermission (_param) {
  return request({
    url: '/permission/setPermissions',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function _settingUserRoleMapByUserId (_package) {
  return request({
    url: '/user/roles/settings',
    method: 'post',
    params: _package.param,
    data: _package.data
  })
}

export function _getUserOfRolesByUserId (query) {
  return request({
    url: '/user/roles?t=' + new Date().getTime(),
    method: 'get',
    params: query
  })
}

export function _resetUserAccountPassword (data) {
  return request({
    url: '/user/reset',
    method: 'post',
    data: data
  })
}

// 获取归属机构下拉列表
export function getOrgSelection (_param) {
  return request({
    url: '/person/getOrgSelection',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

export function exportUserInfo (_param) {
  return request({
    url: '/person/exportUserInfo',
    method: 'post',
    headers: { 'Content-Type': 'application/json;charset=UTF-8 ' },
    responseType: 'blob',
    data: _param
  })
}

export function queryTableDate (_param) {
  return request({
    url: '/person/queryTableDate',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
    data: _param
  })
}

// 重置外部人员密码
export function _resetPassword () {
  return request({
    url: '/person/resetPersonPassword',
    method: 'post',
    headers: { 'Content-Type': 'application/json' },
  })
}

// 验证是否有其他子系统角色
export function _haveOtherSystemRole (_param) {
  return request({
    url: '/person/haveOtherSystemRole',
    method: 'post',
    data: _param
  })
}


export function _mergePerson (_param) {
  return request({
    url: '/person/mergePerson',
    method: 'post',
    data: _param
  })
}

export function _cancelMerge (_param) {
  return request({
    url: '/person/cancelMerge',
    method: 'post',
    params: _param
  })
}


export function _deleteChildrenInfo (_param) {
  return request({
    url: '/person/deleteChildrenInfo',
    method: 'post',
    data: _param
  })
}

/**
 * 获取子账号信息
 * @param _param
 * @returns {*}
 * @private
 */
export function _getChildrenInfo (_param) {
  return request({
    url: '/person/getChildrenInfo',
    method: 'post',
    data: _param
  })
}
