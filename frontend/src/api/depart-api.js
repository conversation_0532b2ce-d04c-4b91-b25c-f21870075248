'use strict'

import request from '@/utils/request'

export function _editNode (data) {
  return request({
    url: '/department/editNode',
    method: 'post',
    data: data
  })
}
export function _addChildNode (data) {
  return request({
    url: '/department/addChildNode',
    method: 'post',
    data: data
  })
}
export function _addBrotherNode (data) {
  return request({
    url: '/department/addBrotherNode',
    method: 'post',
    data: data
  })
}
export function _nodeMoveUp (data) {
  return request({
    url: '/department/nodeMoveUp',
    method: 'post',
    data: data
  })
}
export function _nodeMoveDown (data) {
  return request({
    url: '/department/nodeMoveDown',
    method: 'post',
    data: data
  })
}
export function _deleteNode (data) {
  return request({
    url: '/department/deleteNode',
    method: 'post',
    params: data
  })
}
export function _checkPerson (data) {
  return request({
    url: '/department/checkPerson',
    method: 'post',
    params: data
  })
}
export function _addFirstNode () {
  return request({
    url: '/department/addFirstNode',
    method: 'post'
  })
}
export function _adJustNode (data) {
  return request({
    url: '/department/adjustNode',
    method: 'post',
    params: data
  })
}
export function _queryOrgTree (data) {
  return request({
    url: '/department/queryOrgTree',
    method: 'post',
    params: data
  })
}

export function _listClassification (data) {
  return request({
    url: '/department/listDepartmentClassification',
    method: 'post',
    data: data
  })
}
export function _saveClassification (data) {
  return request({
    url: '/department/saveClassification',
    method: 'post',
    data: data
  })
}
export function _getOrgList (data) {
  return request({
    url: '/department/getOrgList',
    method: 'post',
    data: data
  })
}

export function _getDepartmentTreeList (data) {
  return request({
    url: '/department/getDepartmentTreeList',
    method: 'post',
    data: data
  })
}

