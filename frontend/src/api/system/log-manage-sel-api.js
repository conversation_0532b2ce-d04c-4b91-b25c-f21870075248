'use strict'

import request from '@/utils/request'

export function queryPagingLogManageBySelective (query) {
  return request({
    url: '/logManage/queryPagingLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function queryLoginLogManageBySelective (query) {
  return request({
    url: '/logManage/queryLoginLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function queryOperationLogManageBySelective (query) {
  return request({
    url: '/logManage/queryOperationLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function exportExcel (query) {
  return request({
    url: '/logManage/exportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}

export function loginExportExcel (query) {
  return request({
    url: '/logManage/loginExportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}

export function operationLogExportExcel (query) {
  return request({
    url: '/logManage/operationLogExportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}

// 查询登录协议日志列表
export function queryLoginAgreementLogList (query) {
  return request({
    url: '/logManage/queryLoginAgreementLogList?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize,
    method: 'post',
    data: query
  })
}

export function agreementLogExportExcel (query) {
  return request({
    url: '/logManage/agreementLogExportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}
