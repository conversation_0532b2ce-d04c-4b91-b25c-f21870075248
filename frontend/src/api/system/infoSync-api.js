'use strict'

import request from '@/utils/request'

/**
 *  信息同步功能，  同步 组织架构、角色、上市公司、用户 等信息到平台
 *  上线使用，然后废弃
 */

// 导入部门
export function _importDeptInfo (query) {
    return request({
        url: '/department/importDeptInfo',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: query
    })
}

// 导入角色
export function _importRoleInfo (query) {
    return request({
        url: '/role/outer/importRoleInfo',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: query
    })
}

// 导入上市公司
export function _importListedCompanyInfo (query) {
    return request({
        url: '/system/company/importListedCompanyInfo',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: query
    })
}

// 导入内部用户
export function _importInnerUserInfo (query) {
    return request({
        url: '/person/importInnerUserInfo',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: query
    })
}

// 导入外部用户
export function _importOuterUserInfo (query) {
    return request({
        url: '/person/importOuterUserInfo',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data: query
    })
}
