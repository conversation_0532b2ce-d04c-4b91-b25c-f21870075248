import request from '@/utils/request'

export function _getTasksList (query) { // 获取table数据
  return request({
    url: '/system/timedTasks/getTasksList?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize,
    method: 'post',
    data: query
  })
}

export function _getTasksGroupList (query) { // 获取table数据
  return request({
    url: '/system/timedTasks/getTasksGroupList?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize,
    method: 'post',
    data: query,
  })
}

export function _repeat (query) { // 获取table数据
  return request({
    url: '/system/timedTasks/repeat',
    method: 'post',
    data: query
  })
}
