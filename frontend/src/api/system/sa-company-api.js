'use strict'

import request from '@/utils/request'

export function queryPagingCompanyBySelective (query) { // 获取table数据
  return request({
    url: '/system/company/queryPagingCompanyBySelective?length=' + query.pageSize + '&start=' + query.startRow,
    method: 'post',
    data: query
  })
}

export function getBusDepById (query) { // 获取table数据
  return request({
    url: '/system/company/getBusDepById',
    method: 'post',
    params: query
  })
}

export function createOrUpdate (query) { // 获取table数据
  return request({
    url: '/system/company/createOrUpdate',
    method: 'post',
    data: query
  })
}

export function deleteCompanyById (query) { // 删除公司数据
  return request({
    url: '/system/company/deleteCompanyById',
    method: 'post',
    params: query
  })
}

export function queryBusinessDepartmentOptions (query) { // 查询营业部选项列表
  return request({
    url: '/system/company/queryBusinessDepartmentOptions',
    method: 'post',
    params: query
  })
}
export function checkCompanyCodeConvert (query) { // 查询是否是刷新过的代码
  return request({
    url: '/system/company/checkCompanyCodeConvert',
    method: 'post',
    params: query
  })
}
export function checkBusDepCode (query) { // 查询上市公司代码
  return request({
    url: '/system/company/checkBusDepCode',
    method: 'post',
    params: query
  })
}
export function getCompanyByCode (query) { // 查询上市公司代码
  return request({
    url: '/system/company/getCompanyByCode',
    method: 'post',
    params: query
  })
}
export function getCompanyInfoByCode (query) {
  return request({
    url: '/system/company/getCompanyInfoByCode',
    method: 'post',
    params: query
  })
}

export function checkSocialUnifiedCreditCode (query) { // 查询上市公司代码
  return request({
    url: '/system/company/checkSocialUnifiedCreditCode',
    method: 'post',
    params: query
  })
}

export function _queryTableData (query) {
  return request({
    url: '/system/company/queryTableData',
    method: 'post',
    data: query
  })
}

// 获取员工管理下拉
export function getStaffSelectData (_param) {
  return request({
    url: '/system/company/getStaffSelectData',
    method: 'post',
    data: _param
  })
}
