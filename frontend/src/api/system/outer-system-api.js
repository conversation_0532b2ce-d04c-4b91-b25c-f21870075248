'use strict'

import request from '@/utils/request'

export function _queryTableData (query) {
  return request({
    url: '/outerSystem/queryTableData',
    method: 'post',
    data: query
  })
}

export function _queryDetailData (query) {
  return request({
    url: '/outerSystem/queryDetailData',
    method: 'post',
    data: query
  })
}

export function _saveConfigData (query) {
  return request({
    url: '/outerSystem/saveConfigData',
    method: 'post',
    data: query
  })
}

export function lockStatusClick (query) {
  return request({
    url: '/outerSystem/lockStatusClick',
    method: 'post',
    data: query
  })
}
