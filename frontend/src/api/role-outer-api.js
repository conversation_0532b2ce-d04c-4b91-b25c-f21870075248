'use strict'

import request from '@/utils/request'

export function _getRoleList (_param) {
  return request({
    url: '/role/outer/getRoleList?length=' + _param.pageSize*_param.startRow + '&start=' + (_param.startRow-1)*_param.pageSize,
    method: 'post',
    data: _param
  })
}

export function _cuRoleInfo (_param) {
  return request({
    url: '/role/outer/cuRoleInfo',
    method: 'post',
    data: _param
  })
}

export function _delRoleInfo (_param) {
  return request({
    url: '/role/outer/delRoleInfo',
    method: 'post',
    data: _param
  })
}

export function _getRoleListGroupBySystem (_param) {
  return request({
    url: '/role/outer/getRoleListGroupBySystem',
    method: 'post',
    data: _param
  })
}

export function _saveOuterRoleUserMap (_param) {
  return request({
    url: '/role/outer/saveOuterRoleUserMap',
    method: 'post',
    data: _param
  })
}
export function checkGQJLRole (_param) {
  return request({
    url: '/role/outer/checkGQJLRole',
    method: 'post',
    data: _param
  })
}
export function _getStockRoleList () {
  return request({
    url: '/role/outer/getStockRoleList',
    method: 'post'
  })
}
