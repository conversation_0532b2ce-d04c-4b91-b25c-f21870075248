'use strict'

import request from '@/utils/request'

/**
 * @description 格式化数量
 * @param num：要格式化的数字
 * @param pointNum：保留几位小数 默认0位
 * @param unit：单位.
 */
export function _amountFormat (num, pointNum, unit) {
  let fixPointNum = 0;
  if (_valueIsNotEmpty(pointNum) && !isNaN(pointNum)) {
    fixPointNum = pointNum;
  }
  if (Number(num)) {
    num = Number(num).toFixed(fixPointNum);
    let arr = num.split('.');
    let tmp = arr[0].replace(/\B(?=(\d{3})+$)/g, ',').replace(/,$/g, '');
    if (arr[1] !== undefined) {
      num = tmp + '.' + arr[1];
    } else {
      num = tmp
    }
  }
  if (num && unit) {
    return num + unit;
  } else if (num) {
    return num;
  }
  // 对0.00和-0.00处理
  else if (num === 0 && unit) {
    return '0' + unit;
  } else if (num === 0) {
    return 0;
  } else {
    num = "--";
  }
  return num;
}

/**
 * @description 非空判断
 * @param value：判断的值
 *
 */
export function _valueIsNotEmpty (value) {
  return value !== undefined && value !== null && value !== '' && value !== 'undefined' && value !== 'null';
}

/**
 * 获取当前用户开通的系统
 * @returns {*|AxiosPromise}
 * @private
 */
export function _getOpenedSystems (query) {
  return request({
    url: '/common/getOpenedSystems',
    method: 'post',
    data: query
  })
}

export function _getRoleListGroupBySystem (_param) {
  return request({
    url: '/common/getRoleListGroupBySystem',
    method: 'post',
    data: _param
  })
}

/**
 *
 * @returns {*|AxiosPromise}
 * @private
 */
export function _getSystemConfigConstant () {
  return request({
    url: '/common/getSystemConfigConstant',
    method: 'post',
    data: {}
  })
}

export function stockInfoMergeFirst () {
  return request({
    url: '/common/stockInfoMergeFirst',
    method: 'post'
  })
}

export function getSystemStatus (param) {
  return request({
    url: '/common/getSystemStatus',
    method: 'post',
    data: param
  })
}
