import request from 'stock-vue-plugin/lib/request';
import Vue from 'vue';
import axios from 'axios';

// 获取列表数据
export function _getChatRecordList (params) {
  return request({
    url: '/chat/getChatRecordList',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 新建对话
export function _createChat (params, flag) {
  return request({
    url: '/chat/createChat',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 删除对话
export function _delete (params) {
  return request({
    url: '/chat/delete',
    method: 'post',
    params: params,
    hiddenLoading: true
  })
}

// 编辑聊天名称
export function _editChatName (params) {
  return request({
    url: '/chat/editChatName',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 获取单个聊天记录所有内容
export function _getChatContent (params) {
  return request({
    url: '/chat/getChatContent',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 赞踩接口
export function _getFeedbackType (params) {
  return request({
    url: '/chat/getFeedbackType',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}


// 获取token
export function getTenantInfo () {
  return request({
    url: '/chat/getTenantInfo',
    method: 'get',
    hiddenLoading: true
  })
}

export function _getModelList () {
  return request({
    url: '/chat/getModelList',
    method: 'post',
    hiddenLoading: true
  })
}

export function _getAiAssistantStandardIssues () {
  return request({
    url: '/chat/getAiAssistantStandardIssues',
    method: 'post',
    hiddenLoading: true
  })
}

export function _getMaterial (contentId) {
  return request({
    url: '/chat/getMaterial?contentId=' + contentId,
    method: 'get',
    hiddenLoading: true
  })
}

export function _getContainsCompanyCodeOrName (question) {
  return request({
    url: '/chat/checkQuestion',
    method: 'post',
    data: question,
    hiddenLoading: true
  })
}

// 提交反馈
export function _submitFeedback (params) {
  return request({
    url: '/chat/submitFeedback',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 删除反馈
export function _deleteFeedback (chatContentId) {
  return request({
    url: '/chat/deleteFeedback',
    method: 'post',
    params: { chatContentId: chatContentId },
    hiddenLoading: true
  })
}

// 查询反馈答复列表
export function _getFeedbackReplyList (params) {
  return request({
    url: '/chat/getFeedbackReplyList',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 查询反馈对话记录列表
export function _getConversationRecordList (params) {
  return request({
    url: '/chat/getConversationRecordList',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 继续反馈 - 提交新的反馈内容
export function _submitContinueFeedback (params) {
  return request({
    url: '/chat/submitContinueFeedback',
    method: 'post',
    data: params,
    hiddenLoading: true
  })
}

// 继续反馈 - 提交新的反馈内容
export function _getIsReplyNewCount () {
  return request({
    url: '/chat/getIsReplyNewCount',
    method: 'post',
    hiddenLoading: true
  })
}



