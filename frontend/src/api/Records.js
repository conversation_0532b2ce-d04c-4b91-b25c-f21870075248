'use strict'

import request from '@/utils/request'

export function getTableList(params) {
    return request({
        url: "/records/getTableList",
        method: "post",
        data: params,
    });
}

export function getInformation(params) {
    return request({
        url: "/records/getInformation",
        method: "post",
        data: params,
    });
}

export function exportFieldList(params) {
    return request({
        url: "/records/exportFieldList",
        method: "post",
        data: params,
        responseType: 'blob'// 设置response类型，下载
    });
}
export function _getChatLoginRecord() {
    return request({
        url: '/records/getChatLoginRecord',
        method: 'post',
        hiddenLoading: true
    })
}

// 存点击记录
export function _setChatLoginRecord () {
    return request({
        url: '/records/setChatLoginRecord',
        method: 'post',
        hiddenLoading: true
    })
}