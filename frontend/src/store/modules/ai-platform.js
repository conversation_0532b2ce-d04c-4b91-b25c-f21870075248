import {debounce} from '@/utils'
import Vue from "vue";

const AiPlatform = {
  state: {
    wenChatGpt: '16-1',
    wenChatGptName: 'DeepSeek-R1',
    checkboxTwo: true,
    wenBelongsPlate: '',
    wenNullList: false,
    homeSending: false,
    moreThen: false,
  },

  getters: {
    wenChatGpt: state => state.wenChatGpt,
    wenChatGptName: state => state.wenChatGptName,
    checkboxTwo: state => state.checkboxTwo,
    wenBelongsPlate: state => state.wenBelongsPlate,
    wenNullList: state => state.wenNullList,
    homeSending: state => state.homeSending,
    moreThen: state => state.moreThen,
  },

  mutations: {
    SET_WEN_CHAT_GPT (state, payload) {
      state.wenChatGpt = payload
    },
    SET_WEN_CHAT_GPT_NAME (state, payload) {
      state.wenChatGptName = payload
    },
    SET_WEN_BELONGS_PLATE (state, payload) {
      state.wenBelongsPlate = payload
    },
    SET_WEN_NULL_LIST (state, nullList) {
      state.wenNullList = nullList
    },
    SET_HOME_SENDING (state, homeSending) {
      state.homeSending = homeSending
    },
    SHOW_MORE_THEN(state, moreThen) {
      state.moreThen = moreThen
    },
    SET_CHECKBOX_TWO(state, checkboxTwo) {
      state.checkboxTwo = checkboxTwo
    }
  },

  actions: {
  }
}

export default AiPlatform
