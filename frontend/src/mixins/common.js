import { iframeDoMessage } from 'stock-vue-plugin/src/utils/auth'
export default {
  name: 'common',
  data () {
    return {
      condition_copy: {},
      currentNode: ''// 存放选中节点
    }
  },
  methods: {
    /** 分享 method start */
    /**
         * QQ分享
         * @param {share address,type:String} href
         * @param {title,type:String} name
         * @param {site url, type:String} siteBaseUrl
         */
    qqShare (href, name, siteBaseUrl, content) { // QQ分享
      const p = {
        url: href, /* 获取URL，可加上来自分享到QQ标识，方便统计 */
        title: name, /* 分享标题(可选) */
        summary: this.getValue(content), /* 分享摘要(可选) */
        pics: siteBaseUrl + 'static/images/logo_120.jpg', /* 分享图片(可选) */
        flash: '', /* 视频地址(可选) */
        site: siteBaseUrl, /* 分享来源(可选) 如：QQ分享 */
        style: '202',
        width: 24,
        height: 24
      }
      var s = []
      for (var i in p) {
        s.push(i + '=' + encodeURIComponent(p[i] || ''))
      }
      const shareUrl = 'http://connect.qq.com/widget/shareqq/index.html?' + s.join('&')
      this.$open(shareUrl)
    },
    /**
         * 微博分享
         * @param {share address,type:String} href
         * @param {title,type:String} name
         */
    sinaShare (href, name) {
      href = encodeURIComponent(href)
      name = encodeURIComponent(name)
      const appkey = '3062395763'
      const shareUrl = 'http://service.weibo.com/share/share.php?appkey=' + appkey + '&title=' + name + '&url=' + href
      this.$open(shareUrl)
    },
    /** 分享 method end */
    /** 分页查询 method start */
    /**
         * 设置分页为第一页
         */
    setPage () {
      const limit = this.queryParam.pageSize
      // 设置分页参数
      this.$refs.paper.setPageParam(1, limit)
      this.queryParam.startRow = 1
    },
    /**
         * 分页查询
         * @param {type,type:String} type
         */
    confirmSearch (type) {
      this.setPage()
      this.condition_copy = Object.assign({}, this.queryParam.condition)
      this.pageSearch(type)
    },
    /** 分页查询 method end */
    /** 共同方法 method start */
    /**
         * 去标签
         * @param {value,type:String} title
         */
    delSolrHighLightStr (title) {
      // 创建P标签
      const _p = document.createElement('p')
      _p.innerHTML = title
      return _p.innerText
    },
    /**
         * 消息弹窗
         * @param {message,type:String} msg
         */
    popMsg (msg) {
      iframeDoMessage(window.parent, 'popMsg', [msg])
    },
    /**
         * 判断值空
         * @param {string} val
         */
    getValue (val) {
      if (val === null || val === 'null' || val === undefined || val === 'undefined') {
        val = ''
      }
      return val
    },
    /**
         * 转义json串
         * @param {jsonStr,type:String} item
         */
    jsonParse (item) {
      return JSON.parse(item)
    },
    /** 共同方法 method end */
    /**
         * 根据className清空当前树选中状态
         * @param {tree class} treeClassName
         */
    clearCurrentTreeChecked (treeClassName) {
      for (var tree of document.getElementsByClassName(treeClassName)) {
        for (var obj of tree.querySelectorAll('.el-tree-node__label')) {
          obj.className = 'el-tree-node__label'
        }
      }
    },
    /**
         * 树刷新后恢复上次点击状态
         */
    setLastClickNode () {
      var $this = this
      // 获取当前树选中节点
      const currentNode = $this.currentNode
      if ($this.getValue(currentNode) !== '') {
        const treeNodeArr = currentNode.split(';')
        if ($this.getValue(treeNodeArr) !== '' && treeNodeArr.length > 0) {
          treeNodeArr.map((obj, index) => {
            if ($this.getValue(obj) !== '') {
              const activeTreeRef = obj.split(',')
              if (activeTreeRef.length > 1 &&
                                $this.getValue(activeTreeRef[1]) !== '') {
                // 设置当前树选中
                $this.$refs[activeTreeRef[0]].setCurrentKey(activeTreeRef[1])
              }
            }
          })
        }
      }
    },
    /**
         * 下拉列表回调
         * @param {$event:判断收起或展开,type：Boolean} data
         * @param {tree Ref,type:String} ref
         * @param {selected,type:Array} val
         */
    calls (data, ref, val) {
      if (!data) {
        // 通过node值,当未点击确定时,恢复上一次选中的节点
        this.$refs[ref].setCheckedNodes(val)
      }
    },
    /**
         * 下拉列表清空操作
         * @param {tree Ref,type:String} ref
         */
    clear (ref) {
      this.$refs[ref].setCheckedKeys([])
    },
    /**
         * 下拉列表查询方法
         * @param {输入参数,type:String} value
         * @param {数据,type:Object} data
         * @param {data中那个数据,type:String} type
         */
    commonFilterNode (value, data, type) {
      if (!value) return true
      return data[type].indexOf(value) !== -1
    },
    /**
         * 下拉列表查询方法
         * @param {输入参数,type:String} val
         * @param {树的ref,type:String} treeRef
         */
    commonTreeSearch (val, treeRef) {
      this.$refs[treeRef].filter(val)
    },
    /** 树操作共同方法 method end */

    // 需求7497 点击变灰 start
    /* 设置点击变灰
        savaReadIntoLocalStorage(modalName, id) {
            var storage = window.localStorage;
            var readId = storage.getItem(modalName);
            var allreadyIds = "";
            var exitBoolean = false;
            if (readId) {
                allreadyIds = readId.split(",");
                for (var i = 0; i < allreadyIds.length; i++) {
                    if (allreadyIds[i] != id) {
                        exitBoolean = true;
                        break;
                        //id = readId+","+id;
                    } else {
                        exitBoolean = false;
                    }
                }
                if (exitBoolean == true) {
                    id = readId + "," + id;
                }
            } else {
                id = id;
            }
            return storage.setItem(modalName, id);
        },
 */
    /* 得到变灰 */
    getReadIntoLocalStorageBoolean (setModalName, modalName, id) {
      var storage = window.localStorage
      var readId = ''
      if (setModalName === modalName) {
        readId = storage.getItem(modalName)
      } else {
        readId = modalName[0]
      }

      var allreadyIds = ''
      var readBoolean = false
      if (readId) {
        allreadyIds = readId.split(',')
        for (var i = 0; i < allreadyIds.length; i++) {
          if (allreadyIds[i] === id) {
            readBoolean = true
            break
          }
        }
      }
      return readBoolean
    },

    /* 改变成灰色 */
    setChangeGray (setModalName, modalName, data) {
      var id = ''
      var queryid = ''
      if (data !== null) {
        for (var i = 0; i < data.length; i++) {
          const dataType = data[i]
          id = dataType.id
          if (id !== '' && id !== undefined && id !== null) {
            if (id.indexOf('_') !== -1) {
              queryid = id.split('_')[1]
            } else {
              queryid = id
            }
          } else {
            queryid = ''
          }
          var resultBoolean = this.getReadIntoLocalStorageBoolean(setModalName, modalName, queryid)// 是否存在
          if (resultBoolean) {
            data[i].titleClicked = true
          }
        }
      }
    },
    checkResultBoolean (result, queryid) { // 检查是否存在
      var readBoolean = false
      if (result) {
        var allreadyIds = result.split(',')
        for (var i = 0; i < allreadyIds.length; i++) {
          if (allreadyIds[i] === queryid) {
            readBoolean = true
            break
          }
        }
      }
      return readBoolean
    },
    /* 改变成灰色 */
    ChangeColorGray (result, data) {
      var id = ''
      var queryid = ''
      if (data !== null) {
        for (var i = 0; i < data.length; i++) {
          const dataType = data[i]
          id = dataType.id
          if (id !== '' && id !== undefined && id !== null) {
            if (id.indexOf('_') !== -1) {
              queryid = id.split('_')[1]
            } else {
              queryid = id
            }
          } else {
            queryid = ''
          }
          var resultBoolean = this.checkResultBoolean(result, queryid)// 是否存在
          if (resultBoolean) {
            data[i].titleClicked = true
          }
        }
      }
    },
    savaReadIntoLocalStorage (key, value) { // 保存到父页面
      try {
        iframeDoMessage(window.parent, 'savaReadIntoLocalStorage', [key, value])
        // eslint-disable-next-line no-empty
      } catch (error) {
      }
    },
    setLocalStorageResultReturn (nameSpace, key) { // 向企业端发送消息并返回
      iframeDoMessage(window.parent,
        'microGetValueFromEnter',
        [key, nameSpace, 'SET_REQUEST_GET_LOCAL_STORAGE_DATA', 'commit'])
    },
    // 需求7497 点击变灰 end

    /**
         * 选中同行业公司
         * @param {已关注公司,type:List} concernCompanyList
         * @param {本公司行业id,type:String} companyIndustryId
         * @param {树的ref,type:String} treeRef
         */
    selSameIndustryCompany (concernCompanyList, companyIndustryId, treeRef) {
      var companyCodeList = []
      concernCompanyList.map((obj, idx) => { // 获取同行业公司id
        if (obj.industryId === companyIndustryId) {
          companyCodeList.push(obj.value)
        }
      })
      if (companyCodeList.length > 0) {
        // 获取选中的节点
        const nodeCheck = this.$refs[treeRef].$refs.tree.getCheckedNodes()
        if (nodeCheck !== null && nodeCheck.length > 0) {
          var codeList = []
          nodeCheck.map((obj, idx) => {
            codeList.push(obj.value)
          })
          // this.setSameCompanyChecked(companyCodeList, !this.includes(codeList,companyCodeList), treeRef);
          if (this.includes(codeList, companyCodeList)) {
            this.setSameCompanyChecked(companyCodeList, false, treeRef)
            this.$refs[treeRef].selClearAndSelectCompany(true)
          } else {
            this.setSameCompanyChecked(companyCodeList, true, treeRef)
            this.$refs[treeRef].selClearAndSelectCompany(false)
          }
        } else {
          // 无选中节点时,直接勾选同行业公司
          this.$refs[treeRef].selClearAndSelectCompany(false)
          this.setSameCompanyChecked(companyCodeList, true, treeRef)
        }
      } else {
        this.popMsg('无同行业公司')
      }
    },
    // 选中节点,setcheckedNodes方法会改变下拉列表上显示选中的名字,所以改用别的方法
    setSameCompanyChecked (companyCodeList, checked, treeRef) {
      companyCodeList.map((obj, index) => {
        this.$refs[treeRef].$refs.tree.$children.map((item, itemIndex) => {
          if (item.node.key === obj) {
            item.node.checked = checked
          }
        })
      })
    },
    includes (arr1, arr2) { // 判断数组包含
      return arr2.every(val => arr1.includes(val))
    },
    autoSelectSameCommon (concernCompanyList, companyIndustryId, treeRef) { // 更新选择同行业
      var companyCodeList = []
      concernCompanyList.map((obj, idx) => { // 获取同行业公司id
        if (obj.industryId === companyIndustryId) {
          companyCodeList.push(obj.value)
        }
      })
      if (companyCodeList.length > 0) {
        // 获取选中的节点
        const nodeCheck = this.$refs[treeRef].$refs.tree.getCheckedNodes()
        if (nodeCheck !== null && nodeCheck.length > 0) {
          var codeList = []
          nodeCheck.map((obj, idx) => {
            codeList.push(obj.value)
          })
          if (this.includes(codeList, companyCodeList)) {
            this.$refs[treeRef].selClearAndSelectCompany(false)
          } else {
            this.$refs[treeRef].selClearAndSelectCompany(true)
          }
        } else {
          // 无选中节点时,直接勾选同行业公司
          this.$refs[treeRef].selClearAndSelectCompany(true)
        }
      }
    },
    // 千位符
    numberIntFilter (value) {
      if (!value) return ''
      if (value === '') return ''
      let str = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return str;
    },
    // 千位符（保留两位小数）
    numberFilter (value) {
      if (!value) return ''
      if (value === '') return ''
      let str = parseFloat(value).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
      return str;
    },
    // 千位符（保留两位小数） 为0
    numberNullFilter (value) {
      let str = parseFloat(value).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
      return str;
    },
    fomatFloat (x) {
      // x=x.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
      // 强制保留2位小数，如：2，会在2后面补上00.即2.00
      var f = parseFloat(x);
      if (isNaN(f)) {
        return 0;
      }
      var f = Math.round(x * 100) / 100;
      var s = f.toString();
      var rs = s.indexOf('.');
      if (rs < 0) {
        rs = s.length;
        s += '.';
      }
      while (s.length <= rs + 2) {
        s += '0';
      }
      return this.numberFilter(s);
    },
    // 时间转换 2022-02-03 转换 2022年02月03日
    getDateClick (val) {
      if (this.getValue(val) === "") {
        return "";
      } else {
        return this.$moment(val).format('YYYY年MM月DD日');
      }
    },
    numberIntStrFilter (value) {
      if (!value) return '--'
      if (value === '') return '--'
      let str = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      return str;
    },
    numberFilterStrNull (value) {
      if (!value) return '--'
      if (value === '') return '--'
      if (isNaN(value)) return '--'
      let str = parseFloat(value).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
      return str;
    },
    /**
     * 根据长度截取先使用字符串，超长部分追加…
     * str 对象字符串
     * len 目标字节长度
     */
    cutString (str, len) {
      if (!str) {
        return "- -";
      }
      // length属性读出来的汉字长度为1
      if (str.length * 2 <= len) {
        return str;
      }
      let strlen = 0;
      let s = "";
      for (let i = 0; i < str.length; i++) {
        s = s + str.charAt(i);
        if (str.charCodeAt(i) > 128) {
          strlen = strlen + 2;
          if (strlen >= len) {
            return s.substring(0, s.length - 1) + "...";
          }
        } else {
          strlen = strlen + 1;
          if (strlen >= len) {
            return s.substring(0, s.length - 2) + "...";
          }
        }
      }
      return s;
    },
    /**
     * 获取当前后台环境
     */
    getCurrentBackGroundEnv () {
      let result = '';
      // if (this.getValue(window.localStorage.getItem("jurisdictionData")) !== '') {
      //   let data = JSON.parse(window.localStorage.getItem("jurisdictionData"));
      //   return this.getValue(data['currentEnv']);
      // }
      if (this.getValue(window.localStorage.getItem("platform_jurisdictionData")) !== '') {
        let data = JSON.parse(window.localStorage.getItem("platform_jurisdictionData"));
        return this.getValue(data['currentEnv']);
      }
      return result;
    },
    // 去除字符串前后面空格
    trimStr (str) {
      return str.replace(/(^\s*)|(\s*$)/g, "");
    },
  }
}
