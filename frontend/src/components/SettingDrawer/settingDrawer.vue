<style lang="scss">
    @import "settingDrawer";
</style>
<template>
    <el-drawer
            :visible.sync="openDrawer"
            class="setting-drawer"
            ref="drawer"
    @closed="closed">
        <div>
            <div class="setting-drawer-content">
                <h4>整体风格设置</h4>
                <div class="setting-drawer-item">
                    <el-tooltip class="item" effect="dark" content="暗色菜单风格" placement="top-start">
                        <div @click="handleMenuTheme('dark')">
                            <img src="https://gw.alipayobjects.com/zos/rmsportal/LCkqqYNmvBEbokSDscrm.svg" alt="dark">
                            <div class="setting-drawer-selectIcon" v-if="navTheme === 'dark'">
                                <i class="iconfont ic-votegou"></i>
                            </div>
                        </div>
                    </el-tooltip>
                </div>
                <div class="setting-drawer-item">
                    <el-tooltip class="item" effect="dark" content="亮色菜单风格" placement="top-start">
                        <div @click="handleMenuTheme('light')">
                            <img src="https://gw.alipayobjects.com/zos/rmsportal/jpRkZQMyYRryryPNtyIC.svg" alt="light">
                            <div class="setting-drawer-selectIcon" v-if="navTheme !== 'dark'">
                                <i class="iconfont ic-votegou"></i>
                            </div>
                        </div>
                    </el-tooltip>
                </div>
                <div class="theme-color">
                    <h3 class="theme-color-title">主题色</h3>
                    <div>
                        <color-change @colorChange="colorChange"></color-change>
                        <!--<el-tooltip effect="dark" placement="top-start"-->
                        <!--v-for="(item,index) in colorList" :key="index">-->
                            <!--<div>{{item.key}}</div>-->
                            <!--<el-tag @click="changeColor(item.color)">{{item.color}}</el-tag>-->
                        <!--</el-tooltip>-->
                    </div>
                </div>
            </div>
            <div class="setting-drawer-content">
                <h4>导航模式</h4>
                <div class="setting-drawer-item">
                    <el-tooltip class="item" effect="dark" content="侧边栏导航" placement="top-start">
                        <div  @click="handleLayout('sidemenu')">
                            <img src="https://gw.alipayobjects.com/zos/rmsportal/JopDzEhOqwOjeNTXkoje.svg" alt="sidemenu">
                            <div class="setting-drawer-selectIcon" v-if="layout === 'sidemenu'">
                                <i class="iconfont ic-votegou"></i>
                            </div>
                        </div>
                    </el-tooltip>
                </div>
                <div class="setting-drawer-item">
                    <el-tooltip class="item" effect="dark" content="顶部栏导航" placement="top-start">
                        <div @click="handleLayout('topmenu')">
                            <img src="https://gw.alipayobjects.com/zos/rmsportal/KDNDBbriJhLwuqMoxcAr.svg" alt="topmenu">
                            <div class="setting-drawer-selectIcon" v-if="layout !== 'sidemenu'">
                                <i class="iconfont ic-votegou"></i>
                            </div>
                        </div>
                    </el-tooltip>
                </div>
                <div class="m-c">
                    固定 Header
                    <el-switch
                            class="p-r"
                            v-model="fixHeader"
                            @change="handleFixedHeader">
                    </el-switch>
                </div>
                <div class="m-c">
                    固定侧边菜单
                    <el-switch
                            class="p-r"
                            v-model="fixSidebar"
                            @change="handleFixSiderbar">
                    </el-switch>
                </div>
            </div>
        </div>
        <div class="setting-drawer-close" @click="closeDrawer">
            <i class="iconfont ic-close"></i>
        </div>
    </el-drawer>
</template>
<script>
import { baseMixin } from '@/store/app-mixin'
import colorChange from '../ColorChange/colorChange'
import store from 'store'
export default {
  data () {
    return {
      openDrawer: false,
      fixHeader: this.fixedHeader,
      fixSidebar: this.fixedSidebar
    }
  },
  mixins: [baseMixin],
  props: {
    drawer: Boolean
  },
  components: { colorChange },
  methods: {
    closed () {
      this.$emit('close-drawer')
    },
    closeDrawer () {
      this.$refs.drawer.closeDrawer()
    },
    handleMenuTheme (theme) {
      this.$store.commit('nav_theme', theme)
      this.updateLocal('nav_theme', theme);
    },
    handleLayout (mode) {
      this.$store.commit('layout', mode)
      this.updateLocal('layout', mode);
      // 因为顶部菜单不能固定左侧菜单栏，所以强制关闭
      // this.handleFixSiderbar(false)
    },
    handleFixedHeader (fixed) {
      this.$store.commit('fixed_header', fixed)
      this.updateLocal('fixed_header', fixed);
    },
    handleFixSiderbar (fixed) {
      if (this.layout === 'topmenu') {
        this.$store.commit('fixed_sidebar', false)
        this.updateLocal('fixed_header', false);
        return
      }
      this.$store.commit('fixed_sidebar', fixed)
      this.updateLocal('fixed_header', fixed);
    },
    colorChange (color) {
      this.$store.commit('color', color)
      this.updateLocal('color', color);
    },
    updateLocal (field, value) {
      let settings = store.get('defaultSetting');
      settings[field] = value;
      store.set('defaultSetting', settings)
    }
  },
  watch: {
    drawer (val) {
      this.openDrawer = val
    }
  },
  mounted () {}
}
</script>
