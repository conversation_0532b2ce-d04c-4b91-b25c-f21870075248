.setting-drawer{
  .el-drawer{
    width:300px !important;
    overflow: visible;
  }
  .el-drawer__header{
    margin-bottom:0;
  }
  .el-drawer__body{
    padding:20px;
  }
  .setting-drawer-content{
    padding-bottom:20px;
    border-bottom:1px solid $--el-border-color-lighter;
    .setting-drawer-item{
      display:inline-block;
      margin-right:20px;
      position:relative;
      cursor: pointer;
      .setting-drawer-selectIcon{
        position:absolute;
        bottom:15px;
        right:10px;
        i{
          color:$--el-color-primary;
        }
      }
    }
  }
  .setting-drawer-close{
    position:absolute;
    top:200px;
    width:48px;
    height:48px;
    cursor: pointer;
    background:$--el-color-primary;
    left:-48px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    line-height: 48px;
    color:$--color-white;
    text-align: center;
    i{
      color:$--color-white;
    }
  }
}
.setting-button{
  height:48px;
  width:48px;
  background:$--el-color-primary;
  position:fixed;
  top:200px;
  right:0;
  text-align: center;
  line-height: 48px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  cursor: pointer;
  i{
    font-size:20px;
    color:$--color-white;
    &:hover{
      color:$--color-white;
    }
  }
}
