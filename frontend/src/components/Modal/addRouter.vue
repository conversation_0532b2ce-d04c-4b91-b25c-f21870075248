<template>
    <el-dialog
            :before-close="handleClose"
            :title="formInline.id?'编辑路由':'新增路由'"
            :visible.sync="showing"
            append-to-body
            class = "NewlyCompany"
            modal
            modal-append-to-body
            :close-on-click-modal="false"
            width="700px">
        <div>
            <el-form :model="formInline" :rules="rules" label-width="90px" ref="editForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="上级路由" v-if="formInline.pRouterId">
                            <span>{{formInline.pRouterId}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="路由名称" prop="routerName">
                            <el-input :maxLength="100" placeholder="路由名称" v-model="formInline.routerName"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="路由path" prop="path">
                            <el-input :maxLength="256" placeholder="路由path" v-model="formInline.path"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="元数据" prop="meta">
                            <el-input :maxLength="240" placeholder="meta" v-model="formInline.meta"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="图标">
                            <el-input :maxLength="256" placeholder="图标" v-model="formInline.routerIcon"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="排序">
                            <el-input :maxLength="3" placeholder="排序" v-model="formInline.sortNo"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="菜单类型">
                            <el-radio-group v-model="formInline.routerType">
                                <el-radio label="0">一级菜单</el-radio>
                                <el-radio label="1">二级菜单</el-radio>
                                <el-radio label="2">按钮</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="路由描述">
                            <el-input :maxLength="256" placeholder="路由描述" v-model="formInline.routerDes"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="设置资源">
                            <el-select-multiple class="full" ref="resourceList"
                                                v-model="formInline.resourceIdList">
                                <el-select-multiple-option
                                        :key="index"
                                        :label="item.codeValue"
                                        :value="item.codeName"
                                        v-for="(item, index) in resourceList">
                                </el-select-multiple-option>
                            </el-select-multiple>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="closeModal(false)">取消</el-button>
            <el-button @click="sureClick" type="primary">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import { _createMenu, _getResourceDownList, _queryMenuById, _modifyMenu, _menuNameCheck } from "@/api/manage-api";

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data () {
    /* 检查路由名称是否重复
              * */
    const menuNameCheck = (rule, value, callback) => {
      // 没有输入内容，提示错误信息
      if (!value || value.length === 0) {
        return callback(new Error("请输入路由名称"));
      } else {
        // 检验路由名称是否重复
        _menuNameCheck({ id: this.formInline.id, routerName: value }).then(res => {
          if (res.data.success) {
            if (res.data.result) {
              callback();
            } else {
              return callback(new Error("路由名称已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 资源列表
      resourceList: [],
      // 编辑菜单需要的字段
      formInline: {
        id: '',
        // 父路由id
        pRouterId: "",
        // 路由名
        routerName: '',
        // 图标
        routerIcon: '',
        // path
        path: "",
        // 资源
        resourceIdList: [],
        // 类型
        routerType: "",
        // meta title
        meta: "",
        // 路由描述
        routerDes: "",
        sortNo: ''
      },
      // 表单验证规则
      rules: {
        // 路由名
        routerName: [
          { required: true, message: '请输入路由名称', trigger: 'blur' },
          { validator: menuNameCheck, trigger: 'blur' }
        ],
        // 路径
        path: [
          { required: true, message: '请输入path', trigger: 'blur' }
        ],
        // meta title
        // 'metaData.title': [
        //     {required: true, message: '请输入meta title', trigger: 'blur'}
        // ],
        // 路由描述
        routerDes: [
          { required: true, message: '请输入路由描述', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
            * */
    handleClose () {
      this.closeModal(false)
    },
    /* 保存角色
                      * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          // 编辑路由
          if (this.formInline.id) {
            _modifyMenu(this.formInline).then(res => {
              // 编辑成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示信息
                showMessage('info', this.$messageText['updateSuccess']);
              } else {
                // 提示错误信息
                showMessage('error', res.data.errorMsg);
              }
            })
          } else {
            // 新建路由
            _createMenu(this.formInline).then(res => {
              // 新建成功
              if (res.data.success) {
                // 关闭弹出框
                this.closeModal(true);
                // 提示消息
                showMessage('info', this.$messageText['saveSuccess']);
              } else {
                // 提示错误信息
                showMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 取消操作
             * flag:是取消还是保存
            * */
    closeModal (flag) {
      this.$emit('addRouterModalClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
            *  */
    resetForm () {
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
      // 清空下拉数据
      this.$refs.resourceList.clear();
    },
    /* 获取路由下拉列表数据
              * */
    getRouterData () {
      let params = {};
      _getResourceDownList(params).then(res => {
        // 获取成功
        if (res.data.success) {
          // 设置下拉列表的数据
          this.resourceList = res.data.result;
          // 当前是编辑路由
          if (this.formInline.id) {
            // 根据当前路由的id获取路由的详细信息
            _queryMenuById({ id: this.formInline.id }).then(res => {
              // 获取成功
              if (res.data.success) {
                // 设置显示的数据
                this.formInline = res.data.result;
              } else {
                // 提示错误信息
                showMessage('error', res.data.errorMsg)
              }
            })
          }
        }
      })
    }
  },
  watch: {
    isShow (val) {
      // 打开弹出框
      if (val) {
        // 显示弹出框
        this.showing = this.isShow;
        // 更新路由
        if (this.updateData) {
          // 设置数据
          Object.assign(this.formInline, this.updateData);
        }
        // 获取路由详细数据
        this.getRouterData();
      } else {
        // 重置表单
        this.resetForm();
      }
    }
  }

}
</script>
