<template>
    <el-dialog
            :before-close="handleClose"
            :title="formInline.id?'编辑角色':'新增角色'"
            :visible.sync="showing"
            append-to-body
            class="add-role"
            modal
            destroy-on-close
            modal-append-to-body
            :close-on-click-modal="false"
            width="500px">
        <div>
            <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
                <el-form-item label="角色名" prop="roleName">
                    <el-input :maxlength="50" placeholder="角色名" v-model.trim="formInline.roleName"></el-input>
                </el-form-item>
                <el-form-item label="具体描述">
                    <el-input :maxlength="256" placeholder="具体描述" type="textarea" v-model="formInline.roleDes"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @mousedown.native="closeModal(false)" round>取消</el-button>
            <el-button @mousedown.native="sureClick" type="primary" round>保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import { _createRole, _modifyRole, _roleNameCheck } from "@/api/manage-api";
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data () {
    const roleNameCheck = (rule, value, callback) => {
      if (!value || value.length === 0) {
        return callback(new Error("请输入角色名"));
      } else {
        _roleNameCheck({ id: this.formInline.id, roleName: value }).then(res => {
          if (res.data.success) {
            if (res.data.result) {
              callback();
            } else {
              return callback(new Error("角色名已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 角色名
        roleName: '',
        // 具体描述
        roleDes: ''
      },
      // 表单验证规则
      rules: {
        roleName: [
          { required: true, message: '请输入角色名', trigger: 'blur' },
          { validator: roleNameCheck, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
      * */
    handleClose () {
      this.closeModal(false)
    },
    /* 关闭弹出框
       * flag:是取消还是保存
      * */
    closeModal (flag) {
      this.$emit('addRoleModalClose', flag);
      this.showing = false;
    },
    /* 保存角色
      * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          // 编辑角色
          if (this.formInline.id) {
            _modifyRole(this.formInline).then(res => {
              if (res.data.success) {
                showMessage('info', this.$messageText['updateSuccess']);
                this.closeModal(true);
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          } else {
            _createRole(this.formInline).then(res => {
              if (res.data.success) {
                showMessage('info', this.$messageText['saveSuccess']);
                this.closeModal(true);
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 清空表单数据
     *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline))
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData) {
          Object.assign(this.formInline, this.updateData);
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style lang="scss">
.add-role {
  .el-form-item{
    margin-bottom: 20px;
  }
}
</style>