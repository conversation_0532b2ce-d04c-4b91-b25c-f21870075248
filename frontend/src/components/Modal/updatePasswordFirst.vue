<template>
  <el-dialog
      :before-close="handleClose"
      :visible.sync="isShow"
      append-to-body
      modal
      modal-append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      title="用户首次登录需要"
      width="500px">
    <div>
      <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input placeholder="旧密码" show-password v-model="formInline.oldPassword" @input="handleOldPasswordInput"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input placeholder="新密码" show-password v-model="formInline.newPassword" @input="handleNewPasswordInput"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input placeholder="确认密码" show-password v-model="formInline.confirmPassword" @input="handleConfirmPasswordInput"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <!--      <el-button @click="closeModal(false)">取消</el-button>-->
      <el-button @click="sureClick" type="primary">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import store from '@/store'
import { _changePassword } from "@/api/user-api";
import { _validatePassword } from "@/utils/validate";
import md5 from 'blueimp-md5';
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data () {
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.formInline.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };

    let validatePassword = (rule,value, callback) => {
      if (!_validatePassword(value)) {
        callback(new Error("密码不符合规则：【长度8-32位，需要包含数字、大小写字母、特殊符号】"))
      }else {
        callback();
      }
    };
    return {
      // 编辑角色需要的字段
      formInline: {
        // 用户id
        id: store.state.app.info.id,
        // 老密码
        oldPassword: '',
        // 新密码
        newPassword: '',
        // 确认密码
        confirmPassword: ''
      },
      // 表单验证规则
      rules: {
        oldPassword: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {

    handleClose () {
      this.closeModal(false);
    },
    // 保存密码
    sureClick () {
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          this.formInline.oldPassword = md5(this.formInline.oldPassword)
          this.formInline.newPassword = md5(this.formInline.newPassword)
          this.formInline.confirmPassword = md5(this.formInline.confirmPassword)
          _changePassword(this.formInline).then(res => {
            if (res.data.success) {
              localStorage.setItem("firstLoginUpdated", "1");
              showMessage('info', this.$messageText['updateSuccess']);
              this.closeModal(true);
            } else {
              showMessage('error', res.data.errorMsg);
            }
          })
        } else {
          return false;
        }
      });
    },
    handleOldPasswordInput (e) {
      if (this.formInline.oldPassword.length > 20) {
        this.formInline.oldPassword = e.substring(0, 20);
      }
    },
    handleNewPasswordInput (e) {
      if (this.formInline.newPassword.length > 20) {
        this.formInline.newPassword = e.substring(0, 20);
      }
    },
    handleConfirmPasswordInput (e) {
      if (this.formInline.confirmPassword.length > 20) {
        this.formInline.confirmPassword = e.substring(0, 20);
      }
    },
    /* 清空表单数据
     *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      Object.assign(this.formInline, this.$options.data().formInline);
    },
    /* 取消操作
      * flag:是取消还是保存
     * */
    closeModal (flag) {
      this.$emit('updatePasswordModalFirstClose', flag);
    }
  },
  watch: {}

}
</script>
