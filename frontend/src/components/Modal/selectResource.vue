<template>
    <el-dialog
            :before-close="handleClose"
            :visible.sync="showing"
            append-to-body
            modal
            modal-append-to-body
            :close-on-click-modal="false"
            title="授权"
            width="500px">
        <div>
            <el-checkbox :indeterminate="isIndeterminate" @change="handleCheckAllChange" v-model="checkAll">全选</el-checkbox>
            <div style="margin: 15px 0;"></div>
            <el-checkbox-group v-model="checkedResources">
                <el-checkbox>新建</el-checkbox>
                <el-checkbox>删除</el-checkbox>
            </el-checkbox-group>
        </div>
        <div slot="footer">
            <el-button @click="cancelClick">取消</el-button>
            <el-button @click="sureClick" type="primary">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  data () {
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑菜单需要的字段
      formInline: {
        // 菜单名
        name: '',
        // 图标
        icon: '',
        // 标识
        biaoshi: "",
        // url
        url: ""
      },
      // 是否全部选中
      checkAll: false,
      // 当前选中的资源列表
      checkedResources: [],
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入角色名', trigger: 'blur' }
        ],
        biaoshi: [
          { required: true, message: '请输入授权标识', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '请输入url', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
                      * flag:是取消还是保存
                      * */
    handleClose (flag) {
      this.$emit('addMenuModalClose', flag);
      this.showing = false;
    },
    /* 保存角色
                      * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          this.handleClose(true);
          showMessage('info', this.$messageText['saveSuccess']);
        } else {
          return false;
        }
      });
    },
    /* 点击取消
                      * */
    cancelClick () {
      this.handleClose(false);
    },
    /* 清空表单数据
            *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      Object.assign(this.formInline, this.$options.data().formInline);
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData) {
          Object.assign(this.formInline, this.updateData);
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
