<template>
    <el-dialog
            :before-close="handleClose"
            :title="type === 'setting'?'设置权限':'菜单排序'"
            :visible.sync="showing"
            append-to-body
            modal
            modal-append-to-body
            :close-on-click-modal="false"
            width="500px">
        <div>
            <el-tree
                    :allow-drop="allowDrop"
                    :data="data"
                    :default-expand-all="expandAll"
                    :draggable="type === 'setting'?false:true"
                    :props="type === 'setting'?defaultSettingProps:defaultProps"
                    :show-checkbox="type === 'setting'?true:false"
                    @node-drag-end="handleDragEnd"
                    node-key="id"
                    ref="menuList">
            </el-tree>
        </div>
        <div slot="footer">
            <el-button @click="clearAll">清空</el-button>

            <el-button @click="closeModal(false)">取消</el-button>
            <!--<el-button @click="expandAll = !expandAll" type="primary">-->
                <!--<span v-if="expandAll">收起</span>-->
                <!--<span v-else>展开</span>-->
            <!--</el-button>-->
            <el-button @click="checkAll" type="primary">全选</el-button>
            <el-button @click="sureClick" type="primary">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import { _setAuthority, _getAuthorityDownList, _getAuthorityCheckedList, _getMenuSort, _setMenuSort } from "@/api/manage-api";

export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 弹出框的类型
    type: {
      type: String,
      default: 'setting'
    },
    // 角色id
    roleId: {
      type: String
    }
  },
  data () {
    return {
      // 是否显示弹出框
      showing: false,
      // 树转换数据
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultSettingProps: {
        children: 'menuList',
        label: 'menuDes'
      },
      // 设置权限需要数据
      formInline: {
        // 角色id
        roleId: "",
        // 权限列表
        menuIdList: []
      },
      // 列表数据
      data: [],
      // 全部展开
      expandAll: true
    }
  },
  methods: {
    /* 取消操作
    * */
    handleClose () {
      this.closeModal(false);
    },
    /* 关闭弹出框
   * flag:是取消还是保存
   * */
    closeModal (flag) {
      this.$emit('menuListModalClose', flag);
      this.showing = false;
    },
    /* 保存
    * */
    sureClick () {
      if (this.type === 'setting') {
        // 获取选中的节点
        let nodes = this.$refs.menuList.getCheckedNodes(false, true);
        // 没有选中数据
        if (nodes.length === 0) {
          // 提示信息
          showMessage('error', this.$messageText['menuSelectNone']);
          return;
        }
        let ids = nodes.map((i) => { return i.id })
        // 赋值选中节点数据
        this.formInline.menuIdList = ids;
        _setAuthority(this.formInline).then(res => {
          if (res.data.success) {
            this.closeModal(true);
            showMessage('info', this.$messageText['authorizeSuccess']);
          } else {
            showMessage('error', res.data.errorMsg);
          }
        })
      } else {
        _setMenuSort({ treeDtoList: this.data }).then(res => {
          if (res.data.success) {
            this.closeModal(true);
            showMessage('info', this.$messageText['updateSuccess']);
          } else {
            showMessage('error', res.data.errorMsg);
          }
        })
      }
    },
    /* 拖拽结束
      * */
    handleDragEnd (draggingNode, dropNode, dropType) {
      this.setDataSort(this.data);
      console.log('tree drag end: ', dropNode && dropNode.label, dropType);
    },
    /* 设置排序
      * */
    setDataSort (data) {
      data.map((i, idx) => {
        i.sort = idx;
        if (i.children && i.children.length > 0) {
          this.setDataSort(i.children);
        }
      })
    },
    /* 设置允许拖拽数据
      * draggingNode：当前拖拽节点
      * dropNode：拖拽节点
      * type：拖拽类型
      * */
    allowDrop (draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level) {
        if (draggingNode.parent.id === dropNode.parent.id) {
          // 向上拖拽 || 向下拖拽
          return type === "prev" || type === "next";
        }
      } else {
        // 不同级进行处理
        return false;
      }
    },
    /* 获取菜单列表
     * */
    getAuthorityDownList () {
      // 获取菜单列表
      _getAuthorityDownList().then(res => {
        // 获取成功
        if (res.data.success) {
          // 设置菜单数据
          this.data = res.data.result;
          // 获取选中数据
          _getAuthorityCheckedList({ roleId: this.roleId }).then(response => {
            // 获取成功
            if (response.data.success) {
              // 勾选选中数据
              let result = this.getLeafNode(this.data, []);
              let checkNodes = response.data.result.filter((i) => { return result.indexOf(i) >= 0 });

              this.$refs.menuList.setCheckedKeys(checkNodes);
            } else {
              showMessage('error', response.data.errorMsg);
            }
          })
        }
      })
    },
    /* 设置选中的节点
      * */
    getLeafNode (data, result) {
      data.map((i) => {
        if (!i.menuList || i.menuList.length === 0) {
          result.push(i.id);
        } else {
          this.getLeafNode(i.menuList, result);
        }
      })
      return result;
    },
    getSortList () {
      _getMenuSort().then(res => {
        if (res.data.success) {
          this.data = res.data.result;
        } else {
          showMessage('error', res.data.errorMsg);
        }
      })
    },
    /* 清空
      * */
    clearAll () {
      this.$refs.menuList.setCheckedKeys([]);
    },
    /* 全选
    * */
    checkAll () {
      let keys = this.data.map((i) => { return i.id });
      this.$refs.menuList.setCheckedKeys(keys);
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        // 授权弹出框，设置角色id
        if (this.type === 'setting') {
          this.formInline.roleId = this.roleId;
          this.getAuthorityDownList();
        } else {
          this.getSortList();
        }
      }
    }
  }

}
</script>
