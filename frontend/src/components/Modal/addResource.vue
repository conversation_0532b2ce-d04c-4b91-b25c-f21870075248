<template>
    <el-dialog
            :before-close="handleClose"
            :title="formInline.id?'编辑资源':'新增资源'"
            :visible.sync="showing"
            append-to-body
            class = "NewlyCompany"
            modal
            modal-append-to-body
            :close-on-click-modal="false"
            width="500px">
        <div>
            <el-form :model="formInline" :rules="rules" label-width="110px" ref="editForm">
                <el-form-item label="资源名称" prop="resourceName">
                    <el-input :maxLength="50" placeholder="资源名称" v-model="formInline.resourceName"></el-input>
                </el-form-item>
                <el-form-item label="资源url" prop="resourceUrl">
                    <el-input :maxLength="256" placeholder="资源url" v-model="formInline.resourceUrl"></el-input>
                </el-form-item>
                <el-form-item label="资源描述">
                    <el-input :maxLength="225" placeholder="资源描述" v-model="formInline.resourceDes"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer">
            <el-button @click="closeModal(false)">取消</el-button>
            <el-button @click="sureClick" type="primary">保存</el-button>
        </div>
        <menu-list :isShow="showModal"></menu-list>
    </el-dialog>
</template>
<script>
import { showMessage } from 'stock-vue-plugin/lib/utils/message'
import menuList from '../../components/Modal/menuList'
import { _createResource, _modifyResource, _resourceNameCheck } from "@/api/manage-api";
export default {
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    }
  },
  components: { menuList },
  data () {
    const resourceNameCheck = (rule, value, callback) => {
      if (!value || value.length === 0) {
        return callback(new Error("请输入资源名称"));
      } else {
        _resourceNameCheck({ id: this.formInline.id, resourceName: value }).then(res => {
          if (res.data.success) {
            if (res.data.result) {
              callback();
            } else {
              return callback(new Error("资源名称已存在"));
            }
          }
          callback();
        });
      }
    }
    return {
      // 是否显示弹出框
      showing: false,
      // 编辑角色需要的字段
      formInline: {
        // 资源名
        resourceName: '',
        // 描述
        resourceDes: '',
        // 资源url
        resourceUrl: ''
      },
      // 是否打开选择菜单弹出框
      showModal: false,
      // 表单验证规则
      rules: {
        resourceName: [
          { required: true, message: '请输入资源名称', trigger: 'blur' },
          { validator: resourceNameCheck, trigger: 'blur' }
        ],
        resourceUrl: [
          { required: true, message: '请输入资源url', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    /* 取消操作
              * flag:是取消还是保存
              * */
    handleClose () {
      this.closeModal(false)
    },
    /* 保存角色
              * */
    sureClick () { // 点击确定按钮
      // 开启表单验证
      this.$refs['editForm'].validate((valid) => {
        // 验证成功
        if (valid) {
          // 更新
          if (this.formInline.id) {
            _modifyResource(this.formInline).then(res => {
              if (res.data.success) {
                this.closeModal(true);
                showMessage('info', this.$messageText['updateSuccess']);
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          } else {
            _createResource(this.formInline).then(res => {
              if (res.data.success) {
                this.closeModal(true);
                showMessage('info', this.$messageText['saveSuccess']);
              } else {
                showMessage('error', res.data.errorMsg);
              }
            })
          }
        } else {
          return false;
        }
      });
    },
    /* 取消操作
                * flag:是取消还是保存
                * */
    closeModal (flag) {
      this.$emit('addResourceModalClose', flag);
      this.showing = false;
    },
    /* 清空表单数据
             *  */
    resetForm () {
      // 重置表单
      this.$refs['editForm'].resetFields();
      // 重置数据
      this.formInline = JSON.parse(JSON.stringify(this.$options.data().formInline));
    }
  },
  watch: {
    isShow (val) {
      if (val) {
        this.showing = this.isShow;
        if (this.updateData) {
          Object.assign(this.formInline, this.updateData);
        }
      } else {
        this.resetForm();
      }
    }
  }

}
</script>
<style>
    .NewlyCompany .el-form-item{
        margin-bottom: 20px;
    }
</style>