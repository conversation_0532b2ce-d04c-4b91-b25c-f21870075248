<template>
  <div class="abbreviation" ref='abbreviation'>
      <div class="txt">
        <p :title="htm" v-html="htm"></p>
      </div>
      <div class="abbreviation--slot--default">
        <slot></slot>
      </div>
  </div>
</template>

<script>

export default {
  name: 'DataEllipsis',

  components: {

  },

  props: {
    htm: {
      default: ''
    },
    line: {
      type: Number,
      default: 2
    },
    divWidth: {
      default: ''
    }
    // search:{
    //   type:Array,
    //   default:[]
    // },
  },

  computed: {
  },

  provide () {
    return {
      rootAbbreviation: this
    };
  },

  data () {
    return {
      txtDom: '',
      title: ''
    };
  },

  watch: {
    // search(){
    //   this.cdtmfw(this.htm,this.line,this.$refs['abbreviation'])
    // },
    htm () {
      this.cdtmfw(this.htm, this.line, this.$refs['abbreviation'])
    }
  },

  methods: {

    // created Dom to Match Font Width
    cdtmfw (value, line, obj) {
      let div = document.createElement("div");
      div.style.visibility = 'hidden';
      div.style.whiteSpace = 'nowrap';
      div.style.width = 'auto';
      div.style.position = 'absolute';
      div.style.fontSize = "14px";
      div.innerHTML = value;
      if (typeof obj === 'undefined') {
        return;
      }
      document.body.appendChild(div);
      var _parentElement = div.parentNode;
      let valuefor = div.textContent;
      let aWidth = div.offsetWidth;
      let reduceWidth = this.divWidth * 0.5;
      if (this.divWidth * this.line - reduceWidth >= aWidth) {
        if (_parentElement) {
          _parentElement.removeChild(div);
        }
        this.txtDom = value;
      } else {
        let text = '', newWidth;
        for (var i = 0; i < valuefor.length; i++) {
          text += valuefor[i]
          div.innerHTML = text;
          newWidth = div.offsetWidth;
          if (this.divWidth * this.line - reduceWidth <= newWidth) {
            text = text.substr(0, text.length - 1)
            this.txtDom = text + '...'
            break;
          }
        }
        if (_parentElement) {
          _parentElement.removeChild(div);
        }
      }
    }
  },

  created () {

  },

  mounted () {
    this.$nextTick(function () {
      this.cdtmfw(this.htm, this.line, this.$refs['abbreviation'])
      window.onresize = () => {
        this.cdtmfw(this.htm, this.line, this.$refs['abbreviation'])
      }
    })
  },

  updated () {
  }
};
</script>
