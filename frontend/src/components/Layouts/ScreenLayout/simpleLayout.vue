<template>
    <div>
        <el-container direction="vertical">
            <el-header :class="fixedHeader?'fixed-header':''" class="menu-header">
                <simple-header-layout :isCollapse="isCollapse" @changeCollapse="changeCollapse"></simple-header-layout>
            </el-header>
            <el-main :class="fixedHeader?'fixed-content':''" class="main-content" v-loading="loading">
                <router-view :key="$route.fullPath"/>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import simpleHeaderLayout from '../Header/simpleHeader'
import { baseMixin } from '../../../store/app-mixin'
import store from '@/store'
import storage from 'store'

export default {
  name: 'SimpleLayout',
  components: { simpleHeaderLayout },
  data () {
    return {
      isCollapse: false,
      drawer: false,
      loading: store.getters.storeLoading
    }
  },
  computed: {
    setWidth () {
      return this.isCollapse ? '64px' : '256px'
    }
  },
  mixins: [baseMixin],
  mounted () {
    store.commit('SET_MENU_DATA', storage.get('platform_menuData'));
  },
  methods: {
    changeCollapse () {
      this.isCollapse = !this.isCollapse
    },
    closeDrawer () {
      this.drawer = false
    },
  }
}
</script>

<style lang="scss">
@import "./simpleLayout.scss";
</style>
