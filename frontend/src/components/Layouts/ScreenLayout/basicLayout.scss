.el-menu-item{
  &:hover{
    background-color:transparent !important;
    color:$--el-color-primary;
  }
}
.el-submenu__title{
  &:hover{
    background-color:transparent !important;
    color:$--el-color-primary;
  }
}

.main-content{
  //background:$--main-content-bg;
  overflow: inherit;
  &.fixed-content{
    margin-top:60px;
  }
}

.nav-theme-dark{
  background:$--el-aside-dark;
  .el-menu-item,.el-submenu__title{
    color:rgba(255,255,255,0.6);
    &:hover{
      color:$--color-white;
    }
    &.is-active{
      color:$--color-white;
    }
  }
  .el-submenu{
    &.is-active{
      .el-submenu__title{
        color:$--color-white;
      }
    }
    &.is-opened{
      .el-menu{
        .el-menu-item{
          background-color: #000c17 !important;
          &.is-active{
            background-color: $--el-color-primary !important;
          }
        }
      }
      i{
        color:$--color-white;
      }
    }
  }
}

.nav-theme-light{
  background:$--color-white;
  .sider-menu-logo{
    .title{
      color:$--el-color-primary;
    }
  }
  .menu-layout:not(.el-menu--collapse) .el-menu-item.is-active {
    background: $--el-color-primary-light-8 !important;
    border-right:3px solid $--el-color-primary;
  }
  .left-aside{
    border-top:1px solid $--el-border-color-lighter;
  }
}

.el-menu--horizontal {
  border-bottom:none !important;
  .el-menu-item{
    float: left;
    height: 50px;
    line-height: 50px;
    margin: 0;
  }
  .el-submenu{
    float: left;
    .el-submenu__title{
      height:60px;
      line-height: 50px;
    }
  }
  &.dark{
    .el-submenu{
      .el-submenu__title{
        color:rgba(255,255,255,0.6);
        &:hover{
          color:$--color-white;
        }
      }
      &.is-active{
        .el-submenu__title{
          color:$--color-white;
        }
      }
    }
    .el-menu-item{
      color:rgba(255,255,255,0.6);
      border-bottom: 2px solid transparent;
    }
  }
  .el-submenu__icon-arrow{
    display:none;
  }
  .el-menu .el-menu-item.is-active, .el-menu .el-submenu.is-active>.el-submenu__title{
    background:$--color-white !important;
    color:$--el-color-primary;
  }
  .el-menu-item:not(.is-disabled):focus, .el-menu-item:not(.is-disabled):hover{
    color:$--el-color-primary;
  }
  &.light{
    .el-menu-item{
      &:hover{
        color:$--el-color-primary;
      }
    }
    .el-submenu{
      &.is-active{
        .el-submenu__title{
          color:$--el-color-primary;
        }
      }
      .el-submenu__title{
        &:hover{
          background:transparent !important;
        }
      }
      &.is-active{
        .el-submenu__title{
          height: 60px;
          border-bottom:3px solid $--el-color-primary;
        }
      }
    }
  }
  .el-menu-item:not(.is-active){
    &.light{
      &:hover{
        color:$--el-color-primary;
      }
    }
  }
}

.fixed-container{
  padding-left:256px;
}

.theme-color{
  margin-top:24px;
  overflow: hidden;
  .theme-color-title{
    margin-bottom:12px;
    font-size:14px;
    line-height: 22px;
    margin-top:0;
  }
}

.el-aside{
  overflow-x: hidden;
}
.menu-header{
  height: 60px!important;
}