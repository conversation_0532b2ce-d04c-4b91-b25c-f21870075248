<template>
    <div>
        <el-container direction="horizontal"  v-if="layout === 'sidemenu'">
            <div :class="[navTheme === 'dark'?'nav-theme-dark':'nav-theme-light',fixedSidebar?'fixed-sidebar':'']">
                <div :class="isCollapse?'sider-menu-logo-collapse':''" class="sider-menu-logo">
                    <img alt="logo" class="logo" src="~@/assets/images/logo.png">
                    <span class="title">上市公司及股东一体化服务平台</span>
                </div>
                <el-aside :class="navTheme === 'dark'?'el-aside-dark':'el-aside-light'" :width="setWidth"
                          class="left-aside">
                    <menu-layout :isCollapse="isCollapse"></menu-layout>
                </el-aside>
            </div>

            <el-container :class="fixedSidebar?'fixed-container':''">
                <el-header :class="fixedHeader?'fixed-header':''" class="menu-header">
                    <header-layout  :isCollapse="isCollapse" @changeCollapse="changeCollapse"></header-layout>
                </el-header>
                <el-main :class="fixedHeader?'fixed-content':''" class="main-content" v-loading="loading">
                    <router-view :key="$route.fullPath"/>
                </el-main>
            </el-container>
        </el-container>
        <el-container direction="vertical"  v-else>
            <el-header :class="fixedHeader?'fixed-header':''" class="menu-header">
                <header-layout  :isCollapse="isCollapse" @changeCollapse="changeCollapse"></header-layout>
            </el-header>
            <el-main :class="fixedHeader?'fixed-content':''" class="main-content">
                <router-view :key="$route.fullPath"/>
            </el-main>
        </el-container>
        <!--<right-menu :drawer="drawer" @change="handleSettingChange"-->
                    <!--@close-drawer="closeDrawer"></right-menu>-->
        <!--<div @click="drawer = true" class="setting-button" >-->
            <!--<i class="iconfont ic-set"></i>-->
        <!--</div>-->
    </div>
</template>
<script>
import headerLayout from '../Header/header'
import menuLayout from '../SlideMenu/slideMenu'
import rightMenu from '../../../components/SettingDrawer/settingDrawer'
import { baseMixin } from '../../../store/app-mixin'
import store from '@/store'
import storage from 'store'
export default {
  components: { headerLayout, menuLayout, rightMenu },
  data () {
    return {
      isCollapse: false,
      drawer: false,
      loading: store.getters.storeLoading
    }
  },
  computed: {
    setWidth () {
      return this.isCollapse ? '64px' : '256px'
    }
  },
  mixins: [baseMixin],
  mounted () {
    store.commit('SET_MENU_DATA', storage.get('platform_menuData'));
  },
  methods: {
    changeCollapse () {
      this.isCollapse = !this.isCollapse
    },
    closeDrawer () {
      this.drawer = false
    }
  }
}
</script>
<style lang="scss">
    @import "./basicLayout.scss";
</style>
