<template>
  <el-dialog
      title="选择登入账号"
      :before-close="handleClose"
      :visible.sync="showing"
      append-to-body
      :close-on-click-modal="false"
      class="add-person NewlyCompany"
      modal
      modal-append-to-body
      width="616px" height="318px">
    <div>
        <el-row>
          <el-col :span="12">
            <div class="buttonInfo choice-one" :class="{ active: isChoiceOneActive }" @click="choiceClick('one')">
              <div class="buttonInfo-title" :title="this.updateData.userName">
                {{ this.updateData.userName }}
              </div>
              <div class="buttonInfo-phone" :title="this.updateData.telephone">
                手机号:  {{ this.updateData.telephone }}
              </div>
              <div class="buttonInfo-org" :title="this.updateData.orgName">
                组织架构:  {{ this.updateData.orgName }}
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="buttonInfo choice-two" :class="{ active: isChoiceTwoActive }" @click="choiceClick('two')">
              <div class="buttonInfo-title" :title="this.updateData.childrenUserName">
                {{ this.updateData.childrenUserName }}
              </div>
              <div class="buttonInfo-phone" :title="this.updateData.telephone">
                手机号:  {{ this.updateData.telephone }}
              </div>
              <div class="buttonInfo-org" :title="this.updateData.childrenOrgName">
                组织架构:  {{ this.updateData.childrenOrgName }}
              </div>
            </div>
          </el-col>
        </el-row>
        <!--        <el-row>-->
        <!--          <el-col :span="12">-->
        <!--            <el-button round type="text" style="border-color: #8e8e8e;" @click="choiceUserLogin(updateData.userName)">-->
        <!--              <el-descriptions :title="this.updateData.userName">-->
        <!--                <el-descriptions-item label="手机号">{{ this.updateData.phone }}</el-descriptions-item>-->
        <!--              </el-descriptions>-->
        <!--            </el-button>-->
        <!--          </el-col>-->
        <!--          <el-col :span="12">-->
        <!--            <el-button round type="text" style="border-color: #8e8e8e"-->
        <!--                       @click="choiceUserLogin(updateData.childrenUserName)">-->
        <!--              <el-descriptions :title="this.updateData.childrenUserName">-->
        <!--                <el-descriptions-item label="手机号">{{ this.updateData.phone }}</el-descriptions-item>-->
        <!--              </el-descriptions>-->
        <!--            </el-button>-->
        <!--          </el-col>-->
        <!--        </el-row>-->
    </div>
    <div slot="footer">
      <el-button @click="closeModal(false)">取消</el-button>
      <el-button @click="sureClick" type="primary">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { _getOrgName } from "@/api/organization-api";
export default {
  name: 'choiceUser',
  props: {
    // 是否显示弹出框
    isShow: {
      type: Boolean,
      default: false
    },
    // 编辑用户的信息
    updateData: {
      type: Object
    },
    // 标题
    title: {
      type: String
    }
  },
  data() {
    return {
      isChoiceOneActive: false,
      isChoiceTwoActive: false,
      choiceUser: "",
      // 是否显示弹出框
      showing: false,
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
    choiceUserLogin(userName) {
      window.open(this.updateData.key + '&userName=' + userName);
    },
    /* 关闭弹出框
      * */
    handleClose() {
      this.closeModal(false);
    },
    /* 保存人员信息* */
    sureClick() { // 点击确定按钮
      if (this.isChoiceOneActive || this.isChoiceTwoActive) {
        let userName = this.isChoiceOneActive ? this.updateData.userName : this.updateData.childrenUserName;
        window.open(this.updateData.key + '&userName=' + userName);
        this.closeModal(false);
      } else {
        this.$message({
          message: '请选择登入账号',
          type: 'warning'
        });
      }
    },
    /* 关闭弹出框
    *flag:是打开还是取消
    *  */
    closeModal(flag) {
      this.$emit('choiceClose', flag);
      this.showing = false;
      this.isChoiceOneActive = false;
      this.isChoiceTwoActive = false;
    },
    choiceClick(choice) {
      switch (choice) {
        case 'one':
          this.isChoiceOneActive = !this.isChoiceOneActive;
          this.isChoiceTwoActive = false;
          break;
        case 'two':
          this.isChoiceTwoActive = !this.isChoiceTwoActive;
          this.isChoiceOneActive = false;
          break;
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        this.showing = this.isShow;
      }
    }
  }

}
</script>

<style scoped lang="scss">

</style>

<style>
.buttonInfo {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  margin-left: 28px;
  margin-top: 10%;
  width: 240px;
  height: 140px;
  border: 2px solid #E0E4F2;
  border-radius: 18px;
  //border-color: rgb(224,226,248);
  box-shadow: none;
  background-color: #F4F6FC;
  place-items: center; /* 同时水平和垂直居中 */
}

.buttonInfo:hover {
  border-color: #c1cdff;
  background-color: #DEE4FF;
}

.buttonInfo.active {
  border-color: #c1cdff;
  background-color: #DEE4FF /* 选中状态的背景色 */;
}

.buttonInfo-title {
  margin: 20px 0px 20px 20px;
  text-align: left;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 600;
  line-height: 22.4px;
  text-align: left;
  width: 200px; /* 设置一个固定宽度 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.buttonInfo-phone {
  margin: 20px 0px 20px 20px;
  //styleName: PingFang/14/R;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 19.6px;
  text-align: left;
  width: 200px; /* 设置一个固定宽度 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.buttonInfo-org {
  margin: 20px 0px 20px 20px;
  //styleName: PingFang/14/R;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 19.6px;
  text-align: left;
  width: 200px; /* 设置一个固定宽度 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}

</style>