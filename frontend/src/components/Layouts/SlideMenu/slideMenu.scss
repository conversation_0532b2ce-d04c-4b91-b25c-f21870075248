.left-aside{
  &.el-aside-dark{
    background:$--el-aside-dark;
    transition:all 0.3s;
  }
  .el-menu{
    border-right:none;
  }

}
.menu-header{
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index:9;
  background:$--color-white;
  &.fixed-header{
    position:fixed;
    z-index:9;
    top:0;
    width:100%;
  }
}

.menu-layout:not(.el-menu--collapse):not(.el-menu--horizontal){
  width: 256px;
  min-height: 400px;
  .el-menu-item{
    
    &.is-active{
      background-color:$--el-color-primary !important;
    }
  }
  
}
.el-menu-item{
  padding: 0;
  padding-right: 40px;
}
.sider-menu-logo{
  height:50px;
  line-height: 50px;
  padding-left:16px;
  box-shadow: 1px 1px 0 0 $--logo-shadow;
  overflow: hidden;
}

.sider-menu-logo{
  img{
    height:32px;
    width:32px;
    vertical-align: middle;
  }
  .title{
    font-size:20px;
    margin:0 0 0 12px;
    font-weight:600;
    color:$--color-white;
    vertical-align: middle;
  }
  &.sider-menu-logo-collapse{
    width:48px;
  }
}

.el-menu--collapse .el-menu-item span, .el-menu--collapse .el-submenu>.el-submenu__title span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

.el-menu--collapse .el-menu-item .el-submenu__icon-arrow, .el-menu--collapse .el-submenu>.el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.fixed-sidebar{
  position:fixed;
  top:0;
  left:0;
  flex:0 0 256px;
  z-index:10;
  height:100%;
}

//2021 11 -2
.slide-item-container {
  display: flex;
  justify-content: center;
}
