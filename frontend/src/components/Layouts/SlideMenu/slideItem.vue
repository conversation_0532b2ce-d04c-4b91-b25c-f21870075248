<template>
    <div class="slide-item-container clearfix">
        <template v-for="item in list">
          <el-menu-item style="height: 60px;" :index="item.path" :key="item.id" v-if="item.menuList && item.menuList.length===0">
            <div class="border" >
              <i :class="item['menuIcon']" class="el-icon- iconfont" v-if="item['menuIcon']"></i>
              <span v-html="item.metaData.title" style="font-size:16px"></span>
            </div>
          </el-menu-item>
          <el-submenu :index="item.path" :key="item.id" v-else>
            <template slot="title">
              <div class="border">
                <i :class="item['menuIcon']" class="el-icon- iconfont" v-if="item['menuIcon']"></i>
                <span v-html="item.metaData.title" style="font-size:16px"></span>
                <i class="el-icon-arrow-down"  aria-hidden="false"></i>
              </div>
            </template>
            <template v-for="child in item.menuList">
              <el-menu-item class="light" :index="child.path" :key="child.name"
                            v-if="child.menuList && child.menuList.length === 0">
                <span>{{child.metaData.title}}</span>
              </el-menu-item>
              <el-submenu class="light" :index="child.path" :key="child.id" v-else>
                <template slot="title">
                  <span>{{child.metaData.title}}</span>
                </template>
                <slide-item :is-nest="true" :key="child.id" :list="child.menuList" class="nest-menu"></slide-item>
              </el-submenu>
            </template>
          </el-submenu>
        </template>
    </div>
</template>
<script>
export default {
  name: 'slideItem',
  props: {
    list: Array
  }
}

</script>
<style lang="scss" scoped>
.el-menu--horizontal .el-menu-item:not(.is-active).light:hover{
  color: $--color-white !important;
  background-color: $--el-color-primary !important;
}
.border{
  padding: 7px;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>>

