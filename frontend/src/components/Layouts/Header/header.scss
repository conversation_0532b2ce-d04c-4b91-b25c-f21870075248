.header-expand{
  i{
    font-size:24px;
    cursor: pointer;
  }
  line-height: 50px;
}

.header-index-right{
  float: right;
  height: 100%;
  margin-left: auto;
  overflow: hidden;
  line-height: 50px;
  cursor: pointer;
}

.top-nav-header{
  margin:0 -20px;
  .top-nav-header-main{
    height:50px;
    display:flex;
    padding:0 20px;
    .top-nav-header-left{
      display: flex;
      align-items: center;
      height: 60px;

      .top-nav-header-logo{
        width:auto;
        height:60px;
        overflow: hidden;
        line-height: 60px;
        transition: all 0.3s;
        img{
          //padding-bottom: 5px;
          //height:55px;
          vertical-align: middle;
        }
        h1{
          display:inline-block;
          margin:0 0 0 12px;
          font-size:16px;
          vertical-align: top;
        }
      }
    }
    .top-nav-header-menu{
      flex:1 1 0%;
    }
    .top-nav-header-right{
      line-height:60px;
      cursor: pointer;
      .top-nav-header-agreement {
        color: #666666;
        font-size: 14px;
        cursor: pointer;
        margin-right: 20px;
        font-weight: 400;
      }
    }
  }
  &.dark{
    background:$--el-aside-dark;
    .top-nav-header-logo{
      h1{
        color:$--color-white;
      }
    }
    .top-nav-header-right{
      .el-dropdown{
        color:$--color-white;
      }
    }
  }
}
