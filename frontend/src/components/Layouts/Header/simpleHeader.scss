// 继承原始样式，但调整布局以适应没有菜单的情况
.header-expand{
  i{
    font-size:24px;
    cursor: pointer;
  }
  line-height: 50px;
}

.header-index-right{
  float: right;
  height: 100%;
  margin-left: auto;
  overflow: hidden;
  line-height: 50px;
  cursor: pointer;
}

.top-nav-header{
  margin:0 -20px;
  .top-nav-header-main{
    height:50px;
    display:flex;
    justify-content: space-between; // 修改：左右分布，去掉中间菜单
    align-items: center; // 添加：垂直居中
    padding:0 20px;
    .top-nav-header-left{
      display: flex;
      align-items: center;
      height: 60px;

      .top-nav-header-logo{
        width:auto;
        height:60px;
        overflow: hidden;
        line-height: 60px;
        transition: all 0.3s;
        img{
          //padding-bottom: 5px;
          //height:55px;
          vertical-align: middle;
        }
        h1{
          display:inline-block;
          margin:0 0 0 12px;
          font-size:16px;
          vertical-align: top;
        }
      }
    }
    // 去掉菜单部分
    // .top-nav-header-menu{
    //   flex:1 1 0%;
    // }
    .top-nav-header-right{
      line-height:60px;
      cursor: pointer;
    }
  }
  &.dark{
    background:$--el-aside-dark;
    .top-nav-header-logo{
      h1{
        color:$--color-white;
      }
    }
    .top-nav-header-right{
      .el-dropdown{
        color:$--color-white;
      }
    }
  }
}
