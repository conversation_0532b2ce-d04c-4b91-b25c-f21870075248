<script>
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import mila from 'markdown-it-link-attributes'
import mdKatex from '@traptitech/markdown-it-katex'
import { copyToClip } from '@/utils'
import mikg from 'markdown-it-katex-gpt'

export default {
  name: 'MarkdownContent',
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  data () {
    const id = Math.random().toString(36).substring(2, 15);
    const mdi = new MarkdownIt({
      html: true,
      linkify: true,
      highlight (code, language) {
        const validLang = !!(language && hljs.getLanguage(language))
        if (validLang) {
          const lang = language ?? ''
          return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
        }
        return highlightBlock(hljs.highlightAuto(code).value, '')
      }
    })

    function highlightBlock (str, lang) {
      return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
    }

    mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
    mdi.use(mdKatex, { blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000' })
    mdi.use(mikg);

    const addCopyEvents = (selector) => {
      const copyBtn = this.$el.querySelectorAll(selector)
      copyBtn.forEach((btn) => {
        btn.addEventListener('click', ($event) => {
          const code = $event.target.parentElement?.nextElementSibling?.innerText
          if (code) {
            copyToClip(code).then(() => {
              this.$message.success('复制成功')
            })
          }
        })
      })
    }

    const CodeWrap = (md, options = {}) => {
      // Store defaults
      const proxy = (tokens, idx, options, env, self) => self.renderToken(tokens, idx, options);
      const defaultTableOpenRenderer = mdi.renderer.rules.table_open || proxy;

      // Parse options
      const wrapTag = options.wrapTag || 'div';
      const wrapClass = options.wrapClass || '';
      const hasToolbar = options.hasToolbar || false;
      const hasCopyButton = options.hasOwnProperty('hasCopyButton') ? Boolean(options.hasCopyButton) : true;
      const toolbarTag = options.toolbarTag || 'div';
      const toolbarLabel = options.toolbarLabel || '';
      const toolbarClass = options.toolbarClass || '';
      const isButtonInToolbar = options.isButtonInToolbar || false;
      const copyButtonGlobalAttrs = options.copyButtonAttrs || {};
      const copyButtonLabel = options.copyButtonLabel || 'Copy';

      if (typeof toolbarTag !== 'string') {
        throw new Error('The CodeWrap `toolbarTag` property must be provided as a string');
      }

      // A function that returns a function, so we can pass in the default renderer — currying? Probably…
      const tableOpenCustomRenderer = (defaultRenderer) => function (tokens, idx, options, env, self) {
        // Set up copy button attributes
        let copyButtonAttrs = { type: 'button' }; // You could overwrite this but… why?
        Object.assign(copyButtonAttrs, copyButtonGlobalAttrs); // Expand the attributes with the user-provided ones

        // Create a string for all button attributes
        let buttonAttrs = Object.entries(copyButtonAttrs)
          .map((attr) => `${attr[0]}="${typeof attr[1] === 'function' ? attr[1](tokens, idx, options, env, self) : attr[1]}"`) // All attributes can be callbacks
          .join(' ');

        // Markup
        let wrapOpenTag = wrapClass ? `<${wrapTag} class="${wrapClass}">` : `<${wrapTag}>`;
        let buttonMarkup = `<button ${buttonAttrs}>${typeof copyButtonLabel === 'function' ? copyButtonLabel(tokens, idx, options, env, self) : copyButtonLabel}</button>`;
        let toolbar = hasToolbar
          ? `<${toolbarTag} class="${toolbarClass} ${toolbarClass}-${id}">${typeof toolbarLabel === 'function' ? toolbarLabel(tokens, idx, options, env, self) : toolbarLabel}${
            isButtonInToolbar ? buttonMarkup : ''
          }</${toolbarTag}>`
          : '';
        let beforeClose = hasCopyButton && (!hasToolbar || !isButtonInToolbar) ? buttonMarkup : '';

        return `${wrapOpenTag}${toolbar}${beforeClose}${defaultRenderer(tokens, idx, options, env, self)}`;
      };
      const tableCloseCustomRenderer = (defaultRenderer) => function (tokens, idx, options, env, self) {
        // Markup
        let wrapCloseTag = `</${wrapTag}>`;

        return `${defaultRenderer(tokens, idx, options, env, self)}${wrapCloseTag}`;
      };

      md.renderer.rules.table_open = tableOpenCustomRenderer(defaultTableOpenRenderer);
      md.renderer.rules.table_close = tableCloseCustomRenderer(defaultTableOpenRenderer);
    }

    mdi.use(CodeWrap, {
      hasToolbar: true,
      hasCopyButton: true,
      copyButtonLabel: '复制表格',
      copyButtonAttrs: {
        class: 'el-button el-button--default el-button--mini'
      },
      toolbarClass: 'copy-table-wrapper',
      isButtonInToolbar: true
    })

    return {
      id: id,
      mdi,
      addCopyEvents
    }
  },
  computed: {
    html () {
      return this.mdi.render(this.content)
    }
  },
  mounted () {
    this.$forceUpdate()
    console.log(this.id)
  },
  updated () {
    this.addCopyEvents('.code-block-header__copy-' + this.id)
    this.addCopyEvents('.copy-table-wrapper-' + this.id)
  }
}
</script>

<template>
  <div class="whitespace-pre-wrap markdown-body" v-html="html"/>
</template>

<style lang="scss">
@import "../../styles/common.scss";
.markdown-body {
  background-color: transparent;
  font-size: 14px;

  h3 {
    margin-top: 16px;
    margin-bottom: 8px;
  }

  .copy-table-wrapper {
    padding-bottom: 4px;
  }

  p {
    white-space: pre-wrap;
    margin-top: 4px;
    margin-bottom: 4px;
  }

  ol {
    list-style-type: decimal;
  }

  ul {
    list-style-type: disc;
  }

  pre code,
  pre tt {
    line-height: 1.65;
  }

  .highlight pre,
  pre {
    background-color: #fff;
  }

  code.hljs {
    padding: 0;
  }

  .code-block {
    &-wrapper {
      position: relative;
      padding-top: 24px;
    }

    &-header {
      position: absolute;
      top: 5px;
      right: 0;
      width: 100%;
      padding: 0 1rem;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: #b3b3b3;

      &__copy {
        cursor: pointer;
        margin-left: 0.5rem;
        user-select: none;

        &:hover {
          color: #65a665;
        }
      }
    }
  }

  &.markdown-body-generate > dd:last-child:after,
  &.markdown-body-generate > dl:last-child:after,
  &.markdown-body-generate > dt:last-child:after,
  &.markdown-body-generate > h1:last-child:after,
  &.markdown-body-generate > h2:last-child:after,
  &.markdown-body-generate > h3:last-child:after,
  &.markdown-body-generate > h4:last-child:after,
  &.markdown-body-generate > h5:last-child:after,
  &.markdown-body-generate > h6:last-child:after,
  &.markdown-body-generate > li:last-child:after,
  &.markdown-body-generate > ol:last-child li:last-child:after,
  &.markdown-body-generate > p:last-child:after,
  &.markdown-body-generate > pre:last-child code:after,
  &.markdown-body-generate > td:last-child:after,
  &.markdown-body-generate > ul:last-child li:last-child:after {
    animation: blink 1s steps(5, start) infinite;
    color: #000;
    content: '|';
    font-weight: bold;
    margin-left: 3px;
    vertical-align: baseline;
  }

  @keyframes blink {
    to {
      visibility: hidden;
    }
  }
}

html.dark {

  .markdown-body {

    &.markdown-body-generate > dd:last-child:after,
    &.markdown-body-generate > dl:last-child:after,
    &.markdown-body-generate > dt:last-child:after,
    &.markdown-body-generate > h1:last-child:after,
    &.markdown-body-generate > h2:last-child:after,
    &.markdown-body-generate > h3:last-child:after,
    &.markdown-body-generate > h4:last-child:after,
    &.markdown-body-generate > h5:last-child:after,
    &.markdown-body-generate > h6:last-child:after,
    &.markdown-body-generate > li:last-child:after,
    &.markdown-body-generate > ol:last-child li:last-child:after,
    &.markdown-body-generate > p:last-child:after,
    &.markdown-body-generate > pre:last-child code:after,
    &.markdown-body-generate > td:last-child:after,
    &.markdown-body-generate > ul:last-child li:last-child:after {
      color: #65a665;
    }
  }

  .message-reply {
    .whitespace-pre-wrap {
      white-space: pre-wrap;
      color: var(--n-text-color);
    }
  }

  .highlight pre,
  pre {
    background-color: #282c34;
  }
}

@media screen and (max-width: 533px) {
  .markdown-body .code-block-wrapper {
    padding: unset;

    code {
      padding: 24px 16px 16px 16px;
    }
  }
}

</style>
