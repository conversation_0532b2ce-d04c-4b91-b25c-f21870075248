<style lang="scss" scoped>
    @import 'colorChange';
</style>
<template>
    <!--<el-color-picker v-model="theme" class="theme-picker" :predefine="predefineColors"  popper-class="theme-picker-dropdown">-->
    <!--</el-color-picker>-->
    <div class="color-change">
        <el-tooltip v-for="(item,index) in predefineColors" placement="top-start"
        :content="'设置颜色' + item" :key="index">
            <div class="color-change-item" :style="{background:item}"
            @click="changeColor(item)">
                <i class="iconfont ic-check color-change-item-icon"
                   v-if="item === primaryColor"></i>
            </div>
        </el-tooltip>
    </div>
</template>

<script>
import { baseMixin } from '@/store/app-mixin'
const ORIGINAL_THEME = '#D6001D'
export default {
  name: 'colorChange',
  data () {
    return {
      chalk: '',
      theme: ORIGINAL_THEME,
      predefineColors: [
        '#14bcf5',
        '#F5222D',
        '#FA541C',
        '#FAAD14',
        '#13C2C2',
        '#52C41A',
        '#1890FF',
        '#2F54EB',
        '#722ED1'
      ]
    }
  },
  mixins: [baseMixin],
  mounted () {
    if (this.CheckIsColor(this.primaryColor)) {
      this.theme = this.primaryColor
    }
  },
  watch: {
    theme (val, oldVal) {
      if (typeof val !== 'string') return
      this.$store.commit('color', val)
      const themeCluster = this.getThemeCluster(val.replace('#', ''))
      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))
      // const getHandler = (variable, id) => {
      //     return () => {
      //         const originalCluster = this.getThemeCluster(
      //             ORIGINAL_THEME.replace("#", "")
      //         );
      //         const newStyle = this.updateStyle(
      //             this[variable],
      //             originalCluster,
      //             themeCluster
      //         );
      //         console.log(newStyle);
      //
      //         let styleTag = document.getElementById(id);
      //         if (!styleTag) {
      //             styleTag = document.createElement("style");
      //             styleTag.setAttribute("id", id);
      //             document.head.appendChild(styleTag);
      //         }
      //         styleTag.innerText = newStyle;
      //     };
      // };
      // //
      // const chalkHandler = getHandler("chalk", "chalk-style");
      // // console.log(this.chalk)
      // //
      // if (this.chalk) {
      //     chalkHandler();
      // }
      //
      const styles = [].slice
        .call(document.querySelectorAll('style'))
      // .filter(style => {
      //     const text = style.innerText;
      //     console.log(style,new RegExp(oldVal, "i").test(text),text);
      //     return (
      //         new RegExp(oldVal, "i").test(text) && !/Chalk Variables/.test(text)
      //     );
      // });
      // console.log(styles)
      // console.log(document.querySelectorAll("style"));
      styles.forEach(style => {
        const { innerText } = style
        if (typeof innerText !== 'string') return
        // console.log(style.innerText)
        style.innerText = this.updateStyle(
          innerText,
          originalCluster,
          themeCluster
        )
      })
    }
  },

  methods: {
    changeColor (color) {
      this.theme = color
      this.$emit('colorChange', color)
    },
    CheckIsColor (bgVal) {
      var type = '^#[0-9a-fA-F]{6}$'
      var re = new RegExp(type)
      if (bgVal.match(re) == null) {
        type = '^[rR][gG][Bb][\(]([\\s]*(2[0-4][0-9]|25[0-5]|[01]?[0-9][0-9]?)[\\s]*,){2}[\\s]*(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)[\\s]*[\)]{1}$'
        re = new RegExp(type)
        if (bgVal.match(re) == null) {
          return false
        } else {
          return true
        }
      } else {
        return true
      }
    },
    updateStyle (style, oldCluster, newCluster) {
      let newStyle = style
      oldCluster.forEach((color, index) => {
        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])
      })
      return newStyle
    },

    getThemeCluster (theme) {
      const tintColor = (color, tint) => {
        let red = parseInt(color.slice(0, 2), 16)
        let green = parseInt(color.slice(2, 4), 16)
        let blue = parseInt(color.slice(4, 6), 16)

        if (tint === 0) {
          // when primary color is in its rgb space
          return [red, green, blue].join(',')
        } else {
          red += Math.round(tint * (255 - red))
          green += Math.round(tint * (255 - green))
          blue += Math.round(tint * (255 - blue))

          red = red.toString(16)
          green = green.toString(16)
          blue = blue.toString(16)

          return `#${red}${green}${blue}`
        }
      }

      const shadeColor = (color, shade) => {
        let red = parseInt(color.slice(0, 2), 16)
        let green = parseInt(color.slice(2, 4), 16)
        let blue = parseInt(color.slice(4, 6), 16)

        red = Math.round((1 - shade) * red)
        green = Math.round((1 - shade) * green)
        blue = Math.round((1 - shade) * blue)

        red = red.toString(16)
        green = green.toString(16)
        blue = blue.toString(16)

        return `#${red}${green}${blue}`
      }

      const clusters = [theme]
      for (let i = 0; i <= 9; i++) {
        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))
      }
      clusters.push(shadeColor(theme, 0.1))
      return clusters
    }
  }
}
</script>
